{"timestamp": "2025-07-18T13:54:48.801488", "total_files_analyzed": 553, "total_issues": 257, "issues_by_severity": {"info": 245, "error": 2, "warning": 10}, "issues_by_category": {"htmx": 245, "django": 2, "bootstrap": 3, "template_tags": 4, "fix": 3}, "passes": {"pattern_matching": [{"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/base.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "DJANGO_structure", "severity": "error", "category": "django", "description": "Template not in proper app namespace", "file_path": "/home/<USER>/coding/clear_htmx/templates/home.html", "line_number": null, "fix_available": true, "fix_description": null, "score": 0.7}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/home.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "DJANGO_structure", "severity": "error", "category": "django", "description": "Template not in proper app namespace", "file_path": "/home/<USER>/coding/clear_htmx/templates/htmx_base.html", "line_number": null, "fix_available": true, "fix_description": null, "score": 0.7}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/errors/500.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/errors/404.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/tests/browser_compatibility.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "BS5_attr_data-backdrop", "severity": "warning", "category": "bootstrap", "description": "Bootstrap 4 attribute 'data-backdrop' should be 'data-bs-backdrop'", "file_path": "/home/<USER>/coding/clear_htmx/templates/components/modal_base.html", "line_number": null, "fix_available": true, "fix_description": "Replace 'data-backdrop' with 'data-bs-backdrop'", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/components/modal_base.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/components/conflict_resolution_modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "BS5_spacing_ml-", "severity": "warning", "category": "bootstrap", "description": "Bootstrap 4 class 'ml-' should be 'ms-'", "file_path": "/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Replace 'ml-' with 'ms-'", "score": 0.0}, {"issue_id": "BS5_spacing_mr-", "severity": "warning", "category": "bootstrap", "description": "Bootstrap 4 class 'mr-' should be 'me-'", "file_path": "/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Replace 'mr-' with 'me-'", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/components/projects/project_card.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/messaging/htmx/communication_insights.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/infrastructure/symbol_palette/symbol_search_results.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/infrastructure/htmx/utility_conflicts.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/shared/components/ui/htmx_modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/shared/components/ui/project_assignment_modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/templates/shared/components/ui/htmx_data_table.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/focus_management_demo.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard_widgets.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/timesheet.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/htmx/_dashboard_stats.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/dashboard_stats.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/quick_navigation_widget.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/whisper_count.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/error_message.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_system_status.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/document_card.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/task_datagrid_body.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/recent_activity.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_card.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/task_datagrid.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_success.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/htmx_error.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_progress.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/assignment_result.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/core/templates/core/deprecation/dashboard/components/overview.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/signup.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/login.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/enhanced_dropdown.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/notebook.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/profile/activity_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_profile.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/wizard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/infinite_scroll.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/tooltip_content.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/cached_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/button.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/save_success.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/version_history.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/floating_help_button.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/comment_count.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/progress_bar.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/main.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/search_autocomplete.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/table.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/filters.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/vote_count.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/analytics_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/demo.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/breadcrumb.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/comment_replies.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/empty_tooltip.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_analytics.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/statistics.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/scheduled_report_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/popular_symbols.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/builder_interface.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/financial_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/components/reports/kpi_cards.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/shared/components/dashboard/stats_cards.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/api/templates/api/project_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/operation_details.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dots.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/pulse.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/spinner.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/skeleton.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/search_tips.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_results.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/user_operations.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/default.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_grid.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/upload_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/shared/components/documents/file_preview.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_hypermedia", "severity": "warning", "category": "htmx", "description": "HTMX request without explicit target", "file_path": "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "line_number": null, "fix_available": true, "fix_description": "Add hx-target attribute for explicit state transfer", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_hypermedia", "severity": "warning", "category": "htmx", "description": "HTMX request without explicit target", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Add hx-target attribute for explicit state transfer", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/mapping.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/symbol_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/categories_tree.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/main.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/filters.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/quick_add_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/place_symbol_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/search_results.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/import_library.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/popular_symbols.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/analysis/proximity.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/documentation.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/category_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/test_websocket.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/notifications/list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/team_channels_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/user_status_indicator.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/thread_created.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/mentions_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/thread_reply.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/messages_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread_with_pagination.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/reaction_picker.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/collaboration_settings.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/search_results.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_hypermedia", "severity": "warning", "category": "htmx", "description": "HTMX request without explicit target", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/search_results.html", "line_number": null, "fix_available": true, "fix_description": "Add hx-target attribute for explicit state transfer", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/settings_saved.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/dashboard/team_chat.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_conversation.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/calendar.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "TEMPLATE_tags", "severity": "warning", "category": "template_tags", "description": "Missing {% load static %} for static tag usage", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "line_number": null, "fix_available": true, "fix_description": "Add missing {% load %} statement", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "TEMPLATE_tags", "severity": "warning", "category": "template_tags", "description": "Missing {%  load i18n %} for i18n tag usage", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards.html", "line_number": null, "fix_available": true, "fix_description": "Add missing {% load %} statement", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/map.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/saved_searches.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_empty.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/debug_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create_from_template.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/model_fields.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "TEMPLATE_tags", "severity": "warning", "category": "template_tags", "description": "Missing {%  load i18n %} for i18n tag usage", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html", "line_number": null, "fix_available": true, "fix_description": "Add missing {% load %} statement", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_bar.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/quick_entry_modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/entry_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/confirm_delete.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "TEMPLATE_tags", "severity": "warning", "category": "template_tags", "description": "Missing {%  load i18n %} for i18n tag usage", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_form.html", "line_number": null, "fix_available": true, "fix_description": "Add missing {% load %} statement", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_form.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/notification_list.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/system_history.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}, {"issue_id": "HTMX_naming", "severity": "info", "category": "htmx", "description": "HTMX template should follow partial naming convention", "file_path": "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/modals/search_modal.html", "line_number": null, "fix_available": true, "fix_description": "Rename to include _partial.html suffix or move to partials/ directory", "score": 0.0}], "fix_generation": [{"issue_id": "FIX_BS5_attr_data-backdrop", "severity": "info", "category": "fix", "description": "Fix available: Replace 'data-backdrop' with 'data-bs-backdrop'", "file_path": "/home/<USER>/coding/clear_htmx/templates/components/modal_base.html", "line_number": null, "fix_available": true, "fix_description": ["sed -i 's/\\bdata-backdrop/\\bdata-bs-backdrop/g' /home/<USER>/coding/clear_htmx/templates/components/modal_base.html"], "score": 0.0}, {"issue_id": "FIX_BS5_spacing_ml-", "severity": "info", "category": "fix", "description": "Fix available: Replace 'ml-' with 'ms-'", "file_path": "/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html", "line_number": null, "fix_available": true, "fix_description": ["sed -i 's/\\bml-/\\bms-/g' /home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html"], "score": 0.0}, {"issue_id": "FIX_BS5_spacing_mr-", "severity": "info", "category": "fix", "description": "Fix available: Replace 'mr-' with 'me-'", "file_path": "/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html", "line_number": null, "fix_available": true, "fix_description": ["sed -i 's/\\bmr-/\\bme-/g' /home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html"], "score": 0.0}]}, "fixes_applied": [], "learning_insights": ["Found 3 Bootstrap 4 patterns - full migration needed"], "metadata": {"generated_at": "2025-07-18T13:54:49.438098", "compliance_type": "templates", "version": "1.0", "generator": "CLEAR Compliance System"}}