[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "clear-htmx"
version = "0.1.0"
description = "CLEAR Platform - Django + HTMX Web Application"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}

[tool.pip-tools]
generate-hashes = false
upgrade = false
emit-options = true
emit-find-links = true
strip-extras = true

[tool.black]
line-length = 120
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | venv_wsl
  | build
  | dist
  | migrations
  | staticfiles
  | media
  | resources
  | \.idea
  | node_modules
  | electron
  | tests_backup_20250724_200212
  | google-chrome
  | generated_stubs
  | clear-htmx\.egg-info
)/
'''

[tool.isort]
profile = "black"
line_length = 120
known_django = ["django"]
sections = ["FUTURE", "STDLIB", "DJANGO", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
skip_glob = ["*/migrations/*", "*/static/*", "*/media/*", "*/resources/*", "*/venv/*", "*/venv_wsl/*", "*/node_modules/*", "*/electron/*", "*/staticfiles/*", "*/.idea/*", "*/tests_backup_20250724_200212/*", "*/google-chrome/*", "*/generated_stubs/*"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
plugins = ["mypy_django_plugin.main"]

[tool.django-stubs]
django_settings_module = "config.settings.local"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "config.settings.test"
python_files = ["test_*.py", "*_test.py", "tests.py"]
addopts = [
    "--reuse-db",
    "--nomigrations",
    "--cov=.",
    "--cov-report=html",
    "--cov-report=term-missing:skip-covered",
    "-x",
    "--ff",
    "-p no:warnings",
]
testpaths = ["tests"]

[tool.coverage.run]
source = ["."]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/staticfiles/*",
    "*/media/*",
    "manage.py",
    "*/settings/*",
    "*/wsgi.py",
    "*/asgi.py",
    "venv/*",
    "venv_wsl/*",
    "resources/*",
]

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = true

[tool.bandit]
exclude_dirs = ["venv", "venv_wsl", "resources"]
tests = ["B201", "B301"]
skips = ["B101", "B601"]

[tool.pydocstyle]
inherit = false
convention = "google"
add-ignore = [
    "D100",  # Missing docstring in public module (optional for __init__.py)
    "D104",  # Missing docstring in public package (optional for __init__.py)
    "D105",  # Missing docstring in magic method (optional)
    "D107",  # Missing docstring in __init__ (optional if class is documented)
    "D203",  # 1 blank line required before class docstring (conflicts with D211)
    "D213",  # Multi-line docstring summary should start at the second line (conflicts with D212)
]
match = "(?!migrations|test_|conftest).*\\.py"
match-dir = "(?!migrations|__pycache__|venv|venv_wsl|staticfiles|media|resources|node_modules|electron|tests_backup_20250724_200212|google-chrome|generated_stubs|clear-htmx.egg-info).*"

[tool.djlint]
profile = "django"
max_line_length = 120
format_attribute_template_tags = true
linter_output_format = "{code}: {message} {match} {filename}:{line}"
indent = 2
exclude = ".git,venv,venv_wsl,staticfiles,resources"
ignore = "H006,H013,H016,H017,H020,H021"
blank_line_after_tag = "load,extends,endblock,endfor,endif,endwith,endcomment,endspaceless,endautoescape"
blank_line_before_tag = "block,for,if,with,comment,spaceless,autoescape"
custom_blocks = "tailwind_css"
custom_html = "dialog,x-data,x-show,@click"

[tool.ruff]
line-length = 120
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    "venv",
    "venv_wsl",
    ".tox",
    "dist",
    "staticfiles",
    "media",
    "resources",
    ".idea",
    "node_modules",
    "electron",
    "*.egg-info",
    ".pytest_cache",
    "htmlcov",
    "tests_backup_20250724_200212",
    "google-chrome",
    "generated_stubs",
    "clear-htmx.egg-info",
]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "DJ", # flake8-django
]
ignore = [
    "E501",  # line too long
    "B008",  # do not perform function calls in argument defaults
    "B905",  # zip without explicit strict parameter
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]
# Django setup scripts need imports after django.setup()
"**/analyze_*.py" = ["E402"]
"**/validate_*.py" = ["E402"]
"**/store_*.py" = ["E402"]
"**/debug_*.py" = ["E402"]
"**/test_*.py" = ["E402"]
"tests/e2e/**.py" = ["E402"]
"analyzers/**.py" = ["E402"]

[tool.ruff.lint.isort]
known-third-party = ["django"]

[tool.pylint.MASTER]
load-plugins = ["pylint_django"]

[tool.pylint."MESSAGES CONTROL"]
extension-pkg-whitelist = ["django"]
disable = [
    "missing-module-docstring",
    "missing-function-docstring",
    "too-many-ancestors",
    "too-few-public-methods",
    "duplicate-code",
    "invalid-name",
]

[tool.pylint.BASIC]
good-names = ["pk", "id", "i", "j", "k", "ex", "Run", "_"]

[tool.pylint.FORMAT]
max-line-length = 120
