#!/usr/bin/env python3
"""
fix_htmx_swap.py - Targeted fix for: HTMX requests missing hx-swap
"""

import argparse
import re
from pathlib import Path


def fix_issue(file_path: str, issue_description: str) -> bool:
    """Apply specific character-level fix to the file."""
    try:
        content = Path(file_path).read_text(encoding="utf-8")
        original_content = content

        # Fix HTMX requests missing hx-swap
        fixes_applied = 0

        # Find HTMX request attributes without hx-swap
        htmx_patterns = ["hx-get", "hx-post", "hx-put", "hx-delete", "hx-patch"]

        for htmx_attr in htmx_patterns:
            # Find elements with this HTMX attribute
            pattern = rf'<([^>]*?{htmx_attr}=["\'][^"\']*["\'][^>]*?)(?!.*hx-swap)([^>]*?)>'
            matches = re.finditer(pattern, content, re.IGNORECASE)

            for match in matches:
                element_tag = match.group(0)

                # Check if hx-swap is already present
                if "hx-swap" in element_tag:
                    continue

                # Determine appropriate hx-swap based on context
                swap_method = "innerHTML"  # Default

                if htmx_attr in ["hx-delete"]:
                    swap_method = "outerHTML"
                elif "append" in element_tag.lower() or "add" in element_tag.lower():
                    swap_method = "beforeend"
                elif "prepend" in element_tag.lower():
                    swap_method = "afterbegin"
                elif "replace" in element_tag.lower():
                    swap_method = "outerHTML"

                # Add hx-swap attribute
                new_element = element_tag.replace(">", f' hx-swap="{swap_method}">')
                content = content.replace(element_tag, new_element)
                fixes_applied += 1

        if content != original_content:
            # Create backup
            backup_path = f"{file_path}.backup"
            Path(backup_path).write_text(original_content, encoding="utf-8")

            # Apply fix
            Path(file_path).write_text(content, encoding="utf-8")
            print(f"SUCCESS: Added hx-swap to {fixes_applied} HTMX requests in {file_path}")
            return True
        else:
            print(f"INFO:  No HTMX requests missing hx-swap found in {file_path}")
            return True

    except Exception as e:
        print(f"ERROR: Error fixing {file_path}: {e}")
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Fix HTMX requests missing hx-swap")
    parser.add_argument("--file", required=True, help="File to fix")
    parser.add_argument("--issue", required=True, help="Issue description")

    args = parser.parse_args()

    success = fix_issue(args.file, args.issue)
    exit(0 if success else 1)
