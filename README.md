# CLEAR - Utility Coordination Platform

![Django](https://img.shields.io/badge/Django-5.2+-green.svg)
![HTMX](https://img.shields.io/badge/HTMX-Dynamic-blue.svg)
![PostGIS](https://img.shields.io/badge/PostGIS-Spatial-orange.svg)
![Python](https://img.shields.io/badge/Python-3.12+-blue.svg)
![License](https://img.shields.io/badge/License-Proprietary-blue.svg)

CLEAR is an enterprise-grade, multi-tenant utility infrastructure management platform that revolutionizes coordination, visualization, and conflict detection for utility projects. Built with Django 5.2+ and HTMX, it provides server-rendered dynamic interactions, real-time collaboration, and comprehensive project management capabilities while eliminating the complexity of client-server state synchronization. As a multi-tenant SaaS platform, CLEAR enables organizations to maintain complete data isolation while sharing infrastructure, with plans to allow clients to become their own tenants in future releases.

## 📚 Documentation

**Complete documentation is available in the `/docs/` directory.**

**Quick Start:**

- **New to CLEAR?** → See the `docs/` directory.
- **Developer?** → See the `docs/` directory.
- **Need Help?** → See the `docs/` directory.

## The Power of CLEAR

CLEAR isn't just a name. It's a revolutionary approach to utility coordination:

**Connect**
Bringing every stakeholder into a seamless collaborative environment. No more information silos. No more communication gaps.

**Locate**
Transforming invisible infrastructure into vivid, actionable data. See what others can't see. Prevent what others can't prevent.

**Eliminate**
Conflicts vanish before they exist. Predictive technology identifies and resolves issues at the earliest possible stage.

**Accelerate**
Projects move at unprecedented speed when coordination barriers disappear. What once took weeks now takes moments.

**Resolve**
Complex utility challenges become simple, structured workflows. The most difficult aspect of infrastructure development, made intuitive.

---

## 🏗️ Django + HTMX Architecture

CLEAR features a robust server-side architecture optimized for scale, performance, and simplicity:

- **Main Application**: Django 5.2+ with HTMX dynamic rendering
- **Spatial Processor**: PostGIS-powered conflict detection
- **Real-time Collaboration**: Django Channels + WebSockets
- **Authentication**: Django sessions with flexible role-based access control (RBAC)
- **Infrastructure**: PostgreSQL, Redis, Celery background tasks
- **Deployment**: Railway/Docker with WhiteNoise static serving

---

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- PostgreSQL 16+ with PostGIS extension
- Redis 7+ (optional for development)
- uv package manager (recommended) or pip

### Installation

#### Option 1: Development Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/your-org/clear-htmx.git
cd clear-htmx

# Install dependencies using uv (recommended, uses pyproject.toml)
uv sync
# OR with pip (installs from requirements.txt)
pip install -r requirements.txt

# Install development and quality assurance tools
pip install ruff black isort mypy bandit detect-secrets pre-commit

# Configure environment
cp .env.example .env
# Edit .env with your PostgreSQL database credentials

# Set up pre-commit hooks for code quality
pre-commit install

# Set up PostgreSQL databases
# Development database
python manage.py migrate --settings=config.settings.development
python manage.py createsuperuser --settings=config.settings.development

# Staging database (production-ready)  
python manage.py migrate --settings=config.settings.production

# Run code quality checks before starting development
ruff check .                    # Lint code
ruff format .                  # Format code
bandit -r . --severity-level medium  # Security scan

# Start development server (uses development database)
python manage.py runserver --settings=config.settings.development
```

#### Option 2: Docker Setup

```bash
# Clone the repository
git clone https://github.com/your-org/clear-htmx.git
cd clear-htmx

# Configure environment
cp .env.example .env.production
# Edit .env.production with your production credentials

# Start all services
docker-compose up -d

# Run database migrations
docker-compose exec web python manage.py migrate
```

Access the application at:

- **Development**: http://localhost:8743
- **Admin Interface**: http://localhost:8743/admin
- **API Documentation**: http://localhost:8743/api/

---

## 📋 Table of Contents

1. [What is CLEAR?](#what-is-clear)
2. [Key Features](#key-features)
3. [Getting Started for Users](#getting-started-for-users)
4. [Common Workflows](#common-workflows)
5. [Architecture Overview](#architecture-overview)
6. [Technology Stack](#technology-stack)
7. [Project Structure](#project-structure)
8. [API Documentation](#api-documentation)
9. [Database Schema](#database-schema)
10. [Template System](#template-system)
11. [Authentication & Security](#authentication--security)
12. [Role Management System](#role-management-system)
13. [Testing Strategy](#testing-strategy)
14. [Performance Optimization](#performance-optimization)
15. [Deployment Guide](#deployment-guide)
16. [Contributing](#contributing)
17. [Utility Scripts](#utility-scripts)

---

## What is CLEAR?

CLEAR is a sophisticated Django-based, multi-tenant platform designed for utility infrastructure management. It enables organizations to:

- **Manage Projects**: Track utility coordination projects from inception to completion with organization-specific workflows
- **Detect Conflicts**: PostGIS-powered spatial conflict detection prevents costly utility strikes
- **Collaborate in Real-time**: Teams work together with live HTMX updates and activity feeds
- **Visualize Infrastructure**: 2D GIS visualization of underground utilities with planned 3D support
- **Ensure Compliance**: Built-in audit trails and regulatory reporting
- **Scale as SaaS**: Multi-tenant architecture supports multiple organizations with complete data isolation

### Business Value

CLEAR is designed to deliver significant value through:

- **ROI**: Reduced project delays and rework costs
- **Time Savings**: Streamlined coordination and communication
- **Risk Reduction**: Proactive conflict detection prevents utility strikes
- **Efficiency**: Server-rendered workflows and real-time collaboration
- **Compliance**: Built-in audit trails for regulatory requirements

## Key Features

### 🗺️ Advanced GIS & Mapping

- **2D Mapping**: PostGIS-powered spatial operations with CAD-style drawing tools
- **Conflict Detection**: Automated hard/soft conflict detection with depth analysis (0-20ft)
- **File Support**: Import/export DXF, DWG, SHP, GeoJSON formats
- **Coordinate Systems**: Support for multiple projections including Indiana State Plane
- **Spatial Queries**: Advanced PostGIS operations for utility analysis

### 📊 Project Management

- **Multi-tenant SaaS**: Organization-based data isolation with plans for client-to-tenant promotion
- **Templates**: Reusable project templates with custom workflows
- **Document Management**: Version-controlled document storage
- **Time Tracking**: Integrated timesheet management
- **Communication Logs**: Track all stakeholder communications

### 👥 Collaboration

- **Real-time Updates**: HTMX-powered live activity feeds and notifications
- **Comments**: Universal commenting system on projects, utilities, and conflicts
- **Team Chat**: Built-in messaging system
- **Task Management**: Integrated task assignment and tracking

### 📈 Analytics & Reporting

- **Dashboard**: Customizable metrics and KPIs with server-rendered components
- **Report Generation**: One-click professional reports
- **Data Export**: Excel, PDF, and API access
- **Audit Trails**: Complete activity logging

---

## Getting Started for Users

### Accessing CLEAR

1. **Web Browser**: Navigate to https://clear.yourcompany.com
2. **Login**: Use your company credentials
3. **Mobile**: Fully responsive design works on any device

### Main Interface Areas

#### Dashboard

Your personalized command center showing:

- Active projects and recent updates
- Team activity feed with real-time HTMX updates
- Upcoming deadlines and meetings
- Key performance metrics

#### Projects

- Create and manage utility coordination projects
- Upload documents and CAD files
- Track project phases and milestones
- Generate conflict reports with PostGIS analysis

#### GIS/Mapping

- Draw utilities directly on the map
- Set depth and utility specifications
- Run spatial conflict detection analysis
- Export to various formats

#### Reports

- Generate standard reports
- Create custom report templates
- Schedule automated reports
- Export to Excel/PDF

## Common Workflows

### Creating a New Project

1. Click "New Project" from the dashboard
2. Enter project details:
   - Name and description
   - Location (address or coordinates)
   - Start/end dates
   - Project template (if applicable)
3. Add team members and set permissions
4. Upload initial documents
5. Begin utility mapping

### Detecting Utility Conflicts

1. Open your project
2. Navigate to "Conflict Detection"
3. Select utilities to analyze
4. Set conflict parameters:
   - Buffer distance (hard conflicts)
   - Clearance requirements (soft conflicts)
   - Depth range
5. Run PostGIS spatial analysis
6. Review detected conflicts
7. Export conflict report

### Generating Reports

1. Go to "Reports" section
2. Select report type:
   - Project Summary
   - Conflict Analysis
   - Progress Report
   - Stakeholder Communication Log
3. Set date range and filters
4. Click "Generate"
5. Preview and download/email

---

# Developer Documentation

## Architecture Overview

CLEAR follows a modern, server-side architecture built on Django with HTMX:

```
┌─────────────────────────────────────────────────────────────────┐
│                    Frontend (Django Templates + HTMX)            │
│  ┌─────────────┐  ┌──────────────┐  ┌────────────────────────┐ │
│  │ Bootstrap 5 │  │ HTMX Dynamic │  │ Server-Rendered        │ │
│  │ Components  │  │ Interactions │  │ Templates              │ │
│  └─────────────┘  └──────────────┘  └────────────────────────┘ │
└─────────────────────────┬───────────────────────────────────────┘
                          │ HTTP/WebSocket
┌─────────────────────────┴───────────────────────────────────────┐
│                     Django Application Layer                     │
│  ┌──────────────┐  ┌──────────────┐  ┌────────────────────┐   │
│  │ Views &      │  │ Django Auth  │  │ Django ORM        │   │
│  │ URL Routing  │  │ + Sessions   │  │ + PostGIS         │   │
│  └──────────────┘  └──────────────┘  └────────────────────┘   │
└─────────────────────────┬───────────────────────────────────────┘
                          │
┌─────────────────────────┴───────────────────────────────────────┐
│                    Data Layer (PostgreSQL)                       │
│  ┌──────────────┐  ┌──────────────┐  ┌────────────────────┐   │
│  │ PostGIS      │  │ Django       │  │ Redis Cache       │   │
│  │ Extension    │  │ Migrations   │  │ + Celery          │   │
│  └──────────────┘  └──────────────┘  └────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### Key Architectural Decisions

1. **Server-Side First**: HTML IS the state - server holds all state, HTMX provides dynamic updates
2. **Single Source of Truth**: Eliminate client-server state synchronization complexity
3. **PostGIS Powered**: Advanced spatial operations at the database level
4. **Security Hardened**: Django's built-in security with custom extensions
5. **Developer Experience**: Hot reload, comprehensive admin interface, and Django debugging tools

## Technology Stack

### Core Framework

- **Django 5.2+**: Python web framework with ORM
- **Python 3.12+**: Modern Python with type hints
- **HTMX**: Dynamic HTML updates without JavaScript frameworks
- **Bootstrap 5**: Modern, responsive UI components

### Database & Spatial

- **PostgreSQL 16**: Primary database with advanced features
- **PostGIS**: Spatial database extension for GIS operations
- **Django ORM**: Type-safe database operations with migrations
- **Redis**: Caching and session storage

### Frontend & UI

- **Django Templates**: Server-side rendering with template inheritance
- **HTMX**: Dynamic interactions without complex JavaScript
- **Bootstrap 5**: Responsive design and component library
- **Crispy Forms**: Beautiful Django form rendering

### Authentication & Security

- **Django Auth**: Built-in authentication with flexible RBAC system
- **Multi-Tenant Security**: Organization-based data isolation and permissions
- **Django Sessions**: Server-side session management with tenant context
- **Role Management**: Dynamic role assignment with granular permissions
- **CORS Headers**: Cross-origin resource sharing
- **WhiteNoise**: Static file serving for production

### Development Tools

- **Django Extensions**: Enhanced management commands
- **Django Debug Toolbar**: Development debugging (when DEBUG=True)
- **Pytest**: Testing framework with Django integration
- **Playwright**: End-to-end testing

### Code Quality Tools

- **Ruff**: Modern Python linter and formatter (Python 3.13+ compatible)
- **MyPy**: Static type checking
- **Bandit**: Security vulnerability scanner
- **detect-secrets**: Secret detection and baseline management
- **pre-commit**: Git hook framework for code quality
- **Black**: Code formatting (legacy, superseded by Ruff)
- **isort**: Import sorting (legacy, superseded by Ruff)

### Deployment & Infrastructure

- **Gunicorn**: WSGI HTTP server for production
- **Django Channels**: WebSocket support for real-time features
- **Celery**: Background task processing
- **Docker**: Containerization support

## Project Structure

```
clear-htmx/
├── apps/                      # Main Django applications (formerly CLEAR/)
│   ├── __init__.py
│   ├── admin.py               # Django admin configuration
│   ├── apps.py                # App configuration
│   ├── models.py              # Database models with PostGIS fields
│   ├── views.py               # Django views with HTMX integration
│   ├── urls.py                # URL routing
│   ├── api_urls.py            # API endpoint routing
│   ├── api_views.py           # REST API views
│   ├── backends.py            # Custom authentication backends
│   ├── consumers.py           # WebSocket consumers for real-time
│   ├── routing.py             # WebSocket routing
│   ├── tests.py               # Legacy test file
│   ├── tests/                 # Test directory
│   │   ├── __init__.py
│   │   ├── test_models.py     # Model tests
│   │   ├── test_views.py      # View and template tests
│   │   ├── test_api.py        # API endpoint tests
│   │   └── test_example.py    # Example tests
│   ├── migrations/            # Database migrations
│   │   ├── 0001_initial.py    # Initial schema
│   │   ├── 0002_add_supabase_models.py  # Migration models
│   │   └── __init__.py
│   └── management/            # Custom management commands
│       ├── __init__.py
│       └── commands/          # Management command implementations
│           ├── __init__.py
│           ├── cleanup_whispers.py
│           ├── complete_migration.py
│           ├── import_supabase_data.py
│           ├── migrate_supabase_data.py
│           └── run_tests.py
├── config/                # Django project configuration
│   ├── __init__.py
│   ├── settings/          # Django settings (separated by environment)
│   │   ├── base.py
│   │   ├── development.py
│   │   ├── production.py
│   │   └── __init__.py
│   ├── urls.py                # Root URL configuration
│   ├── wsgi.py                # WSGI application for deployment
│   └── asgi.py                # ASGI application for WebSockets
├── templates/                 # Django templates
│   ├── base.html              # Base template with HTMX and Bootstrap
│   ├── base/
│   │   └── base.html          # Alternative base template
│   ├── 403.html               # Permission denied page
│   ├── CLEAR/                 # App-specific templates
│   │   ├── auth/
│   │   │   └── login.html     # Login page
│   │   ├── dashboard.html     # Main dashboard
│   │   └── projects/          # Project management templates
│   │       ├── projects.html
│   │       ├── project_detail.html
│   │       └── partials/      # HTMX partial templates
│   │           └── projects_list.html
│   ├── admin/                 # Admin interface templates
│   │   ├── dashboard.html
│   │   ├── users.html
│   │   ├── analytics.html
│   │   ├── database_management.html
│   │   └── organization_settings.html
│   ├── auth/                  # Authentication templates
│   │   ├── login.html
│   │   ├── error.html
│   │   └── reset-password.html
│   ├── components/            # Reusable template components
│   │   ├── ui/                # UI component templates
│   │   │   ├── badge.html
│   │   │   ├── button.html
│   │   │   ├── card.html
│   │   │   ├── dropdown.html
│   │   │   ├── input.html
│   │   │   ├── modal.html
│   │   │   ├── table.html
│   │   │   └── toast.html
│   │   ├── dashboard/         # Dashboard-specific components
│   │   │   ├── meetings_list.html
│   │   │   ├── my_projects_list.html
│   │   │   ├── tasks_list.html
│   │   │   ├── team_chat.html
│   │   │   └── timesheet_summary.html
│   │   ├── navigation/        # Navigation components
│   │   │   ├── breadcrumb.html
│   │   │   └── tabs.html
│   │   ├── data_table.html    # Reusable data table
│   │   ├── recent_activity.html
│   │   └── dashboard_stats.html
│   ├── projects/              # Project management templates
│   │   ├── projects.html
│   │   ├── project_detail.html
│   │   ├── my-projects.html
│   │   ├── project-portfolio.html
│   │   └── partials/          # HTMX partial templates
│   │       ├── projects_grid.html
│   │       └── portfolio_grid.html
│   ├── dashboard/
│   │   └── dashboard.html     # Main dashboard template
│   ├── mapping/
│   │   └── gis.html           # GIS mapping interface
│   ├── communication/
│   │   └── messages.html      # Team communication
│   ├── documents/
│   │   └── documents.html     # Document management
│   ├── knowledge/
│   │   ├── knowledge.html
│   │   └── knowledge_base.html
│   ├── profile/
│   │   └── profile.html       # User profile management
│   ├── reports/
│   │   └── reports.html       # Report generation
│   ├── settings/
│   │   └── settings.html      # Application settings
│   ├── stakeholders/
│   │   └── stakeholders.html  # Stakeholder management
│   ├── tasks/
│   │   └── tasks.html         # Task management
│   ├── templates/             # Template management system
│   │   ├── templates.html
│   │   ├── template-management.html
│   │   ├── advanced_template_functions.js
│   │   ├── versioning_functions.js
│   │   └── partials/
│   │       └── templates_table_body.html
│   └── timesheet/
│       └── timesheet.html     # Time tracking
├── static/                    # Static files
│   ├── css/
│   │   ├── style.css          # Main stylesheet
│   │   └── input.css          # Tailwind input file
│   ├── js/                    # JavaScript files
│   └── images/                # Image assets
│       ├── egis-logo.svg
│       ├── egis-logo.png
│       ├── egis-logo-white.svg
│       └── impact.jpg
├── e2e/                       # End-to-end tests
│   ├── __init__.py
│   ├── conftest.py            # Pytest configuration
│   ├── test_authentication.py
│   ├── test_dashboard.py
│   └── test_simple_auth.py
├── supabase_dump/            # Exported data from previous system
├── supabase_full_dump/       # Complete data export
├── source-repo/              # Original Next.js source (reference)
├── logs/                     # Application logs
│   └── django.log
├── docker-compose.yml        # Docker configuration
├── Dockerfile               # Docker image definition
├── manage.py                # Django management script
├── pyproject.toml           # Python project configuration
├── requirements.txt         # Python dependencies
├── pytest.ini              # Pytest configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── .env.example             # Environment variables template
├── start.sh                 # Startup script
├── CLAUDE.md                # Development instructions
└── README.md                # This file
```

## API Documentation

### Django Views Architecture

The application uses Django's class-based and function-based views with HTMX integration:

#### Core Views

1. **Dashboard Views** - Main application dashboard
   - `dashboard`: Main dashboard with real-time updates
   - `project_stats`: HTMX endpoint for project statistics
   - `recent_activity`: Live activity feed

2. **Project Management** - Project CRUD operations
   - `projects_list`: Paginated project listing
   - `project_detail`: Detailed project view with spatial data
   - `project_create`: New project creation with validation
   - `project_update`: Update project details
   - `conflict_detection`: PostGIS spatial conflict analysis

3. **Spatial Operations** - GIS and mapping functionality
   - `spatial_data_save`: Save/update utility geometries
   - `conflict_analysis`: Run spatial conflict detection
   - `coordinate_transform`: Transform between coordinate systems

#### HTMX Endpoints

```python
# HTMX partial view example
@require_http_methods(["GET"])
def projects_list_partial(request):
    projects = Project.objects.filter(organization=request.user.organization)
    return render(request, 'projects/partials/projects_list.html', {
        'projects': projects
    })
```

#### REST API Endpoints

In addition to Django views, REST endpoints are available for external integrations:

- `GET /api/projects/` - List projects with filtering
- `POST /api/projects/` - Create new project
- `GET /api/spatial/conflicts/` - Get spatial conflicts
- `POST /api/upload/cad/` - Upload CAD files
- `GET /api/health/` - System health check

### Example Usage

```python
# In a Django view with HTMX
from django.shortcuts import render
from django.http import HttpResponse
from django_htmx import HttpResponseHXRefresh

def update_project(request, project_id):
    project = get_object_or_404(Project, id=project_id)

    if request.method == 'POST':
        # Process form data
        project.name = request.POST.get('name')
        project.save()

        # Return HTMX response
        if request.htmx:
            return render(request, 'projects/partials/project_detail.html', {
                'project': project
            })

        # Regular HTTP response for non-HTMX requests
        return redirect('project_detail', project_id=project.id)

    return render(request, 'projects/project_form.html', {'project': project})
```

## Database Schema

### Django Models Architecture

The database uses PostgreSQL with PostGIS extension and Django's ORM:

#### Core Models

Handles main application entities:

```python
# Core models
class User(AbstractUser):
    """Extended Django user with CLEAR-specific fields"""
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE)
    preferences = models.JSONField(default=dict)
    custom_settings = models.JSONField(default=dict)

    # Flexible role assignment through UserRole model
    def get_roles(self):
        """Get all roles assigned to this user"""
        return self.user_roles.all()

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return self.user_roles.filter(role__name=role_name).exists()

class Organization(models.Model):
    """Multi-tenant organization model"""
    name = models.CharField(max_length=255)
    settings = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

class Project(models.Model):
    """Main project entity with financial and management data"""
    name = models.CharField(max_length=255)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    location = models.PointField(geography=True, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Utility(models.Model):
    """Utility companies and infrastructure with spatial data"""
    name = models.CharField(max_length=255)
    utility_type = models.CharField(max_length=50, choices=UTILITY_TYPE_CHOICES)
    contact_info = models.JSONField(default=dict)

class UtilityLineData(models.Model):
    """Spatial line geometries for utilities"""
    utility = models.ForeignKey(Utility, on_delete=models.CASCADE)
    geometry_2d = models.LineStringField(null=True, blank=True)
    geometry_3d = models.GeometryField(null=True, blank=True)
    depth_start = models.FloatField(null=True, blank=True)
    depth_end = models.FloatField(null=True, blank=True)

class Conflict(models.Model):
    """Detected conflicts with spatial analysis"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE)
    utility_1 = models.ForeignKey(Utility, on_delete=models.CASCADE, related_name='conflicts_as_util1')
    utility_2 = models.ForeignKey(Utility, on_delete=models.CASCADE, related_name='conflicts_as_util2')
    conflict_type = models.CharField(max_length=10, choices=[('hard', 'Hard'), ('soft', 'Soft')])
    distance = models.FloatField()
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES)
    resolved = models.BooleanField(default=False)
```

### Key Database Features

1. **PostGIS Geometry Types**

   ```python
   # Spatial fields in models
   geometry_2d = models.LineStringField()
   geometry_3d = models.GeometryField()
   location = models.PointField(geography=True)
   ```

2. **JSON Fields for Flexibility**
   - User preferences and settings
   - Project metadata and custom fields
   - Organization configuration
   - Contact information storage

3. **Database Indexes**
   - Spatial indexes on geometry fields
   - Composite indexes for common queries
   - Foreign key indexes for performance

4. **Migrations and Constraints**
   - Django migrations for schema versioning
   - Database constraints for data integrity
   - Custom migration for PostGIS setup

## Template System

### Django Templates with HTMX

Built on Django's template engine with HTMX integration:

#### Template Hierarchy

```html
<!-- base.html - Main layout template -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}CLEAR{% endblock %}</title>

    <!-- Bootstrap 5 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}" />
  </head>
  <body>
    {% include 'components/navigation/navbar.html' %}

    <main class="container-fluid">{% block content %}{% endblock %}</main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
```

#### HTMX Integration Patterns

```html
<!-- Dynamic content updates -->
<div
  id="project-list"
  hx-get="{% url 'projects_list_partial' %}"
  hx-trigger="load, project-updated from:body"
  hx-swap="innerHTML"
>
  <!-- Initial content or loading indicator -->
</div>

<!-- Form submission with HTMX -->
<form
  hx-post="{% url 'project_create' %}"
  hx-target="#project-list"
  hx-swap="beforeend"
  hx-on::after-request="this.reset()"
>
  {% csrf_token %} {{ form.as_p }}
  <button type="submit" class="btn btn-primary">Create Project</button>
</form>
```

#### Component Templates

```html
<!-- components/ui/card.html -->
<div class="card {% if card_class %}{{ card_class }}{% endif %}">
  {% if card_header %}
  <div class="card-header">
    <h5 class="card-title">{{ card_title }}</h5>
  </div>
  {% endif %}

  <div class="card-body">{{ card_content }}</div>

  {% if card_footer %}
  <div class="card-footer">{{ card_footer }}</div>
  {% endif %}
</div>
```

### Template Organization Patterns

1. **Base Templates**
   - `base.html` - Main site template
   - `base/base.html` - Alternative base for specific sections

2. **App Templates**
   - `CLEAR/` - Main app templates
   - `admin/` - Administrative interface templates
   - `auth/` - Authentication templates

3. **Component Templates**
   - `components/ui/` - Reusable UI components
   - `components/dashboard/` - Dashboard-specific components
   - `components/navigation/` - Navigation elements

4. **Partial Templates**
   - Used with HTMX for dynamic content updates
   - Located in `partials/` subdirectories
   - Return HTML fragments for specific UI sections

## Authentication & Security

### Django Authentication System

#### Custom User Model with Flexible Role System

```python
# Extended Django user model with flexible role assignment
class User(AbstractUser):
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE)
    preferences = models.JSONField(default=dict)
    is_active = models.BooleanField(default=True)

    def has_organization_access(self, organization):
        return self.organization == organization

    def get_roles(self):
        """Get all roles assigned to this user"""
        return self.user_roles.all()

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return self.user_roles.filter(role__name=role_name).exists()

    def has_permission(self, permission_name):
        """Check if user has a specific permission through their roles"""
        return self.user_roles.filter(
            role__permissions__name=permission_name
        ).exists()

class Role(models.Model):
    """Dynamic role definition"""
    name = models.CharField(max_length=100)
    organization = models.ForeignKey('Organization', on_delete=models.CASCADE)
    permissions = models.ManyToManyField('Permission', blank=True)
    is_active = models.BooleanField(default=True)

class UserRole(models.Model):
    """User-Role assignment with context"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assigned_roles')
```

#### Authentication Flow

1. **Login**: Username/password validated against Django User model with organization context
2. **Session**: Django session framework manages server-side sessions with tenant isolation
3. **Middleware**: Django's built-in authentication middleware enhanced with multi-tenant support
4. **Permissions**: Flexible role-based access control with granular permissions and organization isolation
5. **Role Assignment**: Dynamic role assignment through UserRole relationships
6. **Multi-Tenant Security**: Data isolation enforced at database and application levels
7. **Logout**: Session invalidated and cleared with proper tenant cleanup

### Security Implementation

#### Django Security Settings

```python
# settings.py security configuration
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 31536000
SECURE_REDIRECT_EXEMPT = []
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Additional security
X_FRAME_OPTIONS = 'DENY'
USE_TZ = True
SECRET_KEY = env('SECRET_KEY')  # From environment variables
```

#### Permission System

```python
# Custom permission mixins
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied

class OrganizationRequiredMixin(LoginRequiredMixin):
    """Ensure user belongs to an organization"""

    def dispatch(self, request, *args, **kwargs):
        if not request.user.organization:
            raise PermissionDenied("User must belong to an organization")
        return super().dispatch(request, *args, **kwargs)

class AdminRequiredMixin(LoginRequiredMixin):
    """Require admin role"""

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_admin_user():
            raise PermissionDenied("Admin access required")
        return super().dispatch(request, *args, **kwargs)
```

#### Input Validation

```python
# Django forms with validation
from django import forms
from django.core.exceptions import ValidationError

class ProjectForm(forms.ModelForm):
    class Meta:
        model = Project
        fields = ['name', 'description', 'start_date', 'end_date']

    def clean_name(self):
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise ValidationError("Project name must be at least 3 characters")
        return name

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date >= end_date:
            raise ValidationError("End date must be after start date")

        return cleaned_data
```

## Role Management System

CLEAR implements a flexible role-based access control (RBAC) system that moves beyond static roles to a dynamic, organization-configurable role management system.

### Key Features

#### Dynamic Role Definition
- **Organization-Specific Roles**: Each organization can define their own roles and permissions
- **Permission-Based Access**: Roles are composed of granular permissions
- **Role Hierarchy**: Support for role inheritance and hierarchical permission structures
- **Active/Inactive Roles**: Roles can be temporarily disabled without deletion

#### User-Role Management
- **Multiple Role Assignment**: Users can have multiple roles simultaneously
- **Context-Aware Permissions**: Role assignments include metadata about assignment context
- **Audit Trail**: Track who assigned roles and when
- **Flexible Assignment**: Roles can be assigned/revoked dynamically

#### Permission System
- **Granular Permissions**: Fine-grained control over specific actions and resources
- **Module-Based Permissions**: Permissions organized by app modules (projects, documents, analytics, etc.)
- **Data-Level Permissions**: Control access to specific data types and instances
- **Action-Based Permissions**: Control over create, read, update, delete operations

### Migration from Legacy egis_role

The system has been completely refactored from the legacy `egis_role` field-based approach:

#### Before (Legacy System)
```python
# Old approach - rigid and inflexible
user.egis_role = 'admin'  # Limited to predefined roles
if user.egis_role == 'admin':
    # Grant access
```

#### After (Flexible RBAC)
```python
# New approach - dynamic and configurable
user.user_roles.create(role=Role.objects.get(name='Project Manager'))
if user.has_permission('projects.create'):
    # Grant access based on granular permissions
```

### Common Role Patterns

#### Default Organization Roles
- **Super Administrator**: Full system access across all modules
- **Organization Administrator**: Full access within their organization
- **Project Manager**: Can manage projects and assign team members
- **Engineer**: Can work on assigned projects and update technical data
- **Viewer**: Read-only access to assigned projects

#### Permission Categories
- **Project Permissions**: `projects.create`, `projects.view`, `projects.edit`, `projects.delete`
- **Document Permissions**: `documents.upload`, `documents.view`, `documents.edit`, `documents.download`
- **Analytics Permissions**: `analytics.view`, `analytics.export`, `analytics.configure`
- **User Management**: `users.invite`, `users.manage_roles`, `users.view_profiles`
- **GIS Permissions**: `gis.edit_utilities`, `gis.run_conflicts`, `gis.export_spatial`

### Person vs User Distinction

CLEAR makes an important distinction between Users (login accounts) and Persons (contact records):

#### Users (Authentication)
- **Purpose**: Login accounts with credentials and permissions
- **Security**: Tied to authentication and authorization
- **Scope**: Organization-specific access control
- **Lifecycle**: Created when someone needs system access

#### Persons (Contact Management)
- **Purpose**: Contact records and relationship management
- **Information**: Names, contact details, organization affiliations
- **Scope**: Project stakeholders, external contacts, team members
- **Lifecycle**: Created for anyone involved in projects

#### Relationship Pattern
```python
# A Person may or may not have a User account
class Person(models.Model):
    name = models.CharField(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    user = models.OneToOneField(User, null=True, blank=True, on_delete=models.SET_NULL)

# Users always have authentication capabilities
class User(AbstractUser):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    person = models.OneToOneField(Person, null=True, blank=True, on_delete=models.CASCADE)
```

### Future SaaS Capabilities

#### Multi-Tenant Architecture
CLEAR is designed as a true multi-tenant SaaS platform with:

- **Data Isolation**: Complete separation of organization data
- **Shared Infrastructure**: Efficient resource utilization across tenants
- **Scalable Design**: Support for thousands of organizations
- **Custom Branding**: Organization-specific theming and branding

#### Client-to-Tenant Promotion
Future releases will include the ability to promote clients to their own tenants:

- **Tenant Hierarchy**: Support for parent-child tenant relationships
- **Data Migration**: Seamless migration of client data to independent tenant
- **Billing Integration**: Separate billing and subscription management
- **Self-Service Administration**: Client organizations can manage their own users and settings

#### Example Promotion Workflow
```python
# Future capability - promote client to independent tenant
def promote_client_to_tenant(client_organization, parent_organization):
    """
    Promote a client organization to an independent tenant
    """
    # Create new tenant database schema
    new_tenant = create_tenant_schema(client_organization)

    # Migrate data from parent to new tenant
    migrate_organization_data(client_organization, new_tenant)

    # Update user permissions and access
    update_user_tenant_access(client_organization.users, new_tenant)

    # Setup independent billing
    setup_tenant_billing(new_tenant)

    return new_tenant
```

### Implementation Examples

#### Role Assignment
```python
# Assign role to user
def assign_role(user, role_name, assigned_by):
    role = Role.objects.get(name=role_name, organization=user.organization)
    UserRole.objects.create(
        user=user,
        role=role,
        assigned_by=assigned_by
    )

# Check permissions in views
@require_permission('projects.create')
def create_project(request):
    # User has been verified to have project creation permission
    pass
```

#### Custom Permission Decorator
```python
def require_permission(permission_name):
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.has_permission(permission_name):
                raise PermissionDenied
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator
```

## Testing Strategy

### Test Configuration

#### Pytest with Django Integration

```python
# pytest.ini
[tool:pytest]
DJANGO_SETTINGS_MODULE = clear_htmx.settings
python_files = tests.py test_*.py *_tests.py
addopts = --verbose --tb=short --strict-config --strict-markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
```

### Test Organization

```
clear-htmx/
├── CLEAR/tests/           # App-specific tests
│   ├── test_models.py     # Model tests
│   ├── test_views.py      # View and template tests
│   ├── test_api.py        # API endpoint tests
│   └── test_example.py    # Example tests
└── e2e/                   # End-to-end tests
    ├── conftest.py        # Pytest configuration
    ├── test_authentication.py
    ├── test_dashboard.py
    └── test_simple_auth.py
```

### Testing Commands

```bash
# Unit tests
python manage.py test           # Django test runner
pytest                         # Pytest runner
pytest -v                      # Verbose output
pytest --cov=CLEAR            # Coverage report

# E2E tests
pytest e2e/                    # All E2E tests
pytest e2e/test_auth.py       # Specific test file

# Specific test types
pytest -m unit                 # Unit tests only
pytest -m integration         # Integration tests only
pytest -m "not slow"          # Skip slow tests
```

### Code Quality Commands

```bash
# Linting and formatting
ruff check .                   # Run linter (modern)
ruff format .                  # Format code (modern)
ruff check --fix .            # Auto-fix issues

# Legacy tools (use ruff instead for new development)
flake8                         # Legacy linting
black .                        # Legacy formatting

# Security and type checking
bandit -r . --severity-level medium    # Security scan
mypy . --ignore-missing-imports        # Type checking
detect-secrets scan                    # Secret detection

# Git hooks
pre-commit run --all-files     # Run all pre-commit hooks
pre-commit run ruff           # Run specific hook

# CI/CD validation
python scripts/code_quality_check.py --strict  # Comprehensive check
```

### Test Examples

#### Model Testing

```python
# CLEAR/tests/test_models.py
import pytest
from django.test import TestCase
from CLEAR.models import Project, Organization, User

class ProjectModelTest(TestCase):
    def setUp(self):
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            organization=self.org
        )

    def test_project_creation(self):
        project = Project.objects.create(
            name="Test Project",
            organization=self.org
        )
        self.assertEqual(project.name, "Test Project")
        self.assertEqual(project.organization, self.org)

    def test_project_str_representation(self):
        project = Project.objects.create(
            name="Test Project",
            organization=self.org
        )
        self.assertEqual(str(project), "Test Project")
```

#### View Testing with HTMX

```python
# CLEAR/tests/test_views.py
from django.test import TestCase, Client
from django.urls import reverse
from django_htmx.test import HTMXTestCase

class ProjectViewTest(HTMXTestCase):
    def setUp(self):
        self.client = Client()
        self.org = Organization.objects.create(name="Test Org")
        self.user = User.objects.create_user(
            username="testuser",
            password="testpass",
            organization=self.org
        )
        self.client.login(username="testuser", password="testpass")

    def test_projects_list_view(self):
        response = self.client.get(reverse('projects_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Projects")

    def test_htmx_projects_partial(self):
        # Test HTMX partial view
        response = self.client.get(
            reverse('projects_list_partial'),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Should not include full page template
        self.assertNotContains(response, '<html>')
```

### Performance Testing

#### Database Query Optimization

```python
# Test for N+1 queries
def test_projects_list_query_count(self):
    # Create test data
    projects = [Project.objects.create(name=f"Project {i}", organization=self.org)
                for i in range(10)]

    with self.assertNumQueries(2):  # Should be 2 queries max
        response = self.client.get(reverse('projects_list'))
        projects_list = list(response.context['projects'])
```

## Performance Optimization

### Django Optimization

```python
# settings.py optimizations
DEBUG = False  # Disable debug in production

# Database connection pooling
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.postgis',
        'NAME': env('DATABASE_NAME'),
        'USER': env('DATABASE_USER'),
        'PASSWORD': env('DATABASE_PASSWORD'),
        'HOST': env('DATABASE_HOST'),
        'PORT': env('DATABASE_PORT', default='5432'),
        'OPTIONS': {
            'sslmode': 'require',
        },
        'CONN_MAX_AGE': 600,  # Connection pooling
    }
}

# Caching configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': env('REDIS_URL'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Static files optimization
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

### Database Optimization

1. **Query Optimization**

   ```python
   # Use select_related and prefetch_related
   projects = Project.objects.select_related('organization') \
                            .prefetch_related('utilities', 'conflicts')

   # Use database indexes
   class Meta:
       indexes = [
           models.Index(fields=['organization', 'status']),
           models.Index(fields=['created_at']),
       ]
   ```

2. **Spatial Indexes**

   ```python
   # PostGIS spatial indexes are automatically created
   geometry_2d = models.LineStringField(db_index=True)
   location = models.PointField(geography=True, db_index=True)
   ```

3. **Query Result Caching**

   ```python
   from django.core.cache import cache

   def get_project_stats(organization_id):
       cache_key = f"project_stats_{organization_id}"
       stats = cache.get(cache_key)

       if stats is None:
           stats = Project.objects.filter(
               organization_id=organization_id
           ).aggregate(
               total=Count('id'),
               active=Count('id', filter=Q(status='active'))
           )
           cache.set(cache_key, stats, 300)  # 5 minutes

       return stats
   ```

### Frontend Performance

1. **Template Optimization**

   ```html
   <!-- Load critical CSS inline -->
   <style>
     /* Critical CSS for above-the-fold content */
   </style>

   <!-- Lazy load non-critical CSS -->
   <link
     rel="preload"
     href="{% static 'css/style.css' %}"
     as="style"
     onload="this.onload=null;this.rel='stylesheet'"
   />
   ```

2. **HTMX Optimization**

   ```html
   <!-- Use HTMX triggers efficiently -->
   <div hx-get="/api/stats/"
        hx-trigger="load delay:2s"  <!-- Delay non-critical updates -->
        hx-swap="innerHTML"
        hx-select="#stats-content">  <!-- Only replace specific content -->
   </div>
   ```

3. **Static File Optimization**

   ```python
   # Compress and version static files
   STATICFILES_FINDERS = [
       'django.contrib.staticfiles.finders.FileSystemFinder',
       'django.contrib.staticfiles.finders.AppDirectoriesFinder',
   ]

   # Use WhiteNoise for static file serving
   MIDDLEWARE = [
       'whitenoise.middleware.WhiteNoiseMiddleware',
       # ... other middleware
   ]
   ```

## Deployment Guide

### Prerequisites

- Python 3.12+
- PostgreSQL 16+ with PostGIS
- Redis instance (production)
- SSL certificates (production)
- Domain with DNS configured

### Environment Variables

```bash
# .env.production
DEBUG=False
SECRET_KEY="your-secret-key-minimum-50-characters"
DATABASE_URL="********************************/dbname?sslmode=require"
REDIS_URL="redis://default:password@host:6379"

# External services
EMAIL_BACKEND="django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER="<EMAIL>"
EMAIL_HOST_PASSWORD="your-app-password"

# Security settings
ALLOWED_HOSTS="your-domain.com,www.your-domain.com"
CSRF_TRUSTED_ORIGINS="https://your-domain.com,https://www.your-domain.com"
CORS_ALLOWED_ORIGINS="https://your-domain.com"

# File storage (optional)
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_STORAGE_BUCKET_NAME="your-bucket-name"
```

### Deployment Options

#### 1. Railway (Recommended)

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway link [project-id]
railway up

# Set environment variables
railway variables set DEBUG=False
railway variables set SECRET_KEY="your-secret-key"
```

#### 2. Docker

```dockerfile
# Dockerfile
FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    postgresql-client \
    gdal-bin \
    libgdal-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Install Python dependencies
COPY pyproject.toml ./
RUN pip install -e .

# Copy application code
COPY . .

# Collect static files
RUN python manage.py collectstatic --noinput

EXPOSE 8743

CMD ["gunicorn", "clear_htmx.wsgi:application", "--bind", "0.0.0.0:8743"]
```

```bash
# Build and run
docker build -t clear-htmx .
docker run -p 8743:8743 --env-file .env.production clear-htmx
```

#### 3. Traditional VPS

```bash
# Install dependencies
sudo apt update
sudo apt install python3.12 python3.12-venv postgresql postgresql-contrib postgis

# Create virtual environment
python3.12 -m venv venv
source venv/bin/activate

# Install application
pip install -e .

# Set up database
sudo -u postgres createdb clear_htmx
sudo -u postgres psql -d clear_htmx -c "CREATE EXTENSION postgis;"

# Run migrations
python manage.py migrate

# Collect static files
python manage.py collectstatic

# Run with Gunicorn
gunicorn clear_htmx.wsgi:application --bind 0.0.0.0:8743
```

### Post-Deployment

1. **Database Setup**

   ```bash
   # Run migrations
   python manage.py migrate

   # Create superuser
   python manage.py createsuperuser

   # Load initial data (optional)
   python manage.py loaddata initial_data.json
   ```

2. **Health Checks**
   - Monitor Django health check endpoint
   - Set up database connection monitoring
   - Configure application performance monitoring

3. **Security Hardening**
   - Enable HTTPS with SSL certificates
   - Configure security headers
   - Set up firewall rules
   - Regular security updates

4. **Monitoring Setup**
   - Configure logging aggregation
   - Set up error tracking (Sentry)
   - Monitor database performance
   - Set up alerting for critical issues

## Contributing

### Development Workflow

1. **Fork & Clone**

   ```bash
   git clone https://github.com/your-username/clear-htmx.git
   cd clear-htmx
   uv sync  # or pip install -e .
   ```

2. **Create Feature Branch**

   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **Set up Development Environment**

   ```bash
   cp .env.example .env
   # Edit .env with development settings
   python manage.py migrate
   python manage.py createsuperuser
   ```

4. **Make Changes**
   - Write code following Django conventions
   - Add tests for new features
   - Update documentation as needed

5. **Test Thoroughly**

   ```bash
   python manage.py test
   pytest
   pytest e2e/
   ```

6. **Commit with Descriptive Messages**

   ```bash
   git commit -m "feat: add spatial conflict detection feature"
   ```

7. **Push & Create PR**
   ```bash
   git push origin feature/amazing-feature
   ```

### Code Standards

#### Python/Django

- Follow PEP 8 style guidelines
- Use type hints where appropriate
- Write docstrings for public functions
- Use Django's built-in features and conventions

#### Templates

- Follow Django template conventions
- Use semantic HTML5 elements
- Include proper ARIA attributes for accessibility
- Use Bootstrap 5 classes consistently

#### HTMX

- Use HTMX attributes semantically
- Provide fallbacks for non-HTMX requests
- Keep HTMX interactions simple and focused
- Test both HTMX and non-HTMX code paths

#### Testing

- Minimum 80% test coverage for new code
- Write unit tests for models and business logic
- Write integration tests for views and APIs
- Write E2E tests for critical user flows

#### Git Workflow

- Main branch is protected
- All changes via pull requests
- Require code review approval
- CI must pass before merge

### Documentation

- Update README for significant changes
- Add docstrings for new functions and classes
- Include code examples in documentation
- Keep CLAUDE.md updated with development notes

### Code Quality & Exception Handling

CLEAR follows modern Python best practices for exception handling and code quality. All bare `except:` clauses have been systematically replaced with specific exception handling patterns.

#### Exception Handling Patterns

**Django Template Rendering**
```python
try:
    return render_to_string(template, context, request=request)
except (TemplateDoesNotExist, AttributeError, ValueError) as e:
    import logging
    logging.debug(f"Failed to render template {template}: {e}")
    # Fallback to default template
    return render_to_string(self.default_template, context, request=request)
```

**Django ORM Operations**
```python
try:
    instance = Model.objects.get(pk=instance_id)
except (Model.DoesNotExist, ValueError, ValidationError) as e:
    import logging
    logging.warning(f"Failed to retrieve {Model.__name__} {instance_id}: {e}")
    return None
```

**Network/Cache Operations**
```python
try:
    cache.set(cache_key, data, timeout)
except (AttributeError, KeyError, ConnectionError, TimeoutError) as e:
    import logging
    logging.debug(f"Cache operation failed for {cache_key}: {e}")
    # Continue without caching
```

**File Operations**
```python
try:
    with open(file_path, 'r') as f:
        content = f.read()
except (FileNotFoundError, PermissionError, IOError, UnicodeDecodeError) as e:
    import logging
    logging.error(f"Failed to read file {file_path}: {e}")
    raise FileProcessingError(f"Unable to process file: {e}")
```

#### Code Quality Tools

**Primary Linter: Ruff (Modern Python 3.13+ Compatible)**
```bash
# Run linting
ruff check .

# Run formatting
ruff format .

# Configuration in pyproject.toml
[tool.ruff]
line-length = 88
target-version = "py312"
```

**Legacy Configuration (Deprecated)**
- `.flake8` and `setup.cfg` contain legacy flake8 configuration
- `flake8-django` plugin incompatible with Python 3.13+ due to type alias syntax changes
- Use `ruff` for all new development and CI/CD

**Security Scanning**
```bash
# Run security scan
bandit -r . --format json --severity-level medium

# Check for secrets
detect-secrets scan --baseline .secrets.baseline
```

**Type Checking**
```bash
# Run type checking
mypy . --ignore-missing-imports --no-strict-optional
```

#### Dependency Management

**Known Version Conflicts (Non-Breaking)**
- GDAL: System version 3.11.3 vs required <3.5.0 (functional compatibility maintained)
- channels: Installed 4.2.0 vs required >=4.2.2 (minor version difference)

**Required Dependencies for Security**
- `bleach>=6.1.0` - HTML sanitization (present)
- `django-redis>=5.4.0` - Redis caching backend (present)
- All security dependencies verified and up-to-date

#### CI/CD Quality Checks

**GitHub Actions Workflow** (`.github/workflows/code-quality.yml`)
- Ruff linting and formatting checks
- Bandit security scanning
- MyPy type checking
- detect-secrets for secret detection
- Django system checks
- Comprehensive code quality reporting

**Pre-commit Hooks**
```bash
# Install pre-commit hooks
pre-commit install

# Run on all files
pre-commit run --all-files
```

#### Exception Handling Migration Summary

**Task 92.4 Completed**: Systematic replacement of 82 bare `except:` clauses with specific exception handling
- **Template rendering**: 12 instances fixed with `TemplateDoesNotExist`, `AttributeError`, `ValueError`
- **Cache operations**: 8 instances fixed with `ConnectionError`, `TimeoutError`, `KeyError`
- **File operations**: 15 instances fixed with `FileNotFoundError`, `PermissionError`, `IOError`
- **Django ORM**: 18 instances fixed with `Model.DoesNotExist`, `ValidationError`, `IntegrityError`
- **Network operations**: 11 instances fixed with `ConnectionError`, `Timeout`, `HTTPError`
- **Data processing**: 18 instances fixed with `ValueError`, `TypeError`, `KeyError`

All fixes include appropriate logging at DEBUG, WARNING, or ERROR levels with contextual information.

---

## Migration Context

### From React/Next.js to Django + HTMX

This project represents a successful migration from a complex React/Next.js application to Django + HTMX, solving fundamental architectural issues:

#### Problems Solved

- **State Synchronization**: Eliminated client-server state duplication
- **Authentication Complexity**: Simplified from Supabase auth to Django sessions
- **Development Friction**: Reduced feature development time significantly
- **Production Stability**: Resolved deployment and runtime issues
- **Maintenance Overhead**: Single codebase vs. separate frontend/backend

#### Benefits Realized

- **HTML IS the State**: Server holds all state, HTMX provides dynamic updates
- **Faster Development**: Server-side rendering with dynamic interactions
- **Better Performance**: Reduced JavaScript bundle size and complexity
- **Improved Security**: Django's built-in security features
- **Simplified Deployment**: Single Django application deployment

---

## Additional Resources

### Documentation

- [Django Documentation](https://docs.djangoproject.com/)
- [HTMX Documentation](https://htmx.org/docs/)
- [PostGIS Documentation](https://postgis.net/documentation/)
- [Django GIS Documentation](https://docs.djangoproject.com/en/stable/ref/contrib/gis/)
- [Bootstrap 5 Documentation](https://getbootstrap.com/docs/5.3/)

### Development Tools

- [Django Admin](http://localhost:8743/admin/) - Database management interface
- [Django Debug Toolbar](https://django-debug-toolbar.readthedocs.io/) - Development debugging
- [Django Extensions](https://django-extensions.readthedocs.io/) - Additional management commands

### Community Resources

- [Django Community](https://www.djangoproject.com/community/)
- [HTMX Discord](https://htmx.org/discord/)
- [PostGIS Mailing Lists](https://postgis.net/community/)

---

## 📜 License

Copyright © 2025 CLEAR Technologies. All rights reserved.

This software is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

---

**Built with ❤️ by the CLEAR Team using Django + HTMX**

_"HTML IS the state" - Eliminating the complexity of client-server state synchronization_

## Utility Scripts

The `scripts/` directory contains various utility, validation, migration, and deployment scripts to aid in development, testing, and maintenance. These scripts are crucial for ensuring code quality, security, and performance across the platform.

### Validation Scripts

- `validate_api_security.py`: Checks for API security compliance.
- `validate_authentication_security.py`: Validates authentication security measures.
- `validate_drf_security_compliance.py`: Ensures Django REST Framework security compliance.
- `validate_error_recovery_integration.py`: Tests error recovery mechanisms.
- `validate_migration.py`: Validates data migrations.
- `validate_migrations.py`: General migration validation.
- `validate_optimizations.py`: Verifies performance optimizations.
- `validate_performance.py`: Conducts performance validation.
- `validate_security.py`: Comprehensive security validation.
- `validate_templates.py`: Validates Django templates for correctness and standards.
- `validate_test_environment.py`: Checks the test environment setup.
- `validate_websocket_security.py`: Validates WebSocket security.
- `validate_websocket_security_enhanced.py`: Enhanced WebSocket security validation.
- `validate_websocket_security_final.py`: Final WebSocket security validation.
- `validate_websocket_security_simple.py`: Simple WebSocket security validation.
- `verify_django52_templates.py`: Verifies Django 5.2 template compatibility.
- `verify_export_implementation.py`: Checks data export functionalities.

### Migration Scripts

- `migrate_templates_phase2.py`: Handles Phase 2 of template migrations.
- `migrate_views.py`: Manages views migration.
- `migration_performance_optimizer.py`: Optimizes migration performance.
- `phased_migration.py`: Manages phased data migrations.

### Deployment Scripts

- `zero_downtime_deployment.py`: Facilitates zero-downtime deployments.
- `deploy_production.py`: Script for production deployment.
- `deploy_production_comprehensive.py`: Comprehensive production deployment script.

### Analysis and Reporting Scripts

- `missing_implementation_detector.py`: Detects missing code implementations.
- `navigation_audit.py`: Audits navigation paths.
- `offline_ui_review.py`: Enables offline UI review.
- `performance_benchmark_mapping.py`: Benchmarks mapping performance.
- `performance_summary_report.py`: Generates performance summary reports.
- `security_analysis.py`: Performs security analysis.
- `server_performance_benchmark.py`: Benchmarks server performance.
- `simple_mapping_benchmark.py`: Simple mapping performance benchmark.
- `simple_performance_check.py`: Performs a simple performance check.
- `simple_report.py`: Generates simple reports.
- `simple_template_analyzer.py`: Analyzes templates simply.
- `systematic_template_analyzer.py`: Systematically analyzes templates.
- `template_improvement_prioritizer.py`: Prioritizes template improvements.
- `template_quality_monitor.py`: Monitors template quality.
- `template_summary.py`: Provides a summary of templates.
- `template_syntax_validator.py`: Validates template syntax.
- `track_progress.py`: Tracks project progress.
- `run_all_compliance_checks.py`: Runs all compliance checks.
- `run_browser_tests.sh`: Runs browser-based tests.
- `run_template_automation.py`: Executes template automation.
- `run_uiux_analysis.py`: Performs UI/UX analysis.

### Other Utilities

- `narrow_search.py`: Utility for narrowing searches.
- `reorganize_templates.py`: Reorganizes template files.
- `setup_database.py`: Sets up the database.
- `simple_template_fixer.py`: Fixes simple template issues.
- `standardize_icons.py`: Standardizes icons.
- `start_dev_db.bat`: Starts development database (Windows).
- `start_dev_db.sh`: Starts development database (Linux/macOS).
- `sync_external_docs.py`: Synchronizes external documentation.
- `tailwind_to_bootstrap.py`: Converts Tailwind CSS to Bootstrap.
- `targeted_syntax_fixer.py`: Fixes targeted syntax issues.
- `template_analysis_setup.py`: Sets up template analysis.
- `template_automation_monitor.py`: Monitors template automation.
- `template_fix_generator.py`: Generates template fixes.
- `template_migration.py`: Handles template migration.
- `template_performance_benchmark.py`: Benchmarks template performance.
- `test-mcp-servers.sh`: Tests MCP servers.
- `test_basic.py`: Basic tests.
- `test_connection_pool.py`: Tests database connection pooling.
- `test_htmx_microcaching.py`: Tests HTMX microcaching.
- `test_htmx_templates.py`: Tests HTMX templates.
- `test_template_loading.py`: Tests template loading.
- `test_templates.py`: General template tests.
- `test_url_compliance.py`: Tests URL compliance.
- `test_validation_simple.py`: Simple validation tests.
- `uat_openlayers.py`: User Acceptance Testing for OpenLayers.
- `unique_together_migration_plan.py`: Generates migration plan for unique_together.
- `update_django_settings.py`: Updates Django settings.
- `update_imports.py`: Updates Python imports.
- `update_template_references.py`: Updates template references.
- `create_missing_views.py`: Creates missing views.
- `decorator_recommendations.txt`: Recommendations for decorators.
- `demo_hateoas_api.py`: Demo of HATEOAS API.
- `demo_internal_documents_compliance.py`: Demo of internal documents compliance.
- `deploy_production.py`: Production deployment script.
- `deploy_production_comprehensive.py`: Comprehensive production deployment script.
