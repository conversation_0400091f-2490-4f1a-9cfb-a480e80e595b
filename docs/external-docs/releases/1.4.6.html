<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Django 1.4.6 release notes &#8212; Django 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=34bb78ad" />
    <link rel="stylesheet" type="text/css" href="../_static/default.css?v=bf4d74af" />
    <script src="../_static/documentation_options.js?v=6593ad68"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Django 1.4.5 release notes" href="1.4.5.html" />
    <link rel="prev" title="Django 1.4.7 release notes" href="1.4.7.html" />



 
<script src="../templatebuiltins.js"></script>
<script>
(function($) {
    if (!django_template_builtins) {
       // templatebuiltins.js missing, do nothing.
       return;
    }
    $(document).ready(function() {
        // Hyperlink Django template tags and filters
        var base = "../ref/templates/builtins.html";
        if (base == "#") {
            // Special case for builtins.html itself
            base = "";
        }
        // Tags are keywords, class '.k'
        $("div.highlight\\-html\\+django span.k").each(function(i, elem) {
             var tagname = $(elem).text();
             if ($.inArray(tagname, django_template_builtins.ttags) != -1) {
                 var fragment = tagname.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + tagname + "</a>");
             }
        });
        // Filters are functions, class '.nf'
        $("div.highlight\\-html\\+django span.nf").each(function(i, elem) {
             var filtername = $(elem).text();
             if ($.inArray(filtername, django_template_builtins.tfilters) != -1) {
                 var fragment = filtername.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + filtername + "</a>");
             }
        });
    });
})(jQuery);</script>

  </head><body>

    <div class="document">
  <div id="custom-doc" class="yui-t6">
    <div id="hd">
      <h1><a href="../index.html">Django 5.2 documentation</a></h1>
      <div id="global-nav">
        <a title="Home page" href="../index.html">Home</a>  |
        <a title="Table of contents" href="../contents.html">Table of contents</a>  |
        <a title="Global index" href="../genindex.html">Index</a>  |
        <a title="Module index" href="../py-modindex.html">Modules</a>
      </div>
      <div class="nav">
    &laquo; <a href="1.4.7.html" title="Django 1.4.7 release notes">previous</a>
     |
    <a href="index.html" title="Release notes" accesskey="U">up</a>
   |
    <a href="1.4.5.html" title="Django 1.4.5 release notes">next</a> &raquo;</div>
    </div>

    <div id="bd">
      <div id="yui-main">
        <div class="yui-b">
          <div class="yui-g" id="releases-1.4.6">
            
  <section id="s-django-1-4-6-release-notes">
<span id="django-1-4-6-release-notes"></span><h1>Django 1.4.6 release notes<a class="headerlink" href="#django-1-4-6-release-notes" title="Link to this heading">¶</a></h1>
<p><em>August 13, 2013</em></p>
<p>Django 1.4.6 fixes one security issue present in previous Django releases in
the 1.4 series, as well as one other bug.</p>
<p>This is the sixth bugfix/security release in the Django 1.4 series.</p>
<section id="s-mitigated-possible-xss-attack-via-user-supplied-redirect-urls">
<span id="mitigated-possible-xss-attack-via-user-supplied-redirect-urls"></span><h2>Mitigated possible XSS attack via user-supplied redirect URLs<a class="headerlink" href="#mitigated-possible-xss-attack-via-user-supplied-redirect-urls" title="Link to this heading">¶</a></h2>
<p>Django relies on user input in some cases (e.g.
<code class="docutils literal notranslate"><span class="pre">django.contrib.auth.views.login()</span></code>, <code class="docutils literal notranslate"><span class="pre">django.contrib.comments</span></code>, and
<a class="reference internal" href="../topics/i18n/index.html"><span class="doc">i18n</span></a>) to redirect the user to an “on success” URL.
The security checks for these redirects (namely
<code class="docutils literal notranslate"><span class="pre">django.utils.http.is_safe_url()</span></code>) didn’t check if the scheme is <code class="docutils literal notranslate"><span class="pre">http(s)</span></code>
and as such allowed <code class="docutils literal notranslate"><span class="pre">javascript:...</span></code> URLs to be entered. If a developer
relied on <code class="docutils literal notranslate"><span class="pre">is_safe_url()</span></code> to provide safe redirect targets and put such a
URL into a link, they could suffer from a XSS attack. This bug doesn’t affect
Django currently, since we only put this URL into the <code class="docutils literal notranslate"><span class="pre">Location</span></code> response
header and browsers seem to ignore JavaScript there.</p>
</section>
<section id="s-bugfixes">
<span id="bugfixes"></span><h2>Bugfixes<a class="headerlink" href="#bugfixes" title="Link to this heading">¶</a></h2>
<ul class="simple">
<li><p>Fixed an obscure bug with the <a class="reference internal" href="../topics/testing/tools.html#django.test.override_settings" title="django.test.override_settings"><code class="xref py py-func docutils literal notranslate"><span class="pre">override_settings()</span></code></a>
decorator. If you hit an <code class="docutils literal notranslate"><span class="pre">AttributeError:</span> <span class="pre">'Settings'</span> <span class="pre">object</span> <span class="pre">has</span> <span class="pre">no</span> <span class="pre">attribute</span>
<span class="pre">'_original_allowed_hosts'</span></code> exception, it’s probably fixed (#20636).</p></li>
</ul>
</section>
</section>


          </div>
        </div>
      </div>
      
        
          <div class="yui-b" id="sidebar">
            
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Django 1.4.6 release notes</a><ul>
<li><a class="reference internal" href="#mitigated-possible-xss-attack-via-user-supplied-redirect-urls">Mitigated possible XSS attack via user-supplied redirect URLs</a></li>
<li><a class="reference internal" href="#bugfixes">Bugfixes</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="1.4.7.html"
                          title="previous chapter">Django 1.4.7 release notes</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="1.4.5.html"
                          title="next chapter">Django 1.4.5 release notes</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/releases/1.4.6.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
              <h3>Last update:</h3>
              <p class="topless">Jul 02, 2025</p>
          </div>
        
      
    </div>

    <div id="ft">
      <div class="nav">
    &laquo; <a href="1.4.7.html" title="Django 1.4.7 release notes">previous</a>
     |
    <a href="index.html" title="Release notes" accesskey="U">up</a>
   |
    <a href="1.4.5.html" title="Django 1.4.5 release notes">next</a> &raquo;</div>
    </div>
  </div>

      <div class="clearer"></div>
    </div>
  </body>
</html>