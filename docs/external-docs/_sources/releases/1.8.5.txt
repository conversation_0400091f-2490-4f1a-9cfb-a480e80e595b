==========================
Django 1.8.5 release notes
==========================

*October 3, 2015*

Django 1.8.5 fixes several bugs in 1.8.4.

Bugfixes
========

* Made the development server's autoreload more robust (:ticket:`24704`).

* Fixed ``AssertionError`` in some delete queries with a model containing a
  field that is both a foreign and primary key (:ticket:`24951`).

* Fixed ``AssertionError`` in some complex queries (:ticket:`24525`).

* Fixed a migrations crash with ``GenericForeignKey`` (:ticket:`25040`).

* Made ``translation.override()`` clear the overridden language when a
  translation isn't initially active (:ticket:`25295`).

* Fixed crash when using a value in ``ModelAdmin.list_display`` that clashed
  with a reverse field on the model (:ticket:`25299`).

* Fixed autocompletion for options of non-``argparse`` management commands
  (:ticket:`25372`).

* Alphabetized ordering of imports in ``from django.db import migrations,
  models`` statement in newly created migrations (:ticket:`25384`).

* Fixed migrations crash on MySQL when adding a text or a blob field with an
  unhashable default (:ticket:`25393`).

* Changed ``Count`` queries to execute ``COUNT(*)`` instead of ``COUNT('*')``
  as versions of Django before 1.8 did (:ticket:`25377`). This may fix a
  performance regression on some databases.

* Fixed custom queryset chaining with ``values()`` and ``values_list()``
  (:ticket:`20625`).

* Moved the :ref:`unsaved model instance assignment data loss check
  <unsaved-model-instance-check-18>` on reverse relations to ``Model.save()``
  (:ticket:`25160`).

* Readded inline foreign keys to form instances when validating model formsets
  (:ticket:`25431`).

* Allowed using ORM write methods after disabling autocommit with
  :func:`set_autocommit(False) <django.db.transaction.set_autocommit>`
  (:ticket:`24921`).

* Fixed the ``manage.py test --keepdb`` option on Oracle (:ticket:`25421`).

* Fixed incorrect queries with multiple many-to-many fields on a model with the
  same 'to' model and with ``related_name`` set to '+' (:ticket:`24505`,
  :ticket:`25486`).

* Fixed pickling a ``SimpleLazyObject`` wrapping a model (:ticket:`25389`).
