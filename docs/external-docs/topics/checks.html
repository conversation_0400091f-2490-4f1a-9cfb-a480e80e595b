<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>System check framework &#8212; Django 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=34bb78ad" />
    <link rel="stylesheet" type="text/css" href="../_static/default.css?v=bf4d74af" />
    <script src="../_static/documentation_options.js?v=6593ad68"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="External packages" href="external-packages.html" />
    <link rel="prev" title="Signals" href="signals.html" />



 
<script src="../templatebuiltins.js"></script>
<script>
(function($) {
    if (!django_template_builtins) {
       // templatebuiltins.js missing, do nothing.
       return;
    }
    $(document).ready(function() {
        // Hyperlink Django template tags and filters
        var base = "../ref/templates/builtins.html";
        if (base == "#") {
            // Special case for builtins.html itself
            base = "";
        }
        // Tags are keywords, class '.k'
        $("div.highlight\\-html\\+django span.k").each(function(i, elem) {
             var tagname = $(elem).text();
             if ($.inArray(tagname, django_template_builtins.ttags) != -1) {
                 var fragment = tagname.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + tagname + "</a>");
             }
        });
        // Filters are functions, class '.nf'
        $("div.highlight\\-html\\+django span.nf").each(function(i, elem) {
             var filtername = $(elem).text();
             if ($.inArray(filtername, django_template_builtins.tfilters) != -1) {
                 var fragment = filtername.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + filtername + "</a>");
             }
        });
    });
})(jQuery);</script>

  </head><body>

    <div class="document">
  <div id="custom-doc" class="yui-t6">
    <div id="hd">
      <h1><a href="../index.html">Django 5.2 documentation</a></h1>
      <div id="global-nav">
        <a title="Home page" href="../index.html">Home</a>  |
        <a title="Table of contents" href="../contents.html">Table of contents</a>  |
        <a title="Global index" href="../genindex.html">Index</a>  |
        <a title="Module index" href="../py-modindex.html">Modules</a>
      </div>
      <div class="nav">
    &laquo; <a href="signals.html" title="Signals">previous</a>
     |
    <a href="index.html" title="Using Django" accesskey="U">up</a>
   |
    <a href="external-packages.html" title="External packages">next</a> &raquo;</div>
    </div>

    <div id="bd">
      <div id="yui-main">
        <div class="yui-b">
          <div class="yui-g" id="topics-checks">
            
  <section id="s-module-django.core.checks">
<span id="s-system-check-framework"></span><span id="module-django.core.checks"></span><span id="system-check-framework"></span><h1>System check framework<a class="headerlink" href="#module-django.core.checks" title="Link to this heading">¶</a></h1>
<p>The system check framework is a set of static checks for validating Django
projects. It detects common problems and provides hints for how to fix them.
The framework is extensible so you can easily add your own checks.</p>
<p>Checks can be triggered explicitly via the <a class="reference internal" href="../ref/django-admin.html#django-admin-check"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">check</span></code></a> command. Checks are
triggered implicitly before most commands, including <a class="reference internal" href="../ref/django-admin.html#django-admin-runserver"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">runserver</span></code></a> and
<a class="reference internal" href="../ref/django-admin.html#django-admin-migrate"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">migrate</span></code></a>. For performance reasons, checks are not run as part of the
WSGI stack that is used in deployment. If you need to run system checks on your
deployment server, trigger them explicitly using <a class="reference internal" href="../ref/django-admin.html#django-admin-check"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">check</span></code></a>.</p>
<p>Serious errors will prevent Django commands (such as <a class="reference internal" href="../ref/django-admin.html#django-admin-runserver"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">runserver</span></code></a>) from
running at all. Minor problems are reported to the console. If you have inspected
the cause of a warning and are happy to ignore it, you can hide specific warnings
using the <a class="reference internal" href="../ref/settings.html#std-setting-SILENCED_SYSTEM_CHECKS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">SILENCED_SYSTEM_CHECKS</span></code></a> setting in your project settings file.</p>
<p>A full list of all checks that can be raised by Django can be found in the
<a class="reference internal" href="../ref/checks.html"><span class="doc">System check reference</span></a>.</p>
<section id="s-writing-your-own-checks">
<span id="writing-your-own-checks"></span><h2>Writing your own checks<a class="headerlink" href="#writing-your-own-checks" title="Link to this heading">¶</a></h2>
<p>The framework is flexible and allows you to write functions that perform
any other kind of check you may require. The following is an example stub
check function:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core.checks</span><span class="w"> </span><span class="kn">import</span> <span class="n">Error</span><span class="p">,</span> <span class="n">register</span>


<span class="nd">@register</span><span class="p">()</span>
<span class="k">def</span><span class="w"> </span><span class="nf">example_check</span><span class="p">(</span><span class="n">app_configs</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="n">errors</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="c1"># ... your check logic here</span>
    <span class="k">if</span> <span class="n">check_failed</span><span class="p">:</span>
        <span class="n">errors</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
            <span class="n">Error</span><span class="p">(</span>
                <span class="s2">&quot;an error&quot;</span><span class="p">,</span>
                <span class="n">hint</span><span class="o">=</span><span class="s2">&quot;A hint.&quot;</span><span class="p">,</span>
                <span class="n">obj</span><span class="o">=</span><span class="n">checked_object</span><span class="p">,</span>
                <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;myapp.E001&quot;</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
    <span class="k">return</span> <span class="n">errors</span>
</pre></div>
</div>
<p>The check function <em>must</em> accept an <code class="docutils literal notranslate"><span class="pre">app_configs</span></code> argument; this argument is
the list of applications that should be inspected. If <code class="docutils literal notranslate"><span class="pre">None</span></code>, the check must
be run on <em>all</em> installed apps in the project.</p>
<p>The check will receive a <code class="docutils literal notranslate"><span class="pre">databases</span></code> keyword argument. This is a list of
database aliases whose connections may be used to inspect database level
configuration. If <code class="docutils literal notranslate"><span class="pre">databases</span></code> is <code class="docutils literal notranslate"><span class="pre">None</span></code>, the check must not use any
database connections.</p>
<p>The <code class="docutils literal notranslate"><span class="pre">**kwargs</span></code> argument is required for future expansion.</p>
<section id="s-messages">
<span id="messages"></span><h3>Messages<a class="headerlink" href="#messages" title="Link to this heading">¶</a></h3>
<p>The function must return a list of messages. If no problems are found as a result
of the check, the check function must return an empty list.</p>
<p>The warnings and errors raised by the check method must be instances of
<a class="reference internal" href="../ref/checks.html#django.core.checks.CheckMessage" title="django.core.checks.CheckMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">CheckMessage</span></code></a>. An instance of
<a class="reference internal" href="../ref/checks.html#django.core.checks.CheckMessage" title="django.core.checks.CheckMessage"><code class="xref py py-class docutils literal notranslate"><span class="pre">CheckMessage</span></code></a> encapsulates a single reportable
error or warning. It also provides context and hints applicable to the
message, and a unique identifier that is used for filtering purposes.</p>
<p>The concept is very similar to messages from the <a class="reference internal" href="../ref/contrib/messages.html"><span class="doc">message framework</span></a> or the <a class="reference internal" href="logging.html"><span class="doc">logging framework</span></a>.
Messages are tagged with a <code class="docutils literal notranslate"><span class="pre">level</span></code> indicating the severity of the message.</p>
<p>There are also shortcuts to make creating messages with common levels easier.
When using these classes you can omit the <code class="docutils literal notranslate"><span class="pre">level</span></code> argument because it is
implied by the class name.</p>
<ul class="simple">
<li><p><a class="reference internal" href="../ref/checks.html#django.core.checks.Debug" title="django.core.checks.Debug"><code class="xref py py-class docutils literal notranslate"><span class="pre">Debug</span></code></a></p></li>
<li><p><a class="reference internal" href="../ref/checks.html#django.core.checks.Info" title="django.core.checks.Info"><code class="xref py py-class docutils literal notranslate"><span class="pre">Info</span></code></a></p></li>
<li><p><a class="reference internal" href="../ref/checks.html#django.core.checks.Warning" title="django.core.checks.Warning"><code class="xref py py-class docutils literal notranslate"><span class="pre">Warning</span></code></a></p></li>
<li><p><a class="reference internal" href="../ref/checks.html#django.core.checks.Error" title="django.core.checks.Error"><code class="xref py py-class docutils literal notranslate"><span class="pre">Error</span></code></a></p></li>
<li><p><a class="reference internal" href="../ref/checks.html#django.core.checks.Critical" title="django.core.checks.Critical"><code class="xref py py-class docutils literal notranslate"><span class="pre">Critical</span></code></a></p></li>
</ul>
</section>
<section id="s-registering-and-labeling-checks">
<span id="s-registering-labeling-checks"></span><span id="registering-and-labeling-checks"></span><span id="registering-labeling-checks"></span><h3>Registering and labeling checks<a class="headerlink" href="#registering-and-labeling-checks" title="Link to this heading">¶</a></h3>
<p>Lastly, your check function must be registered explicitly with system check
registry. Checks should be registered in a file that’s loaded when your
application is loaded; for example, in the <a class="reference internal" href="../ref/applications.html#django.apps.AppConfig.ready" title="django.apps.AppConfig.ready"><code class="xref py py-meth docutils literal notranslate"><span class="pre">AppConfig.ready()</span></code></a> method.</p>
<dl class="py function">
<dt class="sig sig-object py" id="django.core.checks.register">
<span class="sig-name descname"><span class="pre">register</span></span>(<em class="sig-param"><span class="n"><span class="pre">*tags)(function</span></span></em>)<a class="headerlink" href="#django.core.checks.register" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>You can pass as many tags to <code class="docutils literal notranslate"><span class="pre">register</span></code> as you want in order to label your
check. Tagging checks is useful since it allows you to run only a certain
group of checks. For example, to register a compatibility check, you would
make the following call:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core.checks</span><span class="w"> </span><span class="kn">import</span> <span class="n">register</span><span class="p">,</span> <span class="n">Tags</span>


<span class="nd">@register</span><span class="p">(</span><span class="n">Tags</span><span class="o">.</span><span class="n">compatibility</span><span class="p">)</span>
<span class="k">def</span><span class="w"> </span><span class="nf">my_check</span><span class="p">(</span><span class="n">app_configs</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="c1"># ... perform compatibility checks and collect errors</span>
    <span class="k">return</span> <span class="n">errors</span>
</pre></div>
</div>
<p>You can register “deployment checks” that are only relevant to a production
settings file like this:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="nd">@register</span><span class="p">(</span><span class="n">Tags</span><span class="o">.</span><span class="n">security</span><span class="p">,</span> <span class="n">deploy</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="k">def</span><span class="w"> </span><span class="nf">my_check</span><span class="p">(</span><span class="n">app_configs</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span> <span class="o">...</span>
</pre></div>
</div>
<p>These checks will only be run if the <a class="reference internal" href="../ref/django-admin.html#cmdoption-check-deploy"><code class="xref std std-option docutils literal notranslate"><span class="pre">check</span> <span class="pre">--deploy</span></code></a> option is used.</p>
<p>You can also use <code class="docutils literal notranslate"><span class="pre">register</span></code> as a function rather than a decorator by
passing a callable object (usually a function) as the first argument
to <code class="docutils literal notranslate"><span class="pre">register</span></code>.</p>
<p>The code below is equivalent to the code above:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">my_check</span><span class="p">(</span><span class="n">app_configs</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span> <span class="o">...</span>


<span class="n">register</span><span class="p">(</span><span class="n">my_check</span><span class="p">,</span> <span class="n">Tags</span><span class="o">.</span><span class="n">security</span><span class="p">,</span> <span class="n">deploy</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="s-field-model-manager-template-engine-and-database-checks">
<span id="s-field-checking"></span><span id="field-model-manager-template-engine-and-database-checks"></span><span id="field-checking"></span><h3>Field, model, manager, template engine, and database checks<a class="headerlink" href="#field-model-manager-template-engine-and-database-checks" title="Link to this heading">¶</a></h3>
<p>In some cases, you won’t need to register your check function – you can
piggyback on an existing registration.</p>
<p>Fields, models, model managers, template engines, and database backends all
implement a <code class="docutils literal notranslate"><span class="pre">check()</span></code> method that is already registered with the check
framework. If you want to add extra checks, you can extend the implementation
on the base class, perform any extra checks you need, and append any messages
to those generated by the base class. It’s recommended that you delegate each
check to separate methods.</p>
<p>Consider an example where you are implementing a custom field named
<code class="docutils literal notranslate"><span class="pre">RangedIntegerField</span></code>. This field adds <code class="docutils literal notranslate"><span class="pre">min</span></code> and <code class="docutils literal notranslate"><span class="pre">max</span></code> arguments to the
constructor of <code class="docutils literal notranslate"><span class="pre">IntegerField</span></code>. You may want to add a check to ensure that users
provide a min value that is less than or equal to the max value. The following
code snippet shows how you can implement this check:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">checks</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">RangedIntegerField</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">IntegerField</span><span class="p">):</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">min</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="nb">max</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">min</span> <span class="o">=</span> <span class="nb">min</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max</span> <span class="o">=</span> <span class="nb">max</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">check</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="c1"># Call the superclass</span>
        <span class="n">errors</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>

        <span class="c1"># Do some custom checks and add messages to `errors`:</span>
        <span class="n">errors</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">_check_min_max_values</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">))</span>

        <span class="c1"># Return all errors and warnings</span>
        <span class="k">return</span> <span class="n">errors</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">_check_min_max_values</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">min</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">max</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">min</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">max</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">[</span>
                <span class="n">checks</span><span class="o">.</span><span class="n">Error</span><span class="p">(</span>
                    <span class="s2">&quot;min greater than max.&quot;</span><span class="p">,</span>
                    <span class="n">hint</span><span class="o">=</span><span class="s2">&quot;Decrease min or increase max.&quot;</span><span class="p">,</span>
                    <span class="n">obj</span><span class="o">=</span><span class="bp">self</span><span class="p">,</span>
                    <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;myapp.E001&quot;</span><span class="p">,</span>
                <span class="p">)</span>
            <span class="p">]</span>
        <span class="c1"># When no error, return an empty list</span>
        <span class="k">return</span> <span class="p">[]</span>
</pre></div>
</div>
<p>If you wanted to add checks to a model manager, you would take the same
approach on your subclass of <a class="reference internal" href="db/managers.html#django.db.models.Manager" title="django.db.models.Manager"><code class="xref py py-class docutils literal notranslate"><span class="pre">Manager</span></code></a>.</p>
<p>If you want to add a check to a model class, the approach is <em>almost</em> the same:
the only difference is that the check is a classmethod, not an instance method:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">MyModel</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="nd">@classmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">check</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
        <span class="n">errors</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
        <span class="c1"># ... your own checks ...</span>
        <span class="k">return</span> <span class="n">errors</span>
</pre></div>
</div>
<div class="versionchanged">
<span class="title">Changed in Django 5.1:</span> <p>In older versions, template engines didn’t implement a <code class="docutils literal notranslate"><span class="pre">check()</span></code> method.</p>
</div>
</section>
<section id="s-writing-tests">
<span id="writing-tests"></span><h3>Writing tests<a class="headerlink" href="#writing-tests" title="Link to this heading">¶</a></h3>
<p>Messages are comparable. That allows you to easily write tests:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core.checks</span><span class="w"> </span><span class="kn">import</span> <span class="n">Error</span>

<span class="n">errors</span> <span class="o">=</span> <span class="n">checked_object</span><span class="o">.</span><span class="n">check</span><span class="p">()</span>
<span class="n">expected_errors</span> <span class="o">=</span> <span class="p">[</span>
    <span class="n">Error</span><span class="p">(</span>
        <span class="s2">&quot;an error&quot;</span><span class="p">,</span>
        <span class="n">hint</span><span class="o">=</span><span class="s2">&quot;A hint.&quot;</span><span class="p">,</span>
        <span class="n">obj</span><span class="o">=</span><span class="n">checked_object</span><span class="p">,</span>
        <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;myapp.E001&quot;</span><span class="p">,</span>
    <span class="p">)</span>
<span class="p">]</span>
<span class="bp">self</span><span class="o">.</span><span class="n">assertEqual</span><span class="p">(</span><span class="n">errors</span><span class="p">,</span> <span class="n">expected_errors</span><span class="p">)</span>
</pre></div>
</div>
<section id="s-writing-integration-tests">
<span id="writing-integration-tests"></span><h4>Writing integration tests<a class="headerlink" href="#writing-integration-tests" title="Link to this heading">¶</a></h4>
<p>Given the need to register certain checks when the application loads, it can be
useful to test their integration within the system checks framework. This can
be accomplished by using the <a class="reference internal" href="../ref/django-admin.html#django.core.management.call_command" title="django.core.management.call_command"><code class="xref py py-func docutils literal notranslate"><span class="pre">call_command()</span></code></a>
function.</p>
<p>For example, this test demonstrates that the <a class="reference internal" href="../ref/settings.html#std-setting-SITE_ID"><code class="xref std std-setting docutils literal notranslate"><span class="pre">SITE_ID</span></code></a> setting must be
an integer, a built-in <a class="reference internal" href="../ref/checks.html#sites-system-checks"><span class="std std-ref">check from the sites framework</span></a>:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core.management</span><span class="w"> </span><span class="kn">import</span> <span class="n">call_command</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.core.management.base</span><span class="w"> </span><span class="kn">import</span> <span class="n">SystemCheckError</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.test</span><span class="w"> </span><span class="kn">import</span> <span class="n">SimpleTestCase</span><span class="p">,</span> <span class="n">modify_settings</span><span class="p">,</span> <span class="n">override_settings</span>


<span class="k">class</span><span class="w"> </span><span class="nc">SystemCheckIntegrationTest</span><span class="p">(</span><span class="n">SimpleTestCase</span><span class="p">):</span>
    <span class="nd">@override_settings</span><span class="p">(</span><span class="n">SITE_ID</span><span class="o">=</span><span class="s2">&quot;non_integer&quot;</span><span class="p">)</span>
    <span class="nd">@modify_settings</span><span class="p">(</span><span class="n">INSTALLED_APPS</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;prepend&quot;</span><span class="p">:</span> <span class="s2">&quot;django.contrib.sites&quot;</span><span class="p">})</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">test_non_integer_site_id</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">message</span> <span class="o">=</span> <span class="s2">&quot;(sites.E101) The SITE_ID setting must be an integer.&quot;</span>
        <span class="k">with</span> <span class="bp">self</span><span class="o">.</span><span class="n">assertRaisesMessage</span><span class="p">(</span><span class="n">SystemCheckError</span><span class="p">,</span> <span class="n">message</span><span class="p">):</span>
            <span class="n">call_command</span><span class="p">(</span><span class="s2">&quot;check&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Consider the following check which issues a warning on deployment if a custom
setting named <code class="docutils literal notranslate"><span class="pre">ENABLE_ANALYTICS</span></code> is not set to <code class="docutils literal notranslate"><span class="pre">True</span></code>:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.conf</span><span class="w"> </span><span class="kn">import</span> <span class="n">settings</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.core.checks</span><span class="w"> </span><span class="kn">import</span> <span class="ne">Warning</span><span class="p">,</span> <span class="n">register</span>


<span class="nd">@register</span><span class="p">(</span><span class="s2">&quot;myapp&quot;</span><span class="p">,</span> <span class="n">deploy</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="k">def</span><span class="w"> </span><span class="nf">check_enable_analytics_is_true_on_deploy</span><span class="p">(</span><span class="n">app_configs</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
    <span class="n">errors</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="k">if</span> <span class="nb">getattr</span><span class="p">(</span><span class="n">settings</span><span class="p">,</span> <span class="s2">&quot;ENABLE_ANALYTICS&quot;</span><span class="p">,</span> <span class="kc">None</span><span class="p">)</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">errors</span><span class="o">.</span><span class="n">append</span><span class="p">(</span>
            <span class="ne">Warning</span><span class="p">(</span>
                <span class="s2">&quot;The ENABLE_ANALYTICS setting should be set to True in deployment.&quot;</span><span class="p">,</span>
                <span class="nb">id</span><span class="o">=</span><span class="s2">&quot;myapp.W001&quot;</span><span class="p">,</span>
            <span class="p">)</span>
        <span class="p">)</span>
    <span class="k">return</span> <span class="n">errors</span>
</pre></div>
</div>
<p>Given that this check will not raise a <code class="docutils literal notranslate"><span class="pre">SystemCheckError</span></code>, the presence of
the warning message in the <code class="docutils literal notranslate"><span class="pre">stderr</span></code> output can be asserted like so:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">io</span><span class="w"> </span><span class="kn">import</span> <span class="n">StringIO</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">django.core.management</span><span class="w"> </span><span class="kn">import</span> <span class="n">call_command</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.test</span><span class="w"> </span><span class="kn">import</span> <span class="n">SimpleTestCase</span><span class="p">,</span> <span class="n">override_settings</span>


<span class="k">class</span><span class="w"> </span><span class="nc">EnableAnalyticsDeploymentCheckTest</span><span class="p">(</span><span class="n">SimpleTestCase</span><span class="p">):</span>
    <span class="nd">@override_settings</span><span class="p">(</span><span class="n">ENABLE_ANALYTICS</span><span class="o">=</span><span class="kc">None</span><span class="p">)</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">test_when_set_to_none</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">stderr</span> <span class="o">=</span> <span class="n">StringIO</span><span class="p">()</span>
        <span class="n">call_command</span><span class="p">(</span><span class="s2">&quot;check&quot;</span><span class="p">,</span> <span class="s2">&quot;-t&quot;</span><span class="p">,</span> <span class="s2">&quot;myapp&quot;</span><span class="p">,</span> <span class="s2">&quot;--deploy&quot;</span><span class="p">,</span> <span class="n">stderr</span><span class="o">=</span><span class="n">stderr</span><span class="p">)</span>
        <span class="n">message</span> <span class="o">=</span> <span class="p">(</span>
            <span class="s2">&quot;(myapp.W001) The ENABLE_ANALYTICS setting should be set &quot;</span>
            <span class="s2">&quot;to True in deployment.&quot;</span>
        <span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">assertIn</span><span class="p">(</span><span class="n">message</span><span class="p">,</span> <span class="n">stderr</span><span class="o">.</span><span class="n">getvalue</span><span class="p">())</span>
</pre></div>
</div>
</section>
</section>
</section>
</section>


          </div>
        </div>
      </div>
      
        
          <div class="yui-b" id="sidebar">
            
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">System check framework</a><ul>
<li><a class="reference internal" href="#writing-your-own-checks">Writing your own checks</a><ul>
<li><a class="reference internal" href="#messages">Messages</a></li>
<li><a class="reference internal" href="#registering-and-labeling-checks">Registering and labeling checks</a></li>
<li><a class="reference internal" href="#field-model-manager-template-engine-and-database-checks">Field, model, manager, template engine, and database checks</a></li>
<li><a class="reference internal" href="#writing-tests">Writing tests</a><ul>
<li><a class="reference internal" href="#writing-integration-tests">Writing integration tests</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="signals.html"
                          title="previous chapter">Signals</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="external-packages.html"
                          title="next chapter">External packages</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/topics/checks.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
              <h3>Last update:</h3>
              <p class="topless">Jul 02, 2025</p>
          </div>
        
      
    </div>

    <div id="ft">
      <div class="nav">
    &laquo; <a href="signals.html" title="Signals">previous</a>
     |
    <a href="index.html" title="Using Django" accesskey="U">up</a>
   |
    <a href="external-packages.html" title="External packages">next</a> &raquo;</div>
    </div>
  </div>

      <div class="clearer"></div>
    </div>
  </body>
</html>