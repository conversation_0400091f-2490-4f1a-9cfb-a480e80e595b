<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Serializing Django objects &#8212; Django 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=34bb78ad" />
    <link rel="stylesheet" type="text/css" href="../_static/default.css?v=bf4d74af" />
    <script src="../_static/documentation_options.js?v=6593ad68"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Django settings" href="settings.html" />
    <link rel="prev" title="Performance and optimization" href="performance.html" />



 
<script src="../templatebuiltins.js"></script>
<script>
(function($) {
    if (!django_template_builtins) {
       // templatebuiltins.js missing, do nothing.
       return;
    }
    $(document).ready(function() {
        // Hyperlink Django template tags and filters
        var base = "../ref/templates/builtins.html";
        if (base == "#") {
            // Special case for builtins.html itself
            base = "";
        }
        // Tags are keywords, class '.k'
        $("div.highlight\\-html\\+django span.k").each(function(i, elem) {
             var tagname = $(elem).text();
             if ($.inArray(tagname, django_template_builtins.ttags) != -1) {
                 var fragment = tagname.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + tagname + "</a>");
             }
        });
        // Filters are functions, class '.nf'
        $("div.highlight\\-html\\+django span.nf").each(function(i, elem) {
             var filtername = $(elem).text();
             if ($.inArray(filtername, django_template_builtins.tfilters) != -1) {
                 var fragment = filtername.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + filtername + "</a>");
             }
        });
    });
})(jQuery);</script>

  </head><body>

    <div class="document">
  <div id="custom-doc" class="yui-t6">
    <div id="hd">
      <h1><a href="../index.html">Django 5.2 documentation</a></h1>
      <div id="global-nav">
        <a title="Home page" href="../index.html">Home</a>  |
        <a title="Table of contents" href="../contents.html">Table of contents</a>  |
        <a title="Global index" href="../genindex.html">Index</a>  |
        <a title="Module index" href="../py-modindex.html">Modules</a>
      </div>
      <div class="nav">
    &laquo; <a href="performance.html" title="Performance and optimization">previous</a>
     |
    <a href="index.html" title="Using Django" accesskey="U">up</a>
   |
    <a href="settings.html" title="Django settings">next</a> &raquo;</div>
    </div>

    <div id="bd">
      <div id="yui-main">
        <div class="yui-b">
          <div class="yui-g" id="topics-serialization">
            
  <section id="s-serializing-django-objects">
<span id="serializing-django-objects"></span><h1>Serializing Django objects<a class="headerlink" href="#serializing-django-objects" title="Link to this heading">¶</a></h1>
<p>Django’s serialization framework provides a mechanism for “translating” Django
models into other formats. Usually these other formats will be text-based and
used for sending Django data over a wire, but it’s possible for a
serializer to handle any format (text-based or not).</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>If you just want to get some data from your tables into a serialized
form, you could use the <a class="reference internal" href="../ref/django-admin.html#django-admin-dumpdata"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">dumpdata</span></code></a> management command.</p>
</div>
<section id="s-serializing-data">
<span id="serializing-data"></span><h2>Serializing data<a class="headerlink" href="#serializing-data" title="Link to this heading">¶</a></h2>
<p>At the highest level, you can serialize data like this:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">serializers</span>

<span class="n">data</span> <span class="o">=</span> <span class="n">serializers</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">SomeModel</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">())</span>
</pre></div>
</div>
<p>The arguments to the <code class="docutils literal notranslate"><span class="pre">serialize</span></code> function are the format to serialize the data
to (see <a class="reference internal" href="#id2">Serialization formats</a>) and a
<a class="reference internal" href="../ref/models/querysets.html#django.db.models.query.QuerySet" title="django.db.models.query.QuerySet"><code class="xref py py-class docutils literal notranslate"><span class="pre">QuerySet</span></code></a> to serialize. (Actually, the second
argument can be any iterator that yields Django model instances, but it’ll
almost always be a QuerySet).</p>
<dl class="py function">
<dt class="sig sig-object py" id="django.core.serializers.get_serializer">
<span class="sig-prename descclassname"><span class="pre">django.core.serializers.</span></span><span class="sig-name descname"><span class="pre">get_serializer</span></span>(<em class="sig-param"><span class="n"><span class="pre">format</span></span></em>)<a class="headerlink" href="#django.core.serializers.get_serializer" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>You can also use a serializer object directly:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">XMLSerializer</span> <span class="o">=</span> <span class="n">serializers</span><span class="o">.</span><span class="n">get_serializer</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">)</span>
<span class="n">xml_serializer</span> <span class="o">=</span> <span class="n">XMLSerializer</span><span class="p">()</span>
<span class="n">xml_serializer</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span><span class="n">queryset</span><span class="p">)</span>
<span class="n">data</span> <span class="o">=</span> <span class="n">xml_serializer</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>
</pre></div>
</div>
<p>This is useful if you want to serialize data directly to a file-like object
(which includes an <a class="reference internal" href="../ref/request-response.html#django.http.HttpResponse" title="django.http.HttpResponse"><code class="xref py py-class docutils literal notranslate"><span class="pre">HttpResponse</span></code></a>):</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;file.xml&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">out</span><span class="p">:</span>
    <span class="n">xml_serializer</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span><span class="n">SomeModel</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">(),</span> <span class="n">stream</span><span class="o">=</span><span class="n">out</span><span class="p">)</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Calling <a class="reference internal" href="#django.core.serializers.get_serializer" title="django.core.serializers.get_serializer"><code class="xref py py-func docutils literal notranslate"><span class="pre">get_serializer()</span></code></a> with an unknown
<a class="reference internal" href="#serialization-formats"><span class="std std-ref">format</span></a> will raise a
<code class="docutils literal notranslate"><span class="pre">django.core.serializers.SerializerDoesNotExist</span></code> exception.</p>
</div>
<section id="s-subset-of-fields">
<span id="s-id1"></span><span id="subset-of-fields"></span><span id="id1"></span><h3>Subset of fields<a class="headerlink" href="#subset-of-fields" title="Link to this heading">¶</a></h3>
<p>If you only want a subset of fields to be serialized, you can
specify a <code class="docutils literal notranslate"><span class="pre">fields</span></code> argument to the serializer:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">serializers</span>

<span class="n">data</span> <span class="o">=</span> <span class="n">serializers</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">SomeModel</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">(),</span> <span class="n">fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="s2">&quot;size&quot;</span><span class="p">])</span>
</pre></div>
</div>
<p>In this example, only the <code class="docutils literal notranslate"><span class="pre">name</span></code> and <code class="docutils literal notranslate"><span class="pre">size</span></code> attributes of each model will
be serialized. The primary key is always serialized as the <code class="docutils literal notranslate"><span class="pre">pk</span></code> element in the
resulting output; it never appears in the <code class="docutils literal notranslate"><span class="pre">fields</span></code> part.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Depending on your model, you may find that it is not possible to
deserialize a model that only serializes a subset of its fields. If a
serialized object doesn’t specify all the fields that are required by a
model, the deserializer will not be able to save deserialized instances.</p>
</div>
</section>
<section id="s-inherited-models">
<span id="inherited-models"></span><h3>Inherited models<a class="headerlink" href="#inherited-models" title="Link to this heading">¶</a></h3>
<p>If you have a model that is defined using an <a class="reference internal" href="db/models.html#abstract-base-classes"><span class="std std-ref">abstract base class</span></a>, you don’t have to do anything special to serialize
that model. Call the serializer on the object (or objects) that you want to
serialize, and the output will be a complete representation of the serialized
object.</p>
<p>However, if you have a model that uses <a class="reference internal" href="db/models.html#multi-table-inheritance"><span class="std std-ref">multi-table inheritance</span></a>, you also need to serialize all of the base classes
for the model. This is because only the fields that are locally defined on the
model will be serialized. For example, consider the following models:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">Place</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">50</span><span class="p">)</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Restaurant</span><span class="p">(</span><span class="n">Place</span><span class="p">):</span>
    <span class="n">serves_hot_dogs</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">BooleanField</span><span class="p">(</span><span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
</pre></div>
</div>
<p>If you only serialize the Restaurant model:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">data</span> <span class="o">=</span> <span class="n">serializers</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">Restaurant</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">())</span>
</pre></div>
</div>
<p>the fields on the serialized output will only contain the <code class="docutils literal notranslate"><span class="pre">serves_hot_dogs</span></code>
attribute. The <code class="docutils literal notranslate"><span class="pre">name</span></code> attribute of the base class will be ignored.</p>
<p>In order to fully serialize your <code class="docutils literal notranslate"><span class="pre">Restaurant</span></code> instances, you will need to
serialize the <code class="docutils literal notranslate"><span class="pre">Place</span></code> models as well:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">all_objects</span> <span class="o">=</span> <span class="p">[</span><span class="o">*</span><span class="n">Restaurant</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">(),</span> <span class="o">*</span><span class="n">Place</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">()]</span>
<span class="n">data</span> <span class="o">=</span> <span class="n">serializers</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">all_objects</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="s-deserializing-data">
<span id="deserializing-data"></span><h2>Deserializing data<a class="headerlink" href="#deserializing-data" title="Link to this heading">¶</a></h2>
<p>Deserializing data is very similar to serializing it:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">serializers</span><span class="o">.</span><span class="n">deserialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
    <span class="n">do_something_with</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
</pre></div>
</div>
<p>As you can see, the <code class="docutils literal notranslate"><span class="pre">deserialize</span></code> function takes the same format argument as
<code class="docutils literal notranslate"><span class="pre">serialize</span></code>, a string or stream of data, and returns an iterator.</p>
<p>However, here it gets slightly complicated. The objects returned by the
<code class="docutils literal notranslate"><span class="pre">deserialize</span></code> iterator <em>aren’t</em> regular Django objects. Instead, they are
special <code class="docutils literal notranslate"><span class="pre">DeserializedObject</span></code> instances that wrap a created – but unsaved –
object and any associated relationship data.</p>
<p>Calling <code class="docutils literal notranslate"><span class="pre">DeserializedObject.save()</span></code> saves the object to the database.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>If the <code class="docutils literal notranslate"><span class="pre">pk</span></code> attribute in the serialized data doesn’t exist or is
null, a new instance will be saved to the database.</p>
</div>
<p>This ensures that deserializing is a non-destructive operation even if the
data in your serialized representation doesn’t match what’s currently in the
database. Usually, working with these <code class="docutils literal notranslate"><span class="pre">DeserializedObject</span></code> instances looks
something like:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">for</span> <span class="n">deserialized_object</span> <span class="ow">in</span> <span class="n">serializers</span><span class="o">.</span><span class="n">deserialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">object_should_be_saved</span><span class="p">(</span><span class="n">deserialized_object</span><span class="p">):</span>
        <span class="n">deserialized_object</span><span class="o">.</span><span class="n">save</span><span class="p">()</span>
</pre></div>
</div>
<p>In other words, the usual use is to examine the deserialized objects to make
sure that they are “appropriate” for saving before doing so. If you trust your
data source you can instead save the object directly and move on.</p>
<p>The Django object itself can be inspected as <code class="docutils literal notranslate"><span class="pre">deserialized_object.object</span></code>.
If fields in the serialized data do not exist on a model, a
<code class="docutils literal notranslate"><span class="pre">DeserializationError</span></code> will be raised unless the <code class="docutils literal notranslate"><span class="pre">ignorenonexistent</span></code>
argument is passed in as <code class="docutils literal notranslate"><span class="pre">True</span></code>:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">serializers</span><span class="o">.</span><span class="n">deserialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">,</span> <span class="n">ignorenonexistent</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="s-serialization-formats">
<span id="s-id2"></span><span id="serialization-formats"></span><span id="id2"></span><h2>Serialization formats<a class="headerlink" href="#serialization-formats" title="Link to this heading">¶</a></h2>
<p>Django supports a number of serialization formats, some of which require you
to install third-party Python modules:</p>
<table class="docutils">
<thead>
<tr class="row-odd"><th class="head"><p>Identifier</p></th>
<th class="head"><p>Information</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">xml</span></code></p></td>
<td><p>Serializes to and from a simple XML dialect.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">json</span></code></p></td>
<td><p>Serializes to and from <a class="reference external" href="https://json.org/">JSON</a>.</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">jsonl</span></code></p></td>
<td><p>Serializes to and from <a class="reference external" href="https://jsonlines.org/">JSONL</a>.</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">yaml</span></code></p></td>
<td><p>Serializes to YAML (YAML Ain’t a Markup Language). This
serializer is only available if <a class="reference external" href="https://pyyaml.org/">PyYAML</a> is installed.</p></td>
</tr>
</tbody>
</table>
<section id="s-xml">
<span id="xml"></span><h3>XML<a class="headerlink" href="#xml" title="Link to this heading">¶</a></h3>
<p>The basic XML serialization format looks like this:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="cp">&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;</span>
<span class="nt">&lt;django-objects</span><span class="w"> </span><span class="na">version=</span><span class="s">&quot;1.0&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="nt">&lt;object</span><span class="w"> </span><span class="na">pk=</span><span class="s">&quot;123&quot;</span><span class="w"> </span><span class="na">model=</span><span class="s">&quot;sessions.session&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;field</span><span class="w"> </span><span class="na">type=</span><span class="s">&quot;DateTimeField&quot;</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;expire_date&quot;</span><span class="nt">&gt;</span>2013-01-16T08:16:59.844560+00:00<span class="nt">&lt;/field&gt;</span>
<span class="w">        </span><span class="cm">&lt;!-- ... --&gt;</span>
<span class="w">    </span><span class="nt">&lt;/object&gt;</span>
<span class="nt">&lt;/django-objects&gt;</span>
</pre></div>
</div>
<p>The whole collection of objects that is either serialized or deserialized is
represented by a <code class="docutils literal notranslate"><span class="pre">&lt;django-objects&gt;</span></code>-tag which contains multiple
<code class="docutils literal notranslate"><span class="pre">&lt;object&gt;</span></code>-elements. Each such object has two attributes: “pk” and “model”,
the latter being represented by the name of the app (“sessions”) and the
lowercase name of the model (“session”) separated by a dot.</p>
<p>Each field of the object is serialized as a <code class="docutils literal notranslate"><span class="pre">&lt;field&gt;</span></code>-element sporting the
fields “type” and “name”. The text content of the element represents the value
that should be stored.</p>
<p>Foreign keys and other relational fields are treated a little bit differently:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;object</span><span class="w"> </span><span class="na">pk=</span><span class="s">&quot;27&quot;</span><span class="w"> </span><span class="na">model=</span><span class="s">&quot;auth.permission&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="cm">&lt;!-- ... --&gt;</span>
<span class="w">    </span><span class="nt">&lt;field</span><span class="w"> </span><span class="na">to=</span><span class="s">&quot;contenttypes.contenttype&quot;</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;content_type&quot;</span><span class="w"> </span><span class="na">rel=</span><span class="s">&quot;ManyToOneRel&quot;</span><span class="nt">&gt;</span>9<span class="nt">&lt;/field&gt;</span>
<span class="w">    </span><span class="cm">&lt;!-- ... --&gt;</span>
<span class="nt">&lt;/object&gt;</span>
</pre></div>
</div>
<p>In this example we specify that the <code class="docutils literal notranslate"><span class="pre">auth.Permission</span></code> object with the PK 27
has a foreign key to the <code class="docutils literal notranslate"><span class="pre">contenttypes.ContentType</span></code> instance with the PK 9.</p>
<p>ManyToMany-relations are exported for the model that binds them. For instance,
the <code class="docutils literal notranslate"><span class="pre">auth.User</span></code> model has such a relation to the <code class="docutils literal notranslate"><span class="pre">auth.Permission</span></code> model:</p>
<div class="highlight-xml notranslate"><div class="highlight"><pre><span></span><span class="nt">&lt;object</span><span class="w"> </span><span class="na">pk=</span><span class="s">&quot;1&quot;</span><span class="w"> </span><span class="na">model=</span><span class="s">&quot;auth.user&quot;</span><span class="nt">&gt;</span>
<span class="w">    </span><span class="cm">&lt;!-- ... --&gt;</span>
<span class="w">    </span><span class="nt">&lt;field</span><span class="w"> </span><span class="na">to=</span><span class="s">&quot;auth.permission&quot;</span><span class="w"> </span><span class="na">name=</span><span class="s">&quot;user_permissions&quot;</span><span class="w"> </span><span class="na">rel=</span><span class="s">&quot;ManyToManyRel&quot;</span><span class="nt">&gt;</span>
<span class="w">        </span><span class="nt">&lt;object</span><span class="w"> </span><span class="na">pk=</span><span class="s">&quot;46&quot;</span><span class="nt">&gt;&lt;/object&gt;</span>
<span class="w">        </span><span class="nt">&lt;object</span><span class="w"> </span><span class="na">pk=</span><span class="s">&quot;47&quot;</span><span class="nt">&gt;&lt;/object&gt;</span>
<span class="w">    </span><span class="nt">&lt;/field&gt;</span>
<span class="nt">&lt;/object&gt;</span>
</pre></div>
</div>
<p>This example links the given user with the permission models with PKs 46 and 47.</p>
<div class="admonition-control-characters admonition">
<p class="admonition-title">Control characters</p>
<p>If the content to be serialized contains control characters that are not
accepted in the XML 1.0 standard, the serialization will fail with a
<a class="reference external" href="https://docs.python.org/3/library/exceptions.html#ValueError" title="(in Python v3.13)"><code class="xref py py-exc docutils literal notranslate"><span class="pre">ValueError</span></code></a> exception. Read also the W3C’s explanation of <a class="reference external" href="https://www.w3.org/International/questions/qa-controls">HTML,
XHTML, XML and Control Codes</a>.</p>
</div>
</section>
<section id="s-serialization-formats-json">
<span id="s-id3"></span><span id="serialization-formats-json"></span><span id="id3"></span><h3>JSON<a class="headerlink" href="#serialization-formats-json" title="Link to this heading">¶</a></h3>
<p>When staying with the same example data as before it would be serialized as
JSON in the following way:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="p">[</span>
    <span class="p">{</span>
        <span class="s2">&quot;pk&quot;</span><span class="p">:</span> <span class="s2">&quot;4b678b301dfd8a4e0dad910de3ae245b&quot;</span><span class="p">,</span>
        <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;sessions.session&quot;</span><span class="p">,</span>
        <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span>
            <span class="s2">&quot;expire_date&quot;</span><span class="p">:</span> <span class="s2">&quot;2013-01-16T08:16:59.844Z&quot;</span><span class="p">,</span>
            <span class="c1"># ...</span>
        <span class="p">},</span>
    <span class="p">}</span>
<span class="p">]</span>
</pre></div>
</div>
<p>The formatting here is a bit simpler than with XML. The whole collection
is just represented as an array and the objects are represented by JSON objects
with three properties: “pk”, “model” and “fields”. “fields” is again an object
containing each field’s name and value as property and property-value
respectively.</p>
<p>Foreign keys have the PK of the linked object as property value.
ManyToMany-relations are serialized for the model that defines them and are
represented as a list of PKs.</p>
<p>Be aware that not all Django output can be passed unmodified to <a class="reference external" href="https://docs.python.org/3/library/json.html#module-json" title="(in Python v3.13)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code></a>.
For example, if you have some custom type in an object to be serialized, you’ll
have to write a custom <a class="reference external" href="https://docs.python.org/3/library/json.html#module-json" title="(in Python v3.13)"><code class="xref py py-mod docutils literal notranslate"><span class="pre">json</span></code></a> encoder for it. Something like this will
work:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core.serializers.json</span><span class="w"> </span><span class="kn">import</span> <span class="n">DjangoJSONEncoder</span>


<span class="k">class</span><span class="w"> </span><span class="nc">LazyEncoder</span><span class="p">(</span><span class="n">DjangoJSONEncoder</span><span class="p">):</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">default</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">obj</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">obj</span><span class="p">,</span> <span class="n">YourCustomType</span><span class="p">):</span>
            <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
        <span class="k">return</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">default</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
</pre></div>
</div>
<p>You can then pass <code class="docutils literal notranslate"><span class="pre">cls=LazyEncoder</span></code> to the <code class="docutils literal notranslate"><span class="pre">serializers.serialize()</span></code>
function:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.core.serializers</span><span class="w"> </span><span class="kn">import</span> <span class="n">serialize</span>

<span class="n">serialize</span><span class="p">(</span><span class="s2">&quot;json&quot;</span><span class="p">,</span> <span class="n">SomeModel</span><span class="o">.</span><span class="n">objects</span><span class="o">.</span><span class="n">all</span><span class="p">(),</span> <span class="bp">cls</span><span class="o">=</span><span class="n">LazyEncoder</span><span class="p">)</span>
</pre></div>
</div>
<p>Also note that GeoDjango provides a <a class="reference internal" href="../ref/contrib/gis/serializers.html"><span class="doc">customized GeoJSON serializer</span></a>.</p>
<section id="s-djangojsonencoder">
<span id="djangojsonencoder"></span><h4><code class="docutils literal notranslate"><span class="pre">DjangoJSONEncoder</span></code><a class="headerlink" href="#djangojsonencoder" title="Link to this heading">¶</a></h4>
<dl class="py class">
<dt class="sig sig-object py" id="django.core.serializers.json.DjangoJSONEncoder">
<em class="property"><span class="pre">class</span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">django.core.serializers.json.</span></span><span class="sig-name descname"><span class="pre">DjangoJSONEncoder</span></span><a class="headerlink" href="#django.core.serializers.json.DjangoJSONEncoder" title="Link to this definition">¶</a></dt>
<dd></dd></dl>

<p>The JSON serializer uses <code class="docutils literal notranslate"><span class="pre">DjangoJSONEncoder</span></code> for encoding. A subclass of
<a class="reference external" href="https://docs.python.org/3/library/json.html#json.JSONEncoder" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">JSONEncoder</span></code></a>, it handles these additional types:</p>
<dl class="simple">
<dt><a class="reference external" href="https://docs.python.org/3/library/datetime.html#datetime.datetime" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">datetime</span></code></a></dt><dd><p>A string of the form <code class="docutils literal notranslate"><span class="pre">YYYY-MM-DDTHH:mm:ss.sssZ</span></code> or
<code class="docutils literal notranslate"><span class="pre">YYYY-MM-DDTHH:mm:ss.sss+HH:MM</span></code> as defined in <a class="reference external" href="https://262.ecma-international.org/5.1/#sec-15.9.1.15">ECMA-262</a>.</p>
</dd>
<dt><a class="reference external" href="https://docs.python.org/3/library/datetime.html#datetime.date" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">date</span></code></a></dt><dd><p>A string of the form <code class="docutils literal notranslate"><span class="pre">YYYY-MM-DD</span></code> as defined in <a class="reference external" href="https://262.ecma-international.org/5.1/#sec-15.9.1.15">ECMA-262</a>.</p>
</dd>
<dt><a class="reference external" href="https://docs.python.org/3/library/datetime.html#datetime.time" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">time</span></code></a></dt><dd><p>A string of the form <code class="docutils literal notranslate"><span class="pre">HH:MM:ss.sss</span></code> as defined in <a class="reference external" href="https://262.ecma-international.org/5.1/#sec-15.9.1.15">ECMA-262</a>.</p>
</dd>
<dt><a class="reference external" href="https://docs.python.org/3/library/datetime.html#datetime.timedelta" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">timedelta</span></code></a></dt><dd><p>A string representing a duration as defined in ISO-8601. For example,
<code class="docutils literal notranslate"><span class="pre">timedelta(days=1,</span> <span class="pre">hours=2,</span> <span class="pre">seconds=3.4)</span></code> is represented as
<code class="docutils literal notranslate"><span class="pre">'P1DT02H00M03.400000S'</span></code>.</p>
</dd>
<dt><a class="reference external" href="https://docs.python.org/3/library/decimal.html#decimal.Decimal" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">Decimal</span></code></a>, <code class="docutils literal notranslate"><span class="pre">Promise</span></code> (<code class="docutils literal notranslate"><span class="pre">django.utils.functional.lazy()</span></code> objects), <a class="reference external" href="https://docs.python.org/3/library/uuid.html#uuid.UUID" title="(in Python v3.13)"><code class="xref py py-class docutils literal notranslate"><span class="pre">UUID</span></code></a></dt><dd><p>A string representation of the object.</p>
</dd>
</dl>
</section>
</section>
<section id="s-serialization-formats-jsonl">
<span id="s-id4"></span><span id="serialization-formats-jsonl"></span><span id="id4"></span><h3>JSONL<a class="headerlink" href="#serialization-formats-jsonl" title="Link to this heading">¶</a></h3>
<p><em>JSONL</em> stands for <em>JSON Lines</em>. With this format, objects are separated by new
lines, and each line contains a valid JSON object. JSONL serialized data looks
like this:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="p">{</span><span class="s2">&quot;pk&quot;</span><span class="p">:</span> <span class="s2">&quot;4b678b301dfd8a4e0dad910de3ae245b&quot;</span><span class="p">,</span> <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;sessions.session&quot;</span><span class="p">,</span> <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="o">...</span><span class="p">}}</span>
<span class="p">{</span><span class="s2">&quot;pk&quot;</span><span class="p">:</span> <span class="s2">&quot;88bea72c02274f3c9bf1cb2bb8cee4fc&quot;</span><span class="p">,</span> <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;sessions.session&quot;</span><span class="p">,</span> <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="o">...</span><span class="p">}}</span>
<span class="p">{</span><span class="s2">&quot;pk&quot;</span><span class="p">:</span> <span class="s2">&quot;9cf0e26691b64147a67e2a9f06ad7a53&quot;</span><span class="p">,</span> <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;sessions.session&quot;</span><span class="p">,</span> <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="o">...</span><span class="p">}}</span>
</pre></div>
</div>
<p>JSONL can be useful for populating large databases, since the data can be
processed line by line, rather than being loaded into memory all at once.</p>
</section>
<section id="s-yaml">
<span id="yaml"></span><h3>YAML<a class="headerlink" href="#yaml" title="Link to this heading">¶</a></h3>
<p>YAML serialization looks quite similar to JSON. The object list is serialized
as a sequence mappings with the keys “pk”, “model” and “fields”. Each field is
again a mapping with the key being name of the field and the value the value:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">model</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">sessions.session</span>
<span class="w">  </span><span class="nt">pk</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">4b678b301dfd8a4e0dad910de3ae245b</span>
<span class="w">  </span><span class="nt">fields</span><span class="p">:</span>
<span class="w">    </span><span class="nt">expire_date</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2013-01-16 08:16:59.844560+00:00</span>
</pre></div>
</div>
<p>Referential fields are again represented by the PK or sequence of PKs.</p>
</section>
<section id="s-custom-serialization-formats">
<span id="s-id5"></span><span id="custom-serialization-formats"></span><span id="id5"></span><h3>Custom serialization formats<a class="headerlink" href="#custom-serialization-formats" title="Link to this heading">¶</a></h3>
<p>In addition to the default formats, you can create a custom serialization
format.</p>
<p>For example, let’s consider a csv serializer and deserializer. First, define a
<code class="docutils literal notranslate"><span class="pre">Serializer</span></code> and a <code class="docutils literal notranslate"><span class="pre">Deserializer</span></code> class. These can override existing
serialization format classes:</p>
<div class="literal-block-wrapper docutils container" id="id7">
<div class="code-block-caption"><span class="caption-text"><code class="docutils literal notranslate"><span class="pre">path/to/custom_csv_serializer.py</span></code></span><a class="headerlink" href="#id7" title="Link to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span> <span class="kn">import</span><span class="w"> </span><span class="nn">csv</span>

 <span class="kn">from</span><span class="w"> </span><span class="nn">django.apps</span><span class="w"> </span><span class="kn">import</span> <span class="n">apps</span>
 <span class="kn">from</span><span class="w"> </span><span class="nn">django.core</span><span class="w"> </span><span class="kn">import</span> <span class="n">serializers</span>
 <span class="kn">from</span><span class="w"> </span><span class="nn">django.core.serializers.base</span><span class="w"> </span><span class="kn">import</span> <span class="n">DeserializationError</span>


 <span class="k">class</span><span class="w"> </span><span class="nc">Serializer</span><span class="p">(</span><span class="n">serializers</span><span class="o">.</span><span class="n">python</span><span class="o">.</span><span class="n">Serializer</span><span class="p">):</span>
     <span class="k">def</span><span class="w"> </span><span class="nf">get_dump_object</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">obj</span><span class="p">):</span>
         <span class="n">dumped_object</span> <span class="o">=</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">get_dump_object</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
         <span class="n">row</span> <span class="o">=</span> <span class="p">[</span><span class="n">dumped_object</span><span class="p">[</span><span class="s2">&quot;model&quot;</span><span class="p">],</span> <span class="nb">str</span><span class="p">(</span><span class="n">dumped_object</span><span class="p">[</span><span class="s2">&quot;pk&quot;</span><span class="p">])]</span>
         <span class="n">row</span> <span class="o">+=</span> <span class="p">[</span><span class="nb">str</span><span class="p">(</span><span class="n">value</span><span class="p">)</span> <span class="k">for</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">dumped_object</span><span class="p">[</span><span class="s2">&quot;fields&quot;</span><span class="p">]</span><span class="o">.</span><span class="n">values</span><span class="p">()]</span>
         <span class="k">return</span> <span class="s2">&quot;,&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">row</span><span class="p">),</span> <span class="n">dumped_object</span><span class="p">[</span><span class="s2">&quot;model&quot;</span><span class="p">]</span>

     <span class="k">def</span><span class="w"> </span><span class="nf">end_object</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">obj</span><span class="p">):</span>
         <span class="n">dumped_object_str</span><span class="p">,</span> <span class="n">model</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_dump_object</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
         <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">first</span><span class="p">:</span>
             <span class="n">fields</span> <span class="o">=</span> <span class="p">[</span><span class="n">field</span><span class="o">.</span><span class="n">name</span> <span class="k">for</span> <span class="n">field</span> <span class="ow">in</span> <span class="n">apps</span><span class="o">.</span><span class="n">get_model</span><span class="p">(</span><span class="n">model</span><span class="p">)</span><span class="o">.</span><span class="n">_meta</span><span class="o">.</span><span class="n">fields</span><span class="p">]</span>
             <span class="n">header</span> <span class="o">=</span> <span class="s2">&quot;,&quot;</span><span class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">fields</span><span class="p">)</span>
             <span class="bp">self</span><span class="o">.</span><span class="n">stream</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;model,</span><span class="si">{</span><span class="n">header</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>
         <span class="bp">self</span><span class="o">.</span><span class="n">stream</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">dumped_object_str</span><span class="si">}</span><span class="se">\n</span><span class="s2">&quot;</span><span class="p">)</span>

     <span class="k">def</span><span class="w"> </span><span class="nf">getvalue</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
         <span class="k">return</span> <span class="nb">super</span><span class="p">(</span><span class="n">serializers</span><span class="o">.</span><span class="n">python</span><span class="o">.</span><span class="n">Serializer</span><span class="p">,</span> <span class="bp">self</span><span class="p">)</span><span class="o">.</span><span class="n">getvalue</span><span class="p">()</span>


 <span class="k">class</span><span class="w"> </span><span class="nc">Deserializer</span><span class="p">(</span><span class="n">serializers</span><span class="o">.</span><span class="n">python</span><span class="o">.</span><span class="n">Deserializer</span><span class="p">):</span>
     <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">stream_or_string</span><span class="p">,</span> <span class="o">**</span><span class="n">options</span><span class="p">):</span>
         <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">stream_or_string</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">):</span>
             <span class="n">stream_or_string</span> <span class="o">=</span> <span class="n">stream_or_string</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span>
         <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">stream_or_string</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
             <span class="n">stream_or_string</span> <span class="o">=</span> <span class="n">stream_or_string</span><span class="o">.</span><span class="n">splitlines</span><span class="p">()</span>
         <span class="k">try</span><span class="p">:</span>
             <span class="n">objects</span> <span class="o">=</span> <span class="n">csv</span><span class="o">.</span><span class="n">DictReader</span><span class="p">(</span><span class="n">stream_or_string</span><span class="p">)</span>
         <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">exc</span><span class="p">:</span>
             <span class="k">raise</span> <span class="n">DeserializationError</span><span class="p">()</span> <span class="kn">from</span><span class="w"> </span><span class="nn">exc</span>
         <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">objects</span><span class="p">,</span> <span class="o">**</span><span class="n">options</span><span class="p">)</span>

     <span class="k">def</span><span class="w"> </span><span class="nf">_handle_object</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">obj</span><span class="p">):</span>
         <span class="k">try</span><span class="p">:</span>
             <span class="n">model_fields</span> <span class="o">=</span> <span class="n">apps</span><span class="o">.</span><span class="n">get_model</span><span class="p">(</span><span class="n">obj</span><span class="p">[</span><span class="s2">&quot;model&quot;</span><span class="p">])</span><span class="o">.</span><span class="n">_meta</span><span class="o">.</span><span class="n">fields</span>
             <span class="n">obj</span><span class="p">[</span><span class="s2">&quot;fields&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                 <span class="n">field</span><span class="o">.</span><span class="n">name</span><span class="p">:</span> <span class="n">obj</span><span class="p">[</span><span class="n">field</span><span class="o">.</span><span class="n">name</span><span class="p">]</span>
                 <span class="k">for</span> <span class="n">field</span> <span class="ow">in</span> <span class="n">model_fields</span>
                 <span class="k">if</span> <span class="n">field</span><span class="o">.</span><span class="n">name</span> <span class="ow">in</span> <span class="n">obj</span>
             <span class="p">}</span>
             <span class="k">yield from</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">_handle_object</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>
         <span class="k">except</span> <span class="p">(</span><span class="ne">GeneratorExit</span><span class="p">,</span> <span class="n">DeserializationError</span><span class="p">):</span>
             <span class="k">raise</span>
         <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">exc</span><span class="p">:</span>
             <span class="k">raise</span> <span class="n">DeserializationError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Error deserializing object: </span><span class="si">{</span><span class="n">exc</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span> <span class="kn">from</span><span class="w"> </span><span class="nn">exc</span>
</pre></div>
</div>
</div>
<p>Then add the module containing the serializer definitions to your
<a class="reference internal" href="../ref/settings.html#std-setting-SERIALIZATION_MODULES"><code class="xref std std-setting docutils literal notranslate"><span class="pre">SERIALIZATION_MODULES</span></code></a> setting:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">SERIALIZATION_MODULES</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;csv&quot;</span><span class="p">:</span> <span class="s2">&quot;path.to.custom_csv_serializer&quot;</span><span class="p">,</span>
    <span class="s2">&quot;json&quot;</span><span class="p">:</span> <span class="s2">&quot;django.core.serializers.json&quot;</span><span class="p">,</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="versionchanged">
<span class="title">Changed in Django 5.2:</span> <p>A <code class="docutils literal notranslate"><span class="pre">Deserializer</span></code> class definition was added to each of the provided
serialization formats.</p>
</div>
</section>
</section>
<section id="s-natural-keys">
<span id="s-topics-serialization-natural-keys"></span><span id="natural-keys"></span><span id="topics-serialization-natural-keys"></span><h2>Natural keys<a class="headerlink" href="#natural-keys" title="Link to this heading">¶</a></h2>
<p>The default serialization strategy for foreign keys and many-to-many relations
is to serialize the value of the primary key(s) of the objects in the relation.
This strategy works well for most objects, but it can cause difficulty in some
circumstances.</p>
<p>Consider the case of a list of objects that have a foreign key referencing
<a class="reference internal" href="../ref/contrib/contenttypes.html#django.contrib.contenttypes.models.ContentType" title="django.contrib.contenttypes.models.ContentType"><code class="xref py py-class docutils literal notranslate"><span class="pre">ContentType</span></code></a>. If you’re going to
serialize an object that refers to a content type, then you need to have a way
to refer to that content type to begin with. Since <code class="docutils literal notranslate"><span class="pre">ContentType</span></code> objects are
automatically created by Django during the database synchronization process,
the primary key of a given content type isn’t easy to predict; it will
depend on how and when <a class="reference internal" href="../ref/django-admin.html#django-admin-migrate"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">migrate</span></code></a> was executed. This is true for all
models which automatically generate objects, notably including
<a class="reference internal" href="../ref/contrib/auth.html#django.contrib.auth.models.Permission" title="django.contrib.auth.models.Permission"><code class="xref py py-class docutils literal notranslate"><span class="pre">Permission</span></code></a>,
<a class="reference internal" href="../ref/contrib/auth.html#django.contrib.auth.models.Group" title="django.contrib.auth.models.Group"><code class="xref py py-class docutils literal notranslate"><span class="pre">Group</span></code></a>, and
<a class="reference internal" href="../ref/contrib/auth.html#django.contrib.auth.models.User" title="django.contrib.auth.models.User"><code class="xref py py-class docutils literal notranslate"><span class="pre">User</span></code></a>.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>You should never include automatically generated objects in a fixture or
other serialized data. By chance, the primary keys in the fixture
may match those in the database and loading the fixture will
have no effect. In the more likely case that they don’t match, the fixture
loading will fail with an <a class="reference internal" href="../ref/exceptions.html#django.db.IntegrityError" title="django.db.IntegrityError"><code class="xref py py-class docutils literal notranslate"><span class="pre">IntegrityError</span></code></a>.</p>
</div>
<p>There is also the matter of convenience. An integer id isn’t always
the most convenient way to refer to an object; sometimes, a
more natural reference would be helpful.</p>
<p>It is for these reasons that Django provides <em>natural keys</em>. A natural
key is a tuple of values that can be used to uniquely identify an
object instance without using the primary key value.</p>
<section id="s-deserialization-of-natural-keys">
<span id="deserialization-of-natural-keys"></span><h3>Deserialization of natural keys<a class="headerlink" href="#deserialization-of-natural-keys" title="Link to this heading">¶</a></h3>
<p>Consider the following two models:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Person</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">first_name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">last_name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>

    <span class="n">birthdate</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">DateField</span><span class="p">()</span>

    <span class="k">class</span><span class="w"> </span><span class="nc">Meta</span><span class="p">:</span>
        <span class="n">constraints</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">models</span><span class="o">.</span><span class="n">UniqueConstraint</span><span class="p">(</span>
                <span class="n">fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;first_name&quot;</span><span class="p">,</span> <span class="s2">&quot;last_name&quot;</span><span class="p">],</span>
                <span class="n">name</span><span class="o">=</span><span class="s2">&quot;unique_first_last_name&quot;</span><span class="p">,</span>
            <span class="p">),</span>
        <span class="p">]</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Book</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">author</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">ForeignKey</span><span class="p">(</span><span class="n">Person</span><span class="p">,</span> <span class="n">on_delete</span><span class="o">=</span><span class="n">models</span><span class="o">.</span><span class="n">CASCADE</span><span class="p">)</span>
</pre></div>
</div>
<p>Ordinarily, serialized data for <code class="docutils literal notranslate"><span class="pre">Book</span></code> would use an integer to refer to
the author. For example, in JSON, a Book might be serialized as:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">...</span>
<span class="p">{</span><span class="s2">&quot;pk&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span> <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;store.book&quot;</span><span class="p">,</span> <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Mostly Harmless&quot;</span><span class="p">,</span> <span class="s2">&quot;author&quot;</span><span class="p">:</span> <span class="mi">42</span><span class="p">}}</span>
<span class="o">...</span>
</pre></div>
</div>
<p>This isn’t a particularly natural way to refer to an author. It
requires that you know the primary key value for the author; it also
requires that this primary key value is stable and predictable.</p>
<p>However, if we add natural key handling to Person, the fixture becomes
much more humane. To add natural key handling, you define a default
Manager for Person with a <code class="docutils literal notranslate"><span class="pre">get_by_natural_key()</span></code> method. In the case
of a Person, a good natural key might be the pair of first and last
name:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.db</span><span class="w"> </span><span class="kn">import</span> <span class="n">models</span>


<span class="k">class</span><span class="w"> </span><span class="nc">PersonManager</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Manager</span><span class="p">):</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">get_by_natural_key</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">first_name</span><span class="p">,</span> <span class="n">last_name</span><span class="p">):</span>
        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">first_name</span><span class="o">=</span><span class="n">first_name</span><span class="p">,</span> <span class="n">last_name</span><span class="o">=</span><span class="n">last_name</span><span class="p">)</span>


<span class="k">class</span><span class="w"> </span><span class="nc">Person</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">first_name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">last_name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">birthdate</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">DateField</span><span class="p">()</span>

    <span class="n">objects</span> <span class="o">=</span> <span class="n">PersonManager</span><span class="p">()</span>

    <span class="k">class</span><span class="w"> </span><span class="nc">Meta</span><span class="p">:</span>
        <span class="n">constraints</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">models</span><span class="o">.</span><span class="n">UniqueConstraint</span><span class="p">(</span>
                <span class="n">fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;first_name&quot;</span><span class="p">,</span> <span class="s2">&quot;last_name&quot;</span><span class="p">],</span>
                <span class="n">name</span><span class="o">=</span><span class="s2">&quot;unique_first_last_name&quot;</span><span class="p">,</span>
            <span class="p">),</span>
        <span class="p">]</span>
</pre></div>
</div>
<p>Now books can use that natural key to refer to <code class="docutils literal notranslate"><span class="pre">Person</span></code> objects:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">...</span>
<span class="p">{</span>
    <span class="s2">&quot;pk&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>
    <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;store.book&quot;</span><span class="p">,</span>
    <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Mostly Harmless&quot;</span><span class="p">,</span> <span class="s2">&quot;author&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Douglas&quot;</span><span class="p">,</span> <span class="s2">&quot;Adams&quot;</span><span class="p">]},</span>
<span class="p">}</span>
<span class="o">...</span>
</pre></div>
</div>
<p>When you try to load this serialized data, Django will use the
<code class="docutils literal notranslate"><span class="pre">get_by_natural_key()</span></code> method to resolve <code class="docutils literal notranslate"><span class="pre">[&quot;Douglas&quot;,</span> <span class="pre">&quot;Adams&quot;]</span></code>
into the primary key of an actual <code class="docutils literal notranslate"><span class="pre">Person</span></code> object.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Whatever fields you use for a natural key must be able to uniquely
identify an object. This will usually mean that your model will
have a uniqueness clause (either <code class="docutils literal notranslate"><span class="pre">unique=True</span></code> on a single field, or a
<code class="docutils literal notranslate"><span class="pre">UniqueConstraint</span></code> or <code class="docutils literal notranslate"><span class="pre">unique_together</span></code> over multiple fields) for the
field or fields in your natural key. However, uniqueness doesn’t need to be
enforced at the database level. If you are certain that a set of fields
will be effectively unique, you can still use those fields as a natural
key.</p>
</div>
<p>Deserialization of objects with no primary key will always check whether the
model’s manager has a <code class="docutils literal notranslate"><span class="pre">get_by_natural_key()</span></code> method and if so, use it to
populate the deserialized object’s primary key.</p>
</section>
<section id="s-serialization-of-natural-keys">
<span id="serialization-of-natural-keys"></span><h3>Serialization of natural keys<a class="headerlink" href="#serialization-of-natural-keys" title="Link to this heading">¶</a></h3>
<p>So how do you get Django to emit a natural key when serializing an object?
Firstly, you need to add another method – this time to the model itself:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">Person</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">first_name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">last_name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">birthdate</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">DateField</span><span class="p">()</span>

    <span class="n">objects</span> <span class="o">=</span> <span class="n">PersonManager</span><span class="p">()</span>

    <span class="k">class</span><span class="w"> </span><span class="nc">Meta</span><span class="p">:</span>
        <span class="n">constraints</span> <span class="o">=</span> <span class="p">[</span>
            <span class="n">models</span><span class="o">.</span><span class="n">UniqueConstraint</span><span class="p">(</span>
                <span class="n">fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;first_name&quot;</span><span class="p">,</span> <span class="s2">&quot;last_name&quot;</span><span class="p">],</span>
                <span class="n">name</span><span class="o">=</span><span class="s2">&quot;unique_first_last_name&quot;</span><span class="p">,</span>
            <span class="p">),</span>
        <span class="p">]</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">natural_key</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">first_name</span><span class="p">,</span> <span class="bp">self</span><span class="o">.</span><span class="n">last_name</span><span class="p">)</span>
</pre></div>
</div>
<p>That method should always return a natural key tuple – in this
example, <code class="docutils literal notranslate"><span class="pre">(first</span> <span class="pre">name,</span> <span class="pre">last</span> <span class="pre">name)</span></code>. Then, when you call
<code class="docutils literal notranslate"><span class="pre">serializers.serialize()</span></code>, you provide <code class="docutils literal notranslate"><span class="pre">use_natural_foreign_keys=True</span></code>
or <code class="docutils literal notranslate"><span class="pre">use_natural_primary_keys=True</span></code> arguments:</p>
<div class="highlight-pycon notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">serializers</span><span class="o">.</span><span class="n">serialize</span><span class="p">(</span>
<span class="gp">... </span>    <span class="s2">&quot;json&quot;</span><span class="p">,</span>
<span class="gp">... </span>    <span class="p">[</span><span class="n">book1</span><span class="p">,</span> <span class="n">book2</span><span class="p">],</span>
<span class="gp">... </span>    <span class="n">indent</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">use_natural_foreign_keys</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
<span class="gp">... </span>    <span class="n">use_natural_primary_keys</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
<span class="gp">... </span><span class="p">)</span>
</pre></div>
</div>
<p>When <code class="docutils literal notranslate"><span class="pre">use_natural_foreign_keys=True</span></code> is specified, Django will use the
<code class="docutils literal notranslate"><span class="pre">natural_key()</span></code> method to serialize any foreign key reference to objects
of the type that defines the method.</p>
<p>When <code class="docutils literal notranslate"><span class="pre">use_natural_primary_keys=True</span></code> is specified, Django will not provide the
primary key in the serialized data of this object since it can be calculated
during deserialization:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">...</span>
<span class="p">{</span>
    <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;store.person&quot;</span><span class="p">,</span>
    <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span>
        <span class="s2">&quot;first_name&quot;</span><span class="p">:</span> <span class="s2">&quot;Douglas&quot;</span><span class="p">,</span>
        <span class="s2">&quot;last_name&quot;</span><span class="p">:</span> <span class="s2">&quot;Adams&quot;</span><span class="p">,</span>
        <span class="s2">&quot;birth_date&quot;</span><span class="p">:</span> <span class="s2">&quot;1952-03-11&quot;</span><span class="p">,</span>
    <span class="p">},</span>
<span class="p">}</span>
<span class="o">...</span>
</pre></div>
</div>
<p>This can be useful when you need to load serialized data into an existing
database and you cannot guarantee that the serialized primary key value is not
already in use, and do not need to ensure that deserialized objects retain the
same primary keys.</p>
<p>If you are using <a class="reference internal" href="../ref/django-admin.html#django-admin-dumpdata"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">dumpdata</span></code></a> to generate serialized data, use the
<a class="reference internal" href="../ref/django-admin.html#cmdoption-dumpdata-natural-foreign"><code class="xref std std-option docutils literal notranslate"><span class="pre">dumpdata</span> <span class="pre">--natural-foreign</span></code></a> and <a class="reference internal" href="../ref/django-admin.html#cmdoption-dumpdata-natural-primary"><code class="xref std std-option docutils literal notranslate"><span class="pre">dumpdata</span> <span class="pre">--natural-primary</span></code></a>
command line flags to generate natural keys.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>You don’t need to define both <code class="docutils literal notranslate"><span class="pre">natural_key()</span></code> and
<code class="docutils literal notranslate"><span class="pre">get_by_natural_key()</span></code>. If you don’t want Django to output
natural keys during serialization, but you want to retain the
ability to load natural keys, then you can opt to not implement
the <code class="docutils literal notranslate"><span class="pre">natural_key()</span></code> method.</p>
<p>Conversely, if (for some strange reason) you want Django to output
natural keys during serialization, but <em>not</em> be able to load those
key values, just don’t define the <code class="docutils literal notranslate"><span class="pre">get_by_natural_key()</span></code> method.</p>
</div>
</section>
<section id="s-natural-keys-and-forward-references">
<span id="s-id6"></span><span id="natural-keys-and-forward-references"></span><span id="id6"></span><h3>Natural keys and forward references<a class="headerlink" href="#natural-keys-and-forward-references" title="Link to this heading">¶</a></h3>
<p>Sometimes when you use <a class="reference internal" href="#topics-serialization-natural-keys"><span class="std std-ref">natural foreign keys</span></a> you’ll need to deserialize data where
an object has a foreign key referencing another object that hasn’t yet been
deserialized. This is called a “forward reference”.</p>
<p>For instance, suppose you have the following objects in your fixture:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="o">...</span>
<span class="p">{</span>
    <span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;store.book&quot;</span><span class="p">,</span>
    <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Mostly Harmless&quot;</span><span class="p">,</span> <span class="s2">&quot;author&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;Douglas&quot;</span><span class="p">,</span> <span class="s2">&quot;Adams&quot;</span><span class="p">]},</span>
<span class="p">},</span>
<span class="o">...</span>
<span class="p">{</span><span class="s2">&quot;model&quot;</span><span class="p">:</span> <span class="s2">&quot;store.person&quot;</span><span class="p">,</span> <span class="s2">&quot;fields&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;first_name&quot;</span><span class="p">:</span> <span class="s2">&quot;Douglas&quot;</span><span class="p">,</span> <span class="s2">&quot;last_name&quot;</span><span class="p">:</span> <span class="s2">&quot;Adams&quot;</span><span class="p">}},</span>
<span class="o">...</span>
</pre></div>
</div>
<p>In order to handle this situation, you need to pass
<code class="docutils literal notranslate"><span class="pre">handle_forward_references=True</span></code> to <code class="docutils literal notranslate"><span class="pre">serializers.deserialize()</span></code>. This will
set the <code class="docutils literal notranslate"><span class="pre">deferred_fields</span></code> attribute on the <code class="docutils literal notranslate"><span class="pre">DeserializedObject</span></code> instances.
You’ll need to keep track of <code class="docutils literal notranslate"><span class="pre">DeserializedObject</span></code> instances where this
attribute isn’t <code class="docutils literal notranslate"><span class="pre">None</span></code> and later call <code class="docutils literal notranslate"><span class="pre">save_deferred_fields()</span></code> on them.</p>
<p>Typical usage looks like this:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">objs_with_deferred_fields</span> <span class="o">=</span> <span class="p">[]</span>

<span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">serializers</span><span class="o">.</span><span class="n">deserialize</span><span class="p">(</span><span class="s2">&quot;xml&quot;</span><span class="p">,</span> <span class="n">data</span><span class="p">,</span> <span class="n">handle_forward_references</span><span class="o">=</span><span class="kc">True</span><span class="p">):</span>
    <span class="n">obj</span><span class="o">.</span><span class="n">save</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">obj</span><span class="o">.</span><span class="n">deferred_fields</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
        <span class="n">objs_with_deferred_fields</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">obj</span><span class="p">)</span>

<span class="k">for</span> <span class="n">obj</span> <span class="ow">in</span> <span class="n">objs_with_deferred_fields</span><span class="p">:</span>
    <span class="n">obj</span><span class="o">.</span><span class="n">save_deferred_fields</span><span class="p">()</span>
</pre></div>
</div>
<p>For this to work, the <code class="docutils literal notranslate"><span class="pre">ForeignKey</span></code> on the referencing model must have
<code class="docutils literal notranslate"><span class="pre">null=True</span></code>.</p>
</section>
<section id="s-dependencies-during-serialization">
<span id="dependencies-during-serialization"></span><h3>Dependencies during serialization<a class="headerlink" href="#dependencies-during-serialization" title="Link to this heading">¶</a></h3>
<p>It’s often possible to avoid explicitly having to handle forward references by
taking care with the ordering of objects within a fixture.</p>
<p>To help with this, calls to <a class="reference internal" href="../ref/django-admin.html#django-admin-dumpdata"><code class="xref std std-djadmin docutils literal notranslate"><span class="pre">dumpdata</span></code></a> that use the <a class="reference internal" href="../ref/django-admin.html#cmdoption-dumpdata-natural-foreign"><code class="xref std std-option docutils literal notranslate"><span class="pre">dumpdata</span>
<span class="pre">--natural-foreign</span></code></a> option will serialize any model with a <code class="docutils literal notranslate"><span class="pre">natural_key()</span></code>
method before serializing standard primary key objects.</p>
<p>However, this may not always be enough. If your natural key refers to
another object (by using a foreign key or natural key to another object
as part of a natural key), then you need to be able to ensure that
the objects on which a natural key depends occur in the serialized data
before the natural key requires them.</p>
<p>To control this ordering, you can define dependencies on your
<code class="docutils literal notranslate"><span class="pre">natural_key()</span></code> methods. You do this by setting a <code class="docutils literal notranslate"><span class="pre">dependencies</span></code>
attribute on the <code class="docutils literal notranslate"><span class="pre">natural_key()</span></code> method itself.</p>
<p>For example, let’s add a natural key to the <code class="docutils literal notranslate"><span class="pre">Book</span></code> model from the
example above:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">class</span><span class="w"> </span><span class="nc">Book</span><span class="p">(</span><span class="n">models</span><span class="o">.</span><span class="n">Model</span><span class="p">):</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">CharField</span><span class="p">(</span><span class="n">max_length</span><span class="o">=</span><span class="mi">100</span><span class="p">)</span>
    <span class="n">author</span> <span class="o">=</span> <span class="n">models</span><span class="o">.</span><span class="n">ForeignKey</span><span class="p">(</span><span class="n">Person</span><span class="p">,</span> <span class="n">on_delete</span><span class="o">=</span><span class="n">models</span><span class="o">.</span><span class="n">CASCADE</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">natural_key</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="p">,)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">author</span><span class="o">.</span><span class="n">natural_key</span><span class="p">()</span>
</pre></div>
</div>
<p>The natural key for a <code class="docutils literal notranslate"><span class="pre">Book</span></code> is a combination of its name and its
author. This means that <code class="docutils literal notranslate"><span class="pre">Person</span></code> must be serialized before <code class="docutils literal notranslate"><span class="pre">Book</span></code>.
To define this dependency, we add one extra line:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">natural_key</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
    <span class="k">return</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">name</span><span class="p">,)</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">author</span><span class="o">.</span><span class="n">natural_key</span><span class="p">()</span>


<span class="n">natural_key</span><span class="o">.</span><span class="n">dependencies</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;example_app.person&quot;</span><span class="p">]</span>
</pre></div>
</div>
<p>This definition ensures that all <code class="docutils literal notranslate"><span class="pre">Person</span></code> objects are serialized before
any <code class="docutils literal notranslate"><span class="pre">Book</span></code> objects. In turn, any object referencing <code class="docutils literal notranslate"><span class="pre">Book</span></code> will be
serialized after both <code class="docutils literal notranslate"><span class="pre">Person</span></code> and <code class="docutils literal notranslate"><span class="pre">Book</span></code> have been serialized.</p>
</section>
</section>
</section>


          </div>
        </div>
      </div>
      
        
          <div class="yui-b" id="sidebar">
            
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Serializing Django objects</a><ul>
<li><a class="reference internal" href="#serializing-data">Serializing data</a><ul>
<li><a class="reference internal" href="#subset-of-fields">Subset of fields</a></li>
<li><a class="reference internal" href="#inherited-models">Inherited models</a></li>
</ul>
</li>
<li><a class="reference internal" href="#deserializing-data">Deserializing data</a></li>
<li><a class="reference internal" href="#serialization-formats">Serialization formats</a><ul>
<li><a class="reference internal" href="#xml">XML</a></li>
<li><a class="reference internal" href="#serialization-formats-json">JSON</a><ul>
<li><a class="reference internal" href="#djangojsonencoder"><code class="docutils literal notranslate"><span class="pre">DjangoJSONEncoder</span></code></a></li>
</ul>
</li>
<li><a class="reference internal" href="#serialization-formats-jsonl">JSONL</a></li>
<li><a class="reference internal" href="#yaml">YAML</a></li>
<li><a class="reference internal" href="#custom-serialization-formats">Custom serialization formats</a></li>
</ul>
</li>
<li><a class="reference internal" href="#natural-keys">Natural keys</a><ul>
<li><a class="reference internal" href="#deserialization-of-natural-keys">Deserialization of natural keys</a></li>
<li><a class="reference internal" href="#serialization-of-natural-keys">Serialization of natural keys</a></li>
<li><a class="reference internal" href="#natural-keys-and-forward-references">Natural keys and forward references</a></li>
<li><a class="reference internal" href="#dependencies-during-serialization">Dependencies during serialization</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="performance.html"
                          title="previous chapter">Performance and optimization</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="settings.html"
                          title="next chapter">Django settings</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/topics/serialization.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
              <h3>Last update:</h3>
              <p class="topless">Jul 02, 2025</p>
          </div>
        
      
    </div>

    <div id="ft">
      <div class="nav">
    &laquo; <a href="performance.html" title="Performance and optimization">previous</a>
     |
    <a href="index.html" title="Using Django" accesskey="U">up</a>
   |
    <a href="settings.html" title="Django settings">next</a> &raquo;</div>
    </div>
  </div>

      <div class="clearer"></div>
    </div>
  </body>
</html>