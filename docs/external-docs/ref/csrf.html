<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>Cross Site Request Forgery protection &#8212; Django 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=34bb78ad" />
    <link rel="stylesheet" type="text/css" href="../_static/default.css?v=bf4d74af" />
    <script src="../_static/documentation_options.js?v=6593ad68"></script>
    <script src="../_static/doctools.js?v=9bcbadda"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Databases" href="databases.html" />
    <link rel="prev" title="The syndication feed framework" href="contrib/syndication.html" />



 
<script src="../templatebuiltins.js"></script>
<script>
(function($) {
    if (!django_template_builtins) {
       // templatebuiltins.js missing, do nothing.
       return;
    }
    $(document).ready(function() {
        // Hyperlink Django template tags and filters
        var base = "templates/builtins.html";
        if (base == "#") {
            // Special case for builtins.html itself
            base = "";
        }
        // Tags are keywords, class '.k'
        $("div.highlight\\-html\\+django span.k").each(function(i, elem) {
             var tagname = $(elem).text();
             if ($.inArray(tagname, django_template_builtins.ttags) != -1) {
                 var fragment = tagname.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + tagname + "</a>");
             }
        });
        // Filters are functions, class '.nf'
        $("div.highlight\\-html\\+django span.nf").each(function(i, elem) {
             var filtername = $(elem).text();
             if ($.inArray(filtername, django_template_builtins.tfilters) != -1) {
                 var fragment = filtername.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + filtername + "</a>");
             }
        });
    });
})(jQuery);</script>

  </head><body>

    <div class="document">
  <div id="custom-doc" class="yui-t6">
    <div id="hd">
      <h1><a href="../index.html">Django 5.2 documentation</a></h1>
      <div id="global-nav">
        <a title="Home page" href="../index.html">Home</a>  |
        <a title="Table of contents" href="../contents.html">Table of contents</a>  |
        <a title="Global index" href="../genindex.html">Index</a>  |
        <a title="Module index" href="../py-modindex.html">Modules</a>
      </div>
      <div class="nav">
    &laquo; <a href="contrib/syndication.html" title="The syndication feed framework">previous</a>
     |
    <a href="index.html" title="API Reference" accesskey="U">up</a>
   |
    <a href="databases.html" title="Databases">next</a> &raquo;</div>
    </div>

    <div id="bd">
      <div id="yui-main">
        <div class="yui-b">
          <div class="yui-g" id="ref-csrf">
            
  <section id="s-module-django.middleware.csrf">
<span id="s-cross-site-request-forgery-protection"></span><span id="module-django.middleware.csrf"></span><span id="cross-site-request-forgery-protection"></span><h1>Cross Site Request Forgery protection<a class="headerlink" href="#module-django.middleware.csrf" title="Link to this heading">¶</a></h1>
<p>The CSRF middleware and template tag provides easy-to-use protection against
<a class="reference external" href="https://owasp.org/www-community/attacks/csrf#overview">Cross Site Request Forgeries</a>. This type of attack occurs when a malicious
website contains a link, a form button or some JavaScript that is intended to
perform some action on your website, using the credentials of a logged-in user
who visits the malicious site in their browser. A related type of attack,
‘login CSRF’, where an attacking site tricks a user’s browser into logging into
a site with someone else’s credentials, is also covered.</p>
<p>The first defense against CSRF attacks is to ensure that GET requests (and other
‘safe’ methods, as defined by <span class="target" id="index-0"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc9110.html#section-9.2.1"><strong>RFC 9110 Section 9.2.1</strong></a>) are side effect free.
Requests via ‘unsafe’ methods, such as POST, PUT, and DELETE, can then be
protected by the steps outlined in <a class="reference internal" href="../howto/csrf.html#using-csrf"><span class="std std-ref">How to use Django’s CSRF protection</span></a>.</p>
<section id="s-how-it-works">
<span id="s-how-csrf-works"></span><span id="how-it-works"></span><span id="how-csrf-works"></span><h2>How it works<a class="headerlink" href="#how-it-works" title="Link to this heading">¶</a></h2>
<p>The CSRF protection is based on the following things:</p>
<ol class="arabic">
<li><p>A CSRF cookie that is a random secret value, which other sites will not have
access to.</p>
<p><code class="docutils literal notranslate"><span class="pre">CsrfViewMiddleware</span></code> sends this cookie with the response whenever
<code class="docutils literal notranslate"><span class="pre">django.middleware.csrf.get_token()</span></code> is called. It can also send it in
other cases. For security reasons, the value of the secret is changed each
time a user logs in.</p>
</li>
<li><p>A hidden form field with the name ‘csrfmiddlewaretoken’, present in all
outgoing POST forms.</p>
<p>In order to protect against <a class="reference external" href="https://www.breachattack.com/">BREACH</a> attacks, the value of this field is
not simply the secret. It is scrambled differently with each response using
a mask. The mask is generated randomly on every call to <code class="docutils literal notranslate"><span class="pre">get_token()</span></code>, so
the form field value is different each time.</p>
<p>This part is done by the <a class="reference internal" href="templates/builtins.html#std-templatetag-csrf_token"><code class="xref std std-ttag docutils literal notranslate"><span class="pre">csrf_token</span></code></a> template tag.</p>
</li>
<li><p>For all incoming requests that are not using HTTP GET, HEAD, OPTIONS or
TRACE, a CSRF cookie must be present, and the ‘csrfmiddlewaretoken’ field
must be present and correct. If it isn’t, the user will get a 403 error.</p>
<p>When validating the ‘csrfmiddlewaretoken’ field value, only the secret,
not the full token, is compared with the secret in the cookie value.
This allows the use of ever-changing tokens. While each request may use its
own token, the secret remains common to all.</p>
<p>This check is done by <code class="docutils literal notranslate"><span class="pre">CsrfViewMiddleware</span></code>.</p>
</li>
<li><p><code class="docutils literal notranslate"><span class="pre">CsrfViewMiddleware</span></code> verifies the <a class="reference external" href="https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Origin">Origin header</a>, if provided by the
browser, against the current host and the <a class="reference internal" href="settings.html#std-setting-CSRF_TRUSTED_ORIGINS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_TRUSTED_ORIGINS</span></code></a>
setting. This provides protection against cross-subdomain attacks.</p></li>
<li><p>In addition, for HTTPS requests, if the <code class="docutils literal notranslate"><span class="pre">Origin</span></code> header isn’t provided,
<code class="docutils literal notranslate"><span class="pre">CsrfViewMiddleware</span></code> performs strict referer checking. This means that
even if a subdomain can set or modify cookies on your domain, it can’t force
a user to post to your application since that request won’t come from your
own exact domain.</p>
<p>This also addresses a man-in-the-middle attack that’s possible under HTTPS
when using a session independent secret, due to the fact that HTTP
<code class="docutils literal notranslate"><span class="pre">Set-Cookie</span></code> headers are (unfortunately) accepted by clients even when
they are talking to a site under HTTPS. (Referer checking is not done for
HTTP requests because the presence of the <code class="docutils literal notranslate"><span class="pre">Referer</span></code> header isn’t reliable
enough under HTTP.)</p>
<p>If the <a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_DOMAIN"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_DOMAIN</span></code></a> setting is set, the referer is compared
against it. You can allow cross-subdomain requests by including a leading
dot. For example, <code class="docutils literal notranslate"><span class="pre">CSRF_COOKIE_DOMAIN</span> <span class="pre">=</span> <span class="pre">'.example.com'</span></code> will allow POST
requests from <code class="docutils literal notranslate"><span class="pre">www.example.com</span></code> and <code class="docutils literal notranslate"><span class="pre">api.example.com</span></code>. If the setting is
not set, then the referer must match the HTTP <code class="docutils literal notranslate"><span class="pre">Host</span></code> header.</p>
<p>Expanding the accepted referers beyond the current host or cookie domain can
be done with the <a class="reference internal" href="settings.html#std-setting-CSRF_TRUSTED_ORIGINS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_TRUSTED_ORIGINS</span></code></a> setting.</p>
</li>
</ol>
<p>This ensures that only forms that have originated from trusted domains can be
used to POST data back.</p>
<p>It deliberately ignores GET requests (and other requests that are defined as
‘safe’ by <span class="target" id="index-1"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc9110.html#section-9.2.1"><strong>RFC 9110 Section 9.2.1</strong></a>). These requests ought never to have any
potentially dangerous side effects, and so a CSRF attack with a GET request
ought to be harmless. <span class="target" id="index-2"></span><a class="rfc reference external" href="https://datatracker.ietf.org/doc/html/rfc9110.html#section-9.2.1"><strong>RFC 9110 Section 9.2.1</strong></a> defines POST, PUT, and DELETE
as ‘unsafe’, and all other methods are also assumed to be unsafe, for maximum
protection.</p>
<p>The CSRF protection cannot protect against man-in-the-middle attacks, so use
<a class="reference internal" href="../topics/security.html#security-recommendation-ssl"><span class="std std-ref">HTTPS</span></a> with
<a class="reference internal" href="middleware.html#http-strict-transport-security"><span class="std std-ref">HTTP Strict Transport Security</span></a>. It also assumes <a class="reference internal" href="../topics/security.html#host-headers-virtual-hosting"><span class="std std-ref">validation of
the HOST header</span></a> and that there aren’t any
<a class="reference internal" href="../topics/security.html#cross-site-scripting"><span class="std std-ref">cross-site scripting vulnerabilities</span></a> on your site
(because XSS vulnerabilities already let an attacker do anything a CSRF
vulnerability allows and much worse).</p>
<div class="admonition-removing-the-referer-header admonition">
<p class="admonition-title">Removing the <code class="docutils literal notranslate"><span class="pre">Referer</span></code> header</p>
<p>To avoid disclosing the referrer URL to third-party sites, you might want
to <a class="reference external" href="https://www.w3.org/TR/referrer-policy/#referrer-policy-delivery">disable the referer</a> on your site’s <code class="docutils literal notranslate"><span class="pre">&lt;a&gt;</span></code> tags. For example, you
might use the <code class="docutils literal notranslate"><span class="pre">&lt;meta</span> <span class="pre">name=&quot;referrer&quot;</span> <span class="pre">content=&quot;no-referrer&quot;&gt;</span></code> tag or
include the <code class="docutils literal notranslate"><span class="pre">Referrer-Policy:</span> <span class="pre">no-referrer</span></code> header. Due to the CSRF
protection’s strict referer checking on HTTPS requests, those techniques
cause a CSRF failure on requests with ‘unsafe’ methods. Instead, use
alternatives like <code class="docutils literal notranslate"><span class="pre">&lt;a</span> <span class="pre">rel=&quot;noreferrer&quot;</span> <span class="pre">...&gt;&quot;</span></code> for links to third-party
sites.</p>
</div>
</section>
<section id="s-limitations">
<span id="s-csrf-limitations"></span><span id="limitations"></span><span id="csrf-limitations"></span><h2>Limitations<a class="headerlink" href="#limitations" title="Link to this heading">¶</a></h2>
<p>Subdomains within a site will be able to set cookies on the client for the whole
domain. By setting the cookie and using a corresponding token, subdomains will
be able to circumvent the CSRF protection. The only way to avoid this is to
ensure that subdomains are controlled by trusted users (or, are at least unable
to set cookies). Note that even without CSRF, there are other vulnerabilities,
such as session fixation, that make giving subdomains to untrusted parties a bad
idea, and these vulnerabilities cannot easily be fixed with current browsers.</p>
</section>
<section id="s-module-django.views.decorators.csrf">
<span id="s-utilities"></span><span id="module-django.views.decorators.csrf"></span><span id="utilities"></span><h2>Utilities<a class="headerlink" href="#module-django.views.decorators.csrf" title="Link to this heading">¶</a></h2>
<p>The examples below assume you are using function-based views. If you
are working with class-based views, you can refer to <a class="reference internal" href="../topics/class-based-views/intro.html#id1"><span class="std std-ref">Decorating
class-based views</span></a>.</p>
<dl class="py function">
<dt class="sig sig-object py" id="django.views.decorators.csrf.csrf_exempt">
<span class="sig-name descname"><span class="pre">csrf_exempt</span></span>(<em class="sig-param"><span class="n"><span class="pre">view</span></span></em>)<a class="reference external" href="https://github.com/django/django/blob/stable/5.2.x/django/views/decorators/csrf.py#L51"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#django.views.decorators.csrf.csrf_exempt" title="Link to this definition">¶</a></dt>
<dd><p>This decorator marks a view as being exempt from the protection ensured by
the middleware. Example:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.http</span><span class="w"> </span><span class="kn">import</span> <span class="n">HttpResponse</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.views.decorators.csrf</span><span class="w"> </span><span class="kn">import</span> <span class="n">csrf_exempt</span>


<span class="nd">@csrf_exempt</span>
<span class="k">def</span><span class="w"> </span><span class="nf">my_view</span><span class="p">(</span><span class="n">request</span><span class="p">):</span>
    <span class="k">return</span> <span class="n">HttpResponse</span><span class="p">(</span><span class="s2">&quot;Hello world&quot;</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="django.views.decorators.csrf.csrf_protect">
<span class="sig-name descname"><span class="pre">csrf_protect</span></span>(<em class="sig-param"><span class="n"><span class="pre">view</span></span></em>)<a class="headerlink" href="#django.views.decorators.csrf.csrf_protect" title="Link to this definition">¶</a></dt>
<dd><p>Decorator that provides the protection of
<a class="reference internal" href="middleware.html#django.middleware.csrf.CsrfViewMiddleware" title="django.middleware.csrf.CsrfViewMiddleware"><code class="xref py py-class docutils literal notranslate"><span class="pre">CsrfViewMiddleware</span></code></a> to a view.</p>
<p>Usage:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.shortcuts</span><span class="w"> </span><span class="kn">import</span> <span class="n">render</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.views.decorators.csrf</span><span class="w"> </span><span class="kn">import</span> <span class="n">csrf_protect</span>


<span class="nd">@csrf_protect</span>
<span class="k">def</span><span class="w"> </span><span class="nf">my_view</span><span class="p">(</span><span class="n">request</span><span class="p">):</span>
    <span class="n">c</span> <span class="o">=</span> <span class="p">{}</span>
    <span class="c1"># ...</span>
    <span class="k">return</span> <span class="n">render</span><span class="p">(</span><span class="n">request</span><span class="p">,</span> <span class="s2">&quot;a_template.html&quot;</span><span class="p">,</span> <span class="n">c</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="django.views.decorators.csrf.requires_csrf_token">
<span class="sig-name descname"><span class="pre">requires_csrf_token</span></span>(<em class="sig-param"><span class="n"><span class="pre">view</span></span></em>)<a class="headerlink" href="#django.views.decorators.csrf.requires_csrf_token" title="Link to this definition">¶</a></dt>
<dd><p>Normally the <a class="reference internal" href="templates/builtins.html#std-templatetag-csrf_token"><code class="xref std std-ttag docutils literal notranslate"><span class="pre">csrf_token</span></code></a> template tag will not work if
<code class="docutils literal notranslate"><span class="pre">CsrfViewMiddleware.process_view</span></code> or an equivalent like <code class="docutils literal notranslate"><span class="pre">csrf_protect</span></code>
has not run. The view decorator <code class="docutils literal notranslate"><span class="pre">requires_csrf_token</span></code> can be used to
ensure the template tag does work. This decorator works similarly to
<code class="docutils literal notranslate"><span class="pre">csrf_protect</span></code>, but never rejects an incoming request.</p>
<p>Example:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">django.shortcuts</span><span class="w"> </span><span class="kn">import</span> <span class="n">render</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">django.views.decorators.csrf</span><span class="w"> </span><span class="kn">import</span> <span class="n">requires_csrf_token</span>


<span class="nd">@requires_csrf_token</span>
<span class="k">def</span><span class="w"> </span><span class="nf">my_view</span><span class="p">(</span><span class="n">request</span><span class="p">):</span>
    <span class="n">c</span> <span class="o">=</span> <span class="p">{}</span>
    <span class="c1"># ...</span>
    <span class="k">return</span> <span class="n">render</span><span class="p">(</span><span class="n">request</span><span class="p">,</span> <span class="s2">&quot;a_template.html&quot;</span><span class="p">,</span> <span class="n">c</span><span class="p">)</span>
</pre></div>
</div>
</dd></dl>

<dl class="py function">
<dt class="sig sig-object py" id="django.views.decorators.csrf.ensure_csrf_cookie">
<span class="sig-name descname"><span class="pre">ensure_csrf_cookie</span></span>(<em class="sig-param"><span class="n"><span class="pre">view</span></span></em>)<a class="headerlink" href="#django.views.decorators.csrf.ensure_csrf_cookie" title="Link to this definition">¶</a></dt>
<dd><p>This decorator forces a view to send the CSRF cookie.</p>
</dd></dl>

</section>
<section id="s-settings">
<span id="settings"></span><h2>Settings<a class="headerlink" href="#settings" title="Link to this heading">¶</a></h2>
<p>A number of settings can be used to control Django’s CSRF behavior:</p>
<ul class="simple">
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_AGE"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_AGE</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_DOMAIN"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_DOMAIN</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_HTTPONLY"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_HTTPONLY</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_NAME"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_NAME</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_PATH"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_PATH</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_SAMESITE"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_SAMESITE</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_COOKIE_SECURE"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_COOKIE_SECURE</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_FAILURE_VIEW"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_FAILURE_VIEW</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_HEADER_NAME"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_HEADER_NAME</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_TRUSTED_ORIGINS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_TRUSTED_ORIGINS</span></code></a></p></li>
<li><p><a class="reference internal" href="settings.html#std-setting-CSRF_USE_SESSIONS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_USE_SESSIONS</span></code></a></p></li>
</ul>
</section>
<section id="s-frequently-asked-questions">
<span id="frequently-asked-questions"></span><h2>Frequently Asked Questions<a class="headerlink" href="#frequently-asked-questions" title="Link to this heading">¶</a></h2>
<section id="s-is-posting-an-arbitrary-csrf-token-pair-cookie-and-post-data-a-vulnerability">
<span id="is-posting-an-arbitrary-csrf-token-pair-cookie-and-post-data-a-vulnerability"></span><h3>Is posting an arbitrary CSRF token pair (cookie and POST data) a vulnerability?<a class="headerlink" href="#is-posting-an-arbitrary-csrf-token-pair-cookie-and-post-data-a-vulnerability" title="Link to this heading">¶</a></h3>
<p>No, this is by design. Without a man-in-the-middle attack, there is no way for
an attacker to send a CSRF token cookie to a victim’s browser, so a successful
attack would need to obtain the victim’s browser’s cookie via XSS or similar,
in which case an attacker usually doesn’t need CSRF attacks.</p>
<p>Some security audit tools flag this as a problem but as mentioned before, an
attacker cannot steal a user’s browser’s CSRF cookie. “Stealing” or modifying
<em>your own</em> token using Firebug, Chrome dev tools, etc. isn’t a vulnerability.</p>
</section>
<section id="s-is-it-a-problem-that-django-s-csrf-protection-isn-t-linked-to-a-session-by-default">
<span id="is-it-a-problem-that-django-s-csrf-protection-isn-t-linked-to-a-session-by-default"></span><h3>Is it a problem that Django’s CSRF protection isn’t linked to a session by default?<a class="headerlink" href="#is-it-a-problem-that-django-s-csrf-protection-isn-t-linked-to-a-session-by-default" title="Link to this heading">¶</a></h3>
<p>No, this is by design. Not linking CSRF protection to a session allows using
the protection on sites such as a <em>pastebin</em> that allow submissions from
anonymous users which don’t have a session.</p>
<p>If you wish to store the CSRF token in the user’s session, use the
<a class="reference internal" href="settings.html#std-setting-CSRF_USE_SESSIONS"><code class="xref std std-setting docutils literal notranslate"><span class="pre">CSRF_USE_SESSIONS</span></code></a> setting.</p>
</section>
<section id="s-why-might-a-user-encounter-a-csrf-validation-failure-after-logging-in">
<span id="why-might-a-user-encounter-a-csrf-validation-failure-after-logging-in"></span><h3>Why might a user encounter a CSRF validation failure after logging in?<a class="headerlink" href="#why-might-a-user-encounter-a-csrf-validation-failure-after-logging-in" title="Link to this heading">¶</a></h3>
<p>For security reasons, CSRF tokens are rotated each time a user logs in. Any
page with a form generated before a login will have an old, invalid CSRF token
and need to be reloaded. This might happen if a user uses the back button after
a login or if they log in a different browser tab.</p>
</section>
</section>
</section>


          </div>
        </div>
      </div>
      
        
          <div class="yui-b" id="sidebar">
            
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Cross Site Request Forgery protection</a><ul>
<li><a class="reference internal" href="#how-it-works">How it works</a></li>
<li><a class="reference internal" href="#limitations">Limitations</a></li>
<li><a class="reference internal" href="#module-django.views.decorators.csrf">Utilities</a></li>
<li><a class="reference internal" href="#settings">Settings</a></li>
<li><a class="reference internal" href="#frequently-asked-questions">Frequently Asked Questions</a><ul>
<li><a class="reference internal" href="#is-posting-an-arbitrary-csrf-token-pair-cookie-and-post-data-a-vulnerability">Is posting an arbitrary CSRF token pair (cookie and POST data) a vulnerability?</a></li>
<li><a class="reference internal" href="#is-it-a-problem-that-django-s-csrf-protection-isn-t-linked-to-a-session-by-default">Is it a problem that Django’s CSRF protection isn’t linked to a session by default?</a></li>
<li><a class="reference internal" href="#why-might-a-user-encounter-a-csrf-validation-failure-after-logging-in">Why might a user encounter a CSRF validation failure after logging in?</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="contrib/syndication.html"
                          title="previous chapter">The syndication feed framework</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="databases.html"
                          title="next chapter">Databases</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../_sources/ref/csrf.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
              <h3>Last update:</h3>
              <p class="topless">Jul 02, 2025</p>
          </div>
        
      
    </div>

    <div id="ft">
      <div class="nav">
    &laquo; <a href="contrib/syndication.html" title="The syndication feed framework">previous</a>
     |
    <a href="index.html" title="API Reference" accesskey="U">up</a>
   |
    <a href="databases.html" title="Databases">next</a> &raquo;</div>
    </div>
  </div>

      <div class="clearer"></div>
    </div>
  </body>
</html>