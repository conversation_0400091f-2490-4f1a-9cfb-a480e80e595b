<!DOCTYPE html>

<html lang="en" data-content_root="../../../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>JavaScript customizations in the admin &#8212; Django 5.2 documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css?v=34bb78ad" />
    <link rel="stylesheet" type="text/css" href="../../../_static/default.css?v=bf4d74af" />
    <script src="../../../_static/documentation_options.js?v=6593ad68"></script>
    <script src="../../../_static/doctools.js?v=9bcbadda"></script>
    <script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="django.contrib.auth" href="../auth.html" />
    <link rel="prev" title="The Django admin documentation generator" href="admindocs.html" />



 
<script src="../../../templatebuiltins.js"></script>
<script>
(function($) {
    if (!django_template_builtins) {
       // templatebuiltins.js missing, do nothing.
       return;
    }
    $(document).ready(function() {
        // Hyperlink Django template tags and filters
        var base = "../../templates/builtins.html";
        if (base == "#") {
            // Special case for builtins.html itself
            base = "";
        }
        // Tags are keywords, class '.k'
        $("div.highlight\\-html\\+django span.k").each(function(i, elem) {
             var tagname = $(elem).text();
             if ($.inArray(tagname, django_template_builtins.ttags) != -1) {
                 var fragment = tagname.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + tagname + "</a>");
             }
        });
        // Filters are functions, class '.nf'
        $("div.highlight\\-html\\+django span.nf").each(function(i, elem) {
             var filtername = $(elem).text();
             if ($.inArray(filtername, django_template_builtins.tfilters) != -1) {
                 var fragment = filtername.replace(/_/, '-');
                 $(elem).html("<a href='" + base + "#" + fragment + "'>" + filtername + "</a>");
             }
        });
    });
})(jQuery);</script>

  </head><body>

    <div class="document">
  <div id="custom-doc" class="yui-t6">
    <div id="hd">
      <h1><a href="../../../index.html">Django 5.2 documentation</a></h1>
      <div id="global-nav">
        <a title="Home page" href="../../../index.html">Home</a>  |
        <a title="Table of contents" href="../../../contents.html">Table of contents</a>  |
        <a title="Global index" href="../../../genindex.html">Index</a>  |
        <a title="Module index" href="../../../py-modindex.html">Modules</a>
      </div>
      <div class="nav">
    &laquo; <a href="admindocs.html" title="The Django admin documentation generator">previous</a>
     |
    <a href="../../index.html" title="API Reference" accesskey="U">up</a>
   |
    <a href="../auth.html" title="&lt;code class=&#34;docutils literal notranslate&#34;&gt;&lt;span class=&#34;pre&#34;&gt;django.contrib.auth&lt;/span&gt;&lt;/code&gt;">next</a> &raquo;</div>
    </div>

    <div id="bd">
      <div id="yui-main">
        <div class="yui-b">
          <div class="yui-g" id="ref-contrib-admin-javascript">
            
  <section id="s-javascript-customizations-in-the-admin">
<span id="javascript-customizations-in-the-admin"></span><h1>JavaScript customizations in the admin<a class="headerlink" href="#javascript-customizations-in-the-admin" title="Link to this heading">¶</a></h1>
<section id="s-inline-form-events">
<span id="s-admin-javascript-inline-form-events"></span><span id="inline-form-events"></span><span id="admin-javascript-inline-form-events"></span><h2>Inline form events<a class="headerlink" href="#inline-form-events" title="Link to this heading">¶</a></h2>
<p>You may want to execute some JavaScript when an inline form is added or removed
in the admin change form. The <code class="docutils literal notranslate"><span class="pre">formset:added</span></code> and <code class="docutils literal notranslate"><span class="pre">formset:removed</span></code> events
allow this. <code class="docutils literal notranslate"><span class="pre">event.detail.formsetName</span></code> is the formset the row belongs to.
For the <code class="docutils literal notranslate"><span class="pre">formset:added</span></code> event, <code class="docutils literal notranslate"><span class="pre">event.target</span></code> is the newly added row.</p>
<p>In your custom <code class="docutils literal notranslate"><span class="pre">change_form.html</span></code> template, extend the
<code class="docutils literal notranslate"><span class="pre">admin_change_form_document_ready</span></code> block and add the event listener code:</p>
<div class="highlight-html+django notranslate"><div class="highlight"><pre><span></span><span class="cp">{%</span> <span class="k">extends</span> <span class="s1">&#39;admin/change_form.html&#39;</span> <span class="cp">%}</span>
<span class="cp">{%</span> <span class="k">load</span> <span class="nv">static</span> <span class="cp">%}</span>

<span class="cp">{%</span> <span class="k">block</span> <span class="nv">admin_change_form_document_ready</span> <span class="cp">%}</span>
<span class="cp">{{</span> <span class="nb">block</span><span class="nv">.super</span> <span class="cp">}}</span>
<span class="p">&lt;</span><span class="nt">script</span> <span class="na">src</span><span class="o">=</span><span class="s">&quot;</span><span class="cp">{%</span> <span class="k">static</span> <span class="s1">&#39;app/formset_handlers.js&#39;</span> <span class="cp">%}</span><span class="s">&quot;</span><span class="p">&gt;&lt;/</span><span class="nt">script</span><span class="p">&gt;</span>
<span class="cp">{%</span> <span class="k">endblock</span> <span class="cp">%}</span>
</pre></div>
</div>
<div class="literal-block-wrapper docutils container" id="id1">
<div class="code-block-caption"><span class="caption-text"><code class="docutils literal notranslate"><span class="pre">app/static/app/formset_handlers.js</span></code></span><a class="headerlink" href="#id1" title="Link to this code">¶</a></div>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="nb">document</span><span class="p">.</span><span class="nx">addEventListener</span><span class="p">(</span><span class="s1">&#39;formset:added&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">detail</span><span class="p">.</span><span class="nx">formsetName</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="s1">&#39;author_set&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Do something</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">});</span>
<span class="nb">document</span><span class="p">.</span><span class="nx">addEventListener</span><span class="p">(</span><span class="s1">&#39;formset:removed&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Row removed</span>
<span class="p">});</span>
</pre></div>
</div>
</div>
<p>Two points to keep in mind:</p>
<ul class="simple">
<li><p>The JavaScript code must go in a template block if you are inheriting
<code class="docutils literal notranslate"><span class="pre">admin/change_form.html</span></code> or it won’t be rendered in the final HTML.</p></li>
<li><p><code class="docutils literal notranslate"><span class="pre">{{</span> <span class="pre">block.super</span> <span class="pre">}}</span></code> is added because Django’s
<code class="docutils literal notranslate"><span class="pre">admin_change_form_document_ready</span></code> block contains JavaScript code to handle
various operations in the change form and we need that to be rendered too.</p></li>
</ul>
<section id="s-supporting-versions-of-django-older-than-4-1">
<span id="supporting-versions-of-django-older-than-4-1"></span><h3>Supporting versions of Django older than 4.1<a class="headerlink" href="#supporting-versions-of-django-older-than-4-1" title="Link to this heading">¶</a></h3>
<p>If your event listener still has to support older versions of Django you have
to use jQuery to register your event listener. jQuery handles JavaScript events
but the reverse isn’t true.</p>
<p>You could check for the presence of <code class="docutils literal notranslate"><span class="pre">event.detail.formsetName</span></code> and fall back
to the old listener signature as follows:</p>
<div class="highlight-javascript notranslate"><div class="highlight"><pre><span></span><span class="kd">function</span><span class="w"> </span><span class="nx">handleFormsetAdded</span><span class="p">(</span><span class="nx">row</span><span class="p">,</span><span class="w"> </span><span class="nx">formsetName</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Do something</span>
<span class="p">}</span>

<span class="nx">$</span><span class="p">(</span><span class="nb">document</span><span class="p">).</span><span class="nx">on</span><span class="p">(</span><span class="s1">&#39;formset:added&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">,</span><span class="w"> </span><span class="nx">$row</span><span class="p">,</span><span class="w"> </span><span class="nx">formsetName</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">detail</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">detail</span><span class="p">.</span><span class="nx">formsetName</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Django &gt;= 4.1</span>
<span class="w">        </span><span class="nx">handleFormsetAdded</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">target</span><span class="p">,</span><span class="w"> </span><span class="nx">event</span><span class="p">.</span><span class="nx">detail</span><span class="p">.</span><span class="nx">formsetName</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Django &lt; 4.1, use $row and formsetName</span>
<span class="w">        </span><span class="nx">handleFormsetAdded</span><span class="p">(</span><span class="nx">$row</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="mf">0</span><span class="p">),</span><span class="w"> </span><span class="nx">formsetName</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>
<span class="p">})</span>
</pre></div>
</div>
</section>
</section>
</section>


          </div>
        </div>
      </div>
      
        
          <div class="yui-b" id="sidebar">
            
      <div class="sphinxsidebar" role="navigation" aria-label="Main">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../contents.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">JavaScript customizations in the admin</a><ul>
<li><a class="reference internal" href="#inline-form-events">Inline form events</a><ul>
<li><a class="reference internal" href="#supporting-versions-of-django-older-than-4-1">Supporting versions of Django older than 4.1</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="admindocs.html"
                          title="previous chapter">The Django admin documentation generator</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../auth.html"
                          title="next chapter"><code class="docutils literal notranslate"><span class="pre">django.contrib.auth</span></code></a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/ref/contrib/admin/javascript.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<search id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</search>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
              <h3>Last update:</h3>
              <p class="topless">Jul 02, 2025</p>
          </div>
        
      
    </div>

    <div id="ft">
      <div class="nav">
    &laquo; <a href="admindocs.html" title="The Django admin documentation generator">previous</a>
     |
    <a href="../../index.html" title="API Reference" accesskey="U">up</a>
   |
    <a href="../auth.html" title="&lt;code class=&#34;docutils literal notranslate&#34;&gt;&lt;span class=&#34;pre&#34;&gt;django.contrib.auth&lt;/span&gt;&lt;/code&gt;">next</a> &raquo;</div>
    </div>
  </div>

      <div class="clearer"></div>
    </div>
  </body>
</html>