{"all_patterns": {" hx-indicator=": ["/home/<USER>/coding/clear_htmx/templates/infrastructure/htmx/utility_conflicts.html", "/home/<USER>/coding/clear_htmx/templates/shared/components/ui/project_assignment_modal.html", "/home/<USER>/coding/clear_htmx/templates/shared/auth/partials/password_reset_messages.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/mapping.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/main.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/popular_symbols.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dots.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/pulse.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/spinner.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/skeleton.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/user_operations.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/default.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/saved_searches.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/debug_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create_from_template.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/stats_cards.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/project_stats_inline.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/confirm_delete.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_partial.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/task_item.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/stakeholder_search_results.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/gis_layers_list.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/conflicts_summary.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/document_card.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/task_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_list.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_card.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/task_datagrid.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/deprecation/dashboard/components/overview.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/partials/notification_item.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/partials/role_form.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/cached_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/document_item.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/save_success.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_detail.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/version_history.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_list.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_item.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/progress_bar.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/main.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/search_autocomplete.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_item.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_list.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/demo.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_list.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_detail.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/statistics.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/scheduled_report_form.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/builder_interface.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_dashboard_content.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/shared/components/dashboard/stats_cards.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_table.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/users_table.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_grid.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/upload_form.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/shared/components/documents/file_preview.html"], "CLEAR:activity_delete": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_activities.html"], "CLEAR:activity_details": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_activities.html"], "CLEAR:add_time_entry": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/timesheet.html"], "CLEAR:admin": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html"], "CLEAR:admin-analytics": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html"], "CLEAR:admin-panel": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html"], "CLEAR:admin-users": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html"], "CLEAR:admin_database_management": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:admin_panel": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:admin_user_update": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_edit_form.html"], "CLEAR:analytics_dashboard": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:browser_compatibility": ["/home/<USER>/coding/clear_htmx/tests/browser_compatibility.html"], "CLEAR:collaboration_settings_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/collaboration_settings.html"], "CLEAR:comment_list_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/shared/components/documents/file_preview.html"], "CLEAR:contract_administration": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:conversation_archive": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_archive_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_create": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_create_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/search_results.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html"], "CLEAR:conversation_leave": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_leave_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_item.html"], "CLEAR:conversation_list_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_list.html"], "CLEAR:conversation_mark_read_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_item.html"], "CLEAR:conversation_member_add_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html"], "CLEAR:conversation_member_remove_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html"], "CLEAR:conversation_member_role_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html"], "CLEAR:conversation_members": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_members_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_item.html"], "CLEAR:conversation_messages_paginated": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread_with_pagination.html"], "CLEAR:conversation_mute_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_item.html"], "CLEAR:conversation_search": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_search_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:conversation_typing_status": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread_with_pagination.html"], "CLEAR:current_time": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:dashboard": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/403.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_success.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/test_websocket.html"], "CLEAR:dashboard_stats_cards": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/shared/components/dashboard/stats_cards.html"], "CLEAR:delete_time_entry": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/timesheet.html"], "CLEAR:deprecation_analytics": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/admin/deprecation/partials/dashboard_content.html"], "CLEAR:deprecation_detail": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/admin/deprecation/partials/deprecation_list.html"], "CLEAR:deprecation_list": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/admin/deprecation/partials/dashboard_content.html"], "CLEAR:document_activity_feed_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html"], "CLEAR:document_create_folder_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents.html"], "CLEAR:document_discussion_create_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html"], "CLEAR:document_discussion_list_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html"], "CLEAR:document_preview_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html"], "CLEAR:document_upload_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents.html"], "CLEAR:document_version_upload_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html"], "CLEAR:documents": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents.html"], "CLEAR:executive_analytics_export": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/executive_dashboard.html"], "CLEAR:executive_analytics_period": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/executive_dashboard.html"], "CLEAR:export_timesheet_summary_csv": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html"], "CLEAR:export_timesheet_summary_json": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html"], "CLEAR:health_check": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:help": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:help_analytics": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html"], "CLEAR:help_contextual_help": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html"], "CLEAR:help_guided_tours": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html"], "CLEAR:help_reset_help_system": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html"], "CLEAR:htmx": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:htmx:quick_task_create": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:htmx_spatial_statistics": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/statistics.html"], "CLEAR:login": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/login.html"], "CLEAR:logout": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/403.html"], "CLEAR:mapping": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:mark_all_notifications_read": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/notifications/list.html"], "CLEAR:message_create_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread_with_pagination.html"], "CLEAR:message_delete_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/thread_reply.html"], "CLEAR:message_mark_read_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread.html"], "CLEAR:message_reaction_add_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/reaction_picker.html"], "CLEAR:message_send": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:message_send_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:message_thread_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread.html"], "CLEAR:message_thread_preview": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_list_partial.html"], "CLEAR:message_thread_view_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/thread_created.html"], "CLEAR:messages": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_item.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/search_results.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html"], "CLEAR:messages_list_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html"], "CLEAR:messaging": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:migration_roadmap": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/admin/deprecation/partials/dashboard_content.html"], "CLEAR:my_tasks_widget": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:notification_mark_read_toggle_htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/notifications/list.html"], "CLEAR:notification_settings": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/notifications/list.html"], "CLEAR:organization_settings": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:performance": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:profile": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:profile_activities": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_activities.html"], "CLEAR:profile_activities_refresh": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_activities.html"], "CLEAR:profile_activity_htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/profile/activity_list.html"], "CLEAR:profile_projects": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html"], "CLEAR:profile_projects_refresh": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html"], "CLEAR:project_create": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:project_detail": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_activities.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/profiles/partials/profile_activities.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/shared/components/documents/file_preview.html"], "CLEAR:project_team_member_action_htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html"], "CLEAR:projects": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:quick_entry_modal": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html"], "CLEAR:recent_activity_widget": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:refresh_dashboard": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:send_team_message": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/dashboard/team_chat.html"], "CLEAR:set_typing_status": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread_with_pagination.html"], "CLEAR:settings": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:stop_timer_htmx": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html"], "CLEAR:tasks": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html"], "CLEAR:team_chat_partial": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/cached_dashboard.html"], "CLEAR:test_login": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_form.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/test_websocket.html", "/home/<USER>/coding/clear_htmx/tests/browser_compatibility.html"], "CLEAR:test_websocket": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/test_websocket.html", "/home/<USER>/coding/clear_htmx/tests/browser_compatibility.html"], "CLEAR:timer_status_htmx": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:timesheet": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html"], "CLEAR:timesheet_summary_partial": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html"], "CLEAR:toggle_task": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html"], "CLEAR:user_list_for_whisper": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_list.html"], "CLEAR:user_management": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:version_management": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html"], "CLEAR:warm_dashboard_cache": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/cached_dashboard.html"], "CLEAR:whisper_list": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_conversation.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_created.html"], "CLEAR:whisper_mark_read": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_conversation.html"], "CLEAR:whisper_send": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_conversation.html"], "account_login": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/403.html"], "account_signup": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/403.html"], "activity:activity_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/dashboard_stats.html"], "activity:feed": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html"], "activity:status_indicator": ["/home/<USER>/coding/clear_htmx/apps/activity/templates/activity/partials/status_indicator.html"], "admin:3d_assets:asset_edit": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html"], "admin:3d_assets:library_create": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html"], "admin:3d_assets:library_detail": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html"], "admin:3d_assets:library_edit": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html"], "admin:3d_assets:library_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html"], "admin:CLEAR_conflictrule_change": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html"], "admin:CLEAR_conflictrule_changelist": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html"], "admin:CLEAR_project_changelist": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/metrics.html"], "admin:CLEAR_task_changelist": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/metrics.html"], "admin:CLEAR_utility_changelist": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/metrics.html"], "admin:app_list": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/admin/infrastructure/utilitysymbol/import_library.html"], "admin:conflict_deploy_rule": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html"], "admin:conflict_execute_test": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html"], "admin:conflict_load_scenario": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html"], "admin:conflict_run_scenario": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html"], "admin:htmx_performance": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_content.html"], "admin:index": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/admin/infrastructure/utilitysymbol/import_library.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html"], "admin:infrastructure_utilitysymbol_changelist": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/admin/infrastructure/utilitysymbol/import_library.html"], "admin:performance_alerts": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_content.html"], "admin:performance_reports": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_content.html"], "analytics": ["/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html"], "analytics:action": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/modal.html"], "analytics:activity_charts": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profiles/partials/profile_activities.html"], "analytics:activity_dashboard": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profiles/partials/profile_activities.html"], "analytics:activity_log": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "analytics:activity_reports": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profiles/partials/profile_activities.html"], "analytics:admin_reports": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "analytics:comment_count": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/comment_count.html"], "analytics:completion_analysis": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "analytics:completion_trends": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "analytics:dashboard": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/profile/stats_cards.html"], "analytics:dashboard_stats": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html"], "analytics:delivery_details": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html"], "analytics:development_timeline": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "analytics:executive_kpi_refresh": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html"], "analytics:executive_reports": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "analytics:export_delivery": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html"], "analytics:export_executive_kpis": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html"], "analytics:export_metrics": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/metrics_cards.html"], "analytics:export_revenue": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html"], "analytics:filtered_data": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/filters.html"], "analytics:htmx_chart_data": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_dashboard_content.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:htmx_dashboard_stats": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:htmx_metrics_cards": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:htmx_project_analytics": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:htmx_user_analytics": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:insights": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html"], "analytics:kpi_export": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/components/reports/kpi_cards.html"], "analytics:kpi_refresh": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/components/reports/kpi_cards.html"], "analytics:metrics_refresh": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/metrics_cards.html"], "analytics:modal_data": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/modal.html"], "analytics:pending_report": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "analytics:popular_symbols": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/popular_symbols.html"], "analytics:report_builder_create": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/report_builder/dashboard.html"], "analytics:report_builder_detail": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/report_builder/dashboard.html"], "analytics:report_builder_edit": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/report_builder/dashboard.html"], "analytics:report_create": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:reports": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "analytics:revenue_details": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/components/reports/kpi_cards.html"], "analytics:stats_header": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "analytics:track_error": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html"], "analytics:track_error_display": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/error_message.html"], "analytics:track_server_error": ["/home/<USER>/coding/clear_htmx/templates/errors/500.html"], "analytics:track_user_error": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "api:task": ["/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html", "/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_list.html"], "api:task-assign-form": ["/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html"], "api:task-duplicate": ["/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html"], "api:task-list-htmx": ["/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_list.html"], "api:task-toggle-complete": ["/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html"], "authentication:active_sessions": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:activity_detail": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_detail_modal.html"], "authentication:activity_log": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html"], "authentication:activity_stats": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_stats.html"], "authentication:activity_table": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_table.html"], "authentication:admin_dashboard": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_stats_cards.html"], "authentication:admin_panel": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/panel.html"], "authentication:admin_stats": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/admin_stats.html"], "authentication:analytics_content": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/analytics_content.html"], "authentication:api_tokens_list": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:avatar_upload": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:cpu_metric_card": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/cpu_metric_card.html"], "authentication:database_metric_card": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/database_metric_card.html"], "authentication:database_metrics_content": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/database_metrics_content.html"], "authentication:delete_account": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html"], "authentication:edit_profile": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:export_data": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html"], "authentication:login": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/templates/home.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_confirm.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_form.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_complete.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/signup.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_done.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/login.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/login_form.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html"], "authentication:logout": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/templates/users/auth/mfa_login.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/navigation/user_dropdown.html"], "authentication:mfa_backup": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html"], "authentication:mfa_backup_tokens": ["/home/<USER>/coding/clear_htmx/templates/users/auth/mfa_login.html"], "authentication:mfa_disable": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:mfa_login": ["/home/<USER>/coding/clear_htmx/templates/users/auth/mfa_login.html"], "authentication:mfa_setup": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:mfa_verify": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html"], "authentication:notification_preferences": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html"], "authentication:password_change": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:password_reset": ["/home/<USER>/coding/clear_htmx/templates/shared/auth/partials/password_reset_messages.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_confirm.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/login.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_done.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/login.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/login_form.html"], "authentication:privacy_settings": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html"], "authentication:profile": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/navigation/user_dropdown.html"], "authentication:profile_completion": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:profile_edit": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_profile.html"], "authentication:profile_edit_modal": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html"], "authentication:profile_header": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html"], "authentication:profile_info_partial": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:profile_overview": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:profile_picture_upload": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html"], "authentication:profile_settings_partial": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html"], "authentication:profile_share": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:profile_stats": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html"], "authentication:recent_activity": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html"], "authentication:register": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_form.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "authentication:role_create": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/partials/role_form.html"], "authentication:role_management_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "authentication:role_update": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/partials/role_form.html"], "authentication:security_settings": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "authentication:settings": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/navigation/user_dropdown.html"], "authentication:signup": ["/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/signup.html", "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/login.html"], "authentication:stakeholder_management_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "authentication:system_alerts": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/system_alerts_content.html"], "authentication:system_health": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/system_health.html"], "authentication:system_metrics": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/memory_metric_card.html"], "authentication:system_performance": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/performance_metrics_content.html"], "authentication:update_account": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:update_notifications": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:update_preferences": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:update_theme": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html"], "authentication:user_admin": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/delete_user_confirmation_content.html"], "authentication:user_create": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html"], "authentication:user_dashboard": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/users_metric_card.html"], "authentication:user_detail": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_detail_content.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html"], "authentication:user_edit": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_edit_form.html"], "authentication:user_list": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/users_table.html"], "authentication:user_management": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html"], "authentication:user_profile": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_profile.html"], "chat-messages-htmx": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/chat/message_list.html"], "comments:reply_form": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/comment_replies.html"], "common:execute": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_list.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/base.html"], "common:execute-query": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_list.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/base.html"], "common:query": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_list.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/base.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_form.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_confirm_delete.html"], "common:query-create": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/base.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_form.html"], "common:query-delete": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_list.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_confirm_delete.html"], "common:query-list": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/base.html"], "common:query-update": ["/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_list.html", "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_form.html"], "common:ui_components": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/enhanced_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/feature_availability.html"], "compliance:audit": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_detail.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/partials/audit_list_items.html"], "compliance:audit-detail": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/partials/audit_list_items.html"], "compliance:audit-filter-htmx": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html"], "compliance:audit-list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_detail.html"], "compliance:audit_export": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html"], "compliance:audit_list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_detail.html"], "compliance:check": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_detail.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_detail.html"], "compliance:check-list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_detail.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_detail.html"], "compliance:check-status-htmx": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html"], "compliance:check_list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_results.html"], "compliance:check_results": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html"], "compliance:dashboard": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html"], "compliance:gdpr": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/privacy_settings.html"], "compliance:gdpr:consent": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html"], "compliance:gdpr:dashboard": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/privacy_settings.html"], "compliance:gdpr:data_deletion": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html"], "compliance:gdpr:data_export": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html"], "compliance:gdpr:data_request": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html"], "compliance:gdpr:privacy_settings": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html"], "compliance:gdpr:processing_logs": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html"], "compliance:regulation": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html"], "compliance:regulation-list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html"], "compliance:regulation_detail": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html"], "compliance:regulation_list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_detail.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html"], "compliance:report": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_form.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html"], "compliance:report-create": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_form.html"], "compliance:report-list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html"], "compliance:report_create": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html"], "compliance:report_detail": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html"], "compliance:report_list": ["/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_detail.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_form.html", "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html"], "conflict-detection-status-htmx": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/detection_status.html"], "conflict_detail": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_item.html"], "core:add_navigation_item": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/quick_navigation_widget.html"], "core:cached_fragment_refresh": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/cached_fragment.html"], "core:cached_recent_work": ["/home/<USER>/coding/clear_htmx/templates/errors/500.html"], "core:coordinator_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "core:dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/templates/shared/403.html", "/home/<USER>/coding/clear_htmx/templates/errors/500.html", "/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/400.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/500.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/404.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/403.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/error/500.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/400.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/500.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/403.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/stats.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html", "/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html", "/home/<USER>/coding/clear_htmx/tests/browser_compatibility.html"], "core:dashboard_stats": ["/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html"], "core:department_manager_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "core:executive_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "core:health_check": ["/home/<USER>/coding/clear_htmx/templates/errors/500.html"], "core:home": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/templates/errors/400.html", "/home/<USER>/coding/clear_htmx/templates/errors/403.html", "/home/<USER>/coding/clear_htmx/templates/shared/error/500.html"], "core:navigation_customizer": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/quick_navigation_widget.html"], "core:notification_mark_read": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/notification_item.html"], "core:notification_snooze": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/notification_item.html"], "core:recent_activity_partial": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html"], "core:report_broken_link": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html"], "core:request_notification": ["/home/<USER>/coding/clear_htmx/templates/errors/500.html"], "core:sitemap": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html"], "core:stakeholder_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "core:system_status_partial": ["/home/<USER>/coding/clear_htmx/templates/errors/500.html"], "core:warm_cache_fragment": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/cached_fragment.html"], "create_symbol_mapping": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/model_library_list.html"], "d_assets:asset_edit": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html"], "d_assets:library_create": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html"], "d_assets:library_detail": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html"], "d_assets:library_edit": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html"], "d_assets:library_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html"], "deprecation:api_data": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/main.html"], "document_download": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/document_card.html"], "documents": ["/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html"], "documents:create_folder": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/partials/upload_form.html"], "documents:create_folder_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html"], "documents:delete_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html"], "documents:detail": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/search_results.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/partials/search.html"], "documents:edit": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/partials/search.html"], "documents:list": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/upload_form.html"], "documents:preview_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html"], "documents:upload": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/upload_form.html"], "documents:upload_htmx": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/partials/upload_form.html"], "documents:upload_interface": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/upload_form.html"], "documents:workspace": ["/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents_workspace.html", "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html"], "financial:add_time_entry": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/week_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html"], "financial:alertDismissed": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/layout_saved.html"], "financial:approve_time_entry": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:auto_save_time_entry": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html"], "financial:auto_save_timesheet": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html"], "financial:bulk_approve_entries": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html"], "financial:bulk_export_entries": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html"], "financial:bulk_reject_entries": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html"], "financial:create_budget": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html"], "financial:create_invoice": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:create_invoice_from_entry": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:daily_chart_data": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html"], "financial:dashboard": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html"], "financial:duplicate_time_entry": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_status.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:edit_invoice": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:edit_time_entry": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:edit_timesheet": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html"], "financial:export_invoices_csv": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:export_invoices_json": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:export_invoices_pdf": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:export_project_financial_csv": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html"], "financial:export_reports_csv": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:export_reports_json": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:export_reports_pdf": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:export_timesheet": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html"], "financial:export_timesheet_csv": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html"], "financial:export_timesheet_entries_csv": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html"], "financial:export_timesheet_summary_csv": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html"], "financial:financial_admin_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "financial:financial_statistics_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html"], "financial:get_active_timer_data": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html"], "financial:get_last_entry_data": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html"], "financial:invoice_detail": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:invoice_item_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:invoice_list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html"], "financial:invoice_pdf": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:invoice_summary_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:invoice_templates": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:invoices": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:layoutReset": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/layout_saved.html"], "financial:layoutSaved": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/layout_saved.html"], "financial:mark_invoice_paid": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:pause_timer": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html"], "financial:pause_timer_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_status.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/active_timer.html"], "financial:project_distribution_chart_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:project_financial_dashboard_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html"], "financial:project_financial_report": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html"], "financial:project_transactions": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html"], "financial:quick_time_entry_form": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:quick_time_entry_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html"], "financial:refreshComponents": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/layout_saved.html"], "financial:refresh_timesheet_summary": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html"], "financial:reports": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html"], "financial:reports_data_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:reset_dashboard_layout": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/layout_saved.html"], "financial:resume_timer": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html"], "financial:resume_timer_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_status.html"], "financial:revenue": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "financial:revenue-dashboard": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "financial:revenue_chart_data_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html"], "financial:save_time_entry_draft": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html"], "financial:save_timesheet_draft": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html"], "financial:send_invoice": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html"], "financial:start_timer": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/week_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html"], "financial:start_timer_form": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_status.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/active_timer.html"], "financial:stop_timer": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html"], "financial:stop_timer_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_status.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/active_timer.html"], "financial:submit_timesheet": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html"], "financial:time_entries": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/week_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/active_timer.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:time_entries_filtered_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html"], "financial:time_entries_stats_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html"], "financial:time_entry_detail": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:time_tracking": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html"], "financial:timer_display_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html"], "financial:timer_status_htmx": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html"], "financial:timesheet": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html"], "financial:timesheet-monthly": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "financial:timesheet_detail": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html"], "financial:timesheet_summary": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html"], "financial:timesheet_summary_partial": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html"], "financial:timesheet_week_summary": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html"], "financial:timesheets": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html"], "financial:week_summary_partial": ["/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/week_summary.html"], "gis_layer_download": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/gis_layers_list.html"], "help:index": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html"], "help:support": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/htmx_error.html"], "htmx:project_create": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_list.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_form.html"], "htmx:project_update": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_form.html"], "htmx:visual_query_builder:export_definition": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/save_success.html"], "htmx:visual_query_builder:get_fields": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/builder_interface.html"], "htmx:visual_query_builder:toggle_favorite": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/save_success.html"], "infrastructure:active_faults": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:api_conflicts_geojson": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:api_utilities_geojson": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:asset_connectivity": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:asset_library": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:collaboration_sessions": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:conflict_detection": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:conflict_list": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html"], "infrastructure:conflicts_list": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:create_custom_symbol": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html"], "infrastructure:dashboard": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:database_diagnostics": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:dependency_map": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:export_data": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:export_network_data": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:export_proximity_results": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/analysis/proximity.html"], "infrastructure:fault_detection": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:flow_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:gis_professional": ["/home/<USER>/coding/clear_htmx/templates/base.html"], "infrastructure:health_alerts": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:health_component": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:health_metrics": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:health_overview": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:help": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html"], "infrastructure:htmx_filter_by_project": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:htmx_filter_utilities": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:import_builtin_library_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/import_library.html"], "infrastructure:import_data": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html"], "infrastructure:import_library": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html"], "infrastructure:import_symbol_library": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/popular_symbols.html"], "infrastructure:index": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:layer_detail": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/search_results.html"], "infrastructure:map": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "infrastructure:map_filter_data": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/mapping.html"], "infrastructure:network_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:network_modeling": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:network_settings": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:network_stats_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:network_topology": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:optimization": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:performance_metrics": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:performance_tests": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:place_symbol_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/place_symbol_form.html"], "infrastructure:popular_symbols_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/popular_symbols.html"], "infrastructure:pressure_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:project_3d": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:project_map": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:realtime_conflict_stream": ["/home/<USER>/coding/clear_htmx/templates/infrastructure/realtime_monitoring.html"], "infrastructure:realtime_spatial_stream": ["/home/<USER>/coding/clear_htmx/templates/infrastructure/realtime_monitoring.html"], "infrastructure:realtime_status_stream": ["/home/<USER>/coding/clear_htmx/templates/infrastructure/realtime_monitoring.html"], "infrastructure:refresh_network_cache": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:run_network_diagnostics": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:security_audit": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html"], "infrastructure:simulation": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html"], "infrastructure:spatial_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:spatial_buffer_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_collaboration": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:spatial_data_export": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_export": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/mapping.html"], "infrastructure:spatial_feature_popup_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_geojson_features": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_geojson_import": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_intersection_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_layer_toggle_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_location_create": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html"], "infrastructure:spatial_location_delete": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html"], "infrastructure:spatial_location_detail": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html"], "infrastructure:spatial_location_list": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/create.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html"], "infrastructure:spatial_location_update": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html"], "infrastructure:spatial_map": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/analysis/proximity.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/import/shapefile.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/create.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html"], "infrastructure:spatial_map_fullscreen": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:spatial_proximity": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:spatial_proximity_analysis": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/analysis/proximity.html"], "infrastructure:spatial_shapefile_import": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html"], "infrastructure:symbol_admin": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/filters.html"], "infrastructure:symbol_categories": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/main.html"], "infrastructure:symbol_category_symbols": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/categories_tree.html"], "infrastructure:symbol_docs": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html"], "infrastructure:symbol_library": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:symbol_list": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/main.html"], "infrastructure:symbol_palette": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html", "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/popular_symbols.html"], "infrastructure:symbol_quick_add_htmx": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/quick_add_form.html"], "infrastructure:system_status": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html"], "infrastructure:utility": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "infrastructure:utility-list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "infrastructure:utility_conflicts_htmx": ["/home/<USER>/coding/clear_htmx/templates/infrastructure/htmx/utility_conflicts.html"], "infrastructure:utility_create": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html"], "infrastructure:utility_detail": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html"], "infrastructure:utility_list": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html"], "knowledge:active_sessions_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:activity_feed": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:activity_feed_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:advanced_search": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_suggestions.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:analytics": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html"], "knowledge:analytics_dashboard": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:analytics_guide": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html"], "knowledge:api_docs": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html"], "knowledge:article_bookmark": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html"], "knowledge:article_comments_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html"], "knowledge:article_create": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/documentation.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_graph.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_suggestions.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/recent_articles.html"], "knowledge:article_detail": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/category_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_suggestions.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_created.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_updated.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/search_results.html"], "knowledge:article_edit": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_created.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_updated.html"], "knowledge:article_feedback": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_voting.html"], "knowledge:article_list": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/category_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/recent_articles.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/categories_list.html"], "knowledge:article_preview": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html"], "knowledge:article_vote": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html"], "knowledge:article_vote_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html"], "knowledge:cached_articles": ["/home/<USER>/coding/clear_htmx/templates/errors/500.html"], "knowledge:category_create": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:category_detail": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html"], "knowledge:category_list": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/category_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/recent_articles.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_search_results.html"], "knowledge:category_management": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html"], "knowledge:check_notifications": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:check_search_updates": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html"], "knowledge:clear_all_filters": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html"], "knowledge:clear_search": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html"], "knowledge:collaboration_dashboard": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:collaboration_index": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:collaboration_updates_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:create_session_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:dashboard": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html"], "knowledge:dashboard_activity_feed": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html"], "knowledge:dashboard_chart_data": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html"], "knowledge:dashboard_stats": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html"], "knowledge:documentation": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/categories_list.html"], "knowledge:export_dashboard": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:export_search_results": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html"], "knowledge:faq": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html"], "knowledge:feedback": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html"], "knowledge:help_shortcuts": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html"], "knowledge:knowledge_base": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:knowledge_graph": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:list": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html"], "knowledge:load_search": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html"], "knowledge:management_activity_feed": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:management_dashboard": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html"], "knowledge:my_articles": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:my_saved_searches": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html"], "knowledge:note_bookmark": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:note_create": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:note_delete": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:note_detail": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:note_edit": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:note_share": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:notebook_refresh": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:notebook_search": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html"], "knowledge:outdated_articles": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html"], "knowledge:permissions_management": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "knowledge:recent_activity_partial": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:recent_articles": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/recent_articles.html"], "knowledge:remove_search_filter": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html"], "knowledge:reports": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:save_search": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html"], "knowledge:saved_searches": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:search": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "knowledge:search_analytics": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html"], "knowledge:search_articles": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_search_results.html"], "knowledge:search_results": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html"], "knowledge:search_suggestions": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html"], "knowledge:session_participants_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:share_knowledge": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:start_collaboration_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:submit_feedback": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html"], "knowledge:team_dashboard": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html"], "knowledge:teams": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:topic_detail": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html"], "knowledge:topics": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html"], "knowledge:track_article_view_htmx": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html"], "knowledge:track_search_click": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html", "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html"], "knowledge:track_suggestion_click": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_suggestions.html"], "knowledge:workflow_settings": ["/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html"], "logout": ["/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html"], "messaging:channel_join": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/team_channels_list.html"], "messaging:collaboration_dashboard": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/collaboration_status.html"], "messaging:compose": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_search_results.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_list.html"], "messaging:contacts": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_list.html"], "messaging:conversation_detail": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/conversation_list.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages_list.html"], "messaging:conversation_leave": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html"], "messaging:conversation_pin": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html"], "messaging:create_conversation": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/conversation_list.html", "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages_list.html"], "messaging:create_error_report": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/error_message.html"], "messaging:create_message": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "messaging:create_support_ticket": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "messaging:delete_whisper": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_item.html"], "messaging:htmx_react_message": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/partials/message_list.html"], "messaging:make_admin": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html"], "messaging:meetings": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_meetings.html"], "messaging:meetings_create": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_meetings.html"], "messaging:remove_member": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html"], "messaging:search": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_search_results.html"], "messaging:send_message": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages_list.html"], "messaging:toggle_notifications": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html"], "messaging:toggle_reaction": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_item.html"], "messaging:toggle_whisper_pin": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_item.html"], "messaging:user_status": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/user_status_indicator.html"], "messaging:whisper_count_update": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/whisper_count.html"], "messaging:whisper_reply_form": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_item.html"], "model_detail": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/model_library_list.html"], "notes:journal_apply_template": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html"], "notes:journal_calendar": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/stats.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html"], "notes:journal_dashboard": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/stats.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/notebook.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/navigation/user_dropdown.html"], "notes:journal_entry_create": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/calendar.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/stats.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/calendar.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/entry_list.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/notebook.html"], "notes:journal_entry_delete": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/entry_list.html"], "notes:journal_entry_detail": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/calendar.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/entry_list.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html"], "notes:journal_entry_list": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/notebook.html"], "notes:journal_export": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/stats.html", "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html"], "notes:journal_prompts": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html"], "notes:journal_stats": ["/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html"], "notes:notebook_partial": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html"], "notifications:list": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html"], "procedural_template_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/model_library_list.html"], "profile": ["/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html"], "profile_view": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html"], "project_detail": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/activity_feed.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_card.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_detail.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_item.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_item.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_detail.html"], "projects": ["/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html"], "projects:activity_feed": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:activity_log": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:add_field_condition": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/model_fields.html"], "projects:add_utility": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html"], "projects:analytics": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:analytics-dashboard": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "projects:analytics_chart_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:analytics_dashboard": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html"], "projects:analytics_dashboard_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:analytics_export": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:analytics_projects_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:analytics_reports_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:analytics_team_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:analytics_timeline_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html"], "projects:assignment_detail": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/assignment_result.html"], "projects:calendar_view_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html"], "projects:chat_export": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html"], "projects:chat_messages_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html"], "projects:chat_send_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html"], "projects:chat_typing_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html"], "projects:chat_users_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html"], "projects:clients": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html"], "projects:close_all_dropdowns": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html"], "projects:comment_add_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html"], "projects:comment_delete_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html"], "projects:comment_like_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html"], "projects:comment_reply_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html"], "projects:comments_list_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html"], "projects:completed_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:completed_recent": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:conflict": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "projects:conflict-list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "projects:conflict_add_note": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conflict_detail": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conflict_detection": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html"], "projects:conflict_detection_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/map.html"], "projects:conflict_escalate": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conflict_export": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conflict_report_pdf": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conflict_resolve_api": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conflict_settings": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:conversation_create_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:conversation_events_sse": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:conversation_leave_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:conversation_mark_read_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:conversation_mute_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:conversation_pin_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:conversation_settings_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html"], "projects:create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_empty.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html"], "projects:create_from_template": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create_from_template.html"], "projects:create_request": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:critical_path_analysis": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html"], "projects:dashboard": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/update_entry.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html"], "projects:dashboard_metrics": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:dashboard_refresh": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "projects:debug_activity": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/debug_dashboard.html"], "projects:debug_htmx_test": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/debug_dashboard.html"], "projects:debug_queries": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/debug_dashboard.html"], "projects:detail": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/map.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/update_entry.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/search_results.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html", "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html"], "projects:development_active": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:development_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:development_reports": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:edit": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html"], "projects:export": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html"], "projects:financial_summary": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:gantt_chart_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html"], "projects:gantt_enhanced_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html"], "projects:get_project_tasks": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html"], "projects:htmx": ["/home/<USER>/coding/clear_htmx/templates/shared/components/ui/project_assignment_modal.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/saved_searches.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_bar.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/quick_entry_modal.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html"], "projects:htmx-stats-cards": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards.html"], "projects:htmx:advanced_search": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_bar.html"], "projects:htmx:available_projects": ["/home/<USER>/coding/clear_htmx/templates/shared/components/ui/project_assignment_modal.html"], "projects:htmx:global_search": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_bar.html"], "projects:htmx:project_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html"], "projects:htmx:project_tasks": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/quick_entry_modal.html"], "projects:htmx:project_update": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html"], "projects:htmx:quick_time_entry": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/quick_entry_modal.html"], "projects:htmx_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create.html"], "projects:htmx_create_from_template": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create_from_template.html"], "projects:htmx_create_project_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html"], "projects:htmx_delete_task": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html"], "projects:htmx_my_projects_list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html"], "projects:htmx_task_edit_field": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html"], "projects:htmx_task_form": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html"], "projects:htmx_tasks_filter": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html"], "projects:htmx_tasks_list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html"], "projects:htmx_update_task_status": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html"], "projects:import": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html"], "projects:kanban_board": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:list": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_empty.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create_from_template.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/update_entry.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/profile/stats_cards.html"], "projects:map": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects.html"], "projects:map_geojson_data": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html"], "projects:map_layers": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html"], "projects:map_layers_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html"], "projects:map_utility_info_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html"], "projects:my_projects": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_empty.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects.html"], "projects:my_projects_data": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects_list.html"], "projects:my_projects_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "projects:my_projects_refresh": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects_list.html"], "projects:my_tasks": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html"], "projects:note_create_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:notebook_entry_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html"], "projects:notebook_entry_delete": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/confirm_delete.html"], "projects:pending_list": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:pending_priority": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:pending_review": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:person_assign_project_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/mobile_stakeholder_projects.html"], "projects:person_assign_project_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html"], "projects:person_contact_info_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html"], "projects:person_contact_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html"], "projects:person_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html"], "projects:person_detail": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html"], "projects:person_detail_refresh": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html"], "projects:person_edit": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html"], "projects:person_export": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html"], "projects:person_list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html"], "projects:person_list_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html"], "projects:person_projects_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html"], "projects:person_quick_add": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html"], "projects:person_remove_assignment_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/mobile_stakeholder_projects.html"], "projects:person_search": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html"], "projects:portfolio_filter": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html"], "projects:portfolio_stats": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html"], "projects:project": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html", "/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html", "/home/<USER>/coding/clear_htmx/apps/api/templates/api/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/stakeholder_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/coordinator_dashboard.html"], "projects:project-create": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html"], "projects:project-detail": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/stakeholder_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/coordinator_dashboard.html"], "projects:project-list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html"], "projects:project-map": ["/home/<USER>/coding/clear_htmx/apps/api/templates/api/project_detail.html"], "projects:project_3d": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/map.html"], "projects:project_3d_data_api": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html"], "projects:project_activity_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html"], "projects:project_activity_logs": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/activity_logs_partial.html"], "projects:project_add_member_form_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_add_time_entry_form_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_comments_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:project_communication_modal_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html"], "projects:project_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html"], "projects:project_delete": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html"], "projects:project_delete_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_detail": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html"], "projects:project_documents_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_documents_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:project_duplicate": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_edit": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html"], "projects:project_export": ["/home/<USER>/coding/clear_htmx/templates/components/projects/project_card.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_list": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/stats_cards.html", "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_dashboard_content.html"], "projects:project_map": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html"], "projects:project_map_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:project_map_view_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_notebook": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/update_entry.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html"], "projects:project_notebook_activity_logs": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_partial.html"], "projects:project_notebook_entry_delete": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_partial.html"], "projects:project_notebook_entry_form": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/entry_form.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_partial.html"], "projects:project_notebook_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_partial.html"], "projects:project_overview_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:project_recent_tasks_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_reports": ["/home/<USER>/coding/clear_htmx/templates/components/projects/project_card.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_stats_inline": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/project_stats_inline.html"], "projects:project_stats_inline_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html"], "projects:project_status_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html"], "projects:project_status_update_modal_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html"], "projects:project_tasks": ["/home/<USER>/coding/clear_htmx/templates/components/projects/project_card.html"], "projects:project_team_members_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_team_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:project_timeline": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html"], "projects:project_timeline_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_timeline_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:project_upload_document_form_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html"], "projects:project_utilities_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html"], "projects:projects_filter_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html"], "projects:recent_activity_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "projects:recent_projects_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html"], "projects:recent_tasks": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:reporting_update": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html"], "projects:reports": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "projects:resource_allocation": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html"], "projects:run_conflict_detection": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html"], "projects:saved_searches": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/saved_searches.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_bar.html"], "projects:search": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html"], "projects:settings": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html"], "projects:settings_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html"], "projects:share_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html"], "projects:start_timer": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html"], "projects:stats_cards_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/stats_cards.html"], "projects:success_stories": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html"], "projects:task": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/coordinator_dashboard.html"], "projects:task-list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/coordinator_dashboard.html"], "projects:task-update": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html"], "projects:task_activity_feed": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html"], "projects:task_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html"], "projects:task_create_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:task_detail": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/search_results.html"], "projects:task_form": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html"], "projects:task_list": ["/home/<USER>/coding/clear_htmx/templates/errors/404.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html"], "projects:task_stats_partial": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html"], "projects:tasks": ["/home/<USER>/coding/clear_htmx/templates/base.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html"], "projects:tasks:create": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html"], "projects:tasks_clear_filters": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html"], "projects:tasks_filter": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html"], "projects:tasks_today": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html"], "projects:team": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html"], "projects:team_list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/stats_cards.html"], "projects:team_management": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html"], "projects:team_modal": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:team_overview": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:templates": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html"], "projects:timeline": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects.html"], "projects:timeline_chart": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:timeline_refresh": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html"], "projects:timer_status": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html"], "projects:timesheet": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html"], "projects:timesheet_entry_create": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html"], "projects:toggle_project_favorite": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html"], "projects:update": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html"], "projects:update_field": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html"], "projects:utilities_filter": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html"], "projects:utilities_list": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html"], "projects:utilities_map": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html"], "projects:utility_conflicts": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html"], "projects:utility_conflicts_htmx": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html"], "projects:utility_info": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html"], "projects:workflow_form": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html"], "realtime:dashboard": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/websocket_test.html", "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/webrtc_room.html"], "realtime:metrics": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html"], "realtime:notification_list": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html"], "realtime:notification_list_content": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/notification_list.html"], "realtime:recent_notifications": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html"], "realtime:spatial_collaboration_ws": ["/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial_collaboration.html"], "realtime:system_health": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html"], "realtime:websocket_test": ["/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html"], "settings": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html"], "settings:all": ["/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/settings_saved.html"], "support:contact": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/assignment_result.html"], "symbols:browse": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/popular_symbols.html"], "task_detail": ["/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_item.html"], "user-list-htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html"], "user-profile-htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html"], "user-update-htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_profile.html"], "users:admin_activity_detail_htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_table.html"], "users:admin_activity_export": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html"], "users:admin_activity_stats_htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html"], "users:admin_activity_table_htmx": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_table.html"], "users:admin_refresh_stats": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/dashboard.html"], "users:login": ["/home/<USER>/coding/clear_htmx/apps/core/templates/core/403.html", "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/403.html"], "users:mfa_verify": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html"], "users:profile": ["/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html", "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html", "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "users:settings": ["/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html"], "versioning:activity_stream": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/dashboard_activity_widget.html"], "versioning:advanced_search": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/modals/search_modal.html"], "versioning:audit_trail_report": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/system_history.html", "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/audit_trail_report.html"], "versioning:contact_history": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/contact_diff.html"], "versioning:quick_search": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/modals/search_modal.html"], "versioning:recent_changes_dashboard": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/system_history.html"], "versioning:system_history": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/system_history.html"], "versioning:universal_history": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/version_comparison.html", "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/version_detail.html", "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/model_history_widget.html", "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/view_history_button.html", "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/history_tab_content.html"], "versioning:version_comparison": ["/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/version_detail.html"]}, "file_patterns": {"/home/<USER>/coding/clear_htmx/apps/activity/templates/activity/partials/status_indicator.html": ["activity:status_indicator"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/admin_dropdown.html": ["CLEAR:analytics_dashboard", "CLEAR:admin_database_management", "CLEAR:performance", "CLEAR:admin_panel", "CLEAR:organization_settings", "CLEAR:user_management", "CLEAR:contract_administration", "CLEAR:version_management"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_detail.html": ["d_assets:library_detail", "d_assets:asset_edit", "admin:3d_assets:library_list", "admin:3d_assets:asset_edit", "admin:3d_assets:library_detail", "d_assets:library_list"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/asset_library_list.html": ["admin:3d_assets:library_create", "d_assets:library_detail", "d_assets:library_edit", "admin:3d_assets:library_edit", "admin:3d_assets:library_detail", "d_assets:library_create"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/builder_interface.html": ["htmx:visual_query_builder:get_fields", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/cached_dashboard.html": ["CLEAR:warm_dashboard_cache", "CLEAR:team_chat_partial", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/comment_count.html": ["analytics:comment_count"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/comment_replies.html": ["comments:reply_form"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/components/reports/kpi_cards.html": ["analytics:revenue_details", "analytics:kpi_refresh", "analytics:kpi_export"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_detail.html": ["project_detail", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/conflict_list.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard.html": ["financial:timesheet", "projects:list", "projects:create", "analytics:dashboard_stats", "projects:edit", "knowledge:analytics_guide", "projects:detail", "analytics:insights", "projects:tasks_today", "analytics:dashboard", "analytics:reports", "core:dashboard", "knowledge:search", "activity:feed"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard/dashboard.html": ["projects:project_create", "analytics:report_create", "analytics:htmx_dashboard_stats", "analytics:htmx_project_analytics", "analytics:htmx_chart_data", "analytics:htmx_metrics_cards", " hx-indicator=", "analytics:htmx_user_analytics", "analytics:reports"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_content.html": ["admin:htmx_performance", "admin:performance_alerts", "admin:performance_reports"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/dashboard_fixed.html": ["CLEAR:toggle_task", "CLEAR:timer_status_htmx", "CLEAR:health_check", "CLEAR:refresh_dashboard", "CLEAR:my_tasks_widget", " hx-indicator=", "CLEAR:recent_activity_widget", "CLEAR:current_time"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/demo.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/detection_status.html": ["conflict-detection-status-htmx"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/document_item.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/executive_dashboard.html": ["CLEAR:executive_analytics_period", "CLEAR:executive_analytics_export"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/filters.html": ["analytics:filtered_data"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/help_widget.html": ["CLEAR:help_reset_help_system", "CLEAR:help_guided_tours", "CLEAR:help_analytics", " hx-indicator=", "CLEAR:help_contextual_help"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/main.html": ["deprecation:api_data", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/metrics.html": ["admin:CLEAR_utility_changelist", "admin:CLEAR_task_changelist", "admin:CLEAR_project_changelist"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/modal.html": ["analytics:modal_data", "analytics:action"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/model_library_list.html": ["procedural_template_list", "model_detail", "create_symbol_mapping"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_item.html": ["conflict_detail", " hx-indicator=", "project_detail", "task_detail"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/notification_list.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/dashboard_stats.html": ["activity:activity_list"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_dashboard_content.html": ["analytics:htmx_chart_data", "projects:project_list", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/executive_kpi_cards.html": ["analytics:executive_kpi_refresh", "analytics:export_revenue", "analytics:export_delivery", "analytics:delivery_details", "analytics:revenue_details", "analytics:export_executive_kpis"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/partials/metrics_cards.html": ["analytics:export_metrics", "analytics:metrics_refresh"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/popular_symbols.html": ["symbols:browse", "analytics:popular_symbols"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/progress_bar.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/report_builder/dashboard.html": ["analytics:report_builder_detail", "analytics:report_builder_create", "analytics:report_builder_edit"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/save_success.html": ["htmx:visual_query_builder:toggle_favorite", "htmx:visual_query_builder:export_definition", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/scheduled_report_form.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/search_autocomplete.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/shared/components/dashboard/stats_cards.html": ["CLEAR:dashboard_stats_cards", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/statistics.html": ["CLEAR:htmx_spatial_statistics", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/stats_header.html": ["projects:development_active", "projects:development_list", "projects:completed_list", "projects:success_stories", "projects:pending_priority", "projects:kanban_board", "projects:completed_recent", "analytics:pending_report", "analytics:completion_analysis", "analytics:completion_trends", "projects:create_request", "analytics:stats_header", "analytics:development_timeline", "projects:pending_list", "projects:development_reports", "projects:pending_review"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/timesheet_summary.html": ["CLEAR:stop_timer_htmx", "CLEAR:quick_entry_modal", " hx-indicator=", "CLEAR:export_timesheet_summary_csv", "CLEAR:timesheet", "CLEAR:timesheet_summary_partial", "CLEAR:export_timesheet_summary_json"], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_detail.html": ["project_detail", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_item.html": ["project_detail", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/utility_list.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/analytics/templates/analytics/version_history.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/api/templates/api/project_detail.html": ["projects:project-map", "projects:project"], "/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_item.html": ["api:task-toggle-complete", "projects:project", "projects:project-detail", "api:task-duplicate", "api:task-assign-form", "api:task"], "/home/<USER>/coding/clear_htmx/apps/api/templates/api/task_list.html": ["api:task-list-htmx", "api:task"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/login.html": ["authentication:signup", "authentication:password_reset"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/partials/role_form.html": ["authentication:role_create", "authentication:role_update", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_complete.html": ["authentication:login"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_confirm.html": ["authentication:login", "authentication:password_reset"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_done.html": ["authentication:login", "authentication:password_reset"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/password_reset_form.html": ["authentication:login"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/profile.html": ["authentication:mfa_setup", "authentication:export_data", "authentication:notification_preferences", "authentication:activity_log", "authentication:delete_account", "authentication:password_change", "authentication:avatar_upload", "authentication:recent_activity", "authentication:privacy_settings"], "/home/<USER>/coding/clear_htmx/apps/authentication/templates/authentication/signup.html": ["authentication:signup", "authentication:login"], "/home/<USER>/coding/clear_htmx/apps/common/templates/common/base.html": ["logout", "profile", "documents", "projects", "analytics", "core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/base.html": ["common:execute-query", "common:execute", "common:query-create", "common:query-list", "common:query"], "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_confirm_delete.html": ["common:query-delete", "common:query"], "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_form.html": ["common:query-create", "common:query", "common:query-update"], "/home/<USER>/coding/clear_htmx/apps/common/views/visual_query_builder/templates/common/visual_query_builder/query_list.html": ["common:execute-query", "common:query-delete", "common:execute", "common:query", "common:query-update"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_detail.html": ["compliance:audit", "compliance:audit-list", "compliance:audit_list"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/audit_list.html": ["compliance:audit", "compliance:dashboard", "compliance:audit_export", "compliance:audit-filter-htmx", "compliance:audit_list", "compliance:audit-detail"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_list.html": ["compliance:dashboard", "compliance:check-status-htmx", "compliance:check_list", "compliance:check_results", "compliance:check", "compliance:check-list"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/check_results.html": ["compliance:check_list"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dashboard.html": ["compliance:report_create", "compliance:report-list", "compliance:report", "compliance:dashboard", "compliance:check_list", "compliance:report_list", "compliance:check", "compliance:check-list"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/default.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/dots.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/dashboard.html": ["compliance:gdpr:consent", "compliance:gdpr", "compliance:gdpr:data_deletion", "compliance:gdpr:data_request", "compliance:gdpr:processing_logs", "compliance:gdpr:privacy_settings", "compliance:gdpr:data_export"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/gdpr/privacy_settings.html": ["compliance:gdpr:dashboard", "compliance:gdpr"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/partials/audit_list_items.html": ["compliance:audit", "compliance:audit-detail"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/pulse.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_detail.html": ["compliance:check-list", "compliance:regulation_list", "compliance:check"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/regulation_list.html": ["compliance:regulation-list", "compliance:regulation", "compliance:regulation_detail", "compliance:dashboard", "compliance:regulation_list"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_detail.html": ["compliance:check-list", "compliance:report_list", "compliance:check"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_form.html": ["compliance:report", "compliance:report_list", "compliance:report-create"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/report_list.html": ["compliance:report_create", "compliance:report-list", "compliance:report", "compliance:report_list", "compliance:report_detail"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/skeleton.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/spinner.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/test_rule.html": ["admin:CLEAR_conflictrule_changelist", "admin:CLEAR_conflictrule_change", "admin:conflict_deploy_rule", "admin:conflict_run_scenario", "admin:conflict_execute_test", "admin:conflict_load_scenario"], "/home/<USER>/coding/clear_htmx/apps/compliance/templates/compliance/user_operations.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/400.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/403.html": ["users:login", "core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/404.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/500.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/admin/deprecation/partials/dashboard_content.html": ["CLEAR:deprecation_list", "CLEAR:deprecation_analytics", "CLEAR:migration_roadmap"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/admin/deprecation/partials/deprecation_list.html": ["CLEAR:deprecation_detail"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/assignment_result.html": ["support:contact", "projects:assignment_detail"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/cached_fragment.html": ["core:cached_fragment_refresh", "core:warm_cache_fragment"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/command_palette.html": ["CLEAR:settings", "CLEAR:tasks", "CLEAR:mapping", "CLEAR:profile", "CLEAR:documents", "CLEAR:project_create", "CLEAR:timesheet", "CLEAR:help", "CLEAR:htmx", "CLEAR:projects", "core:dashboard", "CLEAR:htmx:quick_task_create", "CLEAR:messaging"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/document_card.html": ["document_download", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/error_message.html": ["messaging:create_error_report", "analytics:track_error_display"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/htmx_error.html": ["help:support"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_card.html": ["project_detail", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_form.html": ["htmx:project_create", "htmx:project_update"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/project_list.html": ["htmx:project_create", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/quick_navigation_widget.html": ["core:add_navigation_item", "core:navigation_customizer"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/task_datagrid.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/task_datagrid_body.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_form.html": ["CLEAR:test_login", "authentication:register"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/test_login_success.html": ["CLEAR:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/user_menu.html": ["settings", "help:index", "notifications:list", "profile_view", "admin:index"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/components/whisper_count.html": ["messaging:whisper_count_update"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/coordinator_dashboard.html": ["projects:task", "projects:project-detail", "projects:task-list", "projects:project"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/department_manager_dashboard.html": ["projects:project-create", "CLEAR:admin-users", "CLEAR:admin-panel", "CLEAR:admin", "CLEAR:admin-analytics", "projects:project", "projects:project-detail"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/dashboard/stakeholder_dashboard.html": ["projects:project", "projects:project-detail"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/deprecation/dashboard/components/overview.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/error/500.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/activity_feed.html": ["project_detail"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/conflicts_summary.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/gis_layers_list.html": ["gis_layer_download", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/notification_item.html": ["core:notification_mark_read", "core:notification_snooze"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/search_results.html": ["documents:detail", "knowledge:article_detail", "projects:detail", "infrastructure:layer_detail", "projects:task_detail"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/stakeholder_search_results.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/partials/task_item.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/403.html": ["account_signup", "CLEAR:logout", "account_login", "CLEAR:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/400.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/403.html": ["users:login", "core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/404.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/errors/500.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/partials/notification_item.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/core/templates/core/shared/timesheet.html": ["CLEAR:delete_time_entry", "CLEAR:add_time_entry", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_grid.html": [" hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/document_workspace.html": ["CLEAR:document_discussion_list_htmx", "CLEAR:document_preview_htmx", "CLEAR:document_discussion_create_htmx", " hx-indicator=", "CLEAR:document_activity_feed_htmx", "CLEAR:document_version_upload_htmx", "CLEAR:comment_list_htmx"], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents.html": ["CLEAR:document_upload_htmx", "CLEAR:documents", "CLEAR:document_create_folder_htmx", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/documents_workspace.html": ["documents:workspace"], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/list.html": ["documents:upload_htmx", " hx-indicator=", "documents:create_folder_htmx", "documents:delete_htmx", "documents:preview_htmx", "documents:workspace"], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/partials/search.html": ["documents:detail", "documents:edit"], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/partials/upload_form.html": ["documents:upload_htmx", "documents:create_folder"], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/shared/components/documents/file_preview.html": ["CLEAR:comment_list_htmx", "CLEAR:project_detail", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/documents/templates/documents/upload_form.html": ["documents:upload", "documents:list", "documents:upload_interface", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/dashboard.html": ["financial:time_tracking", "financial:time_entries", "financial:stop_timer_htmx", "projects:detail", "financial:invoice_summary_htmx", " hx-indicator=", "financial:financial_statistics_htmx", "financial:add_time_entry", "financial:reports"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoice_detail.html": ["financial:invoice_pdf", "financial:send_invoice", "financial:dashboard", "projects:detail", " hx-indicator=", "financial:mark_invoice_paid", "financial:edit_invoice", "financial:invoices"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/invoices.html": ["financial:invoice_pdf", "projects:detail", "financial:dashboard", "financial:create_invoice", " hx-indicator=", "financial:mark_invoice_paid", "financial:invoice_summary_htmx", "financial:invoice_detail", "financial:edit_invoice"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/active_timer.html": ["financial:pause_timer_htmx", "financial:stop_timer_htmx", "financial:start_timer_form", "financial:time_entries"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/invoice_summary.html": ["financial:invoice_pdf", "financial:time_entries", "financial:invoice_item_htmx", "financial:send_invoice", "financial:create_invoice", "financial:invoice_templates", "financial:mark_invoice_paid", "financial:invoice_summary_htmx", "financial:export_invoices_json", "financial:invoice_detail", "financial:export_invoices_pdf", "financial:edit_invoice", "financial:invoices", "financial:export_invoices_csv"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/layout_saved.html": ["financial:layoutSaved", "financial:layoutReset", "financial:reset_dashboard_layout", "financial:refreshComponents", "financial:alertDismissed"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/project_financial_dashboard.html": ["financial:project_financial_dashboard_htmx", "financial:create_budget", "financial:time_entries", "financial:project_financial_report", "financial:create_invoice", "financial:project_transactions", "financial:export_project_financial_csv", "financial:invoice_detail"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_created.html": ["financial:time_tracking", "financial:duplicate_time_entry", "financial:time_entries", " hx-indicator=", "financial:time_entry_detail", "financial:create_invoice_from_entry", "financial:edit_time_entry", "financial:quick_time_entry_form"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/time_entry_updated.html": ["financial:time_tracking", "financial:duplicate_time_entry", "financial:time_entries", " hx-indicator=", "financial:time_entry_detail", "financial:create_invoice_from_entry", "financial:edit_time_entry", "financial:quick_time_entry_form"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_display.html": ["financial:time_tracking", "financial:stop_timer", "financial:timer_display_htmx", "financial:pause_timer", "financial:quick_time_entry_form", "financial:resume_timer"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_started.html": ["financial:time_tracking", "financial:timesheet", "financial:start_timer_form", "financial:time_entries", "financial:stop_timer_htmx", " hx-indicator=", "financial:pause_timer_htmx", "financial:timer_status_htmx", "financial:quick_time_entry_form"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_status.html": ["financial:duplicate_time_entry", "financial:resume_timer_htmx", "financial:start_timer_form", "financial:stop_timer_htmx", "financial:pause_timer_htmx"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timer_stopped.html": ["financial:timesheet", "financial:duplicate_time_entry", "financial:start_timer_form", "financial:time_entries", " hx-indicator=", "financial:time_entry_detail", "financial:create_invoice_from_entry", "financial:edit_time_entry"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_saved.html": ["financial:time_tracking", "financial:edit_timesheet", "financial:time_entries", "financial:timesheets", "financial:submit_timesheet", "financial:timesheet_detail", "financial:export_timesheet"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/timesheet_summary.html": ["financial:start_timer", "financial:edit_timesheet", "financial:time_entries", "financial:timesheet_summary_partial", "financial:submit_timesheet", "financial:refresh_timesheet_summary", "financial:add_time_entry", "financial:daily_chart_data", "financial:timesheet_detail", "financial:export_timesheet"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/partials/week_summary.html": ["financial:start_timer", "financial:week_summary_partial", "financial:time_entries", "financial:add_time_entry"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/reports.html": ["financial:revenue_chart_data_htmx", "financial:reports_data_htmx", "financial:export_reports_json", "financial:dashboard", "financial:export_reports_pdf", "projects:detail", " hx-indicator=", "financial:project_distribution_chart_htmx", "financial:export_reports_csv", "financial:time_entry_detail", "financial:approve_time_entry"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entries.html": ["financial:time_tracking", "financial:duplicate_time_entry", "financial:time_entries_stats_htmx", "financial:dashboard", "projects:detail", " hx-indicator=", "financial:bulk_export_entries", "financial:time_entries_filtered_htmx", "financial:time_entry_detail", "financial:quick_time_entry_htmx", "financial:add_time_entry", "financial:bulk_reject_entries", "financial:bulk_approve_entries", "financial:edit_time_entry", "financial:approve_time_entry"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/time_entry_form.html": ["financial:get_active_timer_data", "financial:get_last_entry_data", "financial:time_entries", "financial:save_time_entry_draft", "financial:dashboard", "financial:time_entry_detail", "financial:auto_save_time_entry", "financial:add_time_entry", "financial:edit_time_entry"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet.html": ["financial:timesheet", "financial:start_timer_form", "financial:timesheet_summary_partial", "financial:submit_timesheet", "financial:export_timesheet_summary_csv", "financial:save_timesheet_draft", "financial:auto_save_timesheet", "financial:timer_status_htmx", "financial:timesheet_week_summary", "financial:quick_time_entry_form"], "/home/<USER>/coding/clear_htmx/apps/financial/templates/financial/timesheet_summary.html": ["financial:timesheet", "financial:export_timesheet_entries_csv", "financial:export_timesheet_csv", "financial:timesheet_summary", "financial:dashboard"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/admin/infrastructure/utilitysymbol/import_library.html": ["admin:app_list", "admin:index", "admin:infrastructure_utilitysymbol_changelist"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/dashboard.html": ["infrastructure:conflict_list", "projects:detail", "infrastructure:utility_list", "infrastructure:utility_detail"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/gis_professional.html": ["infrastructure:symbol_palette", "infrastructure:help", "infrastructure:spatial_analysis", "infrastructure:spatial_map", "infrastructure:import_data", "infrastructure:dashboard", "infrastructure:utility_create"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/index.html": ["infrastructure:symbol_palette", "infrastructure:spatial_collaboration", "infrastructure:spatial_analysis", "infrastructure:collaboration_sessions", "infrastructure:project_3d", "infrastructure:spatial_export", " hx-indicator=", "infrastructure:dashboard", "infrastructure:spatial_proximity", "infrastructure:system_status", "infrastructure:symbol_library", "infrastructure:project_map", "infrastructure:conflicts_list", "infrastructure:asset_library"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/map.html": ["infrastructure:spatial_analysis", "infrastructure:export_data", "infrastructure:import_data", "infrastructure:htmx_filter_by_project", " hx-indicator=", "infrastructure:api_utilities_geojson", "infrastructure:conflict_detection", "infrastructure:htmx_filter_utilities", "infrastructure:api_conflicts_geojson"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/mapping.html": ["infrastructure:map_filter_data", "infrastructure:spatial_export", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/analysis/proximity.html": ["infrastructure:spatial_map", "infrastructure:spatial_proximity_analysis", "infrastructure:export_proximity_results"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/import/shapefile.html": ["infrastructure:spatial_map"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/create.html": ["infrastructure:spatial_location_list", "infrastructure:spatial_map"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/locations/list.html": ["infrastructure:spatial_location_delete", "infrastructure:spatial_map", "infrastructure:spatial_location_detail", "infrastructure:spatial_location_update", "infrastructure:spatial_location_create", "infrastructure:spatial_location_list"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial/map.html": ["infrastructure:spatial_intersection_analysis", "infrastructure:spatial_feature_popup_htmx", "infrastructure:spatial_proximity_analysis", "infrastructure:spatial_layer_toggle_htmx", "infrastructure:spatial_data_export", "infrastructure:spatial_buffer_analysis", "infrastructure:spatial_shapefile_import", "infrastructure:spatial_geojson_features", "infrastructure:spatial_location_create", "infrastructure:spatial_map_fullscreen", "infrastructure:spatial_geojson_import"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/spatial_collaboration.html": ["realtime:spatial_collaboration_ws"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/categories_tree.html": ["infrastructure:symbol_category_symbols"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/filters.html": ["infrastructure:symbol_admin"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/import_library.html": ["infrastructure:import_builtin_library_htmx"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/main.html": ["infrastructure:symbol_list", "infrastructure:symbol_categories", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/no_symbols.html": ["infrastructure:symbol_palette", "infrastructure:symbol_docs", " hx-indicator=", "infrastructure:import_library", "infrastructure:create_custom_symbol"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/place_symbol_form.html": ["infrastructure:place_symbol_htmx"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/popular_symbols.html": ["infrastructure:symbol_palette", "infrastructure:import_symbol_library", "infrastructure:popular_symbols_htmx", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/symbol_palette/quick_add_form.html": ["infrastructure:symbol_quick_add_htmx"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/system_health.html": ["infrastructure:health_alerts", "infrastructure:database_diagnostics", "infrastructure:performance_tests", "infrastructure:health_metrics", "infrastructure:index", "infrastructure:health_overview", "infrastructure:health_component", "infrastructure:security_audit"], "/home/<USER>/coding/clear_htmx/apps/infrastructure/templates/infrastructure/utility_network.html": ["infrastructure:pressure_analysis", "infrastructure:active_faults", "infrastructure:flow_analysis", "infrastructure:dependency_map", "infrastructure:optimization", "infrastructure:refresh_network_cache", "infrastructure:export_network_data", "infrastructure:network_settings", "infrastructure:simulation", "infrastructure:network_analysis", "infrastructure:asset_connectivity", "infrastructure:network_topology", "infrastructure:run_network_diagnostics", "infrastructure:network_stats_htmx", "infrastructure:fault_detection", "infrastructure:network_modeling", "infrastructure:performance_metrics"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/advanced_search.html": ["knowledge:search_results", "knowledge:clear_search", "knowledge:save_search", "knowledge:load_search", "knowledge:dashboard", "knowledge:search_suggestions"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_detail.html": ["knowledge:article_comments_htmx", "knowledge:article_vote_htmx", "knowledge:article_detail", "knowledge:track_article_view_htmx", "knowledge:advanced_search", " hx-indicator=", "knowledge:article_edit", "knowledge:category_list", "knowledge:list"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/article_list.html": ["knowledge:article_detail", "knowledge:article_create", "knowledge:management_dashboard", "knowledge:category_list", "knowledge:list"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/category_list.html": ["knowledge:article_list", "knowledge:category_list", "knowledge:article_detail"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/collaboration/index.html": ["knowledge:session_participants_htmx", "knowledge:share_knowledge", "knowledge:advanced_search", "knowledge:activity_feed", "knowledge:collaboration_updates_htmx", " hx-indicator=", "knowledge:start_collaboration_htmx", "knowledge:team_dashboard", "knowledge:active_sessions_htmx", "knowledge:article_edit", "knowledge:activity_feed_htmx", "knowledge:my_articles", "knowledge:create_session_htmx"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/dashboard.html": ["knowledge:feedback", "knowledge:analytics", "knowledge:article_create", "knowledge:category_management", "knowledge:search_analytics", "knowledge:topic_detail", "knowledge:search", "knowledge:outdated_articles", "knowledge:my_articles"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/documentation.html": ["knowledge:article_create"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/help_dropdown.html": ["knowledge:my_saved_searches", "knowledge:article_create", "knowledge:documentation", "knowledge:advanced_search", " hx-indicator=", "knowledge:article_list", "knowledge:submit_feedback", "knowledge:category_list", "knowledge:collaboration_index", "knowledge:help_shortcuts"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge.html": ["knowledge:reports", "knowledge:search_results", "knowledge:saved_searches", "knowledge:article_create", "knowledge:documentation", "knowledge:advanced_search", "knowledge:activity_feed", "knowledge:dashboard", "knowledge:teams", "knowledge:article_list", "knowledge:knowledge_graph", "knowledge:knowledge_base", "knowledge:recent_activity_partial", "knowledge:api_docs", "knowledge:topics", "knowledge:category_list", "knowledge:search_suggestions", "knowledge:collaboration_index"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base.html": ["knowledge:search_results", "knowledge:article_detail", "knowledge:article_create", "knowledge:documentation", "knowledge:advanced_search", "knowledge:faq", "knowledge:article_list", "knowledge:submit_feedback", "knowledge:api_docs", "knowledge:category_detail"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_created.html": ["knowledge:article_edit", "knowledge:article_detail"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_search_results.html": ["knowledge:article_list", "knowledge:category_list", "knowledge:search_articles"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_updated.html": ["knowledge:article_edit", "knowledge:article_detail"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/article_voting.html": ["knowledge:article_feedback"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/categories_list.html": ["knowledge:article_list", "knowledge:documentation"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_base/partials/recent_articles.html": ["knowledge:article_list", "knowledge:category_list", "knowledge:recent_articles", "knowledge:article_create"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_dashboard.html": ["knowledge:article_detail", "knowledge:article_create", "knowledge:dashboard_chart_data", "knowledge:management_dashboard", "knowledge:dashboard_stats", "knowledge:dashboard_activity_feed", " hx-indicator=", "knowledge:category_detail"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/knowledge_graph.html": ["knowledge:article_create"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/management/dashboard.html": ["knowledge:workflow_settings", "knowledge:article_detail", "knowledge:article_create", "knowledge:analytics_dashboard", "knowledge:check_notifications", "knowledge:collaboration_dashboard", "knowledge:permissions_management", "knowledge:article_edit", "knowledge:export_dashboard", "knowledge:management_activity_feed", "knowledge:category_create"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/notebook.html": ["knowledge:note_share", "knowledge:note_delete", "knowledge:note_detail", " hx-indicator=", "knowledge:notebook_search", "knowledge:note_bookmark", "knowledge:note_create", "knowledge:notebook_refresh", "knowledge:note_edit"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_results.html": ["knowledge:track_search_click", "knowledge:search_results", "knowledge:article_detail", "knowledge:article_vote", "knowledge:article_create", "knowledge:article_bookmark", "knowledge:article_edit", "knowledge:export_search_results", "knowledge:article_preview"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/partials/search_suggestions.html": ["knowledge:article_create", "knowledge:advanced_search", "knowledge:article_detail", "knowledge:track_suggestion_click"], "/home/<USER>/coding/clear_htmx/apps/knowledge/templates/knowledge/search_results.html": ["knowledge:track_search_click", "knowledge:search_results", "knowledge:article_detail", "knowledge:clear_all_filters", "knowledge:article_create", "knowledge:save_search", "knowledge:article_vote", "knowledge:advanced_search", " hx-indicator=", "knowledge:article_preview", "knowledge:article_list", "knowledge:remove_search_filter", "knowledge:article_bookmark", "knowledge:check_search_updates", "knowledge:article_edit", "knowledge:export_search_results", "knowledge:category_detail"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/chat_interface.html": ["CLEAR:messages", "CLEAR:conversation_leave", "CLEAR:message_send", "CLEAR:conversation_create", "CLEAR:conversation_leave_htmx", "CLEAR:message_thread_htmx", "CLEAR:conversation_create_htmx", "CLEAR:conversation_members_htmx", "CLEAR:conversation_search_htmx", "CLEAR:message_create_htmx", "CLEAR:conversation_search", "CLEAR:conversation_members", "CLEAR:conversation_list_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/conversation_list.html": ["messaging:conversation_detail", "messaging:create_conversation"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages.html": ["CLEAR:messages", "CLEAR:conversation_archive", "CLEAR:conversation_leave", "CLEAR:message_send", "CLEAR:conversation_create", "CLEAR:messages_list_htmx", "CLEAR:conversation_leave_htmx", "CLEAR:conversation_archive_htmx", "CLEAR:message_thread_htmx", "CLEAR:message_send_htmx", "CLEAR:conversation_create_htmx", "CLEAR:conversation_members_htmx", "CLEAR:conversation_search_htmx", "CLEAR:conversation_search", "CLEAR:conversation_members"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/messages_list.html": ["messaging:send_message", "messaging:conversation_detail", "messaging:create_conversation"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/notifications/list.html": ["CLEAR:notification_mark_read_toggle_htmx", "CLEAR:notification_settings", "CLEAR:mark_all_notifications_read"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/partials/message_list.html": ["messaging:htmx_react_message"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/chat/message_list.html": ["chat-messages-htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/conversation_info.html": ["messaging:toggle_notifications", "messaging:make_admin", "messaging:conversation_pin", "messaging:remove_member", "messaging:conversation_leave"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/dashboard/team_chat.html": ["CLEAR:send_team_message"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/collaboration_settings.html": ["CLEAR:collaboration_settings_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_item.html": ["CLEAR:messages", "CLEAR:conversation_mute_htmx", "CLEAR:conversation_leave_htmx", "CLEAR:conversation_members_htmx", "CLEAR:conversation_mark_read_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_list.html": ["CLEAR:conversation_list_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/conversation_members.html": ["CLEAR:messages", "CLEAR:conversation_member_role_htmx", "CLEAR:conversation_create_htmx", "CLEAR:conversation_member_add_htmx", "CLEAR:conversation_member_remove_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_list_partial.html": ["CLEAR:message_thread_preview"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread.html": ["CLEAR:message_mark_read_htmx", "CLEAR:message_thread_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/message_thread_with_pagination.html": ["CLEAR:conversation_typing_status", "CLEAR:set_typing_status", "CLEAR:conversation_messages_paginated", "CLEAR:message_create_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/reaction_picker.html": ["CLEAR:message_reaction_add_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/search_results.html": ["CLEAR:messages", "CLEAR:conversation_create_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/settings_saved.html": ["settings:all"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/thread_created.html": ["CLEAR:message_thread_view_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/messages/thread_reply.html": ["CLEAR:message_delete_htmx"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/team_channels_list.html": ["messaging:channel_join"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/user_status_indicator.html": ["messaging:user_status"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_conversation.html": ["CLEAR:whisper_send", "CLEAR:whisper_mark_read", "CLEAR:whisper_list"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_created.html": ["CLEAR:whisper_list"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/shared/components/whispers/whisper_item.html": ["messaging:delete_whisper", "messaging:toggle_reaction", "messaging:whisper_reply_form", "messaging:toggle_whisper_pin"], "/home/<USER>/coding/clear_htmx/apps/messaging/templates/messaging/test_websocket.html": ["CLEAR:test_login", "CLEAR:test_websocket", "CLEAR:dashboard"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/calendar.html": ["notes:journal_entry_create"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/dashboard.html": ["notes:journal_entry_detail", "notes:journal_entry_create", "notes:journal_entry_list", "notes:journal_calendar", "core:dashboard", "notes:journal_stats"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_detail.html": ["notes:journal_dashboard", "notes:journal_apply_template", "notes:journal_entry_detail", "notes:journal_entry_create", "notes:journal_prompts", "notes:journal_entry_delete", "core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/entry_list.html": ["notes:journal_dashboard", "core:dashboard", "notes:journal_entry_create", "notes:journal_entry_list", "notes:journal_calendar", "notes:journal_export"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/calendar.html": ["notes:journal_entry_detail", "notes:journal_entry_create"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/partials/entry_list.html": ["notes:journal_entry_detail", "notes:journal_entry_create", "notes:journal_entry_delete"], "/home/<USER>/coding/clear_htmx/apps/notes/templates/notes/journal/stats.html": ["notes:journal_dashboard", "core:dashboard", "notes:journal_entry_create", "notes:journal_calendar", "notes:journal_export"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d.html": ["projects:dashboard", "projects:project_detail", " hx-indicator=", "projects:project_map", "projects:project_3d_data_api"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/3d_conflict_list.html": ["projects:conflict_detail", "projects:conflict_escalate", "projects:conflict_export", "projects:run_conflict_detection", "projects:project_3d", "projects:conflict_add_note", "projects:project_map", "projects:conflict_resolve_api", "projects:conflict_settings", "projects:conflict_report_pdf"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/analytics_dashboard.html": ["projects:analytics_projects_htmx", "projects:analytics_dashboard_htmx", " hx-indicator=", "projects:analytics_team_htmx", "projects:analytics_chart_htmx", "projects:analytics_export", "projects:analytics_timeline_htmx", "projects:analytics_reports_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/cached_dashboard.html": ["projects:project_create", "projects:project_list", "projects:my_projects", " hx-indicator=", "projects:task_create", "projects:recent_projects_htmx", "projects:analytics_dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/chat.html": ["projects:chat_messages_htmx", "projects:detail", "projects:project_detail", "projects:chat_typing_htmx", " hx-indicator=", "projects:chat_send_htmx", "projects:chat_users_htmx", "projects:chat_export", "core:dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/clients.html": ["projects:person_list_htmx", "projects:person_edit", "projects:person_list", "projects:person_detail", "projects:clients", "projects:person_create", "projects:person_export", "core:dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/comments.html": ["projects:project_activity_htmx", "projects:comment_delete_htmx", "projects:comment_reply_htmx", "projects:detail", " hx-indicator=", "projects:comments_list_htmx", "projects:comment_add_htmx", "projects:comment_like_htmx", "core:dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/mobile_stakeholder_projects.html": ["projects:person_assign_project_htmx", "projects:person_remove_assignment_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/components/project_datagrid_body.html": ["projects:tasks", "projects:create", "projects:edit", "projects:update_field", "projects:detail", "projects:timeline", "projects:team"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/conversation_list.html": ["projects:conversation_leave_htmx", "projects:conversation_mark_read_htmx", "projects:conversation_pin_htmx", "projects:conversation_settings_htmx", " hx-indicator=", "projects:conversation_create_htmx", "projects:conversation_events_sse", "projects:conversation_mute_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create.html": ["projects:htmx_create", "projects:list", "projects:create", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/create_from_template.html": ["projects:htmx_create_from_template", "projects:list", "projects:create_from_template", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/dashboard.html": ["analytics:activity_log", "projects:my_projects_partial", "projects:create", "projects:settings", "projects:analytics", "projects:my_projects", "projects:reports", "projects:recent_activity_partial", "projects:person_list", "analytics:dashboard", "projects:dashboard_refresh", "projects:export", "infrastructure:map", "core:dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/debug_dashboard.html": ["projects:debug_htmx_test", "projects:debug_queries", "projects:debug_activity", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/detail.html": ["projects:task_list", "projects:project_add_member_form_htmx", "projects:project_list", "projects:project_timeline_htmx", "projects:project_upload_document_form_htmx", "projects:project_export", "projects:project_add_time_entry_form_htmx", "projects:project_team_members_htmx", " hx-indicator=", "projects:project_documents_htmx", "projects:project_reports", "projects:task_create", "projects:project_edit", "projects:project_duplicate", "projects:project_recent_tasks_htmx", "projects:project_map_view_htmx", "projects:dashboard", "projects:project_delete_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/index.html": ["projects:create", "projects:import", "projects:search", "projects:templates", "projects:analytics", "projects:export"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/list.html": ["projects:create", "projects:edit", "projects:detail", "projects:project_map", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/map.html": ["projects:detail", "projects:conflict_detection_htmx", "projects:project_3d"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/model_fields.html": ["projects:add_field_condition"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects.html": ["projects:edit", "projects:project_map", "projects:detail", "projects:create"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/my_projects_list.html": ["projects:my_projects_refresh", "projects:my_projects_data", "projects:list", "projects:detail"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub.html": ["projects:create", "projects:close_all_dropdowns", "projects:timesheet_entry_create", "projects:my_projects", " hx-indicator=", "projects:my_tasks", "projects:timesheet", "users:profile", "projects:dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/myhub_dropdown.html": ["authentication:logout", "projects:create", "projects:search", "projects:timesheet_entry_create", "projects:my_projects", "projects:my_tasks", "projects:timesheet", "users:profile", "projects:dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/activity_logs_partial.html": ["projects:project_activity_logs"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/confirm_delete.html": ["projects:notebook_entry_delete", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/create_entry.html": ["projects:dashboard", "projects:detail", "projects:notebook_entry_create", "projects:project_notebook", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/entry_form.html": ["projects:project_notebook_entry_form"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_main.html": ["projects:dashboard", "projects:project_notebook_entry_form", "projects:detail", " hx-indicator=", "projects:project_notebook_partial", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/notebook_partial.html": ["projects:project_notebook_activity_logs", "projects:project_notebook_entry_form", "projects:project_notebook_entry_delete", " hx-indicator=", "projects:project_notebook_partial"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/notebook/update_entry.html": ["projects:project_notebook", "projects:list", "projects:dashboard", "projects:detail"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/project_stats_inline.html": ["projects:project_stats_inline", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_grid.html": ["projects:project_create", "projects:project_status_htmx", "projects:projects_filter_htmx", "projects:project_timeline", "projects:project_detail", "projects:project_communication_modal_htmx", "projects:project_map", "projects:project_status_update_modal_htmx", "projects:project_stats_inline_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/projects_list.html": ["projects:project_create", "projects:toggle_project_favorite", "projects:project_delete", "projects:project_detail", "projects:project_edit", "projects:projects_filter_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/partials/stats_cards.html": ["projects:stats_cards_htmx", "projects:team_list", "projects:project_list", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_detail.html": ["projects:person_contact_info_htmx", "projects:person_detail_refresh", "projects:person_assign_project_modal", "projects:person_list", "projects:person_edit", "projects:person_contact_modal", "projects:person_projects_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/persons/person_list.html": ["projects:person_list_htmx", "projects:person_quick_add", "projects:person_search", "projects:person_list", "projects:person_detail", "projects:person_edit", "projects:person_export"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dashboard.html": ["projects:team_modal", "projects:edit", "projects:task_create_modal", "projects:detail", "projects:team_overview", "projects:note_create_modal", "projects:tasks", " hx-indicator=", "core:dashboard", "projects:timeline_refresh", "projects:activity_feed", "projects:financial_summary", "projects:timeline", "projects:recent_tasks", "projects:share_modal", "projects:export", "projects:map", "projects:activity_log", "projects:timeline_chart", "projects:analytics", "projects:list", "projects:dashboard_metrics"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_detail.html": ["projects:project_map_partial", "projects:project_documents_partial", "projects:project_overview_partial", "projects:edit", "projects:dashboard", "projects:project_timeline_partial", "projects:analytics", "projects:timeline", " hx-indicator=", "projects:project_comments_partial", "projects:project_notebook_partial", "projects:project_utilities_partial", "projects:project_team_partial", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_dropdown.html": ["projects:list", "projects:create", "projects:person_list", "projects:my_projects", "projects:person_quick_add", "projects:dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_list.html": ["projects:edit", "projects:list", "projects:detail", "projects:create"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map.html": ["projects:conflict_detection", "projects:detail", " hx-indicator=", "projects:utility_conflicts", "projects:map_layers", "projects:utility_info", "projects:dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_map_openlayers.html": ["projects:map_geojson_data", "projects:detail", " hx-indicator=", "projects:map_layers_htmx", "projects:utility_conflicts_htmx", "projects:dashboard", "projects:map_utility_info_htmx"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_portfolio.html": ["projects:create", "projects:edit", "projects:portfolio_stats", "projects:detail", " hx-indicator=", "projects:portfolio_filter"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/project_reporting.html": ["projects:dashboard", "projects:detail", "projects:reporting_update", " hx-indicator=", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/projects/create.html": ["projects:htmx", "projects:htmx:project_create", "projects:create", "projects:edit", "projects:htmx:project_update", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/quick_entry_modal.html": ["projects:htmx", "projects:htmx:project_tasks", "projects:htmx:quick_time_entry"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/saved_searches.html": ["projects:htmx", "projects:saved_searches", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_bar.html": ["projects:htmx", "projects:saved_searches", "projects:htmx:advanced_search", "projects:htmx:global_search"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_empty.html": ["projects:list", "projects:my_projects", "projects:create"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/search_results.html": ["projects:task_list", "projects:update", "projects:search", "projects:detail", "projects:person_detail", "projects:person_list", "projects:task_detail", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects.html": ["projects:map", "projects:detail", "projects:my_projects", "projects:timeline", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/my_projects_list.html": ["projects:create", "projects:htmx_my_projects_list", "projects:detail", "projects:my_projects", " hx-indicator=", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/dashboard/tasks_list.html": ["projects:htmx_tasks_list", "projects:htmx_task_form", "projects:htmx_tasks_filter", "projects:htmx_delete_task", "projects:detail", " hx-indicator=", "projects:htmx_update_task_status", "projects:htmx_task_edit_field"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/navigation/project_dropdown.html": ["projects:task_list", "financial:invoice_list", "projects:person_list", "projects:my_projects", " hx-indicator=", "projects:htmx_create_project_modal", "projects:list", "projects:analytics_dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/shared/components/project_datagrid_body.html": ["projects:tasks", "projects:create", "projects:edit", "projects:update_field", "projects:detail", "projects:timeline", "projects:team"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards.html": ["projects:htmx", "projects:htmx-stats-cards"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/stats_cards_content.html": ["financial:revenue", "projects:conflict-list", "financial:timesheet", "financial:revenue-dashboard", "projects:task-list", "projects:project-list", "financial:timesheet-monthly", "projects:conflict", "projects:task", "projects:analytics-dashboard", "projects:analytics", "infrastructure:utility", "infrastructure:utility-list", "projects:project"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/task_dependencies.html": ["projects:task-update", "projects:project-list", "projects:task", " hx-indicator=", "projects:project", "projects:project-detail", "projects:dashboard"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/tasks.html": ["projects:task_activity_feed", "projects:workflow_form", "projects:detail", " hx-indicator=", "projects:task_form", "projects:task_stats_partial", "projects:tasks_clear_filters", "projects:list", "projects:tasks_filter"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline.html": ["projects:detail", " hx-indicator=", "projects:gantt_chart_partial", "projects:settings_modal", "projects:share_modal", "core:dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timeline_enhanced.html": ["projects:calendar_view_partial", "projects:gantt_enhanced_partial", "projects:detail", " hx-indicator=", "projects:resource_allocation", "projects:critical_path_analysis", "core:dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/timer_controls.html": ["projects:timer_status", "projects:start_timer", " hx-indicator=", "projects:get_project_tasks", "projects:timesheet"], "/home/<USER>/coding/clear_htmx/apps/projects/templates/projects/utilities.html": ["projects:utilities_list", "projects:add_utility", "projects:detail", "projects:utilities_map", " hx-indicator=", "projects:utilities_filter", "core:dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/dashboard.html": ["realtime:metrics", "realtime:notification_list", "realtime:recent_notifications", "realtime:websocket_test", "realtime:system_health"], "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/notification_list.html": ["realtime:notification_list_content"], "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/webrtc_room.html": ["realtime:dashboard"], "/home/<USER>/coding/clear_htmx/apps/realtime/templates/realtime/websocket_test.html": ["realtime:dashboard"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/activity_log.html": ["users:admin_activity_stats_htmx", "users:admin_activity_table_htmx", " hx-indicator=", "users:admin_activity_export", "authentication:activity_log"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/dashboard.html": ["users:admin_refresh_stats", "authentication:admin_dashboard", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/panel.html": ["authentication:admin_panel"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_detail_modal.html": ["authentication:activity_detail"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_stats.html": ["authentication:activity_stats"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/activity_table.html": ["authentication:activity_table", "users:admin_activity_table_htmx", "users:admin_activity_detail_htmx", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/admin_stats.html": ["authentication:admin_stats"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/analytics_content.html": ["authentication:analytics_content"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/cpu_metric_card.html": ["authentication:cpu_metric_card"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/database_metric_card.html": ["authentication:database_metric_card"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/database_metrics_content.html": ["authentication:database_metrics_content"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/delete_user_confirmation_content.html": ["authentication:user_admin"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/memory_metric_card.html": ["authentication:system_metrics"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/performance_metrics_content.html": ["authentication:system_performance"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/system_alerts_content.html": ["authentication:system_alerts"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/system_health.html": ["authentication:system_health"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_detail_content.html": ["authentication:user_detail"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_edit_form.html": ["authentication:user_edit", "CLEAR:admin_user_update"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/user_stats_cards.html": ["authentication:admin_dashboard"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/users_metric_card.html": ["authentication:user_dashboard"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/admin/partials/users_table.html": ["authentication:user_list", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/collaboration_status.html": ["messaging:collaboration_dashboard"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/enhanced_dropdown.html": ["common:ui_components"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/error.html": ["authentication:register", "authentication:password_reset", "messaging:create_message", "analytics:track_user_error", "users:settings", "messaging:create_support_ticket", "users:profile", "authentication:login", "core:dashboard", "knowledge:search"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/feature_availability.html": ["common:ui_components"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/login.html": ["authentication:login", "CLEAR:login", "authentication:password_reset"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/login_form.html": ["authentication:login", "authentication:password_reset"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/mfa_verify.html": [" hx-indicator=", "users:mfa_verify", "authentication:login", "authentication:mfa_verify", "authentication:mfa_backup"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_meetings.html": ["messaging:meetings", "messaging:meetings_create"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/my_profile.html": ["authentication:profile", "authentication:profile_settings_partial", "notes:journal_entry_detail", "authentication:profile_edit_modal", "notes:notebook_partial", " hx-indicator=", "authentication:profile_header", "authentication:profile_picture_upload", "notes:journal_dashboard"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/notebook.html": ["notes:journal_entry_list", "notes:journal_dashboard", "notes:journal_entry_create"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_activities.html": ["CLEAR:activity_details", "CLEAR:profile_activities_refresh", "CLEAR:activity_delete", "CLEAR:profile_activities", "CLEAR:project_detail"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/partials/profile_projects.html": ["CLEAR:profile_projects", "projects:create", "projects:edit", "CLEAR:profile_projects_refresh", "projects:detail", "CLEAR:project_detail", "projects:list"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/profile.html": ["authentication:profile_share", "authentication:edit_profile", "authentication:profile_completion", " hx-indicator=", "authentication:profile_overview", "authentication:profile_info_partial", "authentication:avatar_upload", "authentication:user_profile", "authentication:profile_stats"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/profiles/partials/profile_activities.html": ["analytics:activity_dashboard", "analytics:activity_charts", "CLEAR:project_detail", "analytics:activity_reports"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/settings.html": ["authentication:update_theme", "authentication:mfa_setup", "authentication:mfa_disable", "authentication:update_preferences", "authentication:active_sessions", "authentication:settings", " hx-indicator=", "authentication:api_tokens_list", "authentication:password_change", "authentication:update_notifications", "authentication:update_account"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/navigation/user_dropdown.html": ["authentication:settings", "notes:journal_dashboard", "authentication:profile", "authentication:logout"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/profile/activity_list.html": ["CLEAR:profile_activity_htmx"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/profile/stats_cards.html": ["projects:list", "analytics:dashboard"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/projects/team_member_profile_modal.html": ["projects:tasks", "messaging:compose", "projects:tasks:create", "projects:team_management", "CLEAR:project_team_member_action_htmx", " hx-indicator="], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_list.html": ["authentication:user_create", "authentication:user_management", "user-list-htmx", "user-profile-htmx", "authentication:user_detail"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/users/user_profile.html": ["user-update-htmx", "authentication:user_profile", "authentication:profile_edit"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_list.html": ["messaging:contacts", "messaging:compose", "CLEAR:user_list_for_whisper"], "/home/<USER>/coding/clear_htmx/apps/users/templates/users/shared/components/whispers/user_search_results.html": ["messaging:search", "messaging:compose"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/audit_trail_report.html": ["versioning:audit_trail_report"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/contact_diff.html": ["versioning:contact_history"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/modals/search_modal.html": ["versioning:quick_search", "versioning:advanced_search"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/system_history.html": ["versioning:recent_changes_dashboard", "versioning:audit_trail_report", "versioning:system_history"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/version_comparison.html": ["versioning:universal_history"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/version_detail.html": ["versioning:universal_history", "versioning:version_comparison"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/dashboard_activity_widget.html": ["versioning:activity_stream"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/history_tab_content.html": ["versioning:universal_history"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/model_history_widget.html": ["versioning:universal_history"], "/home/<USER>/coding/clear_htmx/apps/versioning/templates/versioning/widgets/view_history_button.html": ["versioning:universal_history"], "/home/<USER>/coding/clear_htmx/templates/analytics/dashboard.html": ["core:dashboard_stats"], "/home/<USER>/coding/clear_htmx/templates/base.html": ["core:stakeholder_dashboard", "authentication:profile", "financial:financial_admin_dashboard", "core:coordinator_dashboard", "infrastructure:gis_professional", "core:department_manager_dashboard", "authentication:role_management_dashboard", "core:home", "projects:tasks", "analytics:admin_reports", "projects:my_projects", "core:dashboard", "analytics:executive_reports", "authentication:security_settings", "authentication:stakeholder_management_dashboard", "authentication:login", "authentication:logout", "core:executive_dashboard", "notes:journal_dashboard", "projects:list"], "/home/<USER>/coding/clear_htmx/templates/components/projects/project_card.html": ["projects:project_reports", "projects:project_tasks", "projects:project_export"], "/home/<USER>/coding/clear_htmx/templates/errors/400.html": ["core:home"], "/home/<USER>/coding/clear_htmx/templates/errors/403.html": ["core:home"], "/home/<USER>/coding/clear_htmx/templates/errors/404.html": ["projects:task_list", "core:sitemap", "analytics:track_error", "projects:project_list", "core:report_broken_link", "knowledge:advanced_search", "messaging:create_message", "knowledge:dashboard", "analytics:dashboard", "core:recent_activity_partial", "core:dashboard"], "/home/<USER>/coding/clear_htmx/templates/errors/500.html": ["core:health_check", "analytics:track_server_error", "core:request_notification", "core:cached_recent_work", "core:system_status_partial", "knowledge:cached_articles", "core:dashboard"], "/home/<USER>/coding/clear_htmx/templates/home.html": ["authentication:login"], "/home/<USER>/coding/clear_htmx/templates/infrastructure/htmx/utility_conflicts.html": ["infrastructure:utility_conflicts_htmx", " hx-indicator="], "/home/<USER>/coding/clear_htmx/templates/infrastructure/realtime_monitoring.html": ["infrastructure:realtime_status_stream", "infrastructure:realtime_conflict_stream", "infrastructure:realtime_spatial_stream"], "/home/<USER>/coding/clear_htmx/templates/shared/403.html": ["core:dashboard"], "/home/<USER>/coding/clear_htmx/templates/shared/auth/partials/password_reset_messages.html": ["authentication:password_reset", " hx-indicator="], "/home/<USER>/coding/clear_htmx/templates/shared/components/ui/project_assignment_modal.html": ["projects:htmx", "projects:htmx:available_projects", " hx-indicator="], "/home/<USER>/coding/clear_htmx/templates/shared/error/500.html": ["core:home"], "/home/<USER>/coding/clear_htmx/templates/users/auth/mfa_login.html": ["authentication:mfa_backup_tokens", "authentication:logout", "authentication:mfa_login"], "/home/<USER>/coding/clear_htmx/tests/browser_compatibility.html": ["CLEAR:browser_compatibility", "CLEAR:test_login", "core:dashboard", "CLEAR:test_websocket"]}, "namespaced_patterns": {"CLEAR": ["activity_delete", "activity_details", "add_time_entry", "admin", "admin-analytics", "admin-panel", "admin-users", "admin_database_management", "admin_panel", "admin_user_update", "analytics_dashboard", "browser_compatibility", "collaboration_settings_htmx", "comment_list_htmx", "contract_administration", "conversation_archive", "conversation_archive_htmx", "conversation_create", "conversation_create_htmx", "conversation_leave", "conversation_leave_htmx", "conversation_list_htmx", "conversation_mark_read_htmx", "conversation_member_add_htmx", "conversation_member_remove_htmx", "conversation_member_role_htmx", "conversation_members", "conversation_members_htmx", "conversation_messages_paginated", "conversation_mute_htmx", "conversation_search", "conversation_search_htmx", "conversation_typing_status", "current_time", "dashboard", "dashboard_stats_cards", "delete_time_entry", "deprecation_analytics", "deprecation_detail", "deprecation_list", "document_activity_feed_htmx", "document_create_folder_htmx", "document_discussion_create_htmx", "document_discussion_list_htmx", "document_preview_htmx", "document_upload_htmx", "document_version_upload_htmx", "documents", "executive_analytics_export", "executive_analytics_period", "export_timesheet_summary_csv", "export_timesheet_summary_json", "health_check", "help", "help_analytics", "help_contextual_help", "help_guided_tours", "help_reset_help_system", "htmx", "htmx:quick_task_create", "htmx_spatial_statistics", "login", "logout", "mapping", "mark_all_notifications_read", "message_create_htmx", "message_delete_htmx", "message_mark_read_htmx", "message_reaction_add_htmx", "message_send", "message_send_htmx", "message_thread_htmx", "message_thread_preview", "message_thread_view_htmx", "messages", "messages_list_htmx", "messaging", "migration_roadmap", "my_tasks_widget", "notification_mark_read_toggle_htmx", "notification_settings", "organization_settings", "performance", "profile", "profile_activities", "profile_activities_refresh", "profile_activity_htmx", "profile_projects", "profile_projects_refresh", "project_create", "project_detail", "project_team_member_action_htmx", "projects", "quick_entry_modal", "recent_activity_widget", "refresh_dashboard", "send_team_message", "set_typing_status", "settings", "stop_timer_htmx", "tasks", "team_chat_partial", "test_login", "test_websocket", "timer_status_htmx", "timesheet", "timesheet_summary_partial", "toggle_task", "user_list_for_whisper", "user_management", "version_management", "warm_dashboard_cache", "whisper_list", "whisper_mark_read", "whisper_send"], "activity": ["activity_list", "feed", "status_indicator"], "admin": ["3d_assets:asset_edit", "3d_assets:library_create", "3d_assets:library_detail", "3d_assets:library_edit", "3d_assets:library_list", "CLEAR_conflictrule_change", "CLEAR_conflictrule_changelist", "CLEAR_project_changelist", "CLEAR_task_changelist", "CLEAR_utility_changelist", "app_list", "conflict_deploy_rule", "conflict_execute_test", "conflict_load_scenario", "conflict_run_scenario", "htmx_performance", "index", "infrastructure_utilitysymbol_changelist", "performance_alerts", "performance_reports"], "analytics": ["action", "activity_charts", "activity_dashboard", "activity_log", "activity_reports", "admin_reports", "comment_count", "completion_analysis", "completion_trends", "dashboard", "dashboard_stats", "delivery_details", "development_timeline", "executive_kpi_refresh", "executive_reports", "export_delivery", "export_executive_kpis", "export_metrics", "export_revenue", "filtered_data", "htmx_chart_data", "htmx_dashboard_stats", "htmx_metrics_cards", "htmx_project_analytics", "htmx_user_analytics", "insights", "kpi_export", "kpi_refresh", "metrics_refresh", "modal_data", "pending_report", "popular_symbols", "report_builder_create", "report_builder_detail", "report_builder_edit", "report_create", "reports", "revenue_details", "stats_header", "track_error", "track_error_display", "track_server_error", "track_user_error"], "api": ["task", "task-assign-form", "task-duplicate", "task-list-htmx", "task-toggle-complete"], "authentication": ["active_sessions", "activity_detail", "activity_log", "activity_stats", "activity_table", "admin_dashboard", "admin_panel", "admin_stats", "analytics_content", "api_tokens_list", "avatar_upload", "cpu_metric_card", "database_metric_card", "database_metrics_content", "delete_account", "edit_profile", "export_data", "login", "logout", "mfa_backup", "mfa_backup_tokens", "mfa_disable", "mfa_login", "mfa_setup", "mfa_verify", "notification_preferences", "password_change", "password_reset", "privacy_settings", "profile", "profile_completion", "profile_edit", "profile_edit_modal", "profile_header", "profile_info_partial", "profile_overview", "profile_picture_upload", "profile_settings_partial", "profile_share", "profile_stats", "recent_activity", "register", "role_create", "role_management_dashboard", "role_update", "security_settings", "settings", "signup", "stakeholder_management_dashboard", "system_alerts", "system_health", "system_metrics", "system_performance", "update_account", "update_notifications", "update_preferences", "update_theme", "user_admin", "user_create", "user_dashboard", "user_detail", "user_edit", "user_list", "user_management", "user_profile"], "comments": ["reply_form"], "common": ["execute", "execute-query", "query", "query-create", "query-delete", "query-list", "query-update", "ui_components"], "compliance": ["audit", "audit-detail", "audit-filter-htmx", "audit-list", "audit_export", "audit_list", "check", "check-list", "check-status-htmx", "check_list", "check_results", "dashboard", "gdpr", "gdpr:consent", "gdpr:dashboard", "gdpr:data_deletion", "gdpr:data_export", "gdpr:data_request", "gdpr:privacy_settings", "gdpr:processing_logs", "regulation", "regulation-list", "regulation_detail", "regulation_list", "report", "report-create", "report-list", "report_create", "report_detail", "report_list"], "core": ["add_navigation_item", "cached_fragment_refresh", "cached_recent_work", "coordinator_dashboard", "dashboard", "dashboard_stats", "department_manager_dashboard", "executive_dashboard", "health_check", "home", "navigation_customizer", "notification_mark_read", "notification_snooze", "recent_activity_partial", "report_broken_link", "request_notification", "sitemap", "stakeholder_dashboard", "system_status_partial", "warm_cache_fragment"], "d_assets": ["asset_edit", "library_create", "library_detail", "library_edit", "library_list"], "deprecation": ["api_data"], "documents": ["create_folder", "create_folder_htmx", "delete_htmx", "detail", "edit", "list", "preview_htmx", "upload", "upload_htmx", "upload_interface", "workspace"], "financial": ["add_time_entry", "alertDismissed", "approve_time_entry", "auto_save_time_entry", "auto_save_timesheet", "bulk_approve_entries", "bulk_export_entries", "bulk_reject_entries", "create_budget", "create_invoice", "create_invoice_from_entry", "daily_chart_data", "dashboard", "duplicate_time_entry", "edit_invoice", "edit_time_entry", "edit_timesheet", "export_invoices_csv", "export_invoices_json", "export_invoices_pdf", "export_project_financial_csv", "export_reports_csv", "export_reports_json", "export_reports_pdf", "export_timesheet", "export_timesheet_csv", "export_timesheet_entries_csv", "export_timesheet_summary_csv", "financial_admin_dashboard", "financial_statistics_htmx", "get_active_timer_data", "get_last_entry_data", "invoice_detail", "invoice_item_htmx", "invoice_list", "invoice_pdf", "invoice_summary_htmx", "invoice_templates", "invoices", "layoutReset", "layoutSaved", "mark_invoice_paid", "pause_timer", "pause_timer_htmx", "project_distribution_chart_htmx", "project_financial_dashboard_htmx", "project_financial_report", "project_transactions", "quick_time_entry_form", "quick_time_entry_htmx", "refreshComponents", "refresh_timesheet_summary", "reports", "reports_data_htmx", "reset_dashboard_layout", "resume_timer", "resume_timer_htmx", "revenue", "revenue-dashboard", "revenue_chart_data_htmx", "save_time_entry_draft", "save_timesheet_draft", "send_invoice", "start_timer", "start_timer_form", "stop_timer", "stop_timer_htmx", "submit_timesheet", "time_entries", "time_entries_filtered_htmx", "time_entries_stats_htmx", "time_entry_detail", "time_tracking", "timer_display_htmx", "timer_status_htmx", "timesheet", "timesheet-monthly", "timesheet_detail", "timesheet_summary", "timesheet_summary_partial", "timesheet_week_summary", "timesheets", "week_summary_partial"], "help": ["index", "support"], "htmx": ["project_create", "project_update", "visual_query_builder:export_definition", "visual_query_builder:get_fields", "visual_query_builder:toggle_favorite"], "infrastructure": ["active_faults", "api_conflicts_geojson", "api_utilities_geojson", "asset_connectivity", "asset_library", "collaboration_sessions", "conflict_detection", "conflict_list", "conflicts_list", "create_custom_symbol", "dashboard", "database_diagnostics", "dependency_map", "export_data", "export_network_data", "export_proximity_results", "fault_detection", "flow_analysis", "gis_professional", "health_alerts", "health_component", "health_metrics", "health_overview", "help", "htmx_filter_by_project", "htmx_filter_utilities", "import_builtin_library_htmx", "import_data", "import_library", "import_symbol_library", "index", "layer_detail", "map", "map_filter_data", "network_analysis", "network_modeling", "network_settings", "network_stats_htmx", "network_topology", "optimization", "performance_metrics", "performance_tests", "place_symbol_htmx", "popular_symbols_htmx", "pressure_analysis", "project_3d", "project_map", "realtime_conflict_stream", "realtime_spatial_stream", "realtime_status_stream", "refresh_network_cache", "run_network_diagnostics", "security_audit", "simulation", "spatial_analysis", "spatial_buffer_analysis", "spatial_collaboration", "spatial_data_export", "spatial_export", "spatial_feature_popup_htmx", "spatial_geojson_features", "spatial_geojson_import", "spatial_intersection_analysis", "spatial_layer_toggle_htmx", "spatial_location_create", "spatial_location_delete", "spatial_location_detail", "spatial_location_list", "spatial_location_update", "spatial_map", "spatial_map_fullscreen", "spatial_proximity", "spatial_proximity_analysis", "spatial_shapefile_import", "symbol_admin", "symbol_categories", "symbol_category_symbols", "symbol_docs", "symbol_library", "symbol_list", "symbol_palette", "symbol_quick_add_htmx", "system_status", "utility", "utility-list", "utility_conflicts_htmx", "utility_create", "utility_detail", "utility_list"], "knowledge": ["active_sessions_htmx", "activity_feed", "activity_feed_htmx", "advanced_search", "analytics", "analytics_dashboard", "analytics_guide", "api_docs", "article_bookmark", "article_comments_htmx", "article_create", "article_detail", "article_edit", "article_feedback", "article_list", "article_preview", "article_vote", "article_vote_htmx", "cached_articles", "category_create", "category_detail", "category_list", "category_management", "check_notifications", "check_search_updates", "clear_all_filters", "clear_search", "collaboration_dashboard", "collaboration_index", "collaboration_updates_htmx", "create_session_htmx", "dashboard", "dashboard_activity_feed", "dashboard_chart_data", "dashboard_stats", "documentation", "export_dashboard", "export_search_results", "faq", "feedback", "help_shortcuts", "knowledge_base", "knowledge_graph", "list", "load_search", "management_activity_feed", "management_dashboard", "my_articles", "my_saved_searches", "note_bookmark", "note_create", "note_delete", "note_detail", "note_edit", "note_share", "notebook_refresh", "notebook_search", "outdated_articles", "permissions_management", "recent_activity_partial", "recent_articles", "remove_search_filter", "reports", "save_search", "saved_searches", "search", "search_analytics", "search_articles", "search_results", "search_suggestions", "session_participants_htmx", "share_knowledge", "start_collaboration_htmx", "submit_feedback", "team_dashboard", "teams", "topic_detail", "topics", "track_article_view_htmx", "track_search_click", "track_suggestion_click", "workflow_settings"], "messaging": ["channel_join", "collaboration_dashboard", "compose", "contacts", "conversation_detail", "conversation_leave", "conversation_pin", "create_conversation", "create_error_report", "create_message", "create_support_ticket", "delete_whisper", "htmx_react_message", "make_admin", "meetings", "meetings_create", "remove_member", "search", "send_message", "toggle_notifications", "toggle_reaction", "toggle_whisper_pin", "user_status", "whisper_count_update", "whisper_reply_form"], "notes": ["journal_apply_template", "journal_calendar", "journal_dashboard", "journal_entry_create", "journal_entry_delete", "journal_entry_detail", "journal_entry_list", "journal_export", "journal_prompts", "journal_stats", "notebook_partial"], "notifications": ["list"], "projects": ["activity_feed", "activity_log", "add_field_condition", "add_utility", "analytics", "analytics-dashboard", "analytics_chart_htmx", "analytics_dashboard", "analytics_dashboard_htmx", "analytics_export", "analytics_projects_htmx", "analytics_reports_htmx", "analytics_team_htmx", "analytics_timeline_htmx", "assignment_detail", "calendar_view_partial", "chat_export", "chat_messages_htmx", "chat_send_htmx", "chat_typing_htmx", "chat_users_htmx", "clients", "close_all_dropdowns", "comment_add_htmx", "comment_delete_htmx", "comment_like_htmx", "comment_reply_htmx", "comments_list_htmx", "completed_list", "completed_recent", "conflict", "conflict-list", "conflict_add_note", "conflict_detail", "conflict_detection", "conflict_detection_htmx", "conflict_escalate", "conflict_export", "conflict_report_pdf", "conflict_resolve_api", "conflict_settings", "conversation_create_htmx", "conversation_events_sse", "conversation_leave_htmx", "conversation_mark_read_htmx", "conversation_mute_htmx", "conversation_pin_htmx", "conversation_settings_htmx", "create", "create_from_template", "create_request", "critical_path_analysis", "dashboard", "dashboard_metrics", "dashboard_refresh", "debug_activity", "debug_htmx_test", "debug_queries", "detail", "development_active", "development_list", "development_reports", "edit", "export", "financial_summary", "gantt_chart_partial", "gantt_enhanced_partial", "get_project_tasks", "htmx", "htmx-stats-cards", "htmx:advanced_search", "htmx:available_projects", "htmx:global_search", "htmx:project_create", "htmx:project_tasks", "htmx:project_update", "htmx:quick_time_entry", "htmx_create", "htmx_create_from_template", "htmx_create_project_modal", "htmx_delete_task", "htmx_my_projects_list", "htmx_task_edit_field", "htmx_task_form", "htmx_tasks_filter", "htmx_tasks_list", "htmx_update_task_status", "import", "kanban_board", "list", "map", "map_geojson_data", "map_layers", "map_layers_htmx", "map_utility_info_htmx", "my_projects", "my_projects_data", "my_projects_partial", "my_projects_refresh", "my_tasks", "note_create_modal", "notebook_entry_create", "notebook_entry_delete", "pending_list", "pending_priority", "pending_review", "person_assign_project_htmx", "person_assign_project_modal", "person_contact_info_htmx", "person_contact_modal", "person_create", "person_detail", "person_detail_refresh", "person_edit", "person_export", "person_list", "person_list_htmx", "person_projects_htmx", "person_quick_add", "person_remove_assignment_htmx", "person_search", "portfolio_filter", "portfolio_stats", "project", "project-create", "project-detail", "project-list", "project-map", "project_3d", "project_3d_data_api", "project_activity_htmx", "project_activity_logs", "project_add_member_form_htmx", "project_add_time_entry_form_htmx", "project_comments_partial", "project_communication_modal_htmx", "project_create", "project_delete", "project_delete_htmx", "project_detail", "project_documents_htmx", "project_documents_partial", "project_duplicate", "project_edit", "project_export", "project_list", "project_map", "project_map_partial", "project_map_view_htmx", "project_notebook", "project_notebook_activity_logs", "project_notebook_entry_delete", "project_notebook_entry_form", "project_notebook_partial", "project_overview_partial", "project_recent_tasks_htmx", "project_reports", "project_stats_inline", "project_stats_inline_htmx", "project_status_htmx", "project_status_update_modal_htmx", "project_tasks", "project_team_members_htmx", "project_team_partial", "project_timeline", "project_timeline_htmx", "project_timeline_partial", "project_upload_document_form_htmx", "project_utilities_partial", "projects_filter_htmx", "recent_activity_partial", "recent_projects_htmx", "recent_tasks", "reporting_update", "reports", "resource_allocation", "run_conflict_detection", "saved_searches", "search", "settings", "settings_modal", "share_modal", "start_timer", "stats_cards_htmx", "success_stories", "task", "task-list", "task-update", "task_activity_feed", "task_create", "task_create_modal", "task_detail", "task_form", "task_list", "task_stats_partial", "tasks", "tasks:create", "tasks_clear_filters", "tasks_filter", "tasks_today", "team", "team_list", "team_management", "team_modal", "team_overview", "templates", "timeline", "timeline_chart", "timeline_refresh", "timer_status", "timesheet", "timesheet_entry_create", "toggle_project_favorite", "update", "update_field", "utilities_filter", "utilities_list", "utilities_map", "utility_conflicts", "utility_conflicts_htmx", "utility_info", "workflow_form"], "realtime": ["dashboard", "metrics", "notification_list", "notification_list_content", "recent_notifications", "spatial_collaboration_ws", "system_health", "websocket_test"], "settings": ["all"], "support": ["contact"], "symbols": ["browse"], "users": ["admin_activity_detail_htmx", "admin_activity_export", "admin_activity_stats_htmx", "admin_activity_table_htmx", "admin_refresh_stats", "login", "mfa_verify", "profile", "settings"], "versioning": ["activity_stream", "advanced_search", "audit_trail_report", "contact_history", "quick_search", "recent_changes_dashboard", "system_history", "universal_history", "version_comparison"]}, "non_namespaced_patterns": [" hx-indicator=", "account_login", "account_signup", "analytics", "chat-messages-htmx", "conflict-detection-status-htmx", "conflict_detail", "create_symbol_mapping", "document_download", "documents", "gis_layer_download", "logout", "model_detail", "procedural_template_list", "profile", "profile_view", "project_detail", "projects", "settings", "task_detail", "user-list-htmx", "user-profile-htmx", "user-update-htmx"], "summary": {"namespaces": 28, "templates_with_urls": 368, "total_templates": 558, "unique_url_patterns": 899}}