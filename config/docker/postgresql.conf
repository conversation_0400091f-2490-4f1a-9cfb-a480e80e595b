# PostgreSQL Configuration for CLEAR HTMX with PostGIS
# Optimized for spatial data operations and Django performance

# Connection Settings
max_connections = 200
superuser_reserved_connections = 3

# Memory Settings (based on 2GB container memory)
shared_buffers = 512MB
effective_cache_size = 1536MB
maintenance_work_mem = 128MB
work_mem = 16MB
wal_buffers = 16MB

# Checkpoint Settings
checkpoint_completion_target = 0.9
checkpoint_timeout = 10min
max_wal_size = 2GB
min_wal_size = 512MB

# Query Planning
random_page_cost = 1.1
effective_io_concurrency = 200
default_statistics_target = 100

# Parallel Query Execution
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
max_worker_processes = 8

# PostGIS Specific Settings
# Enable JIT for better PostGIS performance
jit = on
jit_above_cost = 100000

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 500  # Log slow queries (> 500ms)
log_checkpoints = on
log_connections = on
log_disconnections = on
log_duration = off
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_lock_waits = on
log_statement = 'ddl'
log_temp_files = 0

# Performance Insights
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all
track_activity_query_size = 4096

# Autovacuum Settings (important for PostGIS)
autovacuum = on
autovacuum_max_workers = 4
autovacuum_naptime = 30s
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.1
autovacuum_analyze_scale_factor = 0.05
autovacuum_vacuum_cost_delay = 10ms
autovacuum_vacuum_cost_limit = 1000

# Lock Management
deadlock_timeout = 1s
max_locks_per_transaction = 256

# Django Specific Optimizations
# Enable prepared statements for better Django ORM performance
max_prepared_transactions = 100

# SSL Settings (disabled for development)
ssl = off

# Locale Settings
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'

# Full Text Search
default_text_search_config = 'pg_catalog.english'

# Extensions Preloading (for PostGIS)
shared_preload_libraries = 'pg_stat_statements'

# Statement Tracking
pg_stat_statements.max = 10000
pg_stat_statements.track = all
