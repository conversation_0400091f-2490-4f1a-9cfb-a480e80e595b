"""Project Management Views Module

Comprehensive views for project management including:
- Project CRUD operations with multi-tenant organization isolation
- Task management with Kanban boards and data grids
- Time tracking and financial reporting
- Real-time collaboration and HTMX dynamic interactions
- Analytics, reporting, and visualization features
- Team management and stakeholder communication

All views follow Django 5.2 patterns with proper role-based access control,
organization-based data isolation, and optimized database queries.
"""

# Standard library imports
from __future__ import annotations

import logging
import uuid
from typing import Any, Dict, List, Optional, Type, Union

# Django core imports
from django.contrib import messages
from django.contrib.auth import get_user_model
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.db.models import Q, QuerySet, Sum
from django.http import HttpRequest, HttpResponse, HttpResponseRedirect, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.template.response import TemplateResponse
from django.urls import reverse, reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    CreateView,
    DeleteView,
    DetailView,
    TemplateView,
    UpdateView,
)

# Import our type definitions
from apps.core.types import (
    ContextData,
    DjangoRequest,
    DjangoResponse,
    JSONDict,
    TemplateContext,
)

# Third-party imports
from django_filters.views import FilterView

# Local app imports
from apps.common.decorators import (
    requires_organization_role,
    requires_project_access,
)
from apps.common.mixins import (
    OrganizationAccessMixin,
    RoleRequiredMixin,
)
from apps.financial.models import TimeEntry
from apps.infrastructure.models import Conflict, Utility
from apps.projects.filters import ProjectFilter, TaskFilter
from apps.projects.models import Project, ProjectTemplate, SavedProjectFilter, Task
from apps.projects.views.htmx_mixins import HTMXTemplateResponseMixin

# Initialize logger
logger = logging.getLogger(__name__)
User = get_user_model()


# ============================================================================
# PROJECT CRUD VIEWS - Core project management with role-based access control
# ============================================================================


class ProjectListView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    OrganizationAccessMixin,
    HTMXTemplateResponseMixin,
    FilterView,
):
    """List view for projects with advanced filtering and pagination."""

    model = Project
    template_name = "projects/project_list.html"
    partial_template_name = "projects/partials/project_list_content.html"
    context_object_name = "projects"
    paginate_by = 25
    required_roles = ["utility-coordinator", "department-manager", "executive"]
    filterset_class = ProjectFilter

    def get_queryset(self):
        """Get projects accessible to user with optimizations."""
        try:
            queryset = (
                Project.objects.with_related_data()
                .with_counts()
                .with_financial_calculations()
                .filter(organization=self.request.user.organization)
            )
            return queryset
        except Exception:
            logger.exception("Error in ProjectListView.get_queryset")
            return Project.objects.none()

    def get_filterset_kwargs(self):
        """Pass additional kwargs to the filterset."""
        kwargs = super().get_filterset_kwargs()
        # Add request to kwargs for HTMX URL generation
        kwargs["request"] = self.request
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            # Get base queryset for statistics
            base_queryset = Project.objects.filter(organization=self.request.user.organization)

            # Calculate statistics
            stats = {
                "total": base_queryset.count(),
                "active": base_queryset.filter(status="active").count(),
                "planning": base_queryset.filter(status="planning").count(),
                "on_hold": base_queryset.filter(status="on_hold").count(),
                "completed": base_queryset.filter(status="completed").count(),
            }

            # Get saved filters for the user
            saved_filters = SavedProjectFilter.objects.filter(
                Q(user=self.request.user) | Q(is_shared=True, user__organization=self.request.user.organization)
            ).order_by("-last_used", "name")

            context.update(
                {
                    "page_title": _("Projects"),
                    "can_create_project": self.request.user.has_perm("projects.add_project"),
                    "stats": stats,
                    "saved_filters": saved_filters,
                    "show_filters": True,  # Flag to show filter panel
                },
            )
        except Exception:
            logger.exception("Error in ProjectListView.get_context_data")
        return context


class ProjectDetailView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    OrganizationAccessMixin,
    HTMXTemplateResponseMixin,
    DetailView,
):
    """Detailed project view with comprehensive information and permissions."""

    model = Project
    template_name = "projects/project_detail.html"
    partial_template_name = "projects/partials/project_detail_content.html"
    context_object_name = "project"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_queryset(self):
        """Get projects with related data for performance."""
        try:
            return (
                Project.objects.with_related_data()
                .with_counts()
                .with_financial_calculations()
                .filter(organization=self.request.user.organization)
            )
        except Exception:
            logger.exception("Error in ProjectDetailView.get_queryset")
            return Project.objects.none()

    def get_object(self, queryset=None):
        """Get project object with access control."""
        try:
            obj = super().get_object(queryset)
            if not obj.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))
            return obj
        except Project.DoesNotExist:
            raise PermissionDenied(_("Project not found or access denied."))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = self.object
        try:
            # Calculate task statistics
            tasks = project.tasks.all()
            task_stats = {
                "total": tasks.count(),
                "completed": tasks.filter(status="completed").count(),
                "in_progress": tasks.filter(status="in_progress").count(),
                "pending": tasks.filter(status="pending").count(),
            }

            # Get completion percentage
            completion_percentage = 0
            if task_stats["total"] > 0:
                completion_percentage = int((task_stats["completed"] / task_stats["total"]) * 100)

            # Get team member count
            team_member_count = (
                project.team_members.filter(is_active=True).count() if hasattr(project, "team_members") else 0
            )

            context.update(
                {
                    "page_title": project.name,
                    "can_edit": project.user_has_access(self.request.user, "edit"),
                    "can_manage": project.user_has_access(self.request.user, "manage"),
                    "user_role": project.get_user_role(self.request.user),
                    "recent_activities": (
                        project.project_activities.select_related("user")[:10]
                        if hasattr(project, "project_activities")
                        else []
                    ),
                    "active_tasks": tasks.filter(status__in=["pending", "in_progress"])[:10],
                    "completion_percentage": completion_percentage,
                    "task_count": task_stats["total"],
                    "completed_task_count": task_stats["completed"],
                    "pending_task_count": task_stats["pending"],
                    "team_member_count": team_member_count,
                },
            )

            # Add additional attributes to project object for template use
            project.completion_percentage = completion_percentage
            project.task_count = task_stats["total"]
            project.completed_task_count = task_stats["completed"]
            project.pending_task_count = task_stats["pending"]
            project.team_member_count = team_member_count

        except Exception:
            logger.exception("Error in ProjectDetailView.get_context_data")
        return context


class ProjectCreateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, CreateView):
    """Create view for new projects."""

    model = Project
    template_name = "projects/project_form.html"
    fields = [
        "name",
        "client",
        "description",
        "start_date",
        "end_date",
        "template",
        "utility_coordinator",
    ]
    success_url = reverse_lazy("projects:project-list")
    required_roles = ["department-manager", "executive"]

    def get_form(self, form_class=None):
        """Customize form for organization context."""
        form = super().get_form(form_class)
        try:
            # Filter utility coordinators to organization members with correct role
            if "utility_coordinator" in form.fields:
                form.fields["utility_coordinator"].queryset = User.objects.filter(
                    organization=self.request.user.organization,
                    user_roles__role__slug="utility-coordinator",
                ).distinct()

            # Filter templates to organization templates
            if "template" in form.fields:
                form.fields["template"].queryset = ProjectTemplate.objects.filter(
                    Q(organization=self.request.user.organization) | Q(organization__isnull=True),
                    is_active=True,
                )
        except Exception:
            logger.exception("Error customizing ProjectCreateView form")
        return form

    def form_valid(self, form):
        """Set organization and creator automatically."""
        try:
            form.instance.organization = self.request.user.organization

            # Generate unique project ID if not provided
            if not form.instance.id:
                form.instance.id = f"PRJ-{uuid.uuid4().hex[:8].upper()}"

            messages.success(
                self.request,
                _("Project '%(name)s' created successfully.") % {"name": form.instance.name},
            )
            return super().form_valid(form)
        except Exception:
            logger.exception("Error in ProjectCreateView.form_valid")
            messages.error(self.request, _("Error creating project. Please try again."))
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": _("Create New Project"),
                "form_action": "create",
            },
        )
        return context


class ProjectUpdateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, UpdateView):
    """Update view for existing projects."""

    model = Project
    template_name = "projects/project_form.html"
    fields = [
        "name",
        "client",
        "description",
        "start_date",
        "end_date",
        "rag_status",
        "current_phase",
        "utility_coordinator",
    ]
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_queryset(self):
        """Filter projects by organization."""
        return Project.objects.filter(organization=self.request.user.organization)

    def get_object(self, queryset=None):
        """Get object with edit permission check."""
        try:
            obj = super().get_object(queryset)
            if not obj.user_has_access(self.request.user, "edit"):
                raise PermissionDenied(_("You do not have permission to edit this project."))
            return obj
        except Project.DoesNotExist:
            raise PermissionDenied(_("Project not found or access denied."))

    def get_form(self, form_class=None):
        """Customize form for organization context."""
        form = super().get_form(form_class)
        try:
            # Filter utility coordinators to organization members
            if "utility_coordinator" in form.fields:
                form.fields["utility_coordinator"].queryset = User.objects.filter(
                    organization=self.request.user.organization,
                    user_roles__role__slug="utility-coordinator",
                ).distinct()
        except Exception:
            logger.exception("Error customizing ProjectUpdateView form")
        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        try:
            messages.success(
                self.request,
                _("Project '%(name)s' updated successfully.") % {"name": form.instance.name},
            )
            return super().form_valid(form)
        except Exception:
            logger.exception("Error in ProjectUpdateView.form_valid")
            messages.error(self.request, _("Error updating project. Please try again."))
            return self.form_invalid(form)

    def get_success_url(self):
        """Return to project detail page after update."""
        return reverse("projects:project-detail", kwargs={"pk": self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": _("Edit %(name)s") % {"name": self.object.name},
                "form_action": "update",
                "project": self.object,
            },
        )
        return context


class ProjectDeleteView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, DeleteView):
    """Delete view for projects - requires manager role."""

    model = Project
    template_name = "projects/project_confirm_delete.html"
    success_url = reverse_lazy("projects:project-list")
    required_roles = ["department-manager", "executive"]

    def get_queryset(self):
        """Filter projects by organization."""
        return Project.objects.filter(organization=self.request.user.organization)

    def get_object(self, queryset=None):
        """Get object with delete permission check."""
        try:
            obj = super().get_object(queryset)
            if not obj.user_has_access(self.request.user, "manage"):
                raise PermissionDenied(_("You do not have permission to delete this project."))
            return obj
        except Project.DoesNotExist:
            raise PermissionDenied(_("Project not found or access denied."))

    def delete(self, request, *args, **kwargs):
        """Handle project deletion with logging."""
        try:
            project_name = self.get_object().name
            response = super().delete(request, *args, **kwargs)
            messages.success(
                request,
                _("Project '%(name)s' deleted successfully.") % {"name": project_name},
            )
            return response
        except Exception:
            logger.exception("Error deleting project")
            messages.error(request, _("Error deleting project. Please try again."))
            return redirect("projects:project-list")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": _("Delete %(name)s") % {"name": self.object.name},
            },
        )
        return context


# Legacy alias for compatibility
ProjectEditView = ProjectUpdateView


# ============================================================================
# TASK MANAGEMENT VIEWS - Task tracking with Kanban boards and data grids
# ============================================================================


class TaskListView(
    LoginRequiredMixin,
    RoleRequiredMixin,
    OrganizationAccessMixin,
    HTMXTemplateResponseMixin,
    FilterView,
):
    """List view for tasks with advanced filtering and pagination."""

    model = Task
    template_name = "projects/task_list.html"
    partial_template_name = "projects/partials/task_list_content.html"
    context_object_name = "tasks"
    paginate_by = 50
    required_roles = ["utility-coordinator", "department-manager", "executive"]
    filterset_class = TaskFilter

    def get_queryset(self):
        """Get tasks accessible to user with optimizations."""
        try:
            queryset = Task.objects.with_related_data().filter(project__organization=self.request.user.organization)
            return queryset
        except Exception:
            logger.exception("Error in TaskListView.get_queryset")
            return Task.objects.none()

    def get_filterset_kwargs(self):
        """Pass additional kwargs to the filterset."""
        kwargs = super().get_filterset_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            context.update(
                {
                    "page_title": _("Tasks"),
                    "show_filters": True,
                },
            )
        except Exception:
            logger.exception("Error in TaskListView.get_context_data")
        return context


class TaskDetailView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, DetailView):
    """Detailed task view with comments and activity."""

    model = Task
    template_name = "projects/task_detail.html"
    context_object_name = "task"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_queryset(self):
        """Get tasks with related data."""
        try:
            return Task.objects.with_related_data().filter(project__organization=self.request.user.organization)
        except Exception:
            logger.exception("Error in TaskDetailView.get_queryset")
            return Task.objects.none()

    def get_object(self, queryset=None):
        """Get task with access control."""
        try:
            obj = super().get_object(queryset)
            if not obj.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this task."))
            return obj
        except Task.DoesNotExist:
            raise PermissionDenied(_("Task not found or access denied."))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        task = self.object
        try:
            context.update(
                {
                    "page_title": task.title,
                    "can_edit": task.user_has_access(self.request.user, "edit"),
                    "can_assign": task.user_has_access(self.request.user, "assign"),
                    "recent_comments": task.get_recent_comments(),
                },
            )
        except Exception:
            logger.exception("Error in TaskDetailView.get_context_data")
        return context


class TaskCreateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, CreateView):
    """Create view for new tasks."""

    model = Task
    template_name = "projects/task_form.html"
    fields = [
        "title",
        "description",
        "project",
        "assigned_to",
        "priority",
        "due_date",
        "estimated_hours",
    ]
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_form(self, form_class=None):
        """Customize form for organization context."""
        form = super().get_form(form_class)
        try:
            # Filter projects to organization projects
            if "project" in form.fields:
                form.fields["project"].queryset = Project.objects.filter(organization=self.request.user.organization)

            # Filter assignees to organization members
            if "assigned_to" in form.fields:
                form.fields["assigned_to"].queryset = User.objects.filter(organization=self.request.user.organization)
        except Exception:
            logger.exception("Error customizing TaskCreateView form")
        return form

    def form_valid(self, form):
        """Set creator automatically."""
        try:
            form.instance.created_by = self.request.user
            messages.success(
                self.request,
                _("Task '%(title)s' created successfully.") % {"title": form.instance.title},
            )
            return super().form_valid(form)
        except Exception:
            logger.exception("Error in TaskCreateView.form_valid")
            messages.error(self.request, _("Error creating task. Please try again."))
            return self.form_invalid(form)

    def get_success_url(self):
        """Return to task detail page after creation."""
        return reverse("projects:task-detail", kwargs={"pk": self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": _("Create New Task"),
                "form_action": "create",
            },
        )
        return context


class TaskUpdateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, UpdateView):
    """Update view for existing tasks."""

    model = Task
    template_name = "projects/task_form.html"
    fields = [
        "title",
        "description",
        "assigned_to",
        "status",
        "priority",
        "due_date",
        "estimated_hours",
    ]
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_queryset(self):
        """Filter tasks by organization."""
        return Task.objects.filter(project__organization=self.request.user.organization)

    def get_object(self, queryset=None):
        """Get object with edit permission check."""
        try:
            obj = super().get_object(queryset)
            if not obj.user_has_access(self.request.user, "edit"):
                raise PermissionDenied(_("You do not have permission to edit this task."))
            return obj
        except Task.DoesNotExist:
            raise PermissionDenied(_("Task not found or access denied."))

    def get_form(self, form_class=None):
        """Customize form for organization context."""
        form = super().get_form(form_class)
        try:
            # Filter assignees to organization members
            if "assigned_to" in form.fields:
                form.fields["assigned_to"].queryset = User.objects.filter(organization=self.request.user.organization)
        except Exception:
            logger.exception("Error customizing TaskUpdateView form")
        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        try:
            messages.success(
                self.request,
                _("Task '%(title)s' updated successfully.") % {"title": form.instance.title},
            )
            return super().form_valid(form)
        except Exception:
            logger.exception("Error in TaskUpdateView.form_valid")
            messages.error(self.request, _("Error updating task. Please try again."))
            return self.form_invalid(form)

    def get_success_url(self):
        """Return to task detail page after update."""
        return reverse("projects:task-detail", kwargs={"pk": self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": _("Edit %(title)s") % {"title": self.object.title},
                "form_action": "update",
                "task": self.object,
            },
        )
        return context


class TaskDeleteView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, DeleteView):
    """Delete view for tasks."""

    model = Task
    template_name = "projects/task_confirm_delete.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_queryset(self):
        """Filter tasks by organization."""
        return Task.objects.filter(project__organization=self.request.user.organization)

    def get_object(self, queryset=None):
        """Get object with delete permission check."""
        try:
            obj = super().get_object(queryset)
            if not obj.user_has_access(self.request.user, "delete"):
                raise PermissionDenied(_("You do not have permission to delete this task."))
            return obj
        except Task.DoesNotExist:
            raise PermissionDenied(_("Task not found or access denied."))

    def delete(self, request, *args, **kwargs):
        """Handle task deletion with logging."""
        try:
            task_title = self.get_object().title
            self.get_object().project.pk
            response = super().delete(request, *args, **kwargs)
            messages.success(
                request,
                _("Task '%(title)s' deleted successfully.") % {"title": task_title},
            )
            return response
        except Exception:
            logger.exception("Error deleting task")
            messages.error(request, _("Error deleting task. Please try again."))
            return redirect("projects:task-list")

    def get_success_url(self):
        """Return to project detail page after deletion."""
        return reverse("projects:project-detail", kwargs={"pk": self.object.project.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(
            {
                "page_title": _("Delete %(title)s") % {"title": self.object.title},
            },
        )
        return context


# ============================================================================
# SPECIALIZED PROJECT VIEWS - Analytics, timelines, and enhanced features
# ============================================================================


class ProjectTimelineView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project timeline and Gantt chart interface."""

    template_name = "projects/timeline.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Timeline - %(name)s") % {"name": project.name},
                    "tasks": project.tasks.select_related("assigned_to").order_by("start_date", "created_at"),
                },
            )
        except Exception:
            logger.exception("Error in ProjectTimelineView.get_context_data")
            messages.error(self.request, _("Error loading project timeline."))
        return context


class ProjectAnalyticsView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project analytics and reporting view."""

    template_name = "projects/analytics.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.with_financial_calculations().filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Analytics - %(name)s") % {"name": project.name},
                    "completion_percentage": project.get_completion_percentage(),
                },
            )
        except Exception:
            logger.exception("Error in ProjectAnalyticsView.get_context_data")
            messages.error(self.request, _("Error loading project analytics."))
        return context


# ============================================================================
# DASHBOARD AND OVERVIEW VIEWS
# ============================================================================


@login_required
@requires_organization_role(["utility-coordinator", "department-manager", "executive"])
def projects_dashboard(request: HttpRequest) -> HttpResponse:
    """Main projects dashboard with overview statistics."""
    try:
        user_projects = (
            Project.objects.with_counts().filter(organization=request.user.organization).order_by("-updated_at")[:10]
        )

        # Calculate dashboard metrics
        total_projects = Project.objects.filter(organization=request.user.organization).count()
        active_projects = Project.objects.filter(
            organization=request.user.organization,
            rag_status__in=["Green", "Amber", "Red"],
        ).count()

        context = {
            "page_title": _("Projects Dashboard"),
            "recent_projects": user_projects,
            "total_projects": total_projects,
            "active_projects": active_projects,
            "user_tasks": Task.objects.filter(
                assigned_to=request.user,
                project__organization=request.user.organization,
                status__in=["pending", "in_progress"],
            )[:5],
        }

        return render(request, "projects/dashboard.html", context)
    except Exception:
        logger.exception("Error in projects_dashboard")
        messages.error(request, _("Error loading dashboard. Please try again."))
        return redirect("projects:project-list")


# ============================================================================
# FUNCTION-BASED VIEWS FOR COMPATIBILITY
# ============================================================================


@login_required
@requires_project_access
def project_utilities(request: HttpRequest, project_id: str) -> HttpResponse:
    """Display project utilities with conflict detection."""
    try:
        project = get_object_or_404(Project, pk=project_id, organization=request.user.organization)

        utilities = Utility.objects.filter(project=project).select_related()
        conflicts = Conflict.objects.filter(project=project, status__in=["open", "pending"])

        context = {
            "project": project,
            "utilities": utilities,
            "conflicts": conflicts,
            "page_title": _("Utilities - %(name)s") % {"name": project.name},
        }

        return render(request, "projects/utilities.html", context)
    except Exception:
        logger.exception("Error in project_utilities")
        messages.error(request, _("Error loading project utilities."))
        return redirect("projects:project-detail", pk=project_id)


# Legacy function-based views for URL compatibility
def tasks_view(request: HttpRequest) -> HttpResponse:
    """Legacy tasks view - redirects to TaskListView."""
    return redirect("projects:task-list")


# ============================================================================
# FILTER MANAGEMENT VIEWS - Save and manage project filters
# ============================================================================


@login_required
@requires_organization_role(["utility-coordinator", "department-manager", "executive"])
def save_project_filter(request: HttpRequest) -> HttpResponse:
    """Save current filter configuration."""
    if request.method == "POST":
        try:
            name = request.POST.get("name", "").strip()
            query_string = request.POST.get("query_string", "")

            if not name:
                messages.error(request, _("Filter name is required."))
                return redirect("projects:project-list")

            # Check if filter with same name exists for user
            existing = SavedProjectFilter.objects.filter(user=request.user, name=name).first()

            if existing:
                # Update existing filter
                existing.query_string = query_string
                existing.save()
                messages.success(request, _("Filter '%(name)s' updated.") % {"name": name})
            else:
                # Create new filter
                SavedProjectFilter.objects.create(user=request.user, name=name, query_string=query_string)
                messages.success(request, _("Filter '%(name)s' saved.") % {"name": name})

            # Return updated filters panel if HTMX request
            if request.headers.get("HX-Request"):
                context = {
                    "saved_filters": SavedProjectFilter.objects.filter(
                        Q(user=request.user) | Q(is_shared=True, user__organization=request.user.organization)
                    ).order_by("-last_used", "name"),
                    "filter": ProjectFilter(request.GET, queryset=Project.objects.none(), request=request),
                }
                return render(request, "projects/partials/project_filters.html", context)

        except Exception:
            logger.exception("Error saving project filter")
            messages.error(request, _("Error saving filter. Please try again."))

    return redirect("projects:project-list")


@login_required
@requires_organization_role(["utility-coordinator", "department-manager", "executive"])
def delete_saved_filter(request: HttpRequest, pk: int) -> HttpResponse:
    """Delete a saved filter."""
    if request.method == "DELETE":
        try:
            filter_obj = get_object_or_404(SavedProjectFilter, pk=pk, user=request.user)
            filter_name = filter_obj.name
            filter_obj.delete()

            messages.success(request, _("Filter '%(name)s' deleted.") % {"name": filter_name})

            # Return 204 No Content for HTMX
            if request.headers.get("HX-Request"):
                return HttpResponse(status=204)

        except Exception:
            logger.exception("Error deleting saved filter")
            messages.error(request, _("Error deleting filter."))

    return redirect("projects:project-list")


@login_required
@requires_organization_role(["utility-coordinator", "department-manager", "executive"])
def export_projects(request: HttpRequest) -> HttpResponse:
    """Export filtered projects to various formats."""
    try:
        # Get filtered queryset
        queryset = Project.objects.filter(organization=request.user.organization)
        filterset = ProjectFilter(request.POST or request.GET, queryset=queryset, request=request)
        projects = filterset.qs

        format_type = request.POST.get("format", "csv")

        if format_type == "csv":
            # Create CSV response
            response = HttpResponse(content_type="text/csv")
            response["Content-Disposition"] = 'attachment; filename="projects_export.csv"'

            import csv

            writer = csv.writer(response)

            # Write headers
            writer.writerow(
                [
                    "ID",
                    "Name",
                    "Client",
                    "Status",
                    "Priority",
                    "Start Date",
                    "End Date",
                    "Budget",
                    "Progress %",
                    "Created",
                    "Updated",
                ]
            )

            # Write data
            for project in projects:
                writer.writerow(
                    [
                        project.id,
                        project.name,
                        project.client or "",
                        (project.get_status_display() if hasattr(project, "get_status_display") else project.status),
                        (
                            project.get_project_priority_display()
                            if hasattr(project, "get_project_priority_display")
                            else project.project_priority
                        ),
                        (project.start_date.strftime("%Y-%m-%d") if project.start_date else ""),
                        (project.end_date.strftime("%Y-%m-%d") if project.end_date else ""),
                        project.budget or "",
                        (project.completion_percentage if hasattr(project, "completion_percentage") else 0),
                        project.created_at.strftime("%Y-%m-%d %H:%M"),
                        project.updated_at.strftime("%Y-%m-%d %H:%M"),
                    ]
                )

            return response

        elif format_type == "excel":
            # Excel export would require openpyxl
            messages.info(request, _("Excel export coming soon."))

        elif format_type == "pdf":
            # PDF export would require reportlab or similar
            messages.info(request, _("PDF export coming soon."))

    except Exception:
        logger.exception("Error exporting projects")
        messages.error(request, _("Error exporting projects."))

    return redirect("projects:project-list")


# ============================================================================
# SPECIALIZED VIEW CLASSES - Advanced features
# ============================================================================


class TaskDataGridView(TaskListView):
    """Task data grid view with enhanced filtering."""

    template_name = "projects/task_datagrid.html"


class TaskBoardView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Kanban board view for tasks."""

    template_name = "projects/task_board.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project_id = self.kwargs.get("project_id")
            if project_id:
                project = get_object_or_404(
                    Project.objects.filter(organization=self.request.user.organization),
                    pk=project_id,
                )
                tasks = Task.objects.filter(project=project)
                context["project"] = project
            else:
                tasks = Task.objects.filter(project__organization=self.request.user.organization)

            # Group tasks by status
            task_columns = {
                "pending": tasks.filter(status="pending"),
                "in_progress": tasks.filter(status="in_progress"),
                "completed": tasks.filter(status="completed"),
            }

            context.update(
                {
                    "page_title": _("Task Board"),
                    "task_columns": task_columns,
                },
            )
        except Exception:
            logger.exception("Error in TaskBoardView.get_context_data")
            messages.error(self.request, _("Error loading task board."))
        return context


# ============================================================================
# ADDITIONAL SPECIALIZED VIEWS
# ============================================================================


class ProjectMembersView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project team members management view."""

    template_name = "projects/members.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Team - %(name)s") % {"name": project.name},
                    "team_members": project.team_members.filter(is_active=True).select_related("user"),
                    "can_manage_team": project.user_has_access(self.request.user, "manage"),
                },
            )
        except Exception:
            logger.exception("Error in ProjectMembersView.get_context_data")
            messages.error(self.request, _("Error loading project team."))
        return context


class ProjectMapView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project map view with spatial features."""

    template_name = "projects/map.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Map - %(name)s") % {"name": project.name},
                    "utilities": Utility.objects.filter(project=project),
                    "conflicts": Conflict.objects.filter(project=project),
                },
            )
        except Exception:
            logger.exception("Error in ProjectMapView.get_context_data")
            messages.error(self.request, _("Error loading project map."))
        return context


class ProjectStatisticsView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project statistics and KPI view."""

    template_name = "projects/statistics.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.with_financial_calculations().filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Statistics - %(name)s") % {"name": project.name},
                    "task_stats": self._calculate_task_statistics(project),
                    "time_stats": self._calculate_time_statistics(project),
                },
            )
        except Exception:
            logger.exception("Error in ProjectStatisticsView.get_context_data")
            messages.error(self.request, _("Error loading project statistics."))
        return context

    def _calculate_task_statistics(self, project: Project) -> dict[str, Any]:
        """Calculate task-related statistics."""
        from django.db.models import Case, Count, IntegerField, When

        # Use database-level aggregation instead of multiple count() queries
        stats = project.tasks.aggregate(
            total=Count("id"),
            completed=Count(Case(When(status="completed", then=1), output_field=IntegerField())),
            in_progress=Count(Case(When(status="in_progress", then=1), output_field=IntegerField())),
            pending=Count(Case(When(status="pending", then=1), output_field=IntegerField())),
        )

        return {
            "total": stats["total"] or 0,
            "completed": stats["completed"] or 0,
            "in_progress": stats["in_progress"] or 0,
            "pending": stats["pending"] or 0,
        }

    def _calculate_time_statistics(self, project: Project) -> dict[str, Any]:
        """Calculate time-related statistics."""
        try:
            time_entries = TimeEntry.objects.filter(project=project)
            total_minutes = time_entries.aggregate(total=Sum("duration_minutes"))["total"] or 0
            return {
                "total_hours": total_minutes / 60.0,
                "billable_hours": time_entries.filter(billable=True).aggregate(total=Sum("duration_minutes"))["total"]
                or 0 / 60.0,
            }
        except (AttributeError, KeyError, ValueError, TypeError):
            return {"total_hours": 0, "billable_hours": 0}


class ProjectSettingsView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project settings and configuration view."""

    template_name = "projects/settings.html"
    required_roles = ["department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "manage"):
                raise PermissionDenied(_("You do not have permission to manage this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Settings - %(name)s") % {"name": project.name},
                },
            )
        except Exception:
            logger.exception("Error in ProjectSettingsView.get_context_data")
            messages.error(self.request, _("Error loading project settings."))
        return context


# ============================================================================
# ADDITIONAL COMPATIBILITY VIEWS
# ============================================================================


class Project3DView(ProjectMapView):
    """3D project visualization view."""

    template_name = "projects/3d.html"


class ProjectCommentsView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project comments and discussions view."""

    template_name = "projects/comments.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "view"):
                raise PermissionDenied(_("You do not have permission to view this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Comments - %(name)s") % {"name": project.name},
                    "recent_comments": project.get_recent_comments(20),
                },
            )
        except Exception:
            logger.exception("Error in ProjectCommentsView.get_context_data")
            messages.error(self.request, _("Error loading project comments."))
        return context


class ProjectChatView(ProjectCommentsView):
    """Project chat/messaging view."""

    template_name = "projects/chat.html"


class ProjectFileUploadView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project file upload and document management view."""

    template_name = "projects/file_upload.html"
    required_roles = ["utility-coordinator", "department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            project = get_object_or_404(
                Project.objects.filter(organization=self.request.user.organization),
                pk=self.kwargs["project_id"],
            )

            if not project.user_has_access(self.request.user, "edit"):
                raise PermissionDenied(_("You do not have permission to upload files for this project."))

            context.update(
                {
                    "project": project,
                    "page_title": _("Files - %(name)s") % {"name": project.name},
                },
            )
        except Exception:
            logger.exception("Error in ProjectFileUploadView.get_context_data")
            messages.error(self.request, _("Error loading file upload page."))
        return context


class ProjectPortfolioView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Project portfolio overview view."""

    template_name = "projects/portfolio.html"
    required_roles = ["department-manager", "executive"]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            projects = (
                Project.objects.with_counts()
                .filter(organization=self.request.user.organization)
                .order_by("-updated_at")
            )

            context.update(
                {
                    "page_title": _("Project Portfolio"),
                    "projects": projects,
                    "total_projects": projects.count(),
                    "active_projects": projects.filter(rag_status__in=["Green", "Amber", "Red"]).count(),
                },
            )
        except Exception:
            logger.exception("Error in ProjectPortfolioView.get_context_data")
            messages.error(self.request, _("Error loading project portfolio."))
        return context
