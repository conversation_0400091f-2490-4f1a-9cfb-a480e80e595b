"""Task Management Views for Projects App

from requests.exceptions import HTTPError
from celery.decorators import task
from datetime import date
from datetime import datetime
from datetime import timedelta
from django import forms
from django.contrib import messages
from django.contrib.auth import get_user_model
User = get_user_model()
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.db import models
from django.db.models import Prefetch
from django.db.models import Q
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.shortcuts import render
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework import authentication
from rest_framework import status
from rest_framework.response import Response
from typing import Any
import logging
import logging
logger = logging.getLogger(__name__)
import re
import time

This module provides comprehensive task management functionality including CRUD operations,
Kanban boards, timeline management, and HTMX-powered dynamic interactions.

Views:
    - TaskListView: Task listing with filtering and search
    - TaskDetailView: Detailed task view with dependencies and time tracking
    - Task CRUD operations with organization isolation
    - HTMX endpoints for dynamic task interactions
    - Timeline and Gantt chart functionality
    - Time tracking and timesheet integration
"""

import csv
import logging
from datetime import datetime, timedelta
from typing import Any

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.paginator import Paginator
from django.db.models import Prefetch, Q
from django.http import HttpRequest, HttpResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.generic import (
    CreateView,
    DeleteView,
    DetailView,
    ListView,
    TemplateView,
    UpdateView,
)

from apps.analytics.models import Activity
from apps.authentication.models import User
from apps.common.decorators import requires_organization_role
from apps.common.mixins import OrganizationAccessMixin, RoleRequiredMixin
from apps.financial.models import TimeEntry
from apps.projects.forms import CommentForm, TaskDependencyForm, TaskForm
from apps.projects.models import Project, Task

logger = logging.getLogger(__name__)


class TaskListView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, ListView):
    """Task list view with comprehensive filtering, search, and organization isolation.

    Features:
    - Organization-based data isolation
    - Advanced filtering by status, priority, assignee, project
    - Search functionality across title, description, and notes
    - Sorting and pagination
    - Task statistics and analytics
    """

    required_roles = ["stakeholder"]

    model = Task
    template_name = "projects/tasks/task_list.html"
    context_object_name = "tasks"
    paginate_by = 25

    def get_queryset(self):
        """Get optimized Task queryset with organization filtering."""
        organization = self.get_organization()
        if not organization:
            return Task.objects.none()

        # Base queryset with optimization
        queryset = (
            Task.objects.select_related(
                "project__organization",
                "assigned_to",
                "created_by",
            )
            .prefetch_related(
                "dependencies",
                "dependents",
                Prefetch("comments", queryset=Comment.objects.select_related("user")),
            )
            .filter(project__organization=organization)
        )

        # Apply project filter if specified
        project_id = self.request.GET.get("project")
        if project_id:
            try:
                project = Project.objects.get(id=project_id, organization=organization)
                queryset = queryset.filter(project=project)
                self.current_project = project
            except Project.DoesNotExist:
                pass

        # Apply status filter
        status_filter = self.request.GET.get("status")
        if status_filter and status_filter in dict(Task.STATUS_CHOICES):
            queryset = queryset.filter(status=status_filter)

        # Apply priority filter
        priority_filter = self.request.GET.get("priority")
        if priority_filter and priority_filter in dict(Task.PRIORITY_CHOICES):
            queryset = queryset.filter(priority=priority_filter)

        # Apply assignee filter
        assignee_filter = self.request.GET.get("assignee")
        if assignee_filter:
            try:
                assignee = User.objects.get(id=assignee_filter)
                queryset = queryset.filter(assigned_to=assignee)
            except User.DoesNotExist:
                pass

        # Apply search
        search_query = self.request.GET.get("search", "").strip()
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(notes__icontains=search_query),
            )

        # Apply date filters
        due_date_filter = self.request.GET.get("due_date")
        if due_date_filter == "overdue":
            queryset = queryset.filter(
                due_date__lt=timezone.now().date(),
                status__in=["pending", "in_progress"],
            )
        elif due_date_filter == "this_week":
            week_start = timezone.now().date()
            week_end = week_start + timedelta(days=7)
            queryset = queryset.filter(due_date__range=[week_start, week_end])
        elif due_date_filter == "next_week":
            week_start = timezone.now().date() + timedelta(days=7)
            week_end = week_start + timedelta(days=7)
            queryset = queryset.filter(due_date__range=[week_start, week_end])

        # Apply sorting
        sort_by = self.request.GET.get("sort", "-created_at")
        valid_sorts = [
            "title",
            "-title",
            "priority",
            "-priority",
            "due_date",
            "-due_date",
            "status",
            "-status",
            "created_at",
            "-created_at",
            "progress_percentage",
            "-progress_percentage",
        ]
        return queryset.order_by(sort_by) if sort_by in valid_sorts else queryset.order_by("-created_at")

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add comprehensive context for task management."""
        context = super().get_context_data(**kwargs)
        organization = self.get_organization()

        if not organization:
            return context

        # Current filters
        context["current_filters"] = {
            "project": self.request.GET.get("project", ""),
            "status": self.request.GET.get("status", ""),
            "priority": self.request.GET.get("priority", ""),
            "assignee": self.request.GET.get("assignee", ""),
            "search": self.request.GET.get("search", ""),
            "due_date": self.request.GET.get("due_date", ""),
            "sort": self.request.GET.get("sort", "-created_at"),
        }

        # Filter options
        context.update(
            {
                "status_choices": Task.STATUS_CHOICES,
                "priority_choices": Task.PRIORITY_CHOICES,
                "projects": Project.objects.filter(organization=organization).order_by("name"),
                "assignees": User.objects.filter(project_memberships__project__organization=organization)
                .distinct()
                .order_by("first_name", "last_name"),
            },
        )

        # Current project if filtered
        if hasattr(self, "current_project"):
            context["current_project"] = self.current_project

        # Task statistics
        all_tasks = Task.objects.filter(project__organization=organization)
        context.update(
            {
                "total_tasks": all_tasks.count(),
                "pending_tasks": all_tasks.filter(status="pending").count(),
                "in_progress_tasks": all_tasks.filter(status="in_progress").count(),
                "completed_tasks": all_tasks.filter(status="completed").count(),
                "overdue_tasks": all_tasks.filter(
                    due_date__lt=timezone.now().date(),
                    status__in=["pending", "in_progress"],
                ).count(),
            },
        )

        return context


class TaskDetailView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, DetailView):
    """Detailed task view with dependencies, time tracking, and activity history.

    Features:
    - Comprehensive task details
    - Dependency management
    - Time tracking integration
    - Comment system
    - Activity history
    - File attachments
    """

    required_roles = ["stakeholder"]

    model = Task
    template_name = "projects/tasks/task_detail.html"
    context_object_name = "task"

    def get_queryset(self):
        """Ensure task belongs to user's organization."""
        organization = self.get_organization()
        if not organization:
            return Task.objects.none()

        return (
            Task.objects.select_related(
                "project__organization",
                "assigned_to",
                "created_by",
            )
            .prefetch_related(
                "dependencies",
                "dependents",
                Prefetch("comments", queryset=Comment.objects.select_related("user")),
                "time_entries",
            )
            .filter(project__organization=organization)
        )

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add comprehensive context for task detail."""
        context = super().get_context_data(**kwargs)
        task = self.object

        # Time tracking statistics
        time_entries = task.time_entries.all()
        total_time = sum([entry.duration_minutes for entry in time_entries]) / 60.0 if time_entries else 0

        context.update(
            {
                "time_entries": time_entries.order_by("-start_time")[:10],
                "total_time_logged": total_time,
                "time_remaining": (task.estimated_hours or 0) - total_time,
                "time_efficiency": (
                    (total_time / task.estimated_hours * 100)
                    if task.estimated_hours and task.estimated_hours > 0
                    else 0
                ),
            },
        )

        # Dependencies
        context.update(
            {
                "dependencies": task.dependencies.select_related("project").order_by("title"),
                "dependents": task.dependents.select_related("project").order_by("title"),
                "blocking_dependencies": task.dependencies.exclude(status="completed"),
            },
        )

        # Comments and activity
        context.update(
            {
                "comments": task.comments.select_related("user").order_by("-created_at"),
                "comment_form": CommentForm(),
                "recent_activities": Activity.objects.filter(target_model="Task", target_id=task.id).order_by(
                    "-timestamp",
                )[:20],
            },
        )

        # Task progression
        if task.due_date:
            days_until_due = (task.due_date - timezone.now().date()).days
            context.update(
                {
                    "days_until_due": days_until_due,
                    "is_overdue": days_until_due < 0 and task.status != "completed",
                    "is_due_soon": 0 <= days_until_due <= 3 and task.status != "completed",
                },
            )

        # Team members who can be assigned
        context["assignable_users"] = User.objects.filter(
            project_memberships__project=task.project,
            project_memberships__is_active=True,
        ).distinct()

        return context


class TaskCreateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, CreateView):
    """Create new task with organization and project validation."""

    required_roles = ["utility-coordinator"]

    model = Task
    form_class = TaskForm
    template_name = "projects/tasks/task_create.html"

    def get_form_kwargs(self):
        """Add organization context to form."""
        kwargs = super().get_form_kwargs()
        kwargs["organization"] = self.get_organization()

        # If project_id is in URL, pre-select it
        project_id = self.kwargs.get("project_id")
        if project_id:
            try:
                project = Project.objects.get(id=project_id, organization=self.get_organization())
                kwargs["initial"] = kwargs.get("initial", {})
                kwargs["initial"]["project"] = project
            except Project.DoesNotExist:
                pass

        return kwargs

    def form_valid(self, form):
        """Set created_by and create activity log."""
        form.instance.created_by = self.request.user
        response = super().form_valid(form)

        # Create activity log
        Activity.objects.create(
            user=self.request.user,
            action="created",
            target_model="Task",
            target_id=self.object.id,
            target_name=self.object.title,
            description=f"Created task: {self.object.title}",
        )

        messages.success(self.request, f'Task "{self.object.title}" created successfully.')
        return response

    def get_success_url(self):
        """Return to project task list or task detail."""
        if self.object.project:
            return reverse("projects:task_list") + f"?project={self.object.project.id}"
        return reverse("projects:task_detail", kwargs={"pk": self.object.pk})


class TaskUpdateView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, UpdateView):
    """Update task with organization validation."""

    required_roles = ["utility-coordinator"]

    model = Task
    form_class = TaskForm
    template_name = "projects/tasks/task_update.html"

    def get_queryset(self):
        """Ensure task belongs to user's organization."""
        organization = self.get_organization()
        if not organization:
            return Task.objects.none()
        return Task.objects.filter(project__organization=organization)

    def get_form_kwargs(self):
        """Add organization context to form."""
        kwargs = super().get_form_kwargs()
        kwargs["organization"] = self.get_organization()
        return kwargs

    def form_valid(self, form):
        """Create activity log for update."""
        old_values = {}
        new_values = {}

        # Track changes
        for field in form.changed_data:
            old_values[field] = getattr(self.object, field, None)
            new_values[field] = form.cleaned_data.get(field)

        response = super().form_valid(form)

        # Create activity log
        if form.changed_data:
            changes = ", ".join(form.changed_data)
            Activity.objects.create(
                user=self.request.user,
                action="updated",
                target_model="Task",
                target_id=self.object.id,
                target_name=self.object.title,
                description=f"Updated task fields: {changes}",
            )

        messages.success(self.request, f'Task "{self.object.title}" updated successfully.')
        return response

    def get_success_url(self):
        """Return to task detail page."""
        return reverse("projects:task_detail", kwargs={"pk": self.object.pk})


class TaskDeleteView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, DeleteView):
    """Delete task with organization validation."""

    required_roles = ["utility-coordinator"]

    model = Task
    template_name = "projects/tasks/task_confirm_delete.html"

    def get_queryset(self):
        """Ensure task belongs to user's organization."""
        organization = self.get_organization()
        if not organization:
            return Task.objects.none()
        return Task.objects.filter(project__organization=organization)

    def delete(self, request, *args, **kwargs):
        """Create activity log before deletion."""
        task = self.get_object()
        task_title = task.title

        # Create activity log
        Activity.objects.create(
            user=request.user,
            action="deleted",
            target_model="Task",
            target_id=task.id,
            target_name=task_title,
            description=f"Deleted task: {task_title}",
        )

        messages.success(request, f'Task "{task_title}" deleted successfully.')
        return super().delete(request, *args, **kwargs)

    def get_success_url(self):
        """Return to task list, filtered by project if available."""
        project_id = self.request.POST.get("project_id") or self.request.GET.get("project_id")
        if project_id:
            return reverse("projects:task_list") + f"?project={project_id}"
        return reverse("projects:task_list")


# HTMX Views for Dynamic Interactions


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def task_quick_create_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for quick task creation."""
    if request.method != "POST":
        return HttpResponse("Method not allowed", status=405)

    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    # Get form data
    title = request.POST.get("title", "").strip()
    project_id = request.POST.get("project_id")
    priority = request.POST.get("priority", "medium")

    if not title:
        return render(
            request,
            "projects/tasks/partials/task_create_form.html",
            {"error": _("Title is required"), "form_data": request.POST},
        )

    if not project_id:
        return render(
            request,
            "projects/tasks/partials/task_create_form.html",
            {"error": _("Project is required"), "form_data": request.POST},
        )

    try:
        project = Project.objects.get(id=project_id, organization=organization)

        # Create task
        task = Task.objects.create(
            title=title,
            project=project,
            priority=priority,
            assigned_to=request.user,
            created_by=request.user,
            status="pending",
        )

        # Create activity log
        Activity.objects.create(
            user=request.user,
            action="created",
            target_model="Task",
            target_id=task.id,
            target_name=task.title,
            description=f"Quick created task: {task.title}",
        )

        return render(
            request,
            "projects/tasks/partials/task_item.html",
            {"task": task, "created": True},
        )

    except Project.DoesNotExist:
        return render(
            request,
            "projects/tasks/partials/task_create_form.html",
            {"error": _("Project not found"), "form_data": request.POST},
        )
    except (ConnectionError, TimeoutError, HTTPError) as e:
        logger.error(f"Error creating task: {e}")
        return render(
            request,
            "projects/tasks/partials/task_create_form.html",
            {"error": _("Error creating task"), "form_data": request.POST},
        )


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def task_update_field_htmx(request: HttpRequest, task_id: int) -> HttpResponse:
    """HTMX endpoint for updating individual task fields."""
    if request.method != "POST":
        return HttpResponse("Method not allowed", status=405)

    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    try:
        task = get_object_or_404(
            Task.objects.select_related("project"),
            id=task_id,
            project__organization=organization,
        )

        field = request.POST.get("field")
        value = request.POST.get("value")

        # Update specific field
        updated = False

        if field == "title" and value:
            task.title = value.strip()
            updated = True
        elif field == "status" and value in dict(Task.STATUS_CHOICES):
            old_status = task.status
            task.status = value
            if value == "completed" and old_status != "completed":
                task.completed_at = timezone.now()
                task.progress_percentage = 100
            updated = True
        elif field == "priority" and value in dict(Task.PRIORITY_CHOICES):
            task.priority = value
            updated = True
        elif field == "progress_percentage":
            try:
                progress = int(value)
                if 0 <= progress <= 100:
                    task.progress_percentage = progress
                    if progress == 100 and task.status != "completed":
                        task.status = "completed"
                        task.completed_at = timezone.now()
                    updated = True
            except (ValueError, TypeError):
                pass
        elif field == "due_date":
            if value:
                try:
                    task.due_date = datetime.strptime(value, "%Y-%m-%d").date()
                    updated = True
                except ValueError:
                    pass
            else:
                task.due_date = None
                updated = True
        elif field == "assigned_to":
            if value:
                try:
                    # Add organization filter to prevent IDOR - only get users who are project members
                    assignee = (
                        User.objects.filter(
                            project_memberships__project=task.project,
                            project_memberships__is_active=True,
                            id=value,
                        )
                        .distinct()
                        .first()
                    )

                    if assignee:
                        task.assigned_to = assignee
                        updated = True
                except User.DoesNotExist:
                    pass
            else:
                task.assigned_to = None
                updated = True
        elif field == "estimated_hours":
            try:
                hours = float(value) if value else None
                if hours is None or hours >= 0:
                    task.estimated_hours = hours
                    updated = True
            except (ValueError, TypeError):
                pass

        if updated:
            task.save()

            # Create activity log
            Activity.objects.create(
                user=request.user,
                action="updated",
                target_model="Task",
                target_id=task.id,
                target_name=task.title,
                description=f"Updated task {field}: {value}",
            )

            return render(
                request,
                "projects/tasks/partials/task_field_updated.html",
                {"task": task, "field": field, "success": True},
            )
        return render(
            request,
            "projects/tasks/partials/task_field_updated.html",
            {"task": task, "field": field, "error": _("Invalid value")},
        )

    except (ConnectionError, TimeoutError, HTTPError) as e:
        logger.error(f"Error updating task field: {e}")
        return HttpResponse(_("Error updating task"), status=500)


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def task_filter_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for filtering tasks with real-time updates."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    # Base queryset
    queryset = Task.objects.select_related("project", "assigned_to", "created_by").filter(
        project__organization=organization,
    )

    # Apply filters
    filters = {}

    project_filter = request.GET.get("project")
    if project_filter:
        try:
            project = Project.objects.get(id=project_filter, organization=organization)
            filters["project"] = project
        except Project.DoesNotExist:
            pass

    status_filter = request.GET.get("status")
    if status_filter and status_filter in dict(Task.STATUS_CHOICES):
        filters["status"] = status_filter

    priority_filter = request.GET.get("priority")
    if priority_filter and priority_filter in dict(Task.PRIORITY_CHOICES):
        filters["priority"] = priority_filter

    assignee_filter = request.GET.get("assignee")
    if assignee_filter:
        try:
            assignee = User.objects.get(id=assignee_filter)
            filters["assigned_to"] = assignee
        except User.DoesNotExist:
            pass

    # Apply all filters
    if filters:
        queryset = queryset.filter(**filters)

    # Apply search
    search_query = request.GET.get("search", "").strip()
    if search_query:
        queryset = queryset.filter(Q(title__icontains=search_query) | Q(description__icontains=search_query))

    # Apply sorting
    sort_by = request.GET.get("sort", "-created_at")
    valid_sorts = [
        "title",
        "-title",
        "priority",
        "-priority",
        "due_date",
        "-due_date",
        "status",
        "-status",
        "created_at",
        "-created_at",
    ]
    if sort_by in valid_sorts:
        queryset = queryset.order_by(sort_by)

    # Pagination
    paginator = Paginator(queryset, 25)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    context = {
        "tasks": page_obj,
        "page_obj": page_obj,
        "current_filters": {
            "project": project_filter,
            "status": status_filter,
            "priority": priority_filter,
            "assignee": assignee_filter,
            "search": search_query,
            "sort": sort_by,
        },
    }

    return render(request, "projects/tasks/partials/task_filtered_list.html", context)


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def task_dependencies_htmx(request: HttpRequest, task_id: int) -> HttpResponse:
    """HTMX endpoint for managing task dependencies."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    task = get_object_or_404(
        Task.objects.select_related("project"),
        id=task_id,
        project__organization=organization,
    )

    if request.method == "POST":
        # Add dependency
        dependency_id = request.POST.get("dependency_id")
        if dependency_id:
            try:
                dependency = Task.objects.get(id=dependency_id, project__organization=organization)

                # Check for circular dependencies
                if not _would_create_circular_dependency(task, dependency):
                    task.dependencies.add(dependency)

                    # Create activity log
                    Activity.objects.create(
                        user=request.user,
                        action="updated",
                        target_model="Task",
                        target_id=task.id,
                        target_name=task.title,
                        description=f"Added dependency: {dependency.title}",
                    )

                    messages.success(request, _("Dependency added successfully."))
                else:
                    messages.error(
                        request,
                        _("Cannot add dependency: would create circular reference."),
                    )

            except Task.DoesNotExist:
                messages.error(request, _("Dependency task not found."))

    elif request.method == "DELETE":
        # Remove dependency
        dependency_id = request.GET.get("dependency_id")
        if dependency_id:
            try:
                dependency = Task.objects.get(id=dependency_id)
                task.dependencies.remove(dependency)

                # Create activity log
                Activity.objects.create(
                    user=request.user,
                    action="updated",
                    target_model="Task",
                    target_id=task.id,
                    target_name=task.title,
                    description=f"Removed dependency: {dependency.title}",
                )

                messages.success(request, _("Dependency removed successfully."))
            except Task.DoesNotExist:
                messages.error(request, _("Dependency task not found."))

    # Get available tasks for dependencies (excluding self and current dependencies)
    available_tasks = (
        Task.objects.filter(project__organization=organization)
        .exclude(id=task.id)
        .exclude(id__in=task.dependencies.values_list("id", flat=True))
        .select_related("project")
        .order_by("project__name", "title")
    )

    context = {
        "task": task,
        "dependencies": task.dependencies.select_related("project").order_by("title"),
        "dependents": task.dependents.select_related("project").order_by("title"),
        "available_tasks": available_tasks,
        "dependency_form": TaskDependencyForm(),
    }

    return render(request, "projects/tasks/partials/task_dependencies.html", context)


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def task_timeline_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for task timeline/Gantt chart view."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    # Get project filter
    project_id = request.GET.get("project")
    if project_id:
        try:
            project = Project.objects.get(id=project_id, organization=organization)
            tasks = Task.objects.filter(project=project)
        except Project.DoesNotExist:
            tasks = Task.objects.none()
    else:
        tasks = Task.objects.filter(project__organization=organization)

    # Get tasks with timeline data
    tasks = (
        tasks.select_related("project", "assigned_to")
        .filter(start_date__isnull=False, due_date__isnull=False)
        .order_by("start_date")
    )

    # Calculate timeline metrics
    if tasks.exists():
        earliest_start = min(task.start_date for task in tasks if task.start_date)
        latest_end = max(task.due_date for task in tasks if task.due_date)
        total_duration = (latest_end - earliest_start).days
    else:
        earliest_start = latest_end = timezone.now().date()
        total_duration = 0

    context = {
        "tasks": tasks,
        "earliest_start": earliest_start,
        "latest_end": latest_end,
        "total_duration": total_duration,
        "current_date": timezone.now().date(),
    }

    return render(request, "projects/tasks/partials/task_timeline.html", context)


# Time Tracking Views


@login_required
@requires_organization_role(["executive", "department-manager", "utility-coordinator"])
def task_time_entry_htmx(request: HttpRequest, task_id: int) -> HttpResponse:
    """HTMX endpoint for adding time entries to tasks."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        return HttpResponse(_("No organization access"), status=403)

    task = get_object_or_404(
        Task.objects.select_related("project"),
        id=task_id,
        project__organization=organization,
    )

    if request.method == "POST":
        # Create time entry
        hours = request.POST.get("hours")
        description = request.POST.get("description", "")
        billable = request.POST.get("billable") == "on"

        try:
            duration_minutes = float(hours) * 60 if hours else 0

            time_entry = TimeEntry.objects.create(
                user=request.user,
                project=task.project,
                task=task,
                duration_minutes=duration_minutes,
                description=description,
                billable=billable,
                start_time=timezone.now(),
            )

            # Create activity log
            Activity.objects.create(
                user=request.user,
                action="created",
                target_model="TimeEntry",
                target_id=time_entry.id,
                target_name=f"Time entry for {task.title}",
                description=f"Logged {hours} hours on task: {task.title}",
            )

            messages.success(request, _("Time entry added successfully."))

            return render(
                request,
                "projects/tasks/partials/task_time_entries.html",
                {"task": task, "time_entry_created": True},
            )

        except (ValueError, TypeError):
            messages.error(request, _("Invalid hours value."))
        except (ConnectionError, TimeoutError, HTTPError) as e:
            logger.error(f"Error creating time entry: {e}")
            messages.error(request, _("Error creating time entry."))

    # Get existing time entries
    time_entries = TimeEntry.objects.filter(task=task).select_related("user").order_by("-start_time")

    context = {
        "task": task,
        "time_entries": time_entries,
        "total_time": sum(entry.duration_minutes for entry in time_entries) / 60.0,
    }

    return render(request, "projects/tasks/partials/task_time_entries.html", context)


# Export Functions


@login_required
@requires_organization_role(["executive", "department-manager"])
def task_export_csv(request: HttpRequest) -> HttpResponse:
    """Export tasks to CSV format with filtering support."""
    organization = getattr(request.user, "organization", None)
    if not organization:
        raise PermissionDenied(_("No organization access"))

    # Create CSV response
    response = HttpResponse(content_type="text/csv")
    response["Content-Disposition"] = (
        f'attachment; filename="tasks_{organization.slug}_{timezone.now().strftime("%Y%m%d")}.csv"'
    )

    writer = csv.writer(response)

    # Write header
    writer.writerow(
        [
            "ID",
            "Title",
            "Description",
            "Project",
            "Status",
            "Priority",
            "Assigned To",
            "Created By",
            "Due Date",
            "Start Date",
            "Progress %",
            "Estimated Hours",
            "Actual Hours",
            "Created At",
            "Updated At",
        ],
    )

    # Get tasks with filters
    tasks = Task.objects.filter(project__organization=organization).select_related(
        "project",
        "assigned_to",
        "created_by",
    )

    # Apply filters if provided
    project_filter = request.GET.get("project")
    if project_filter:
        try:
            project = Project.objects.get(id=project_filter, organization=organization)
            tasks = tasks.filter(project=project)
        except Project.DoesNotExist:
            pass

    status_filter = request.GET.get("status")
    if status_filter and status_filter in dict(Task.STATUS_CHOICES):
        tasks = tasks.filter(status=status_filter)

    # Write data
    for task in tasks:
        # Calculate actual hours from time entries
        actual_hours = (
            sum(entry.duration_minutes for entry in task.time_entries.all()) / 60.0
            if hasattr(task, "time_entries")
            else 0
        )

        writer.writerow(
            [
                task.id,
                task.title,
                task.description or "",
                task.project.name if task.project else "",
                task.get_status_display(),
                task.get_priority_display(),
                task.assigned_to.get_full_name() if task.assigned_to else "",
                task.created_by.get_full_name() if task.created_by else "",
                task.due_date.strftime("%Y-%m-%d") if task.due_date else "",
                task.start_date.strftime("%Y-%m-%d") if task.start_date else "",
                task.progress_percentage or 0,
                task.estimated_hours or "",
                actual_hours or "",
                task.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                task.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            ],
        )

    return response


# Utility Functions


def _would_create_circular_dependency(task: Task, dependency: Task) -> bool:
    """Check if adding a dependency would create a circular reference."""

    def has_path(start: Task, end: Task, visited: set | None = None) -> bool:
        """Check if there's a path from start to end through dependencies."""
        if visited is None:
            visited = set()

        if start.id in visited:
            return False

        if start == end:
            return True

        visited.add(start.id)

        return any(has_path(dep, end, visited.copy()) for dep in start.dependencies.all())

    # Check if dependency already has a path to task
    return has_path(dependency, task)


# Legacy Compatibility Views


class TaskBoardView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Kanban board view for tasks."""

    required_roles = ["stakeholder"]

    template_name = "projects/tasks/task_board.html"

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add Kanban board context."""
        context = super().get_context_data(**kwargs)
        organization = self.get_organization()

        if not organization:
            return context

        # Get project if specified
        project_id = self.request.GET.get("project")
        if project_id:
            try:
                project = Project.objects.get(id=project_id, organization=organization)
                tasks = Task.objects.filter(project=project)
                context["current_project"] = project
            except Project.DoesNotExist:
                tasks = Task.objects.none()
        else:
            tasks = Task.objects.filter(project__organization=organization)

        # Group tasks by status for Kanban columns
        context.update(
            {
                "pending_tasks": tasks.filter(status="pending").select_related("assigned_to", "project"),
                "in_progress_tasks": tasks.filter(status="in_progress").select_related("assigned_to", "project"),
                "completed_tasks": tasks.filter(status="completed").select_related("assigned_to", "project"),
                "projects": Project.objects.filter(organization=organization).order_by("name"),
            },
        )

        return context


class TaskDataGridView(LoginRequiredMixin, RoleRequiredMixin, OrganizationAccessMixin, TemplateView):
    """Data grid view for tasks with advanced filtering."""

    required_roles = ["stakeholder"]

    template_name = "projects/tasks/task_datagrid.html"

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add data grid context."""
        context = super().get_context_data(**kwargs)
        organization = self.get_organization()

        if not organization:
            return context

        context.update(
            {
                "projects": Project.objects.filter(organization=organization).order_by("name"),
                "assignees": User.objects.filter(project_memberships__project__organization=organization)
                .distinct()
                .order_by("first_name", "last_name"),
                "status_choices": Task.STATUS_CHOICES,
                "priority_choices": Task.PRIORITY_CHOICES,
            },
        )

        return context


@login_required
@requires_organization_role(["stakeholder"])
def tasks_view(request):
    """Simple tasks view function for backward compatibility."""
    # Use the TaskListView for the actual functionality
    task_list_view = TaskListView.as_view()
    return task_list_view(request)


# Additional task management views can be added here
# TaskEditView alias for backward compatibility
TaskEditView = TaskUpdateView
