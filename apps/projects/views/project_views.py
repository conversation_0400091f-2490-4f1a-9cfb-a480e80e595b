"""Project Management Views with Django 5.2 Compliance

Comprehensive project management views providing:
- Project CRUD operations with organization isolation
- Financial analytics and reporting
- Team management and collaboration
- Timeline and analytics interfaces
- HTMX-powered dynamic interactions
- Role-based access control
"""

import logging
from typing import Any

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.db.models import Count, Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.views.generic import (
    <PERSON><PERSON><PERSON>ie<PERSON>,
    DetailView,
    <PERSON>View,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    UpdateView,
)

from apps.authentication.models import Organization
from apps.common.mixins import OrganizationAccessMixin, RoleRequiredMixin
from apps.infrastructure.models import CoordinateSystem
from apps.messaging.models import Comment
from apps.projects.forms.project_forms import ProjectForm
from apps.projects.models import Project, ProjectActivity

User = get_user_model()
logger = logging.getLogger(__name__)


def _get_user_organization(user: User) -> Organization:
    """Get user's organization with validation.

    Args:
    ----
        user: The authenticated user

    Returns:
    -------
        Organization: User's organization

    Raises:
    ------
        PermissionDenied: If user has no organization access

    """
    try:
        if hasattr(user, "organization") and user.organization:
            return user.organization

        # Try to get from memberships
        memberships = getattr(user, "org_memberships", None)
        if memberships and memberships.filter(is_active=True).exists():
            return memberships.filter(is_active=True).first().organization

        raise PermissionDenied(_("Organization membership required for project access"))
    except Exception:
        logger.exception("Error getting user organization")
        raise PermissionDenied(_("Unable to determine organization access"))


def _check_project_access(user: User, project: Project, required_level: str = "view") -> bool:
    """Check if user has required access to project.

    Args:
    ----
        user: The authenticated user
        project: The project to check access for
        required_level: Required access level ('view', 'edit', 'manage')

    Returns:
    -------
        bool: True if user has access

    """
    try:
        organization = _get_user_organization(user)

        # Check organization match
        if project.organization != organization:
            return False

        # Check if user is project creator or utility coordinator
        if project.created_by == user or getattr(project, "utility_coordinator", None) == user:
            return True

        # Check team membership
        if hasattr(project, "team_members"):
            team_member = project.team_members.filter(user=user, is_active=True).first()
            if team_member and (
                required_level == "view"
                or (required_level == "edit" and team_member.role in ["manager", "coordinator", "contributor"])
                or (required_level == "manage" and team_member.role == "manager")
            ):
                return True

        # Check organization role level
        if hasattr(user, "org_memberships"):
            membership = user.org_memberships.filter(organization=organization, is_active=True).first()
            if membership and hasattr(membership, "role"):
                role_level = getattr(membership.role, "level", 100)
                if role_level <= 30:  # Manager level or higher
                    return True

        return False

    except Exception:
        logger.exception("Error checking project access")
        return False


# ============================================================================
# DASHBOARD VIEWS
# ============================================================================


@login_required
def projects_dashboard(request: HttpRequest) -> HttpResponse:
    """Main projects dashboard with comprehensive analytics and organization filtering"""
    try:
        organization = _get_user_organization(request.user)

        # Get user's accessible projects
        user_projects = (
            Project.objects.filter(organization=organization)
            .select_related("created_by", "utility_coordinator")
            .prefetch_related("team_members", "tasks")
            .order_by("-updated_at")[:20]
        )

        # Filter by access permissions
        accessible_projects = [p for p in user_projects if _check_project_access(request.user, p)]

        context = {
            "organization": organization,
            "user_projects": accessible_projects,
            "now": timezone.now(),
            "today": timezone.now().date(),
        }

        return render(request, "projects/dashboard.html", context)

    except PermissionDenied as e:
        logger.warning(f"Dashboard access denied: {e}")
        return render(request, "projects/dashboard.html", {"error": str(e)})
    except Exception:
        logger.exception("Error in projects dashboard")
        return render(request, "projects/dashboard.html", {"error": _("Dashboard error")})


# ============================================================================
# PROJECT CRUD VIEWS
# ============================================================================


class ProjectListView(LoginRequiredMixin, RoleRequiredMixin, ListView, OrganizationAccessMixin):
    """List all projects with organization filtering and search"""

    model = Project
    template_name = "projects/project_list.html"
    context_object_name = "projects"
    paginate_by = 25
    required_roles = ["stakeholder"]  # Minimum role required

    def get_queryset(self):
        """Get organization-filtered and optimized project queryset"""
        try:
            # Get base queryset with organization filtering
            queryset = (
                Project.objects.filter(organization=self.organization)
                .select_related("created_by", "utility_coordinator", "organization")
                .prefetch_related("team_members", "tasks")
                .annotate(
                    task_count=Count("tasks"),
                    utility_count=Count("utilities", distinct=True),
                    conflict_count=Count("conflicts", distinct=True),
                )
            )

            # Apply search filter
            search = self.request.GET.get("search", "").strip()
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) | Q(client__icontains=search) | Q(description__icontains=search),
                )

            # Apply status filter
            status = self.request.GET.get("status")
            if status and status != "all":
                queryset = queryset.filter(rag_status=status)

            # Apply sorting
            sort_by = self.request.GET.get("sort", "-updated_at")
            valid_sorts = ["-updated_at", "name", "-created_at", "rag_status", "client"]
            if sort_by in valid_sorts:
                queryset = queryset.order_by(sort_by)

            return queryset

        except Exception:
            logger.exception("Error building project list queryset")
            return Project.objects.none()

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add project statistics and filter options"""
        context = super().get_context_data(**kwargs)

        try:
            # Calculate project statistics
            all_projects = Project.objects.filter(organization=self.organization)

            context.update(
                {
                    "organization": self.organization,
                    "project_stats": {
                        "total_projects": all_projects.count(),
                        "active_projects": all_projects.exclude(rag_status="Complete").count(),
                        "completed_projects": all_projects.filter(rag_status="Complete").count(),
                        "red_status_projects": all_projects.filter(rag_status="Red").count(),
                    },
                    "filter_options": {
                        "status_choices": [
                            ("all", _("All Statuses")),
                            ("Green", _("Green")),
                            ("Amber", _("Amber")),
                            ("Red", _("Red")),
                            ("Complete", _("Complete")),
                        ],
                    },
                    "current_filters": {
                        "search": self.request.GET.get("search", ""),
                        "status": self.request.GET.get("status", "all"),
                        "sort": self.request.GET.get("sort", "-updated_at"),
                    },
                },
            )

        except Exception:
            logger.exception("Error building project list context")
            context["project_stats"] = {}

        return context


class ProjectDetailView(LoginRequiredMixin, RoleRequiredMixin, DetailView, OrganizationAccessMixin):
    """Detailed project view with comprehensive data and analytics"""

    model = Project
    template_name = "projects/project_detail.html"
    context_object_name = "project"
    required_roles = ["stakeholder"]

    def get_object(self, queryset=None) -> Project:
        """Get project with comprehensive prefetching and access validation"""
        try:
            project = get_object_or_404(
                Project.objects.select_related("organization", "created_by", "utility_coordinator").prefetch_related(
                    "team_members__user",
                    "tasks__assigned_to",
                    "utilities",
                    "conflicts",
                ),
                pk=self.kwargs["pk"],
                organization=self.organization,
            )

            # Check project access
            if not _check_project_access(self.request.user, project, "view"):
                raise PermissionDenied(_("You don't have access to this project"))

            return project

        except PermissionDenied:
            raise
        except Exception:
            logger.exception("Error getting project detail")
            raise

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        """Add comprehensive project analytics and related data"""
        context = super().get_context_data(**kwargs)
        project = self.object

        try:
            # Get project statistics
            tasks = project.tasks.all()

            context.update(
                {
                    "organization": self.organization,
                    "project_stats": {
                        "total_tasks": tasks.count(),
                        "completed_tasks": tasks.filter(status="completed").count(),
                        "active_tasks": tasks.exclude(status="completed").count(),
                        "utility_count": project.utilities.count(),
                        "conflict_count": project.conflicts.count(),
                    },
                    "recent_activities": ProjectActivity.objects.filter(project=project)
                    .select_related("user")
                    .order_by("-timestamp")[:10],
                    "team_members": project.team_members.filter(is_active=True).select_related("user"),
                    "user_can_edit": _check_project_access(self.request.user, project, "edit"),
                    "user_can_manage": _check_project_access(self.request.user, project, "manage"),
                },
            )

            # Calculate completion rate
            total_tasks = context["project_stats"]["total_tasks"]
            completed_tasks = context["project_stats"]["completed_tasks"]
            context["project_stats"]["completion_rate"] = (
                (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            )

        except Exception:
            logger.exception("Error building project detail context")
            context["project_stats"] = {}

        return context


class ProjectCreateView(LoginRequiredMixin, RoleRequiredMixin, CreateView, OrganizationAccessMixin):
    """Create new project with organization context"""

    model = Project
    form_class = ProjectForm
    template_name = "projects/project_form.html"
    required_roles = ["utility-coordinator"]

    def form_valid(self, form):
        """Set organization and creator before saving"""
        try:
            form.instance.organization = self.organization
            form.instance.created_by = self.request.user

            # Set project manager if not specified
            if not form.instance.utility_coordinator:
                form.instance.utility_coordinator = self.request.user

            response = super().form_valid(form)

            # Log project creation
            logger.info(f"Project created: {self.object.name} by {self.request.user}")

            return response

        except Exception:
            logger.exception("Error creating project")
            form.add_error(None, _("Error creating project. Please try again."))
            return self.form_invalid(form)

    def get_success_url(self):
        return reverse("projects:project-detail", kwargs={"pk": self.object.pk})


class ProjectUpdateView(LoginRequiredMixin, RoleRequiredMixin, UpdateView, OrganizationAccessMixin):
    """Update existing project with validation"""

    model = Project
    form_class = ProjectForm
    template_name = "projects/project_form.html"
    required_roles = ["utility-coordinator"]

    def get_object(self, queryset=None) -> Project:
        """Get project with access validation"""
        try:
            project = get_object_or_404(Project, pk=self.kwargs["pk"], organization=self.organization)

            # Check edit access
            if not _check_project_access(self.request.user, project, "edit"):
                raise PermissionDenied(_("You don't have permission to edit this project"))

            return project

        except PermissionDenied:
            raise
        except Exception:
            logger.exception("Error getting project for update")
            raise

    def form_valid(self, form):
        """Update modification tracking"""
        try:
            form.instance.updated_at = timezone.now()
            response = super().form_valid(form)

            # Log project update
            logger.info(f"Project updated: {self.object.name} by {self.request.user}")

            return response

        except Exception:
            logger.exception("Error updating project")
            form.add_error(None, _("Error updating project. Please try again."))
            return self.form_invalid(form)

    def get_success_url(self):
        return reverse("projects:project-detail", kwargs={"pk": self.object.pk})


# ============================================================================
# SPECIALIZED PROJECT VIEWS
# ============================================================================


class ProjectTimelineView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project timeline and Gantt chart interface"""

    template_name = "projects/project_timeline.html"
    required_roles = ["stakeholder"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        try:
            project = get_object_or_404(Project, pk=self.kwargs["project_id"], organization=self.organization)

            # Check access
            if not _check_project_access(self.request.user, project, "view"):
                raise PermissionDenied(_("Access denied to project timeline"))

            # Get timeline data
            tasks = project.tasks.select_related("assigned_to").order_by("start_date", "created_at")

            context.update(
                {
                    "project": project,
                    "tasks": tasks,
                    "organization": self.organization,
                    "view_modes": [
                        {"value": "gantt", "label": _("Gantt Chart")},
                        {"value": "timeline", "label": _("Timeline")},
                        {"value": "calendar", "label": _("Calendar")},
                        {"value": "kanban", "label": _("Kanban")},
                    ],
                },
            )

        except PermissionDenied:
            raise
        except Exception:
            logger.exception("Error building timeline context")
            context["error"] = _("Error loading timeline data")

        return context


class ProjectAnalyticsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project analytics and reporting interface"""

    template_name = "projects/project_analytics.html"
    required_roles = ["utility-coordinator"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        try:
            project = get_object_or_404(Project, pk=self.kwargs["project_id"], organization=self.organization)

            # Check access
            if not _check_project_access(self.request.user, project, "view"):
                raise PermissionDenied(_("Access denied to project analytics"))

            # Calculate analytics
            analytics_data = self._calculate_project_analytics(project)

            context.update(
                {
                    "project": project,
                    "organization": self.organization,
                    **analytics_data,
                },
            )

        except PermissionDenied:
            raise
        except Exception:
            logger.exception("Error building analytics context")
            context["error"] = _("Error loading analytics data")

        return context

    def _calculate_project_analytics(self, project: Project) -> dict[str, Any]:
        """Calculate comprehensive project analytics"""
        try:
            # Time-based analytics
            time_entries = getattr(project, "time_entries", project.tasks.none())
            if hasattr(time_entries, "aggregate"):
                total_hours = time_entries.aggregate(total=Sum("duration_minutes"))["total"] or 0
                total_hours = total_hours / 60.0  # Convert to hours
            else:
                total_hours = 0

            # Task analytics
            tasks = project.tasks.all()
            completed_tasks = tasks.filter(status="completed")

            # Financial analytics
            contract_amount = float(getattr(project, "contract_amount", 0) or 0)
            billed_to_date = float(getattr(project, "billed_to_date", 0) or 0)
            current_cost = float(getattr(project, "current_cost", 0) or 0)

            return {
                "time_analytics": {
                    "total_hours": round(total_hours, 1),
                    "billable_hours": round(total_hours * 0.8, 1),  # Estimate
                },
                "task_analytics": {
                    "total_tasks": tasks.count(),
                    "completed_tasks": completed_tasks.count(),
                    "completion_rate": ((completed_tasks.count() / tasks.count() * 100) if tasks.count() > 0 else 0),
                },
                "financial_analytics": {
                    "contract_amount": contract_amount,
                    "billed_to_date": billed_to_date,
                    "current_cost": current_cost,
                    "profit_margin": (
                        ((billed_to_date - current_cost) / billed_to_date * 100) if billed_to_date > 0 else 0
                    ),
                },
            }

        except Exception:
            logger.exception("Error calculating project analytics")
            return {}


class ProjectMapView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project mapping interface with spatial data"""

    template_name = "projects/project_map.html"
    required_roles = ["stakeholder"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        try:
            project = get_object_or_404(Project, pk=self.kwargs["project_id"], organization=self.organization)

            # Check access
            if not _check_project_access(self.request.user, project, "view"):
                raise PermissionDenied(_("Access denied to project map"))

            # Get spatial data
            utilities = project.utilities.all()[:100]  # Limit for performance
            conflicts = project.conflicts.select_related("utility1", "utility2")

            # Get coordinate systems
            try:
                coordinate_systems = CoordinateSystem.objects.all()
                default_coord_system = (
                    coordinate_systems.filter(name__icontains="4326").first() or coordinate_systems.first()
                )
            except (ValidationError, ValueError):
                coordinate_systems = []
                default_coord_system = None

            context.update(
                {
                    "project": project,
                    "organization": self.organization,
                    "utilities": utilities,
                    "conflicts": conflicts,
                    "coordinate_systems": coordinate_systems,
                    "default_coord_system": default_coord_system,
                    "user_can_edit": _check_project_access(self.request.user, project, "edit"),
                },
            )

        except PermissionDenied:
            raise
        except Exception:
            logger.exception("Error building map context")
            context["error"] = _("Error loading map data")

        return context


# ============================================================================
# HTMX ENDPOINTS
# ============================================================================


@login_required
@require_http_methods(["GET"])
def project_search_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX project search endpoint with organization filtering"""
    try:
        organization = _get_user_organization(request.user)
        query = request.GET.get("q", "").strip()

        if len(query) < 2:
            return render(
                request,
                "projects/partials/project_search_results.html",
                {
                    "projects": [],
                    "query": query,
                },
            )

        # Search projects with organization filtering
        projects = (
            Project.objects.filter(organization=organization)
            .filter(Q(name__icontains=query) | Q(client__icontains=query) | Q(description__icontains=query))
            .select_related("created_by")
            .order_by("name")[:15]
        )

        # Filter by access permissions
        accessible_projects = [p for p in projects if _check_project_access(request.user, p)]

        return render(
            request,
            "projects/partials/project_search_results.html",
            {
                "projects": accessible_projects,
                "query": query,
                "organization": organization,
            },
        )

    except PermissionDenied as e:
        logger.warning(f"Permission denied in project search: {e}")
        return HttpResponse(f'<div class="alert alert-danger">{e}</div>', status=403)
    except Exception:
        logger.exception("Error in project search")
        return HttpResponse(f'<div class="alert alert-danger">{_("Search error")}</div>', status=500)


@login_required
@require_http_methods(["GET"])
def project_stats_htmx(request: HttpRequest, project_id: str) -> JsonResponse:
    """HTMX project statistics endpoint"""
    try:
        organization = _get_user_organization(request.user)

        project = get_object_or_404(Project, id=project_id, organization=organization)

        # Check access
        if not _check_project_access(request.user, project, "view"):
            return JsonResponse({"error": _("Access denied")}, status=403)

        # Calculate statistics
        tasks = project.tasks.all()
        stats = {
            "total_tasks": tasks.count(),
            "completed_tasks": tasks.filter(status="completed").count(),
            "active_tasks": tasks.exclude(status="completed").count(),
            "utility_count": project.utilities.count(),
            "conflict_count": project.conflicts.count(),
            "team_members": project.team_members.filter(is_active=True).count(),
            "completion_rate": 0,
        }

        # Calculate completion rate
        if stats["total_tasks"] > 0:
            stats["completion_rate"] = round(stats["completed_tasks"] / stats["total_tasks"] * 100, 1)

        return JsonResponse(stats)

    except PermissionDenied as e:
        logger.warning(f"Permission denied for project stats {project_id}: {e}")
        return JsonResponse({"error": str(e)}, status=403)
    except Exception:
        logger.exception("Error getting project stats")
        return JsonResponse({"error": _("Statistics error")}, status=500)


@login_required
@require_http_methods(["GET"])
def project_activity_feed_htmx(request: HttpRequest, project_id: str) -> HttpResponse:
    """HTMX project activity feed endpoint"""
    try:
        organization = _get_user_organization(request.user)

        project = get_object_or_404(Project, id=project_id, organization=organization)

        # Check access
        if not _check_project_access(self.request.user, project, "view"):
            return HttpResponse('<div class="alert alert-danger">Access denied</div>', status=403)

        # Get recent activities
        limit = min(int(request.GET.get("limit", 20)), 50)
        activities = (
            ProjectActivity.objects.filter(project=project).select_related("user").order_by("-timestamp")[:limit]
        )

        return render(
            request,
            "projects/partials/activity_feed.html",
            {
                "activities": activities,
                "project": project,
                "organization": organization,
            },
        )

    except PermissionDenied as e:
        logger.warning(f"Permission denied for activity feed {project_id}: {e}")
        return HttpResponse(f'<div class="alert alert-danger">{e}</div>', status=403)
    except Exception:
        logger.exception("Error loading activity feed")
        return HttpResponse(
            f'<div class="alert alert-danger">{_("Error loading activity feed")}</div>',
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def project_update_field_htmx(request: HttpRequest, project_id: str) -> HttpResponse:
    """HTMX endpoint to update a single project field"""
    try:
        organization = _get_user_organization(request.user)

        project = get_object_or_404(Project, id=project_id, organization=organization)

        # Check edit access
        if not _check_project_access(request.user, project, "edit"):
            return HttpResponse("Access denied", status=403)

        field_name = request.POST.get("field")
        field_value = request.POST.get("value")

        # Validate allowed fields
        allowed_fields = ["name", "description", "rag_status", "client"]

        if field_name not in allowed_fields:
            return HttpResponse("Invalid field", status=400)

        # Update field
        setattr(project, field_name, field_value)
        project.updated_at = timezone.now()
        project.save(update_fields=[field_name, "updated_at"])

        # Log update
        logger.info(f"Project field updated: {project.name}.{field_name} by {request.user}")

        return render(
            request,
            "projects/partials/field_updated.html",
            {
                "project": project,
                "field_name": field_name,
                "success": True,
            },
        )

    except PermissionDenied as e:
        logger.warning(f"Permission denied for field update {project_id}: {e}")
        return HttpResponse("Access denied", status=403)
    except Exception as e:
        logger.exception("Error updating project field")
        return HttpResponse(f"Update failed: {e}", status=400)


# ============================================================================
# LEGACY COMPATIBILITY VIEWS
# ============================================================================


class ProjectCommentsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project comments and discussions interface"""

    template_name = "projects/project_comments.html"
    required_roles = ["stakeholder"]

    def get_context_data(self, **kwargs) -> dict[str, Any]:
        context = super().get_context_data(**kwargs)

        try:
            project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)

            # Check access
            if not _check_project_access(self.request.user, project, "view"):
                raise PermissionDenied(_("Access denied to project comments"))

            # Get comments
            try:
                comments = Comment.objects.for_entity("project", project.id)
            except (AttributeError, Exception):
                comments = []

            context.update(
                {
                    "project": project,
                    "comments": comments,
                    "organization": self.organization,
                },
            )

        except PermissionDenied:
            raise
        except Exception:
            logger.exception("Error loading project comments")
            context["error"] = _("Error loading comments")

        return context


# Additional Project Views


class Project3DView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """3D visualization view for project infrastructure."""

    required_roles = ["stakeholder"]
    template_name = "projects/project_3d.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)
        context["project"] = project
        return context


class ProjectChatView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project chat and messaging interface."""

    required_roles = ["stakeholder"]
    template_name = "projects/project_chat.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)
        context["project"] = project
        return context


class ProjectDeleteView(LoginRequiredMixin, RoleRequiredMixin, DetailView, OrganizationAccessMixin):
    """Project deletion confirmation view."""

    required_roles = ["utility-coordinator"]
    model = Project
    template_name = "projects/project_confirm_delete.html"

    def get_object(self, queryset=None):
        return get_object_or_404(Project, id=self.kwargs["pk"], organization=self.organization)


class ProjectFileUploadView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project file upload interface."""

    required_roles = ["utility-coordinator"]
    template_name = "projects/project_file_upload.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)
        context["project"] = project
        return context


class ProjectMembersView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project team members management view."""

    required_roles = ["utility-coordinator"]
    template_name = "projects/project_members.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)
        context["project"] = project
        return context


class ProjectPortfolioView(LoginRequiredMixin, RoleRequiredMixin, ListView, OrganizationAccessMixin):
    """Project portfolio overview."""

    required_roles = ["department-manager"]
    model = Project
    template_name = "projects/project_portfolio.html"
    context_object_name = "projects"

    def get_queryset(self):
        return Project.objects.filter(organization=self.organization)


class ProjectSettingsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project settings and configuration."""

    required_roles = ["utility-coordinator"]
    template_name = "projects/project_settings.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)
        context["project"] = project
        return context


class ProjectStatisticsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project statistics and metrics view."""

    required_roles = ["stakeholder"]
    template_name = "projects/project_statistics.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["project_id"], organization=self.organization)
        context["project"] = project
        return context


def project_utilities(request):
    """Project utilities function-based view."""
    return render(request, "projects/project_utilities.html")


class ProjectDuplicateView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project duplication view."""

    required_roles = ["utility_coordinator"]
    template_name = "projects/project_duplicate.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["pk"], organization=self.organization)
        context["project"] = project
        return context

    def post(self, request, *args, **kwargs):
        """Handle project duplication."""
        project = get_object_or_404(Project, id=kwargs["pk"], organization=self.organization)

        # Create a duplicate project
        duplicate = Project.objects.create(
            name=f"{project.name} (Copy)",
            description=project.description,
            organization=self.organization,
            status="planning",
            created_by=request.user,
        )

        return JsonResponse(
            {
                "success": True,
                "project_id": str(duplicate.id),
                "redirect_url": reverse("projects:detail", kwargs={"pk": duplicate.id}),
            }
        )


class ProjectReportsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView, OrganizationAccessMixin):
    """Project reports and analytics view."""

    required_roles = ["stakeholder"]
    template_name = "projects/project_reports.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, id=self.kwargs["pk"], organization=self.organization)

        # Get project statistics
        total_tasks = project.tasks.count() if hasattr(project, "tasks") else 0
        completed_tasks = project.tasks.filter(status="completed").count() if hasattr(project, "tasks") else 0

        context.update(
            {
                "project": project,
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_rate": ((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0),
            }
        )
        return context


# Backward compatibility aliases
ProjectEditView = ProjectUpdateView
project_details = ProjectDetailView.as_view()


@login_required
@require_http_methods(["GET"])
def utility_info(request: HttpRequest, project_id: str, utility_id: str) -> HttpResponse:
    """Get utility information for the project map.

    Args:
    ----
        request: HTTP request
        project_id: Project ID
        utility_id: Utility line ID

    Returns:
    -------
        HTML fragment with utility details
    """
    # Get project and check permissions
    project = get_object_or_404(Project, pk=project_id)

    # Check if user has access to this project
    if not (
        request.user.is_staff
        or project.created_by == request.user
        or project.members.filter(id=request.user.id).exists()
    ):
        raise PermissionDenied

    # For now, return a simple template with mock data
    # In a real implementation, this would fetch from infrastructure.UtilityLine model
    context = {
        "project": project,
        "utility_id": utility_id,
        "utility_data": {
            "id": utility_id,
            "name": f"Utility Line {utility_id}",
            "type": "Water Main",
            "status": "Active",
            "owner": "City Water Department",
            "diameter": "12 inches",
            "material": "Ductile Iron",
            "depth": "4.5 feet",
            "installed_date": "2020-03-15",
            "location": "Main Street between 1st and 2nd Avenue",
            "coordinates": "40.7128°N, 74.0060°W",
        },
    }

    # Return a simple HTML fragment for HTMX
    return render(request, "projects/partials/utility_details.html", context)
