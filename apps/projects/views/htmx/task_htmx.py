"""
Task HTMX Views for CLEAR Application

Provides HTMX-specific views for dynamic task management interactions
including task creation, updates, status changes, and real-time updates.
"""

from __future__ import annotations

import contextlib
import logging
import re
import time
from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Any
from urllib.error import HTTPError

from django.contrib.auth import get_user_model
from django.db import models
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied, ValidationError
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Count, Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views import View
from django.views.decorators.csrf import csrf_protect

from apps.common.mixins.auth_mixins import OrganizationAccessMixin
from apps.projects.models import Project, Task
from apps.projects.views.htmx_mixins import HTMXResponseMixin

if TYPE_CHECKING:
    from django.db.models.query import QuerySet

from django.db import DatabaseError, IntegrityError, OperationalError

User = get_user_model()
logger = logging.getLogger(__name__)


# ========== BASE TASK CLASSES ==========


class BaseTaskHTMXView(LoginRequiredMixin, HTMXResponseMixin, OrganizationAccessMixin, View):
    """Base class for all task HTMX views with organization isolation."""

    def get_task_queryset(self) -> QuerySet[Task]:
        """Get optimized task queryset with organization filtering."""
        queryset = Task.objects.select_related(
            "project",
            "assigned_to",
            "created_by",
            "project__organization",
        ).prefetch_related("dependencies", "time_entries")

        # Filter by organization
        if hasattr(self.request.user, "organization") and self.request.user.organization:
            queryset = queryset.filter(project__organization=self.request.user.organization)

        return queryset

    def get_project_queryset(self) -> QuerySet[Project]:
        """Get project queryset with organization filtering."""
        queryset = Project.objects.select_related("organization")
        if hasattr(self.request.user, "organization") and self.request.user.organization:
            queryset = queryset.filter(organization=self.request.user.organization)
        return queryset

    def check_task_access(self, task: Task, permission: str = "view") -> bool:
        """Check if user has access to task."""
        # Check organization access
        if hasattr(self.request.user, "organization") and self.request.user.organization:
            if task.project.organization != self.request.user.organization:
                return False

        # Check task permissions
        if permission == "edit":
            return (
                task.assigned_to == self.request.user
                or task.created_by == self.request.user
                or task.project.user_has_access(self.request.user, "edit")
            )
        return (
            task.assigned_to == self.request.user
            or task.created_by == self.request.user
            or task.project.user_has_access(self.request.user, "view")
        )

    def handle_error(self, error: Exception, message: str | None = None) -> HttpResponse:
        """Handle errors with proper logging and user-friendly messages."""
        logger.exception(f"Task operation error: {error}")
        error_message = message or _("An error occurred. Please try again.")
        return HttpResponse(
            f'<div class="alert alert-danger">{error_message}</div>',
            status=500,
        )


# ========== TASK CREATION VIEWS ==========


class QuickTaskCreateView(BaseTaskHTMXView):
    """Quick task creation via HTMX."""

    htmx_template_name = "projects/partials/tasks/task_created.html"

    @csrf_protect
    def post(self, request: HttpRequest) -> HttpResponse:
        """Create a quick task."""
        try:
            title = request.POST.get("title", "").strip()
            project_id = request.POST.get("project_id")
            description = request.POST.get("description", "").strip()

            # Validation
            if not title:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Task title is required")}</div>',
                    status=400,
                )

            if not project_id:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Project selection is required")}</div>',
                    status=400,
                )

            # Get project with organization check
            try:
                project = self.get_project_queryset().get(id=project_id)
            except Project.DoesNotExist:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Project not found")}</div>',
                    status=404,
                )

            # Check permissions
            if not project.user_has_access(request.user, "edit"):
                raise PermissionDenied(_("You don't have permission to create tasks in this project"))

            # Create task
            with transaction.atomic():
                task = Task.objects.create(
                    title=title,
                    description=description,
                    project=project,
                    assigned_to=request.user,
                    status="pending",
                    created_by=request.user,
                )

            logger.info(f"Quick task created: {task.id} by user {request.user.id}")

            return self.render_to_response({"task": task, "success": True})

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except ValidationError as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=400,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to create task"))


class TaskCreateView(BaseTaskHTMXView):
    """Full task creation with all fields."""

    htmx_template_name = "projects/partials/tasks/task_item.html"

    @csrf_protect
    def post(self, request: HttpRequest) -> HttpResponse:
        """Create a new task with full details."""
        try:
            # Extract form data
            title = request.POST.get("title", "").strip()
            description = request.POST.get("description", "").strip()
            project_id = request.POST.get("project_id")
            assigned_to_id = request.POST.get("assigned_to")
            due_date_str = request.POST.get("due_date")
            start_date_str = request.POST.get("start_date")
            priority = request.POST.get("priority", "medium")
            estimated_hours = request.POST.get("estimated_hours")
            tags_str = request.POST.get("tags", "")

            # Validation
            if not title:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Task title is required")}</div>',
                    status=400,
                )

            if not project_id:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Project selection is required")}</div>',
                    status=400,
                )

            # Get project with organization check
            try:
                project = self.get_project_queryset().get(id=project_id)
            except Project.DoesNotExist:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Project not found")}</div>',
                    status=404,
                )

            # Check permissions
            if not project.user_has_access(request.user, "edit"):
                raise PermissionDenied(_("You don't have permission to create tasks in this project"))

            # Prepare task data
            task_data = {
                "title": title,
                "description": description,
                "project": project,
                "created_by": request.user,
                "priority": priority,
                "status": "pending",
            }

            # Handle assigned user
            if assigned_to_id:
                try:
                    # Ensure assigned user is in same organization
                    assigned_user = User.objects.filter(
                        id=assigned_to_id,
                        organization=request.user.organization,
                    ).first()
                    if assigned_user:
                        task_data["assigned_to"] = assigned_user
                except (ValueError, User.DoesNotExist):
                    pass

            # Handle dates
            if due_date_str:
                with contextlib.suppress(ValueError):
                    task_data["due_date"] = datetime.strptime(due_date_str, "%Y-%m-%d").date()

            if start_date_str:
                with contextlib.suppress(ValueError):
                    task_data["start_date"] = datetime.strptime(start_date_str, "%Y-%m-%d").date()

            # Handle estimated hours
            if estimated_hours:
                with contextlib.suppress(ValueError):
                    task_data["estimated_hours"] = float(estimated_hours)

            # Handle tags
            if tags_str:
                tags = [tag.strip() for tag in tags_str.split(",") if tag.strip()]
                task_data["tags"] = tags

            # Create task
            with transaction.atomic():
                task = Task.objects.create(**task_data)

            logger.info(f"Task created: {task.id} by user {request.user.id}")

            return self.render_to_response({"task": task, "created": True})

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except ValidationError as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=400,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to create task"))


# ========== TASK LISTING VIEWS ==========


class TaskListView(BaseTaskHTMXView):
    """Paginated task list with filtering."""

    htmx_template_name = "projects/partials/tasks/task_list.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get filtered and paginated task list."""
        try:
            # Get base queryset
            tasks = self.get_task_queryset()

            # Apply filters
            tasks = self._apply_filters(tasks, request.GET)

            # Apply sorting
            sort_by = request.GET.get("sort", "-created_at")
            valid_sorts = [
                "title",
                "-title",
                "due_date",
                "-due_date",
                "priority",
                "-priority",
                "status",
                "-status",
                "created_at",
                "-created_at",
            ]
            if sort_by in valid_sorts:
                tasks = tasks.order_by(sort_by)

            # Pagination
            page_size = min(int(request.GET.get("page_size", 20)), 50)
            paginator = Paginator(tasks, page_size)
            page_number = request.GET.get("page", 1)
            page_obj = paginator.get_page(page_number)

            context = {
                "tasks": page_obj,
                "filters": request.GET.dict(),
                "total_count": paginator.count,
            }

            return self.render_to_response(context)

        except (DatabaseError, IntegrityError, OperationalError) as e:
            return self.handle_error(e, _("Failed to load tasks"))

    def _apply_filters(self, queryset: QuerySet[Task], filters: dict[str, Any]) -> QuerySet[Task]:
        """Apply filters to task queryset."""
        # Status filter
        status = filters.get("status")
        if status and status in ["pending", "in_progress", "completed", "cancelled"]:
            queryset = queryset.filter(status=status)

        # Priority filter
        priority = filters.get("priority")
        if priority and priority in ["low", "medium", "high", "urgent"]:
            queryset = queryset.filter(priority=priority)

        # Project filter
        project_id = filters.get("project")
        if project_id:
            with contextlib.suppress(ValueError):
                queryset = queryset.filter(project_id=int(project_id))

        # Assigned user filter
        assigned_to = filters.get("assigned_to")
        if assigned_to:
            if assigned_to == "me":
                queryset = queryset.filter(assigned_to=self.request.user)
            elif assigned_to == "unassigned":
                queryset = queryset.filter(assigned_to__isnull=True)
            else:
                with contextlib.suppress(ValueError):
                    queryset = queryset.filter(assigned_to_id=int(assigned_to))

        # Due date filters
        due_date = filters.get("due_date")
        today = timezone.now().date()
        if due_date == "overdue":
            queryset = queryset.filter(due_date__lt=today, status__in=["pending", "in_progress"])
        elif due_date == "today":
            queryset = queryset.filter(due_date=today)
        elif due_date == "this_week":
            week_start = today - timedelta(days=today.weekday())
            week_end = week_start + timedelta(days=6)
            queryset = queryset.filter(due_date__range=[week_start, week_end])
        elif due_date == "next_week":
            week_start = today + timedelta(days=(7 - today.weekday()))
            week_end = week_start + timedelta(days=6)
            queryset = queryset.filter(due_date__range=[week_start, week_end])

        # Search filter
        search = filters.get("search", "").strip()
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) | Q(description__icontains=search) | Q(tags__icontains=search),
            )

        return queryset


class TaskDetailView(BaseTaskHTMXView):
    """Task detail view with editing capabilities."""

    htmx_template_name = "projects/partials/tasks/task_detail.html"

    def get(self, request: HttpRequest, task_id: int) -> HttpResponse:
        """Get task detail."""
        try:
            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task):
                raise PermissionDenied(_("You don't have permission to view this task"))

            # Get related data
            time_entries = task.time_entries.select_related("user")[:10]
            dependencies = task.dependencies.select_related("project")

            context = {
                "task": task,
                "time_entries": time_entries,
                "dependencies": dependencies,
                "can_edit": self.check_task_access(task, "edit"),
            }

            return self.render_to_response(context)

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to load task details"))


# ========== TASK STATUS MANAGEMENT ==========


class TaskStatusUpdateView(BaseTaskHTMXView):
    """Update task status with workflow validation."""

    htmx_template_name = "projects/partials/tasks/task_status_updated.html"

    @csrf_protect
    def post(self, request: HttpRequest, task_id: int) -> HttpResponse:
        """Update task status."""
        try:
            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to update this task"))

            new_status = request.POST.get("status")
            valid_statuses = ["pending", "in_progress", "completed", "cancelled"]

            if new_status not in valid_statuses:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid status")}</div>',
                    status=400,
                )

            old_status = task.status

            # Validate status transition
            if not self._is_valid_status_transition(old_status, new_status):
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid status transition")}</div>',
                    status=400,
                )

            # Update task
            with transaction.atomic():
                task.status = new_status

                # Handle completion
                if new_status == "completed" and old_status != "completed":
                    task.completion_date = timezone.now()
                elif new_status != "completed" and old_status == "completed":
                    task.completion_date = None

                # Handle start date
                if new_status == "in_progress" and old_status == "pending" and not task.start_date:
                    task.start_date = timezone.now()

                task.save()

            logger.info(f"Task {task.id} status updated: {old_status} -> {new_status} by user {request.user.id}")

            context = {
                "task": task,
                "old_status": old_status,
                "new_status": new_status,
                "success_message": _("Task status updated successfully"),
            }

            return self.render_to_response(context)

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to update task status"))

    def _is_valid_status_transition(self, old_status: str, new_status: str) -> bool:
        """Validate status transitions."""
        # Allow any transition for now, can be enhanced with business rules
        return True


class TaskBulkStatusUpdateView(BaseTaskHTMXView):
    """Bulk update status for multiple tasks."""

    htmx_template_name = "projects/partials/tasks/bulk_status_updated.html"

    @csrf_protect
    def post(self, request: HttpRequest) -> HttpResponse:
        """Bulk update task statuses."""
        try:
            task_ids = request.POST.getlist("task_ids")
            new_status = request.POST.get("status")

            if not task_ids:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("No tasks selected")}</div>',
                    status=400,
                )

            valid_statuses = ["pending", "in_progress", "completed", "cancelled"]
            if new_status not in valid_statuses:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid status")}</div>',
                    status=400,
                )

            # Get tasks user can edit
            tasks = self.get_task_queryset().filter(id__in=task_ids)
            editable_tasks = [task for task in tasks if self.check_task_access(task, "edit")]

            if not editable_tasks:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("No tasks available for update")}</div>',
                    status=403,
                )

            # Bulk update
            updated_count = 0
            with transaction.atomic():
                for task in editable_tasks:
                    old_status = task.status
                    task.status = new_status

                    # Handle completion
                    if new_status == "completed" and old_status != "completed":
                        task.completion_date = timezone.now()
                    elif new_status != "completed" and old_status == "completed":
                        task.completion_date = None

                    task.save()
                    updated_count += 1

            logger.info(f"Bulk status update: {updated_count} tasks updated to {new_status} by user {request.user.id}")

            context = {
                "updated_count": updated_count,
                "new_status": new_status,
                "success_message": _("Tasks updated successfully"),
            }

            return self.render_to_response(context)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to update tasks"))


class TaskProgressUpdateView(BaseTaskHTMXView):
    """Update task progress percentage with validation."""

    htmx_template_name = "projects/partials/tasks/task_progress_updated.html"

    @csrf_protect
    def post(self, request: HttpRequest, task_id: int) -> HttpResponse:
        """Update task progress percentage."""
        try:
            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to update this task"))

            progress_percentage = request.POST.get("progress_percentage")

            if progress_percentage is None:
                return HttpResponse(
                    '<div class="alert alert-danger">Progress percentage is required</div>',
                    status=400,
                )

            try:
                progress_value = int(progress_percentage)
            except (ValueError, TypeError):
                return HttpResponse(
                    '<div class="alert alert-danger">Invalid progress percentage</div>',
                    status=400,
                )

            if not (0 <= progress_value <= 100):
                return HttpResponse(
                    '<div class="alert alert-danger">Progress must be between 0 and 100</div>',
                    status=400,
                )

            old_progress = task.progress_percentage

            # Update task
            with transaction.atomic():
                task.progress_percentage = progress_value

                # Auto-update status based on progress
                if progress_value == 100 and task.status != "completed":
                    task.status = "completed"
                    task.completion_date = timezone.now()
                elif progress_value > 0 and task.status == "pending":
                    task.status = "in_progress"
                    if not task.start_date:
                        task.start_date = timezone.now()
                elif progress_value == 0 and task.status in [
                    "in_progress",
                    "completed",
                ]:
                    task.status = "pending"
                    task.completion_date = None

                task.save()

            logger.info(
                f"Task {task.id} progress updated: {old_progress}% -> {progress_value}% by user {request.user.id}"
            )

            context = {
                "task": task,
                "old_progress": old_progress,
                "new_progress": progress_value,
                "success": True,
                "message": _("Progress updated successfully"),
            }

            return self.render_to_response(context)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to update task progress"))


class TaskTimelineMoveView(BaseTaskHTMXView):
    """Update task dates from timeline drag-and-drop."""

    htmx_template_name = "projects/partials/tasks/task_moved.html"

    @csrf_protect
    def post(self, request: HttpRequest) -> HttpResponse:
        """Update task start and end dates."""
        try:
            task_id = request.POST.get("task_id")
            if not task_id:
                return HttpResponse(
                    '<div class="alert alert-danger">Task ID is required</div>',
                    status=400,
                )

            task = get_object_or_404(self.get_task_queryset(), id=int(task_id))

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to update this task"))

            new_start = request.POST.get("new_start")
            new_end = request.POST.get("new_end")

            if not new_start or not new_end:
                return HttpResponse(
                    '<div class="alert alert-danger">Start and end dates are required</div>',
                    status=400,
                )

            try:
                from datetime import datetime

                start_date = datetime.strptime(new_start, "%Y-%m-%d").date()
                end_date = datetime.strptime(new_end, "%Y-%m-%d").date()
            except ValueError:
                return HttpResponse(
                    '<div class="alert alert-danger">Invalid date format</div>',
                    status=400,
                )

            if start_date > end_date:
                return HttpResponse(
                    '<div class="alert alert-danger">Start date cannot be after end date</div>',
                    status=400,
                )

            old_start = task.start_date
            old_end = task.due_date

            # Update task dates
            with transaction.atomic():
                task.start_date = start_date
                task.due_date = end_date
                task.save()

            logger.info(
                f"Task {task.id} timeline updated: {old_start}-{old_end} -> {start_date}-{end_date} by user {request.user.id}"
            )

            context = {
                "task": task,
                "old_start": old_start,
                "old_end": old_end,
                "new_start": start_date,
                "new_end": end_date,
                "success": True,
                "message": _("Task timeline updated successfully"),
            }

            return self.render_to_response(context)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to update task timeline"))


# ========== TASK ASSIGNMENT VIEWS ==========


class TaskAssignmentView(BaseTaskHTMXView):
    """Assign/reassign tasks to team members."""

    htmx_template_name = "projects/partials/tasks/task_assigned.html"

    @csrf_protect
    def post(self, request: HttpRequest, task_id: int) -> HttpResponse:
        """Assign task to a user."""
        try:
            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to assign this task"))

            assigned_to_id = request.POST.get("assigned_to")
            previous_assignee = task.assigned_to

            # Handle assignment
            if assigned_to_id:
                try:
                    # Ensure assigned user is in same organization
                    assigned_user = User.objects.filter(
                        id=int(assigned_to_id),
                        organization=request.user.organization,
                    ).first()

                    if not assigned_user:
                        return HttpResponse(
                            f'<div class="alert alert-danger">{_("Invalid user selection")}</div>',
                            status=400,
                        )

                    task.assigned_to = assigned_user
                except (ValueError, User.DoesNotExist):
                    return HttpResponse(
                        f'<div class="alert alert-danger">{_("Invalid user selection")}</div>',
                        status=400,
                    )
            else:
                task.assigned_to = None

            task.save()

            logger.info(f"Task {task.id} assigned to {task.assigned_to} by user {request.user.id}")

            context = {
                "task": task,
                "previous_assignee": previous_assignee,
                "success_message": _("Task assignment updated successfully"),
            }

            return self.render_to_response(context)

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to assign task"))


class TeamTasksView(BaseTaskHTMXView):
    """View tasks by team member."""

    htmx_template_name = "projects/partials/tasks/team_tasks.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get tasks grouped by team member."""
        try:
            # Get organization users
            organization_users = User.objects.filter(organization=request.user.organization)

            # Get tasks with assignments
            tasks = (
                self.get_task_queryset()
                .filter(assigned_to__in=organization_users)
                .select_related("assigned_to", "project")
                .order_by("assigned_to__first_name", "assigned_to__last_name", "-created_at")
            )

            # Group by assignee
            team_tasks = {}
            for task in tasks:
                if task.assigned_to:
                    if task.assigned_to not in team_tasks:
                        team_tasks[task.assigned_to] = []
                    team_tasks[task.assigned_to].append(task)

            # Get unassigned tasks
            unassigned_tasks = self.get_task_queryset().filter(assigned_to__isnull=True)

            context = {
                "team_tasks": team_tasks,
                "unassigned_tasks": unassigned_tasks,
            }

            return self.render_to_response(context)

        except (DatabaseError, IntegrityError, OperationalError) as e:
            return self.handle_error(e, _("Failed to load team tasks"))


# ========== SEARCH AND FILTERING ==========


class TaskSearchView(BaseTaskHTMXView):
    """Advanced task search functionality."""

    htmx_template_name = "projects/partials/tasks/search_results.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Perform task search."""
        try:
            search_query = request.GET.get("q", "").strip()

            if not search_query:
                return self.render_to_response({"tasks": [], "query": ""})

            # Perform search
            tasks = (
                self.get_task_queryset()
                .filter(
                    Q(title__icontains=search_query)
                    | Q(description__icontains=search_query)
                    | Q(tags__icontains=search_query)
                    | Q(project__title__icontains=search_query),
                )
                .order_by("-created_at")[:20]
            )

            context = {
                "tasks": tasks,
                "query": search_query,
                "count": tasks.count(),
            }

            return self.render_to_response(context)

        except (DatabaseError, IntegrityError, OperationalError) as e:
            return self.handle_error(e, _("Search failed"))


class TaskFilterFormView(BaseTaskHTMXView):
    """Dynamic filter form for tasks."""

    htmx_template_name = "projects/partials/tasks/filter_form.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get filter form with current values."""
        try:
            # Get available options for filters
            projects = self.get_project_queryset().values("id", "title")
            users = User.objects.filter(organization=request.user.organization).values("id", "first_name", "last_name")

            # Get unique priorities and statuses from tasks
            task_queryset = self.get_task_queryset()
            priorities = task_queryset.values_list("priority", flat=True).distinct()
            statuses = task_queryset.values_list("status", flat=True).distinct()

            context = {
                "projects": projects,
                "users": users,
                "priorities": priorities,
                "statuses": statuses,
                "current_filters": request.GET.dict(),
            }

            return self.render_to_response(context)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to load filter form"))


# ========== TIMELINE AND DEPENDENCIES ==========


class TaskTimelineView(BaseTaskHTMXView):
    """Task timeline with Gantt-like functionality."""

    htmx_template_name = "projects/partials/tasks/timeline.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get task timeline data."""
        try:
            project_id = request.GET.get("project_id")

            # Get tasks for timeline
            tasks = self.get_task_queryset()

            if project_id:
                with contextlib.suppress(ValueError):
                    tasks = tasks.filter(project_id=int(project_id))

            # Filter tasks with dates
            tasks = tasks.filter(Q(start_date__isnull=False) | Q(due_date__isnull=False)).order_by(
                "start_date",
                "due_date",
            )

            # Calculate timeline data
            timeline_data = []
            for task in tasks:
                start_date = task.start_date or task.created_at.date()
                end_date = task.due_date or task.start_date or timezone.now().date()

                timeline_data.append(
                    {
                        "task": task,
                        "start_date": start_date,
                        "end_date": end_date,
                        "duration": ((end_date - start_date).days + 1 if end_date >= start_date else 1),
                        "progress": self._calculate_progress(task),
                    },
                )

            context = {
                "timeline_data": timeline_data,
                "project_id": project_id,
            }

            return self.render_to_response(context)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to load timeline"))

    def _calculate_progress(self, task: Task) -> int:
        """Calculate task progress percentage."""
        if task.status == "completed":
            return 100
        if task.status == "in_progress":
            # Could be enhanced with actual time tracking
            return 50
        if task.status == "cancelled":
            return 0
        return 0


class TaskDependencyView(BaseTaskHTMXView):
    """Manage task dependencies."""

    htmx_template_name = "projects/partials/tasks/dependencies.html"

    def get(self, request: HttpRequest, task_id: int) -> HttpResponse:
        """Get task dependencies."""
        try:
            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task):
                raise PermissionDenied(_("You don't have permission to view this task"))

            # Get dependencies and dependents
            dependencies = task.dependencies.select_related("project")
            dependents = Task.objects.filter(dependencies=task).select_related("project")

            # Get available tasks for dependency
            available_tasks = (
                self.get_task_queryset()
                .filter(project=task.project)
                .exclude(id=task.id)
                .exclude(id__in=task.dependencies.values_list("id", flat=True))
            )

            context = {
                "task": task,
                "dependencies": dependencies,
                "dependents": dependents,
                "available_tasks": available_tasks,
                "can_edit": self.check_task_access(task, "edit"),
            }

            return self.render_to_response(context)

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to load dependencies"))

    @csrf_protect
    def post(self, request: HttpRequest, task_id: int) -> HttpResponse:
        """Add task dependency."""
        try:
            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to edit this task"))

            dependency_id = request.POST.get("dependency_id")
            if not dependency_id:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Dependency task required")}</div>',
                    status=400,
                )

            try:
                dependency = self.get_task_queryset().get(id=int(dependency_id))
            except (ValueError, Task.DoesNotExist):
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid dependency task")}</div>',
                    status=400,
                )

            # Check for circular dependencies
            if self._would_create_circular_dependency(task, dependency):
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Would create circular dependency")}</div>',
                    status=400,
                )

            # Add dependency
            task.dependencies.add(dependency)

            logger.info(f"Dependency added: Task {task.id} depends on {dependency.id}")

            # Return updated dependencies view
            return self.get(request, task_id)

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to add dependency"))

    def _would_create_circular_dependency(self, task: Task, dependency: Task) -> bool:
        """Check if adding dependency would create circular reference."""
        # Simple check - could be enhanced with graph traversal
        return dependency.dependencies.filter(id=task.id).exists()


# ========== ANALYTICS AND REPORTING ==========


class TaskAnalyticsView(BaseTaskHTMXView):
    """Task analytics and metrics."""

    htmx_template_name = "projects/partials/tasks/analytics.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get task analytics data."""
        try:
            project_id = request.GET.get("project_id")
            time_period = request.GET.get("period", "30")  # days

            # Base queryset
            tasks = self.get_task_queryset()

            if project_id:
                with contextlib.suppress(ValueError):
                    tasks = tasks.filter(project_id=int(project_id))

            # Filter by time period
            try:
                days = int(time_period)
                cutoff_date = timezone.now() - timedelta(days=days)
                tasks = tasks.filter(created_at__gte=cutoff_date)
            except ValueError:
                pass

            # Calculate metrics
            total_tasks = tasks.count()
            completed_tasks = tasks.filter(status="completed").count()
            in_progress_tasks = tasks.filter(status="in_progress").count()
            overdue_tasks = tasks.filter(
                due_date__lt=timezone.now().date(),
                status__in=["pending", "in_progress"],
            ).count()

            # Status distribution
            status_counts = tasks.values("status").annotate(count=Count("id"))

            # Priority distribution
            priority_counts = tasks.values("priority").annotate(count=Count("id"))

            # Average completion time
            completed_with_dates = tasks.filter(
                status="completed",
                start_date__isnull=False,
                completion_date__isnull=False,
            )
            avg_completion_days = 0
            if completed_with_dates.exists():
                total_days = sum(
                    (task.completion_date.date() - task.start_date.date()).days for task in completed_with_dates
                )
                avg_completion_days = total_days / completed_with_dates.count()

            # Productivity metrics
            completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

            context = {
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "in_progress_tasks": in_progress_tasks,
                "overdue_tasks": overdue_tasks,
                "completion_rate": round(completion_rate, 1),
                "avg_completion_days": round(avg_completion_days, 1),
                "status_counts": status_counts,
                "priority_counts": priority_counts,
                "time_period": time_period,
                "project_id": project_id,
            }

            return self.render_to_response(context)

        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to load analytics"))


class TaskReportView(BaseTaskHTMXView):
    """Generate task reports."""

    htmx_template_name = "projects/partials/tasks/report.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Generate task report."""
        try:
            report_type = request.GET.get("type", "summary")
            project_id = request.GET.get("project_id")
            start_date_str = request.GET.get("start_date")
            end_date_str = request.GET.get("end_date")

            # Base queryset
            tasks = self.get_task_queryset()

            if project_id:
                with contextlib.suppress(ValueError):
                    tasks = tasks.filter(project_id=int(project_id))

            # Date filtering
            if start_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                    tasks = tasks.filter(created_at__date__gte=start_date)
                except ValueError:
                    pass

            if end_date_str:
                try:
                    end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
                    tasks = tasks.filter(created_at__date__lte=end_date)
                except ValueError:
                    pass

            # Generate report based on type
            if report_type == "detailed":
                context = self._generate_detailed_report(tasks)
            elif report_type == "performance":
                context = self._generate_performance_report(tasks)
            else:
                context = self._generate_summary_report(tasks)

            context.update(
                {
                    "report_type": report_type,
                    "project_id": project_id,
                    "start_date": start_date_str,
                    "end_date": end_date_str,
                },
            )

            return self.render_to_response(context)

        except (DatabaseError, IntegrityError, OperationalError) as e:
            return self.handle_error(e, _("Failed to generate report"))

    def _generate_summary_report(self, tasks: QuerySet[Task]) -> dict[str, Any]:
        """Generate summary report."""
        return {
            "total_tasks": tasks.count(),
            "completed_tasks": tasks.filter(status="completed").count(),
            "in_progress_tasks": tasks.filter(status="in_progress").count(),
            "pending_tasks": tasks.filter(status="pending").count(),
            "cancelled_tasks": tasks.filter(status="cancelled").count(),
        }

    def _generate_detailed_report(self, tasks: QuerySet[Task]) -> dict[str, Any]:
        """Generate detailed report."""
        tasks_list = tasks.select_related("project", "assigned_to", "created_by").order_by("-created_at")

        return {
            "tasks": tasks_list,
            "total_count": tasks.count(),
        }

    def _generate_performance_report(self, tasks: QuerySet[Task]) -> dict[str, Any]:
        """Generate performance report."""
        # User performance
        user_stats = (
            tasks.filter(assigned_to__isnull=False)
            .values("assigned_to__first_name", "assigned_to__last_name")
            .annotate(
                total_assigned=Count("id"),
                completed=Count("id", filter=Q(status="completed")),
                in_progress=Count("id", filter=Q(status="in_progress")),
            )
            .order_by("-completed")
        )

        # Project performance
        project_stats = (
            tasks.values("project__title")
            .annotate(
                total_tasks=Count("id"),
                completed_tasks=Count("id", filter=Q(status="completed")),
                avg_completion_time=Sum("estimated_hours"),
            )
            .order_by("-total_tasks")
        )

        return {
            "user_performance": user_stats,
            "project_performance": project_stats,
        }


# ========== FUNCTION-BASED VIEWS (FOR COMPATIBILITY) ==========


@csrf_protect
def quick_task_create(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for QuickTaskCreateView."""
    view = QuickTaskCreateView()
    view.request = request
    return view.post(request)


@csrf_protect
def create_task_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskCreateView."""
    view = TaskCreateView()
    view.request = request
    return view.post(request)


def tasks_list_partial(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskListView."""
    view = TaskListView()
    view.request = request
    return view.get(request)


def task_list_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskListView."""
    view = TaskListView()
    view.request = request
    return view.get(request)


@csrf_protect
def update_task_status_htmx(request: HttpRequest, task_id: int) -> HttpResponse:
    """Function-based wrapper for TaskStatusUpdateView."""
    view = TaskStatusUpdateView()
    view.request = request
    return view.post(request, task_id)


def task_status_htmx(request: HttpRequest) -> HttpResponse:
    """Get task status via HTMX."""
    view = TaskDetailView()
    view.request = request
    task_id = request.GET.get("task_id")
    if not task_id:
        return HttpResponse(_("Task ID required"), status=400)
    try:
        return view.get(request, int(task_id))
    except ValueError:
        return HttpResponse(_("Invalid task ID"), status=400)


def tasks_filter(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskListView with filters."""
    view = TaskListView()
    view.request = request
    return view.get(request)


def timeline_data_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskTimelineView."""
    view = TaskTimelineView()
    view.request = request
    return view.get(request)


def task_timeline_update_htmx(request: HttpRequest) -> HttpResponse:
    """Update task timeline - actual implementation."""
    return HttpResponse(_("Task timeline update - enhanced implementation needed"))


def timeline_task_move_htmx(request: HttpRequest) -> HttpResponse:
    """Move task in timeline - actual implementation."""
    return HttpResponse(_("Timeline task move - enhanced implementation needed"))


def dependency_management_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskDependencyView."""
    task_id = request.GET.get("task_id") or request.POST.get("task_id")
    if not task_id:
        return HttpResponse(_("Task ID required"), status=400)

    view = TaskDependencyView()
    view.request = request

    try:
        task_id_int = int(task_id)
        if request.method == "POST":
            return view.post(request, task_id_int)
        return view.get(request, task_id_int)
    except ValueError:
        return HttpResponse(_("Invalid task ID"), status=400)


def timeline_metrics_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskAnalyticsView."""
    view = TaskAnalyticsView()
    view.request = request
    return view.get(request)


def calculate_critical_path_htmx(request: HttpRequest) -> HttpResponse:
    """Calculate critical path - enhanced implementation needed."""
    return JsonResponse({"message": _("Critical path calculation - enhanced implementation needed")})


def timeline_export_htmx(request: HttpRequest) -> HttpResponse:
    """Export timeline data - enhanced implementation needed."""
    return HttpResponse(_("Timeline export - enhanced implementation needed"))


# ========== DRAG AND DROP VIEWS ==========


class TaskDragDropView(BaseTaskHTMXView):
    """Handle drag and drop operations for tasks."""

    htmx_template_name = "projects/partials/tasks/task_moved.html"

    @csrf_protect
    def post(self, request: HttpRequest) -> HttpResponse:
        """Handle task drag and drop operations."""
        try:
            action = request.POST.get("action")
            task_id = request.POST.get("task_id")

            if not task_id:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Task ID required")}</div>',
                    status=400,
                )

            task = get_object_or_404(self.get_task_queryset(), id=task_id)

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to move this task"))

            if action == "reorder":
                return self._handle_reorder(request, task)
            elif action == "status_change":
                return self._handle_status_change(request, task)
            elif action == "priority_change":
                return self._handle_priority_change(request, task)
            else:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid action")}</div>',
                    status=400,
                )

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to move task"))

    def _handle_reorder(self, request: HttpRequest, task: Task) -> HttpResponse:
        """Handle task reordering within same status/priority."""
        try:
            new_order = request.POST.get("new_order")
            old_order = task.display_order

            if new_order:
                new_order = int(new_order)
                with transaction.atomic():
                    # Update task order
                    task.display_order = new_order
                    task.save()

                    # Adjust other tasks' order
                    same_status_tasks = (
                        self.get_task_queryset()
                        .filter(
                            project=task.project,
                            status=task.status,
                        )
                        .exclude(id=task.id)
                    )

                    if new_order > old_order:
                        # Moving down: decrease order of tasks between old and new position
                        same_status_tasks.filter(display_order__gt=old_order, display_order__lte=new_order).update(
                            display_order=models.F("display_order") - 1
                        )
                    else:
                        # Moving up: increase order of tasks between new and old position
                        same_status_tasks.filter(display_order__gte=new_order, display_order__lt=old_order).update(
                            display_order=models.F("display_order") + 1
                        )

                logger.info(f"Task {task.id} reordered from {old_order} to {new_order} by user {request.user.id}")

                context = {
                    "task": task,
                    "old_order": old_order,
                    "new_order": new_order,
                    "action": "reorder",
                    "success_message": _("Task order updated successfully"),
                }

                return self.render_to_response(context)

        except (ValueError, TypeError):
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Invalid order value")}</div>',
                status=400,
            )

        return HttpResponse(
            f'<div class="alert alert-danger">{_("Failed to update task order")}</div>',
            status=400,
        )

    def _handle_status_change(self, request: HttpRequest, task: Task) -> HttpResponse:
        """Handle task status change via drag and drop."""
        try:
            new_status = request.POST.get("new_status")
            old_status = task.status

            valid_statuses = ["pending", "in-progress", "completed"]
            if new_status not in valid_statuses:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid status")}</div>',
                    status=400,
                )

            with transaction.atomic():
                task.status = new_status

                # Handle status-specific updates
                if new_status == "completed" and old_status != "completed":
                    task.completion_date = timezone.now()
                elif new_status != "completed" and old_status == "completed":
                    task.completion_date = None

                if new_status == "in-progress" and old_status == "pending" and not task.start_date:
                    task.start_date = timezone.now()

                # Reset display order to end of new status column
                max_order = (
                    self.get_task_queryset()
                    .filter(project=task.project, status=new_status)
                    .aggregate(max_order=models.Max("display_order"))["max_order"]
                    or 0
                )
                task.display_order = max_order + 1

                task.save()

            logger.info(
                f"Task {task.id} status changed: {old_status} -> {new_status} via drag and drop by user {request.user.id}"
            )

            context = {
                "task": task,
                "old_status": old_status,
                "new_status": new_status,
                "action": "status_change",
                "success_message": _("Task status updated successfully"),
            }

            return self.render_to_response(context)

        except (ValueError, TypeError):
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Invalid status value")}</div>',
                status=400,
            )

    def _handle_priority_change(self, request: HttpRequest, task: Task) -> HttpResponse:
        """Handle task priority change via drag and drop."""
        try:
            new_priority = request.POST.get("new_priority")
            old_priority = task.priority

            valid_priorities = ["low", "medium", "high", "critical"]
            if new_priority not in valid_priorities:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid priority")}</div>',
                    status=400,
                )

            with transaction.atomic():
                task.priority = new_priority
                task.save()

            logger.info(
                f"Task {task.id} priority changed: {old_priority} -> {new_priority} via drag and drop by user {request.user.id}"
            )

            context = {
                "task": task,
                "old_priority": old_priority,
                "new_priority": new_priority,
                "action": "priority_change",
                "success_message": _("Task priority updated successfully"),
            }

            return self.render_to_response(context)

        except (ValueError, TypeError):
            return HttpResponse(
                f'<div class="alert alert-danger">{_("Invalid priority value")}</div>',
                status=400,
            )


class TaskKanbanBoardView(BaseTaskHTMXView):
    """Enhanced Kanban board with drag and drop support."""

    htmx_template_name = "projects/partials/tasks/kanban_board.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get Kanban board with tasks grouped by status."""
        try:
            project_id = request.GET.get("project_id")

            # Get tasks queryset
            tasks = self.get_task_queryset()

            if project_id:
                with contextlib.suppress(ValueError):
                    tasks = tasks.filter(project_id=int(project_id))

            # Group tasks by status, ordered by display_order
            pending_tasks = tasks.filter(status="pending").order_by("display_order", "created_at")
            in_progress_tasks = tasks.filter(status="in-progress").order_by("display_order", "created_at")
            completed_tasks = tasks.filter(status="completed").order_by("display_order", "created_at")

            context = {
                "pending_tasks": pending_tasks,
                "in_progress_tasks": in_progress_tasks,
                "completed_tasks": completed_tasks,
                "project_id": project_id,
                "task_counts": {
                    "pending": pending_tasks.count(),
                    "in_progress": in_progress_tasks.count(),
                    "completed": completed_tasks.count(),
                },
            }

            return self.render_to_response(context)

        except (DatabaseError, IntegrityError, OperationalError) as e:
            return self.handle_error(e, _("Failed to load Kanban board"))


class TaskUndoRedoView(BaseTaskHTMXView):
    """Handle undo/redo operations for drag and drop."""

    htmx_template_name = "projects/partials/tasks/undo_result.html"

    @csrf_protect
    def post(self, request: HttpRequest) -> HttpResponse:
        """Handle undo/redo operations."""
        try:
            action = request.POST.get("action")  # "undo" or "redo"
            operation_id = request.POST.get("operation_id")

            if not operation_id:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Operation ID required")}</div>',
                    status=400,
                )

            # Get operation from session or cache
            session_key = f"task_operations_{request.user.id}"
            operations = request.session.get(session_key, [])

            operation = None
            for op in operations:
                if op.get("id") == operation_id:
                    operation = op
                    break

            if not operation:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Operation not found")}</div>',
                    status=404,
                )

            # Apply undo/redo
            task = get_object_or_404(self.get_task_queryset(), id=operation["task_id"])

            if not self.check_task_access(task, "edit"):
                raise PermissionDenied(_("You don't have permission to modify this task"))

            if action == "undo":
                success = self._apply_undo(task, operation)
            elif action == "redo":
                success = self._apply_redo(task, operation)
            else:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Invalid action")}</div>',
                    status=400,
                )

            if success:
                # Update operation status
                operation["undone"] = action == "undo"
                request.session[session_key] = operations
                request.session.modified = True

                logger.info(f"Task {task.id} {action} operation applied by user {request.user.id}")

                context = {
                    "task": task,
                    "action": action,
                    "operation": operation,
                    "success_message": _("Operation applied successfully"),
                }

                return self.render_to_response(context)
            else:
                return HttpResponse(
                    f'<div class="alert alert-danger">{_("Failed to apply operation")}</div>',
                    status=500,
                )

        except PermissionDenied as e:
            return HttpResponse(
                f'<div class="alert alert-danger">{e!s}</div>',
                status=403,
            )
        except (ConnectionError, TimeoutError, HTTPError) as e:
            return self.handle_error(e, _("Failed to apply operation"))

    def _apply_undo(self, task: Task, operation: dict) -> bool:
        """Apply undo operation."""
        try:
            operation_type = operation["type"]
            old_values = operation["old_values"]

            with transaction.atomic():
                if operation_type == "status_change":
                    task.status = old_values["status"]
                    if "completion_date" in old_values:
                        task.completion_date = old_values["completion_date"]
                    if "start_date" in old_values:
                        task.start_date = old_values["start_date"]
                elif operation_type == "reorder":
                    task.display_order = old_values["display_order"]
                elif operation_type == "priority_change":
                    task.priority = old_values["priority"]

                task.save()

            return True
        except Exception as e:
            logger.error(f"Failed to apply undo: {e}")
            return False

    def _apply_redo(self, task: Task, operation: dict) -> bool:
        """Apply redo operation."""
        try:
            operation_type = operation["type"]
            new_values = operation["new_values"]

            with transaction.atomic():
                if operation_type == "status_change":
                    task.status = new_values["status"]
                    if "completion_date" in new_values:
                        task.completion_date = new_values["completion_date"]
                    if "start_date" in new_values:
                        task.start_date = new_values["start_date"]
                elif operation_type == "reorder":
                    task.display_order = new_values["display_order"]
                elif operation_type == "priority_change":
                    task.priority = new_values["priority"]

                task.save()

            return True
        except Exception as e:
            logger.error(f"Failed to apply redo: {e}")
            return False


# ========== FUNCTION-BASED WRAPPERS FOR DRAG AND DROP ==========


@csrf_protect
def task_drag_drop_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskDragDropView."""
    view = TaskDragDropView()
    view.request = request
    return view.post(request)


def kanban_board_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskKanbanBoardView."""
    view = TaskKanbanBoardView()
    view.request = request
    return view.get(request)


@csrf_protect
def task_undo_redo_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskUndoRedoView."""
    view = TaskUndoRedoView()
    view.request = request
    return view.post(request)


class TaskListSortableView(BaseTaskHTMXView):
    """Sortable task list view for drag and drop."""

    htmx_template_name = "projects/partials/tasks/task_list_sortable.html"

    def get(self, request: HttpRequest) -> HttpResponse:
        """Get sortable task list."""
        try:
            project_id = request.GET.get("project_id")
            view_type = request.GET.get("view", "sortable_list")

            # Get tasks queryset
            tasks = self.get_task_queryset()

            if project_id:
                with contextlib.suppress(ValueError):
                    tasks = tasks.filter(project_id=int(project_id))

            # Order tasks for list view
            tasks = tasks.order_by("display_order", "created_at")

            context = {
                "tasks": tasks,
                "project_id": project_id,
                "view_type": view_type,
            }

            return self.render_to_response(context)

        except (DatabaseError, IntegrityError, OperationalError) as e:
            return self.handle_error(e, _("Failed to load task list"))


# ========== ADDITIONAL FUNCTION WRAPPERS ==========


def task_list_sortable_htmx(request: HttpRequest) -> HttpResponse:
    """Function-based wrapper for TaskListSortableView."""
    view = TaskListSortableView()
    view.request = request
    return view.get(request)


def save_drag_operation(
    request: HttpRequest,
    task: Task,
    operation_type: str,
    old_values: dict,
    new_values: dict,
) -> str:
    """Save drag operation for undo/redo functionality."""
    import uuid

    operation_id = str(uuid.uuid4())
    operation = {
        "id": operation_id,
        "task_id": task.id,
        "type": operation_type,
        "old_values": old_values,
        "new_values": new_values,
        "timestamp": timezone.now().isoformat(),
        "undone": False,
    }

    session_key = f"task_operations_{request.user.id}"
    operations = request.session.get(session_key, [])

    # Keep only last 10 operations per user
    operations = operations[-9:] + [operation]

    request.session[session_key] = operations
    request.session.modified = True

    return operation_id
