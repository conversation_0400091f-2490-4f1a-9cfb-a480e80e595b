"""Projects Forms Module.

Comprehensive form imports for the projects app.
"""

# Comment Forms
from .comment_forms import (
    TaskCommentEditForm,
    TaskCommentForm,
    TaskCommentReactionForm,
    TaskCommentReplyForm,
    TaskCommentSearchForm,
)

# Meeting Forms
from .meeting_forms import (
    MeetingFilterForm,
    MeetingForm,
)

# Notebook Forms
from .notebook_forms import (
    ProjectNotebookEntryForm,
)

# Project Forms
from .project_forms import (
    ProjectForm,
)

# Recurring Task Forms
from .recurring_forms import (
    RecurringTaskCreateForm,
    RecurringTaskExceptionForm,
    RecurringTaskPatternForm,
    RecurringTaskUpdateForm,
)

# Task Filter Forms
from .task_filter_forms import (
    BulkTaskActionForm,
    QuickFilterForm,
    SavedViewForm,
    TaskFilterForm,
)

# Task Forms
from .task_forms import (
    CommentForm,
    QuickTaskForm,
    TaskDependencyForm,
    TaskForm,
)

# Time Entry Forms
from .time_entry_forms import (
    QuickTimeEntryForm,
    TimeEntryForm,
    TimerForm,
)

# Export all forms
__all__ = [
    # Comment Forms
    "TaskCommentForm",
    "TaskCommentReplyForm",
    "TaskCommentEditForm",
    "TaskCommentReactionForm",
    "TaskCommentSearchForm",
    # Project Forms
    "ProjectForm",
    # Task Forms
    "TaskForm",
    "CommentForm",
    "TaskDependencyForm",
    "QuickTaskForm",
    # Task Filter Forms
    "TaskFilterForm",
    "QuickFilterForm",
    "SavedViewForm",
    "BulkTaskActionForm",
    # Time Entry Forms
    "TimeEntryForm",
    "QuickTimeEntryForm",
    "TimerForm",
    # Meeting Forms
    "MeetingForm",
    "MeetingFilterForm",
    # Notebook Forms
    "ProjectNotebookEntryForm",
    # Recurring Task Forms
    "RecurringTaskPatternForm",
    "RecurringTaskCreateForm",
    "RecurringTaskExceptionForm",
    "RecurringTaskUpdateForm",
]
