{% extends "base.html" %}

{% load static %}

{% block title %}Recurring Task Patterns{% endblock %}

{% block content %}
  <div class="container-fluid">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title">Recurring Task Patterns</h3>
            <div>
              <a href="{% url 'projects:recurring-task-create' %}"
                 class="btn btn-success me-2">
                <i class="fas fa-plus"></i> Create Recurring Task
              </a>
              <a href="{% url 'projects:recurring-pattern-create' %}"
                 class="btn btn-primary">
                <i class="fas fa-cog"></i> Create Pattern
              </a>
            </div>
          </div>
          <div class="card-body">
            <!-- Filter and Search -->
            <form method="get" class="row mb-3">
              <div class="col-md-4">
                <select name="status" class="form-select" onchange="this.form.submit()">
                  <option value="all" 
                    {% if status_filter == 'all' %}selected{% endif %}
                    >All Patterns</option>
                  <option value="active" 
                    {% if status_filter == 'active' %}selected{% endif %}
                    >Active Only</option>
                  <option value="inactive" 
                    {% if status_filter == 'inactive' %}selected{% endif %}
                    >Inactive Only</option>
                </select>
              </div>
              <div class="col-md-6">
                <div class="input-group">
                  <input type="text"
                         name="search"
                         class="form-control"
                         placeholder="Search patterns..."
                         value="{{ search_query }}">
                  <button class="btn btn-outline-secondary" type="submit">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>
              <div class="col-md-2">
                <a href="{% url 'projects:recurring-patterns' %}"
                   class="btn btn-outline-secondary w-100">Clear</a>
              </div>
            </form>
            <!-- Patterns List -->

            {% if patterns %}
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Type</th>
                      <th>Interval</th>
                      <th>Status</th>
                      <th>Tasks Created</th>
                      <th>Next Due</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>

                    {% for pattern in patterns %}
                      <tr>
                        <td>
                          <a href="{% url 'projects:recurring-pattern-detail' pattern.pk %}">{{ pattern.name }}</a>
                        </td>
                        <td>
                          <span class="badge bg-info">{{ pattern.get_recurrence_type_display }}</span>
                        </td>
                        <td>
                          Every {{ pattern.interval }}

                          {% if pattern.recurrence_type == 'weekly' and pattern.days_of_week %}
                            on

                            {% for day in pattern.days_of_week %}

                              {% if day == 0 %}
                                Mon
                              {% elif day == 1 %}
                                Tue
                              {% elif day == 2 %}
                                Wed
                              {% elif day == 3 %}
                                Thu
                              {% elif day == 4 %}
                                Fri
                              {% elif day == 5 %}
                                Sat
                              {% else %}
                                Sun
                              {% endif %}

                              {% if not forloop.last %},{% endif %}

                            {% endfor %}

                          {% elif pattern.recurrence_type == 'monthly' and pattern.day_of_month %}
                            on day {{ pattern.day_of_month }}
                          {% endif %}

                        </td>
                        <td>

                          {% if pattern.is_active %}
                            <span class="badge bg-success">Active</span>
                          {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                          {% endif %}

                        </td>
                        <td>
                          <span class="badge bg-primary">{{ pattern.task_instances.count }}</span>
                        </td>
                        <td>

                          {% if pattern.next_due %}
                            {{ pattern.next_due|date:"M d, Y g:i A" }}
                          {% else %}
                            <span class="text-muted">—</span>
                          {% endif %}

                        </td>
                        <td>
                          <div class="btn-group btn-group-sm">
                            <a href="{% url 'projects:recurring-pattern-detail' pattern.pk %}"
                               class="btn btn-outline-primary">
                              <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'projects:recurring-pattern-update' pattern.pk %}"
                               class="btn btn-outline-secondary">
                              <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'projects:recurring-pattern-toggle' pattern.pk %}" class="btn btn-outline- 
                              {% if pattern.is_active %}warning{% else %}success{% endif %}
                               " onclick="return confirm(' 
                              {% if pattern.is_active %}Deactivate{% else %}Activate{% endif %}
                               this pattern?')">
                              <i class="fas fa- 
                                {% if pattern.is_active %}pause{% else %}play{% endif %}
                               "></i>
                            </a>
                          </div>
                        </td>
                      </tr>
                    {% endfor %}

                  </tbody>
                </table>
              </div>
              <!-- Pagination -->

              {% if is_paginated %}
                <nav aria-label="Patterns pagination">
                  <ul class="pagination justify-content-center">

                    {% if page_obj.has_previous %}
                      <li class="page-item">
                        <a class="page-link" href="?page=1 
                          {% if search_query %}&search={{ search_query }}{% endif %}
  
                          {% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}
                         ">First</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }} 
                          {% if search_query %}&search={{ search_query }}{% endif %}
  
                          {% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}
                         ">Previous</a>
                      </li>
                    {% endif %}

                    <li class="page-item active">
                      <span class="page-link">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                    </li>

                    {% if page_obj.has_next %}
                      <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }} 
                          {% if search_query %}&search={{ search_query }}{% endif %}
  
                          {% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}
                         ">Next</a>
                      </li>
                      <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }} 
                          {% if search_query %}&search={{ search_query }}{% endif %}
  
                          {% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}
                         ">Last</a>
                      </li>
                    {% endif %}
                  </ul>
                </nav>
              {% endif %}

            {% else %}
              <div class="text-center py-5">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Recurring Patterns Found</h4>
                <p class="text-muted">

                  {% if search_query or status_filter != 'all' %}
                    No patterns match your current filters. Try adjusting your search or filter criteria.
                  {% else %}
                    Create your first recurring task pattern to automate repetitive tasks.
                  {% endif %}

                </p>
                <div class="mt-3">
                  <a href="{% url 'projects:recurring-task-create' %}"
                     class="btn btn-success me-2">
                    <i class="fas fa-plus"></i> Create Recurring Task
                  </a>
                  <a href="{% url 'projects:recurring-pattern-create' %}"
                     class="btn btn-primary">
                    <i class="fas fa-cog"></i> Create Pattern
                  </a>
                </div>
              </div>
            {% endif %}

          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
