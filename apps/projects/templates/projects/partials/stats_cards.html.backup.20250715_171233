{% extends "htmx_base.html" %}
{% load static %}
{% load i18n %}

{% block extra_css %}
  <style>
    .stats-cards-container {
      padding: 1rem 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 1rem;
      padding: 1.5rem;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--card-gradient);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
      border-color: var(--card-border-color);
    }

    .stat-card:hover::before {
      transform: scaleX(1);
    }

    .stat-card.total-projects {
      --card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --card-border-color: #667eea;
      --icon-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --icon-color: white;
    }

    .stat-card.active-projects {
      --card-gradient: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      --card-border-color: #28a745;
      --icon-bg: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      --icon-color: #155724;
    }

    .stat-card.at-risk-projects {
      --card-gradient: linear-gradient(135deg, #dc3545 0%, #e55a71 100%);
      --card-border-color: #dc3545;
      --icon-bg: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      --icon-color: #721c24;
    }

    .stat-card.completed-projects {
      --card-gradient: linear-gradient(135deg, #6f42c1 0%, #9C27B0 100%);
      --card-border-color: #6f42c1;
      --icon-bg: linear-gradient(135deg, #e2d9f3 0%, #d4d4f0 100%);
      --icon-color: #4a1a5c;
    }

    .stat-card.amber-projects {
      --card-gradient: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
      --card-border-color: #ffc107;
      --icon-bg: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
      --icon-color: #856404;
    }

    .stat-card.team-members {
      --card-gradient: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
      --card-border-color: #17a2b8;
      --icon-bg: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
      --icon-color: #0c5460;
    }

    .stat-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 2;
    }

    .stat-info {
      flex: 1;
      min-width: 0;
    }

    .stat-label {
      font-size: 0.875rem;
      color: #6c757d;
      margin: 0 0 0.5rem 0;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-value {
      font-size: 2.5rem;
      font-weight: 700;
      color: #212529;
      margin: 0 0 0.25rem 0;
      line-height: 1;
      position: relative;
    }

    .stat-change {
      font-size: 0.8rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.25rem;
      margin-top: 0.5rem;
    }

    .stat-change.positive {
      color: #28a745;
    }

    .stat-change.negative {
      color: #dc3545;
    }

    .stat-change.neutral {
      color: #6c757d;
    }

    .stat-icon-container {
      width: 4rem;
      height: 4rem;
      background: var(--icon-bg);
      border-radius: 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      position: relative;
      overflow: hidden;
    }

    .stat-icon-container::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transform: rotate(45deg);
      transition: all 0.3s ease;
    }

    .stat-card:hover .stat-icon-container::before {
      top: -25%;
      right: -25%;
    }

    .stat-icon {
      font-size: 1.5rem;
      color: var(--icon-color);
      position: relative;
      z-index: 2;
    }

    .loading-stat-card {
      background: #f8f9fa;
      border: 1px dashed #dee2e6;
      animation: pulse 2s infinite;
    }

    .loading-stat-card .stat-value {
      background: linear-gradient(90deg, #e9ecef 25%, #f8f9fa 50%, #e9ecef 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 0.25rem;
      color: transparent;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.8; }
      50% { opacity: 1; }
    }

    @keyframes shimmer {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    .stat-trend {
      position: absolute;
      top: 1rem;
      right: 1rem;
      width: 30px;
      height: 20px;
      opacity: 0.3;
    }

    .trend-line {
      stroke: currentColor;
      stroke-width: 2;
      fill: none;
      stroke-linecap: round;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
      }

      .stat-card {
        padding: 1.25rem;
      }

      .stat-value {
        font-size: 2rem;
      }

      .stat-icon-container {
        width: 3rem;
        height: 3rem;
      }

      .stat-icon {
        font-size: 1.25rem;
      }
    }

    /* High contrast mode */
    @media (prefers-contrast: high) {
      .stat-card {
        border-width: 2px;
      }

      .stat-label {
        font-weight: 600;
      }
    }

    /* Reduced motion */
    @media (prefers-reduced-motion: reduce) {
      .stat-card {
        transition: none;
      }

      .stat-card:hover {
        transform: none;
      }

      .loading-stat-card {
        animation: none;
      }

      .loading-stat-card .stat-value {
        animation: none;
      }
    }

    /* Update animations */
    .stat-card.updated {
      animation: flash-update 0.6s ease-in-out;
    }

    @keyframes flash-update {
      0% { transform: scale(1); }
      50% { transform: scale(1.02); box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
      100% { transform: scale(1); }
    }

    /* Performance optimizations */
    .stat-card {
      will-change: transform;
    }

    .stat-icon-container {
      will-change: transform;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="stats-cards-container"
       hx-get="{% url 'projects:stats_cards_htmx' %}"
       hx-trigger="stats-updated from:body, every 60s"
       hx-swap="outerHTML"
       role="region"
       aria-label="{% trans 'Project statistics overview' %}">

    <div class="stats-grid">
      <!-- Total Projects Card -->
      <div class="stat-card total-projects"
           tabindex="0"
           role="button"
           aria-label="{% blocktrans with count=total_projects %}Total projects: {{ count }}{% endblocktrans %}"
           title="{% trans 'Click to view all projects' %}">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">{% trans "Total Projects" %}</p>
            <p class="stat-value">{{ total_projects|default:0 }}</p>
            {% if total_projects_change is not None %}
              <div class="stat-change {% if total_projects_change > 0 %}positive{% elif total_projects_change < 0 %}negative{% else %}neutral{% endif %}">
                <i class="fas fa-{% if total_projects_change > 0 %}arrow-up{% elif total_projects_change < 0 %}arrow-down{% else %}minus{% endif %}" aria-hidden="true"></i>
                <span>{{ total_projects_change|floatformat:0 }}%</span>
                <span class="sr-only">{% trans "compared to last period" %}</span>
              </div>
            {% endif %}
          </div>
          <div class="stat-icon-container">
            <i class="fas fa-project-diagram stat-icon" aria-hidden="true"></i>
          </div>
        </div>
        <!-- Trend indicator -->
        <svg class="stat-trend" viewBox="0 0 30 20" aria-hidden="true">
          <polyline class="trend-line" points="2,18 8,12 14,15 20,8 26,5"></polyline>
        </svg>
      </div>

      <!-- Active Projects Card -->
      <div class="stat-card active-projects"
           tabindex="0"
           role="button"
           aria-label="{% blocktrans with count=active_projects %}Active projects: {{ count }}{% endblocktrans %}"
           title="{% trans 'Click to view active projects' %}">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">{% trans "Active Projects" %}</p>
            <p class="stat-value">{{ active_projects|default:0 }}</p>
            {% if active_projects_change is not None %}
              <div class="stat-change {% if active_projects_change > 0 %}positive{% elif active_projects_change < 0 %}negative{% else %}neutral{% endif %}">
                <i class="fas fa-{% if active_projects_change > 0 %}arrow-up{% elif active_projects_change < 0 %}arrow-down{% else %}minus{% endif %}" aria-hidden="true"></i>
                <span>{{ active_projects_change|floatformat:0 }}%</span>
                <span class="sr-only">{% trans "compared to last period" %}</span>
              </div>
            {% endif %}
          </div>
          <div class="stat-icon-container">
            <i class="bi bi-play-circle stat-icon" aria-hidden="true"></i>
          </div>
        </div>
        <svg class="stat-trend" viewBox="0 0 30 20" aria-hidden="true">
          <polyline class="trend-line" points="2,15 8,10 14,12 20,7 26,4"></polyline>
        </svg>
      </div>

      <!-- At Risk Projects Card -->
      <div class="stat-card at-risk-projects"
           tabindex="0"
           role="button"
           aria-label="{% blocktrans with count=red_status_projects %}At risk projects: {{ count }}{% endblocktrans %}"
           title="{% trans 'Click to view at-risk projects' %}">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">{% trans "At Risk Projects" %}</p>
            <p class="stat-value">{{ red_status_projects|default:0 }}</p>
            {% if red_status_change is not None %}
              <div class="stat-change {% if red_status_change > 0 %}negative{% elif red_status_change < 0 %}positive{% else %}neutral{% endif %}">
                <i class="fas fa-{% if red_status_change > 0 %}arrow-up{% elif red_status_change < 0 %}arrow-down{% else %}minus{% endif %}" aria-hidden="true"></i>
                <span>{{ red_status_change|floatformat:0 }}%</span>
                <span class="sr-only">{% trans "compared to last period" %}</span>
              </div>
            {% endif %}
          </div>
          <div class="stat-icon-container">
            <i class="fas fa-exclamation-triangle stat-icon" aria-hidden="true"></i>
          </div>
        </div>
        <svg class="stat-trend" viewBox="0 0 30 20" aria-hidden="true">
          <polyline class="trend-line" points="2,12 8,8 14,11 20,6 26,9"></polyline>
        </svg>
      </div>

      <!-- Completed Projects Card -->
      <div class="stat-card completed-projects"
           tabindex="0"
           role="button"
           aria-label="{% blocktrans with count=completed_projects %}Completed projects: {{ count }}{% endblocktrans %}"
           title="{% trans 'Click to view completed projects' %}">
        <div class="stat-content">
          <div class="stat-info">
            <p class="stat-label">{% trans "Completed Projects" %}</p>
            <p class="stat-value">{{ completed_projects|default:0 }}</p>
            {% if completed_projects_change is not None %}
              <div class="stat-change {% if completed_projects_change > 0 %}positive{% elif completed_projects_change < 0 %}negative{% else %}neutral{% endif %}">
                <i class="fas fa-{% if completed_projects_change > 0 %}arrow-up{% elif completed_projects_change < 0 %}arrow-down{% else %}minus{% endif %}" aria-hidden="true"></i>
                <span>{{ completed_projects_change|floatformat:0 }}%</span>
                <span class="sr-only">{% trans "compared to last period" %}</span>
              </div>
            {% endif %}
          </div>
          <div class="stat-icon-container">
            <i class="bi bi-check-circle stat-icon" aria-hidden="true"></i>
          </div>
        </div>
        <svg class="stat-trend" viewBox="0 0 30 20" aria-hidden="true">
          <polyline class="trend-line" points="2,16 8,11 14,8 20,5 26,3"></polyline>
        </svg>
      </div>

      <!-- Amber Status Projects Card -->
      {% if amber_status_projects is not None %}
        <div class="stat-card amber-projects"
             tabindex="0"
             role="button"
             aria-label="{% blocktrans with count=amber_status_projects %}Amber status projects: {{ count }}{% endblocktrans %}"
             title="{% trans 'Click to view amber status projects' %}">
          <div class="stat-content">
            <div class="stat-info">
              <p class="stat-label">{% trans "Amber Status" %}</p>
              <p class="stat-value">{{ amber_status_projects|default:0 }}</p>
              {% if amber_status_change is not None %}
                <div class="stat-change {% if amber_status_change > 0 %}negative{% elif amber_status_change < 0 %}positive{% else %}neutral{% endif %}">
                  <i class="fas fa-{% if amber_status_change > 0 %}arrow-up{% elif amber_status_change < 0 %}arrow-down{% else %}minus{% endif %}" aria-hidden="true"></i>
                  <span>{{ amber_status_change|floatformat:0 }}%</span>
                  <span class="sr-only">{% trans "compared to last period" %}</span>
                </div>
              {% endif %}
            </div>
            <div class="stat-icon-container">
              <i class="bi bi-clock stat-icon" aria-hidden="true"></i>
            </div>
          </div>
          <svg class="stat-trend" viewBox="0 0 30 20" aria-hidden="true">
            <polyline class="trend-line" points="2,14 8,9 14,12 20,8 26,6"></polyline>
          </svg>
        </div>
      {% endif %}

      <!-- Total Team Members Card -->
      {% if total_team_members is not None %}
        <div class="stat-card team-members"
             tabindex="0"
             role="button"
             aria-label="{% blocktrans with count=total_team_members %}Total team members: {{ count }}{% endblocktrans %}"
             title="{% trans 'Click to view team members' %}">
          <div class="stat-content">
            <div class="stat-info">
              <p class="stat-label">{% trans "Team Members" %}</p>
              <p class="stat-value">{{ total_team_members|default:0 }}</p>
              {% if team_members_change is not None %}
                <div class="stat-change {% if team_members_change > 0 %}positive{% elif team_members_change < 0 %}negative{% else %}neutral{% endif %}">
                  <i class="fas fa-{% if team_members_change > 0 %}arrow-up{% elif team_members_change < 0 %}arrow-down{% else %}minus{% endif %}" aria-hidden="true"></i>
                  <span>{{ team_members_change|floatformat:0 }}%</span>
                  <span class="sr-only">{% trans "compared to last period" %}</span>
                </div>
              {% endif %}
            </div>
            <div class="stat-icon-container">
              <i class="bi bi-persons stat-icon" aria-hidden="true"></i>
            </div>
          </div>
          <svg class="stat-trend" viewBox="0 0 30 20" aria-hidden="true">
            <polyline class="trend-line" points="2,13 8,10 14,7 20,9 26,4"></polyline>
          </svg>
        </div>
      {% endif %}
    </div>

    <!-- Screen reader announcements for updates -->
    <div id="stats-announcements"
         class="sr-only"
         aria-live="polite"
         aria-atomic="true">
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      initializeStatsCards();
    });

    // Re-initialize after HTMX swaps
    document.body.addEventListener('htmx:afterSwap', function(event) {
      if (event.target.closest('.stats-cards-container')) {
        initializeStatsCards();
      }
    });

    function initializeStatsCards() {
      const statCards = document.querySelectorAll('.stat-card');

      // Add click handlers for navigation
      statCards.forEach(card => {
        card.addEventListener('click', function() {
          const cardType = this.classList[1]; // e.g., 'total-projects'
          navigateToProjectsView(cardType);
        });

        // Keyboard navigation
        card.addEventListener('keydown', function(e) {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
          }
        });

        // Add hover effects for better UX
        card.addEventListener('mouseenter', function() {
          this.style.zIndex = '10';
        });

        card.addEventListener('mouseleave', function() {
          this.style.zIndex = '';
        });
      });

      // Handle HTMX updates
      document.body.addEventListener('htmx:beforeSwap', function(event) {
        if (event.target.classList.contains('stats-cards-container')) {
          // Add loading states to existing cards
          const existingCards = event.target.querySelectorAll('.stat-card');
          existingCards.forEach(card => {
            card.classList.add('loading-stat-card');
          });
        }
      });

      document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.classList.contains('stats-cards-container')) {
          // Add update animation to new cards
          const newCards = event.target.querySelectorAll('.stat-card');
          newCards.forEach((card, index) => {
            setTimeout(() => {
              card.classList.add('updated');
              setTimeout(() => {
                card.classList.remove('updated');
              }, 600);
            }, index * 100); // Stagger animations
          });

          announceStatsUpdate();
        }
      });

      function navigateToProjectsView(cardType) {
        let filterUrl = '';

        switch(cardType) {
          case 'total-projects':
            filterUrl = '{% url "projects:project_list" %}';
            break;
          case 'active-projects':
            filterUrl = '{% url "projects:project_list" %}?status=active';
            break;
          case 'at-risk-projects':
            filterUrl = '{% url "projects:project_list" %}?rag_status=Red';
            break;
          case 'completed-projects':
            filterUrl = '{% url "projects:project_list" %}?status=completed';
            break;
          case 'amber-projects':
            filterUrl = '{% url "projects:project_list" %}?rag_status=Amber';
            break;
          case 'team-members':
            filterUrl = '{% url "projects:team_list" %}';
            break;
          default:
            filterUrl = '{% url "projects:project_list" %}';
        }

        // Use HTMX navigation if available
        if (window.htmx) {
          htmx.ajax('GET', filterUrl, {
            target: 'main',
            indicator: '#page-loader'
          });
        } else {
          window.location.href = filterUrl;
        }
      }

      function announceStatsUpdate() {
        const announcements = document.getElementById('stats-announcements');
        if (announcements) {
          announcements.textContent = '{% trans "Project statistics have been updated" %}';
          setTimeout(() => {
            announcements.textContent = '';
          }, 2000);
        }
      }

      // Animate trend lines on hover
      statCards.forEach(card => {
        const trendLine = card.querySelector('.trend-line');
        if (trendLine) {
          const originalPoints = trendLine.getAttribute('points');

          card.addEventListener('mouseenter', function() {
            // Animate trend line
            const pathLength = trendLine.getTotalLength();
            trendLine.style.strokeDasharray = pathLength;
            trendLine.style.strokeDashoffset = pathLength;
            trendLine.style.transition = 'stroke-dashoffset 0.8s ease-in-out';

            // Trigger animation
            setTimeout(() => {
              trendLine.style.strokeDashoffset = '0';
            }, 50);
          });

          card.addEventListener('mouseleave', function() {
            trendLine.style.strokeDasharray = '';
            trendLine.style.strokeDashoffset = '';
            trendLine.style.transition = '';
          });
        }
      });

      // Performance optimization: Use Intersection Observer for animations
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.style.animationPlayState = 'running';
          } else {
            entry.target.style.animationPlayState = 'paused';
          }
        });
      }, { threshold: 0.1 });

      statCards.forEach(card => {
        if (card.classList.contains('loading-stat-card')) {
          observer.observe(card);
        }
      });
    }
  </script>
{% endblock %}
