"""Project Management Models

Handles projects, templates, phases, teams, and workflows with comprehensive
multi-tenant organization isolation and PostGIS spatial features.
"""

from __future__ import annotations

import logging
import uuid
from collections import deque
from datetime import datetime, timedelta
from typing import Any, ClassVar, Dict, List, Optional, Tuple, Union

try:
    import pytz
    from dateutil.relativedelta import relativedelta
except ImportError:
    # These will be imported inline if not available at module level
    relativedelta = None
    pytz = None

from django.conf import settings
from django.contrib.gis.db import models
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db.models import Count, F, Prefetch, Q, QuerySet, Sum
from django.db.models.signals import m2m_changed, post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from apps.core.models.business_rules import ProjectBusinessRules
from apps.core.models.validation_mixins import ProjectValidationMixin
from apps.core.types import (
    <PERSON>undingBox,
    <PERSON>ordinates,
    DjangoQuerySet,
    JSONDict,
    MetaData,
    PrimaryKey,
    SRID,
)
from apps.versioning.mixins import VersionedModelMixin

logger = logging.getLogger(__name__)


class BaseOptimizedManager(models.Manager[Any]):
    """Base manager with common optimization patterns."""

    def with_counts(self) -> QuerySet[Any]:
        """Add aggregated counts to prevent N+1 queries."""
        return self.get_queryset().annotate(
            utility_count=Count("utilities", distinct=True),
            conflict_count=Count("conflicts", distinct=True),
            task_count=Count("tasks", distinct=True),
            completed_task_count=Count("tasks", filter=Q(tasks__status="completed"), distinct=True),
        )

    def with_financial_data(self):
        """Add financial aggregations."""
        return self.get_queryset().annotate(
            total_time_hours=Sum("time_entries__duration_minutes") / 60.0,
            billable_hours=Sum("time_entries__duration_minutes", filter=Q(time_entries__billable=True)) / 60.0,
        )


class ProjectOptimizedManager(BaseOptimizedManager):
    """Optimized manager for Project model with comprehensive prefetching."""

    def get_queryset(self):
        """Return ProjectQuerySet for access to custom queryset methods."""
        return ProjectQuerySet(self.model, using=self._db)

    def with_related_data(self):
        """Prefetch all commonly accessed related data."""
        from django.apps import apps

        return (
            self.get_queryset()
            .select_related(
                "organization",
                "template",
                "utility_coordinator",
            )
            .prefetch_related(
                # Tasks with assignee information
                Prefetch(
                    "tasks",
                    queryset=apps.get_model("projects", "Task").objects.select_related("assigned_to"),
                ),
                # Recent activities
                Prefetch(
                    "project_activities",
                    queryset=apps.get_model("projects", "ProjectActivity")
                    .objects.select_related("user")
                    .order_by("-timestamp")[:10],
                ),
                # Team members
                Prefetch(
                    "team_members",
                    queryset=apps.get_model("projects", "ProjectMember")
                    .objects.filter(is_active=True)
                    .select_related("user"),
                ),
            )
        )

    def with_financial_calculations(self):
        """Add comprehensive financial calculations."""
        return self.get_queryset().annotate(
            # Time-based calculations
            total_time_minutes=Sum("financial_time_entries__duration_minutes"),
            billable_time_minutes=Sum(
                "financial_time_entries__duration_minutes",
                filter=Q(financial_time_entries__billable=True),
            ),
            # Cost calculations
            labor_cost=Sum("financial_time_entries__duration_minutes") * F("hourly_rate") / 60.0,
            # Progress calculations
            completed_tasks=Count("tasks", filter=Q(tasks__status="completed")),
            total_tasks=Count("tasks"),
        )

    def active_projects(self):
        """Get active projects with optimizations."""
        return (
            self.with_counts()
            .with_related_data()
            .filter(rag_status__in=["Green", "Amber", "Red"])
            .exclude(rag_status="Complete")
        )

    def for_dashboard(self):
        """Get projects optimized for dashboard display - delegate to queryset."""
        return self.get_queryset().for_dashboard()

    def for_user(self, user):
        """Get projects accessible to a specific user."""
        return (
            self.get_queryset()
            .filter(
                Q(manager_id=user.id)
                | Q(coordinator_id=str(user.id))
                | Q(egis_project_manager=user.username)
                | Q(utility_coordinator=user)
                | Q(team_members__user=user, team_members__is_active=True)
                | Q(tasks__assigned_to=user),
            )
            .distinct()
        )


class TaskOptimizedManager(BaseOptimizedManager):
    """Optimized manager for Task model with organization filtering."""

    def get_queryset(self):
        """Return base queryset for Task model."""
        return super().get_queryset()

    def for_organization(self, organization):
        """Filter tasks by organization for multi-tenant isolation."""
        return self.get_queryset().filter(organization=organization)

    def with_related_data(self):
        """Prefetch commonly accessed related data."""
        return (
            self.get_queryset()
            .select_related(
                "project",
                "created_by",
                "organization",
            )
            .prefetch_related(
                "assigned_to",
                "dependencies",
                "time_entries",
            )
        )

    def with_time_tracking(self):
        """Add time tracking aggregations."""
        return self.get_queryset().annotate(
            total_time_logged=Sum("time_entries__duration_minutes"),
            estimated_hours_remaining=F("estimated_hours") - (Sum("time_entries__duration_minutes") / 60.0),
        )


class QueryOptimizationMixin:
    """Mixin to add query optimization tracking."""

    def get_queryset(self):
        """Override to add query tracking."""
        return super().get_queryset()


class ProjectQuerySet(models.QuerySet):
    """Optimized QuerySet for Project model to eliminate N+1 queries."""

    def with_counts(self):
        """Add commonly needed counts to prevent N+1 queries."""
        return self.annotate(
            utility_count=Count("utilities"),
            conflict_count=Count("conflicts"),
            active_conflict_count=Count(
                "conflicts",
                filter=Q(conflicts__status__in=["open", "pending"]),
            ),
            task_count=Count("tasks"),
            pending_task_count=Count(
                "tasks",
                filter=Q(tasks__status__in=["pending", "in_progress"]),
            ),
            completed_task_count=Count("tasks", filter=Q(tasks__status="completed")),
            team_member_count=Count("team_members", filter=Q(team_members__is_active=True)),
            activity_count=Count("project_activities"),
        )

    def with_financial_calculations(self):
        """Add financial aggregations to prevent multiple queries."""
        return self.annotate(
            total_time_minutes=Sum("financial_time_entries__duration_minutes"),
            billable_time_minutes=Sum(
                "financial_time_entries__duration_minutes",
                filter=Q(financial_time_entries__is_billable=True),
            ),
        )

    def with_related_data(self):
        """Add select_related and prefetch_related for common data access."""
        from django.apps import apps

        Task = apps.get_model("projects", "Task")
        ProjectActivity = apps.get_model("projects", "ProjectActivity")
        ProjectMember = apps.get_model("projects", "ProjectMember")

        return self.select_related(
            "organization",
            "template",
        ).prefetch_related(
            # Prefetch active team members
            Prefetch(
                "team_members",
                queryset=ProjectMember.objects.filter(is_active=True).select_related("user"),
            ),
            # Prefetch recent activities
            Prefetch(
                "project_activities",
                queryset=ProjectActivity.objects.select_related("user").order_by("-timestamp")[:10],
            ),
            # Prefetch recent tasks
            Prefetch(
                "tasks",
                queryset=Task.objects.filter(status__in=["pending", "in_progress"]).select_related("assigned_to")[:20],
            ),
        )

    def for_dashboard(self):
        """Optimized queryset for dashboard display."""
        from django.apps import apps

        Task = apps.get_model("projects", "Task")

        return (
            self.with_counts()
            .with_financial_calculations()
            .select_related(
                "organization",
            )
            .prefetch_related(
                Prefetch(
                    "tasks",
                    queryset=Task.objects.filter(status__in=["pending", "in_progress"]).select_related("assigned_to")[
                        :5
                    ],
                ),
            )
        )

    def for_user(self, user):
        """Get projects accessible to a specific user with optimizations."""
        return self.filter(
            Q(manager_id=user.id)
            | Q(coordinator_id=str(user.id))
            | Q(egis_project_manager=user.username)
            | Q(utility_coordinator=user)
            | Q(team_members__user=user, team_members__is_active=True),
        ).distinct()

    def active(self):
        """Get active projects only."""
        return self.exclude(rag_status__in=["Complete", "Cancelled", "Archived"])


class ProjectManager(models.Manager):
    """Custom manager for Project model with optimized queries."""

    def get_queryset(self):
        return ProjectQuerySet(self.model, using=self._db)

    def with_counts(self):
        return self.get_queryset().with_counts()

    def with_financial_calculations(self):
        return self.get_queryset().with_financial_calculations()

    def with_related_data(self):
        return self.get_queryset().with_related_data()

    def for_dashboard(self):
        return self.get_queryset().for_dashboard()

    def for_user(self, user):
        return self.get_queryset().for_user(user)

    def active(self):
        return self.get_queryset().active()


class ProjectTemplate(models.Model):
    """Project templates for standardized workflows."""

    # Primary key
    id = models.CharField(max_length=255, primary_key=True)

    # Basic information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    icon = models.CharField(max_length=50, blank=True, null=True)
    color = models.CharField(max_length=7, blank=True, null=True)

    # Status flags
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)

    # Relationships
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="project_templates",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_templates",
    )

    # Configuration
    workflow_phases = models.JSONField(default=list)
    settings = models.JSONField(default=dict)

    # External IDs
    monday_id = models.CharField(max_length=50, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Project Template"
        verbose_name_plural = "Project Templates"
        ordering = ["name"]
        constraints = [models.UniqueConstraint(fields=["organization", "name"], name="unique_project_template_per_org")]

    def __str__(self) -> str:
        return self.name


class Project(VersionedModelMixin, ProjectValidationMixin, models.Model):
    """Main project entity with comprehensive validation."""

    # Primary key and basic info
    id = models.CharField(max_length=255, primary_key=True)
    name = models.CharField(max_length=255)
    client = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    # Spatial data (PostGIS)
    location = models.PointField(
        null=True,
        blank=True,
        srid=4326,
        help_text="Project location coordinates (WGS84)",
    )

    # Dates
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    ntp_date = models.DateField(blank=True, null=True)
    letting_bid_date = models.DateField(blank=True, null=True)
    status_update_date = models.DateField(blank=True, null=True)

    # Relationships
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="projects",
    )
    template = models.ForeignKey(
        "ProjectTemplate",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="projects",
    )
    utility_coordinator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="coordinated_projects",
        help_text="User with utility_coordinator role who coordinates utilities for this project",
    )

    # Management fields
    manager_id = models.UUIDField(blank=True, null=True)
    coordinator_id = models.CharField(max_length=100, blank=True, null=True)
    egis_project_manager = models.CharField(max_length=255, blank=True, null=True)
    egis_project_manager_email = models.EmailField(blank=True, null=True)
    client_pm = models.CharField(max_length=255, blank=True, null=True)
    client_contact = models.CharField(max_length=255, blank=True, null=True)

    # Priority and status
    high_priority_items = models.IntegerField(blank=True, null=True)
    medium_priority_items = models.IntegerField(blank=True, null=True)
    low_priority_items = models.IntegerField(blank=True, null=True)
    project_priority = models.CharField(max_length=50, blank=True, null=True)
    status = models.CharField(
        max_length=50,
        default="planning",
        choices=[
            ("planning", "Planning"),
            ("active", "Active"),
            ("completed", "Completed"),
            ("on-hold", "On Hold"),
        ],
        help_text="Overall project status",
    )
    rag_status = models.CharField(max_length=50, blank=True, null=True)
    project_health_rag = models.CharField(max_length=50, default="Project Health")
    current_phase = models.CharField(max_length=100, blank=True, null=True)
    last_milestone = models.CharField(max_length=255, blank=True, null=True)
    this_month_status = models.CharField(max_length=255, blank=True, null=True)

    # External identifiers
    monday_id = models.CharField(max_length=50, blank=True, null=True)
    record_id = models.CharField(max_length=255, blank=True, null=True)
    client_job_number = models.CharField(max_length=255, blank=True, null=True)
    project_id_only = models.CharField(max_length=100, blank=True, null=True)
    phase_id_only = models.CharField(max_length=100, blank=True, null=True)

    # Project type and funding
    work_type = models.CharField(max_length=100, blank=True, null=True)
    coordination_type = models.CharField(max_length=100, blank=True, null=True)
    project_funding = models.CharField(max_length=100, blank=True, null=True)

    # Financial fields
    budget = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Project budget in dollars",
    )
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    contract_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    billed_to_date = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    project_hours_for_billed = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    wip = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    billed_plus_wip = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    current_cost = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    billed_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)
    profit_to_date = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    profit_percentage = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Versioning configuration
    version_fields = [
        "name",
        "client",
        "description",
        "location",
        "start_date",
        "end_date",
        "manager_id",
        "status",
        "rag_status",
        "current_phase",
        "coordination_type",
        "project_funding",
        "ntp_date",
        "letting_bid_date",
        "this_month_status",
        "status_update_date",
        "client_pm",
        "budget",
        "hourly_rate",
        "contract_amount",
        "billed_to_date",
        "project_hours_for_billed",
        "wip",
        "billed_plus_wip",
        "current_cost",
        "billed_percentage",
        "profit_to_date",
        "profit_percentage",
        "project_priority",
        "egis_project_manager",
        "egis_project_manager_email",
        "client_contact",
        "project_health_rag",
        "coordinator_id",
        "utility_coordinator",
    ]

    def get_log_name(self) -> str:
        """Generate log namespace for project changes."""
        return "projects.log"

    # Custom manager for optimized queries
    objects = ProjectOptimizedManager()

    class Meta:
        app_label = "projects"
        verbose_name = "Project"
        verbose_name_plural = "Projects"
        ordering = ["name"]
        indexes = [
            models.Index(fields=["organization", "status"]),
            models.Index(fields=["organization", "rag_status"]),
            models.Index(fields=["client", "name"]),
            models.Index(fields=["start_date", "end_date"]),
            models.Index(fields=["manager_id"]),
            models.Index(fields=["utility_coordinator"]),
        ]
        permissions = [
            ("view_project_budget", "Can view project budget"),
            ("change_project_budget", "Can change project budget"),
            ("view_project_financials", "Can view all project financial information"),
            ("manage_project_team", "Can manage project team members"),
            ("export_project_data", "Can export project data"),
        ]

    def __str__(self) -> str:
        return f"{self.name} ({self.client})"

    def clean(self) -> None:
        """Validate project data using business rules and validation mixins."""
        # Call parent clean method (from ProjectValidationMixin)
        super().clean()

        # Validate budget constraints
        if self.budget is not None:
            if self.budget < 0:
                raise ValidationError({"budget": "Budget cannot be negative."})

            # Check if budget exceeds a reasonable maximum (e.g., $1 billion)
            if self.budget > 1000000000:
                raise ValidationError({"budget": "Budget exceeds maximum allowed value ($1 billion)."})

        # Validate date logic
        if self.start_date and self.end_date:
            if self.end_date < self.start_date:
                raise ValidationError(
                    {
                        "end_date": "End date cannot be before start date.",
                    }
                )

        # Validate NTP date logic
        if self.ntp_date and self.start_date:
            if self.start_date < self.ntp_date:
                raise ValidationError(
                    {
                        "start_date": "Project start date cannot be before Notice to Proceed (NTP) date.",
                    }
                )

        # Validate letting/bid date logic
        if self.letting_bid_date and self.start_date:
            if self.start_date < self.letting_bid_date:
                logger.warning(
                    "Project %s: Start date (%s) is before letting/bid date (%s)",
                    self.id,
                    self.start_date,
                    self.letting_bid_date,
                )

        # Validate status transitions
        if self.pk:  # Only check on updates
            try:
                old_instance = Project.objects.get(pk=self.pk)
                # Don't allow changing from completed back to active states
                if old_instance.status == "completed" and self.status in [
                    "planning",
                    "active",
                ]:
                    raise ValidationError(
                        {
                            "status": "Cannot change status from completed back to planning or active.",
                        }
                    )
            except Project.DoesNotExist:
                pass

        # Validate utility_coordinator has the correct role
        if self.utility_coordinator:
            # Check if user has utility coordinator role
            user_roles = getattr(self.utility_coordinator, "user_roles", None)
            if user_roles:
                has_coordinator_role = user_roles.filter(role__slug="utility-coordinator").exists()
                if not has_coordinator_role:
                    raise ValidationError(
                        {"utility_coordinator": "Selected user must have the utility_coordinator role."},
                    )

        # Apply business rules validation
        try:
            ProjectBusinessRules.validate_project_lifecycle(self)
        except ValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            # Log unexpected errors and convert to validation error
            logger.exception("Unexpected error in project lifecycle validation")
            raise ValidationError({"__all__": f"Validation error: {e!s}"})

    def get_comment_count(self) -> int:
        """Get total number of comments for this project - optimized to use annotation when available."""
        # Try to use annotation first (from with_counts())
        if hasattr(self, "comment_count"):
            return self.comment_count

        # Fallback to individual query if not annotated
        try:
            from django.apps import apps

            Comment = apps.get_model("CLEAR", "Comment")
            return Comment.objects.filter(
                commentable_type="project",
                commentable_id=str(self.id),
                deleted_at__isnull=True,
            ).count()
        except (ImportError, LookupError) as e:
            logger.warning(f"Could not load Comment model: {e}")
            return 0

    def get_recent_comments(self, limit: int = 5):
        """Get recent comments for this project."""
        try:
            from django.apps import apps

            Comment = apps.get_model("CLEAR", "Comment")
            return (
                Comment.objects.filter(
                    commentable_type="project",
                    commentable_id=str(self.id),
                    deleted_at__isnull=True,
                )
                .select_related("user")
                .order_by("-created_at")[:limit]
            )
        except (ImportError, LookupError) as e:
            logger.warning(f"Could not load Comment model: {e}")
            return Comment.objects.none() if "Comment" in locals() else []

    def get_utility_count(self) -> int:
        """Get utility count - optimized to use annotation when available."""
        try:
            return getattr(self, "utility_count", self.utilities.count())
        except AttributeError as e:
            logger.warning(f"Could not access utilities relationship: {e}")
            return 0

    def get_conflict_count(self) -> int:
        """Get conflict count - optimized to use annotation when available."""
        try:
            return getattr(self, "conflict_count", self.conflicts.count())
        except AttributeError as e:
            logger.warning(f"Could not access conflicts relationship: {e}")
            return 0

    def get_active_conflict_count(self) -> int:
        """Get active conflict count - optimized to use annotation when available."""
        try:
            return getattr(
                self,
                "active_conflict_count",
                self.conflicts.filter(status__in=["open", "pending"]).count(),
            )
        except AttributeError as e:
            logger.warning(f"Could not access conflicts relationship: {e}")
            return 0

    def get_task_count(self) -> int:
        """Get task count - optimized to use annotation when available."""
        return getattr(self, "task_count", self.tasks.count())

    def get_pending_task_count(self) -> int:
        """Get pending task count - optimized to use annotation when available."""
        return getattr(
            self,
            "pending_task_count",
            self.tasks.filter(status__in=["pending", "in_progress"]).count(),
        )

    def get_completion_percentage(self) -> float:
        """Calculate project completion percentage based on tasks."""
        total_tasks = self.get_task_count()
        if total_tasks == 0:
            return 0.0
        completed_tasks = getattr(self, "completed_task_count", self.tasks.filter(status="completed").count())
        return round((completed_tasks / total_tasks) * 100, 1)

    # Object-Level Permission Methods
    def user_has_access(self, user, permission_level: str = "view") -> bool:
        """Check if user has specific access level to this project.

        Permission levels:
        - 'view': Can view project details
        - 'edit': Can edit project details
        - 'manage': Can manage project members/settings
        - 'admin': Full project admin access
        """
        if not user or not user.is_authenticated:
            return False

        # Check organization membership first
        if not hasattr(user, "organization") or not user.organization:
            return False

        if user.organization != self.organization:
            return False

        # System admins have full access
        if user.is_superuser:
            return True

        # Check organization role - owners and admins have full access
        try:
            org_member = user.org_memberships.filter(organization=self.organization).first()
            if org_member and org_member.role in ["owner", "admin"]:
                return True
        except AttributeError as e:
            # User doesn't have org_memberships relationship
            logger.debug(f"Could not check organization membership: {e}")

        # Check if user is project manager (from legacy fields)
        if self.manager_id and self.manager_id == user.id:
            return True

        if self.coordinator_id and self.coordinator_id == str(user.id):
            return True

        if self.egis_project_manager and self.egis_project_manager == user.username:
            return True

        # Check if user is utility coordinator
        if self.utility_coordinator == user:
            return True

        # Check project membership
        project_member = self.team_members.filter(user=user, is_active=True).first()
        if not project_member:
            return False

        # Check permission level against member role
        return self.role_has_permission(project_member.role, permission_level)

    def get_user_role(self, user) -> str | None:
        """Get user role in this project."""
        if not user or not user.is_authenticated:
            return None

        # Check organization role first
        try:
            org_member = user.org_memberships.filter(organization=self.organization).first()
            if org_member and org_member.role in ["owner", "admin"]:
                return "admin"
        except AttributeError as e:
            # User doesn't have org_memberships relationship
            logger.debug(f"Could not check organization membership: {e}")

        # Check legacy manager fields
        if (
            (self.manager_id and self.manager_id == user.id)
            or (self.coordinator_id and self.coordinator_id == str(user.id))
            or (self.egis_project_manager and self.egis_project_manager == user.username)
        ):
            return "manager"

        # Check if user is utility coordinator
        if self.utility_coordinator == user:
            return "coordinator"

        # Check project membership
        project_member = self.team_members.filter(user=user, is_active=True).first()
        if project_member:
            return project_member.role

        return None

    def role_has_permission(self, role: str, permission_level: str) -> bool:
        """Check if a role has the required permission level."""
        # Role-based permissions mapping
        role_permissions = {
            "manager": ["view", "edit", "manage", "admin"],
            "coordinator": ["view", "edit", "manage"],
            "contributor": ["view", "edit"],
            "viewer": ["view"],
        }

        user_permissions = role_permissions.get(role, [])
        return permission_level in user_permissions

    def add_member(self, user, role="contributor", added_by=None):
        """Add a user to the project team."""
        try:
            from apps.core.models.permissions import PermissionAuditLog
        except ImportError:
            PermissionAuditLog = None

        # Check if user is already a member
        existing_member = self.team_members.filter(user=user).first()
        if existing_member:
            if existing_member.is_active:
                return existing_member
            # Reactivate inactive member
            existing_member.is_active = True
            existing_member.role = role
            existing_member.save()
            return existing_member

        # Create new membership
        member = ProjectMember.objects.create(project=self, user=user, role=role, added_by=added_by)

        # Log the action
        if PermissionAuditLog:
            try:
                PermissionAuditLog.log_permission_change(
                    action_type="member_added",
                    resource_type="project",
                    resource_id=self.id,
                    user=user,
                    performed_by=added_by,
                    new_value={"role": role},
                )
            except (ImportError, AttributeError) as e:
                # Permission audit log not available or error logging
                logger.debug(f"Could not log permission change: {e}")

        return member

    def remove_member(self, user, removed_by=None):
        """Remove a user from the project team."""
        try:
            from apps.core.models.permissions import PermissionAuditLog
        except ImportError:
            PermissionAuditLog = None

        member = self.team_members.filter(user=user, is_active=True).first()
        if member:
            old_role = member.role
            member.is_active = False
            member.save()

            # Log the action
            if PermissionAuditLog:
                try:
                    PermissionAuditLog.log_permission_change(
                        action_type="member_removed",
                        resource_type="project",
                        resource_id=self.id,
                        user=user,
                        performed_by=removed_by,
                        old_value={"role": old_role},
                    )
                except (ImportError, AttributeError) as e:
                    # Permission audit log not available or error logging
                    logger.debug(f"Could not log permission change: {e}")

            return True
        return False

    def change_member_role(self, user, new_role, changed_by=None):
        """Change a project member role."""
        try:
            from apps.core.models.permissions import PermissionAuditLog
        except ImportError:
            PermissionAuditLog = None

        member = self.team_members.filter(user=user, is_active=True).first()
        if member:
            old_role = member.role
            member.role = new_role
            member.save()

            # Log the action
            if PermissionAuditLog:
                try:
                    PermissionAuditLog.log_permission_change(
                        action_type="role_changed",
                        resource_type="project",
                        resource_id=self.id,
                        user=user,
                        performed_by=changed_by,
                        old_value={"role": old_role},
                        new_value={"role": new_role},
                    )
                except (ImportError, AttributeError) as e:
                    # Permission audit log not available or error logging
                    logger.debug(f"Could not log permission change: {e}")

            return True
        return False

    def get_accessible_to_user(self, user, permission_level="view"):
        """Get all projects accessible to a user with specified permission level."""
        if not user or not user.is_authenticated:
            return self.objects.none()

        if not hasattr(user, "organization") or not user.organization:
            return self.objects.none()

        # Start with organization filter
        queryset = self.objects.filter(organization=user.organization)

        # System admins have access to all projects in their org
        if user.is_superuser:
            return queryset

        # Check organization role
        try:
            org_member = user.org_memberships.filter(organization=user.organization).first()
            if org_member and org_member.role in ["owner", "admin"]:
                return queryset
        except AttributeError as e:
            # User doesn't have org_memberships relationship
            logger.debug(f"Could not check organization membership: {e}")

        # Filter by project membership
        accessible_projects = []
        for project in queryset:
            if project.user_has_access(user, permission_level):
                accessible_projects.append(project.id)

        return queryset.filter(id__in=accessible_projects)


class Person(VersionedModelMixin, models.Model):
    """Master stakeholder directory - comprehensive person management."""

    # Basic Information
    full_name = models.CharField(max_length=255, db_index=True)
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100, blank=True, null=True)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    nickname = models.CharField(max_length=100, blank=True, null=True)
    title = models.CharField(max_length=100, blank=True, null=True)
    suffix = models.CharField(max_length=20, blank=True, null=True)  # Jr., Sr., III, etc.

    # Professional Information
    job_title = models.CharField(max_length=255, blank=True, null=True)
    department = models.CharField(max_length=255, blank=True, null=True)
    contact_company = models.CharField(max_length=255, blank=True, null=True)
    company_abbreviation = models.CharField(max_length=20, blank=True, null=True)
    expertise = models.JSONField(default=list, blank=True)  # List of expertise areas
    certifications = models.JSONField(default=list, blank=True)  # Professional certifications

    # Contact Information
    email = models.EmailField(blank=True, null=True, db_index=True)
    secondary_email = models.EmailField(blank=True, null=True)
    business_phone = models.CharField(max_length=20, blank=True, null=True)
    mobile_phone = models.CharField(max_length=20, blank=True, null=True)
    home_phone = models.CharField(max_length=20, blank=True, null=True)
    fax_number = models.CharField(max_length=20, blank=True, null=True)
    preferred_contact_method = models.CharField(
        max_length=50,
        choices=[
            ("email", "Email"),
            ("phone", "Phone"),
            ("mobile", "Mobile"),
            ("text", "Text Message"),
            ("mail", "Physical Mail"),
        ],
        default="email",
        blank=True,
        null=True,
    )

    # Address Information
    address = models.TextField(blank=True, null=True)
    address_line2 = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    state = models.CharField(max_length=50, blank=True, null=True)
    zip_code = models.CharField(max_length=20, blank=True, null=True)
    country = models.CharField(max_length=100, default="United States", blank=True, null=True)

    # Secondary Address (e.g., billing address)
    secondary_address = models.TextField(blank=True, null=True)
    secondary_address_line2 = models.CharField(max_length=255, blank=True, null=True)
    secondary_city = models.CharField(max_length=100, blank=True, null=True)
    secondary_state = models.CharField(max_length=50, blank=True, null=True)
    secondary_zip_code = models.CharField(max_length=20, blank=True, null=True)
    secondary_country = models.CharField(max_length=100, blank=True, null=True)

    # Availability and Preferences
    timezone = models.CharField(max_length=50, default="America/New_York", blank=True, null=True)
    preferred_meeting_times = models.JSONField(default=dict, blank=True)  # Day/time preferences
    do_not_contact_before = models.TimeField(blank=True, null=True)
    do_not_contact_after = models.TimeField(blank=True, null=True)
    communication_preferences = models.JSONField(default=dict, blank=True)  # Communication settings
    language_preferences = models.JSONField(default=list, blank=True)  # Preferred languages

    # Emergency Contact Information
    emergency_contact_name = models.CharField(max_length=255, blank=True, null=True)
    emergency_contact_relationship = models.CharField(max_length=100, blank=True, null=True)
    emergency_contact_phone = models.CharField(max_length=20, blank=True, null=True)
    emergency_contact_email = models.EmailField(blank=True, null=True)

    # Social Media and Professional Profiles
    linkedin_url = models.URLField(blank=True, null=True)
    twitter_handle = models.CharField(max_length=100, blank=True, null=True)
    github_username = models.CharField(max_length=100, blank=True, null=True)
    website_url = models.URLField(blank=True, null=True)
    social_media_profiles = models.JSONField(default=dict, blank=True)  # Other social profiles

    # Document Attachments
    profile_photo_url = models.URLField(blank=True, null=True)
    resume_url = models.URLField(blank=True, null=True)
    business_card_url = models.URLField(blank=True, null=True)
    document_attachments = models.JSONField(default=list, blank=True)  # List of document URLs

    # Categorization and Tags
    person_type = models.CharField(
        max_length=50,
        choices=[
            ("employee", "Employee"),
            ("contractor", "Contractor"),
            ("client", "Client"),
            ("vendor", "Vendor"),
            ("partner", "Partner"),
            ("stakeholder", "Stakeholder"),
            ("consultant", "Consultant"),
            ("government", "Government Official"),
            ("community", "Community Member"),
            ("other", "Other"),
        ],
        default="stakeholder",
        db_index=True,
    )
    stakeholder_type = models.CharField(max_length=100, blank=True, null=True)  # Legacy field
    type_delivery = models.CharField(max_length=100, blank=True, null=True)  # Legacy field
    tags = models.JSONField(default=list, blank=True)  # Flexible tagging system
    custom_fields = models.JSONField(default=dict, blank=True)  # For org-specific fields

    # Notes and Internal Information
    notes = models.TextField(blank=True, null=True)
    internal_notes = models.TextField(blank=True, null=True)  # Private notes not shared
    importance_level = models.CharField(
        max_length=20,
        choices=[
            ("vip", "VIP"),
            ("high", "High"),
            ("medium", "Medium"),
            ("low", "Low"),
        ],
        default="medium",
        blank=True,
        null=True,
    )

    # Status and Lifecycle
    is_active = models.BooleanField(default=True)
    date_of_birth = models.DateField(blank=True, null=True)
    anniversary_date = models.DateField(blank=True, null=True)  # Work anniversary or other
    last_contact_date = models.DateTimeField(blank=True, null=True)
    next_follow_up_date = models.DateTimeField(blank=True, null=True)

    # Relationships - Required for multi-tenant isolation
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="persons",
        help_text="Organization this person belongs to (for multi-tenant isolation)",
    )
    projects = models.ManyToManyField("Project", related_name="persons", blank=True, through="PersonProject")
    companies = models.ManyToManyField(
        "authentication.Organization",
        related_name="affiliated_persons",
        blank=True,
        through="PersonCompany",
    )

    # User Account Link (if this person has a system account)
    user_account = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="person_profile",
    )

    # Metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="persons_created",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Search and Display
    search_vector = models.TextField(blank=True, null=True)  # For full-text search
    display_order = models.IntegerField(default=0)  # For custom ordering

    # Versioning configuration
    version_fields = [
        "full_name",
        "first_name",
        "last_name",
        "middle_name",
        "nickname",
        "title",
        "suffix",
        "job_title",
        "department",
        "contact_company",
        "company_abbreviation",
        "expertise",
        "certifications",
        "email",
        "secondary_email",
        "business_phone",
        "mobile_phone",
        "home_phone",
        "fax_number",
        "preferred_contact_method",
        "address",
        "address_line2",
        "city",
        "state",
        "zip_code",
        "country",
        "secondary_address",
        "secondary_city",
        "secondary_state",
        "secondary_zip_code",
        "timezone",
        "preferred_meeting_times",
        "communication_preferences",
        "emergency_contact_name",
        "emergency_contact_phone",
        "linkedin_url",
        "website_url",
        "social_media_profiles",
        "person_type",
        "stakeholder_type",
        "tags",
        "custom_fields",
        "notes",
        "internal_notes",
        "importance_level",
        "is_active",
        "date_of_birth",
        "last_contact_date",
        "next_follow_up_date",
    ]

    def get_log_name(self) -> str:
        """Generate log namespace for person changes."""
        return "persons.log"

    class Meta:
        app_label = "projects"
        verbose_name = "Person"
        verbose_name_plural = "People"
        indexes = [
            models.Index(fields=["full_name", "contact_company"]),
            models.Index(fields=["email"]),
            models.Index(fields=["person_type", "is_active"]),
            models.Index(fields=["organization", "person_type"]),
            models.Index(fields=["last_contact_date"]),
            models.Index(fields=["next_follow_up_date"]),
        ]
        ordering = ["full_name"]
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "email"],
                name="unique_person_email_per_org",
                condition=models.Q(email__isnull=False) & ~models.Q(email=""),
            )
        ]

    def __str__(self) -> str:
        company_info = f" ({self.contact_company})" if self.contact_company else ""
        return f"{self.full_name}{company_info}"

    def save(self, *args, **kwargs) -> None:
        """Auto-populate search vector and parse full name if needed."""
        # Parse full name into components if not provided
        if self.full_name and not (self.first_name or self.last_name):
            name_parts = self.full_name.split()
            if len(name_parts) >= 2:
                self.first_name = name_parts[0]
                self.last_name = name_parts[-1]
                if len(name_parts) > 2:
                    self.middle_name = " ".join(name_parts[1:-1])
            elif len(name_parts) == 1:
                self.last_name = name_parts[0]

        # Build search vector for full-text search
        search_terms = [
            self.full_name,
            self.first_name,
            self.last_name,
            self.nickname,
            self.email,
            self.contact_company,
            self.job_title,
            self.department,
        ]
        self.search_vector = " ".join(filter(None, search_terms))

        super().save(*args, **kwargs)

    @property
    def display_name(self) -> str:
        """Get the best display name for the person."""
        if self.nickname:
            return f"{self.nickname} ({self.full_name})"
        return self.full_name

    @property
    def contact_info(self) -> dict:
        """Get primary contact information as a dict."""
        return {
            "email": self.email,
            "phone": self.mobile_phone or self.business_phone or self.home_phone,
            "preferred_method": self.preferred_contact_method,
        }

    def get_active_projects(self):
        """Get all active projects this person is involved in."""
        return self.projects.filter(rag_status__in=["Green", "Amber", "Red"]).distinct()

    def get_communication_history(self):
        """Get communication history for this person."""
        # This would integrate with messaging/activity tracking
        try:
            from django.apps import apps

            Activity = apps.get_model("activity", "Activity")
            return Activity.objects.filter(target_type="person", target_id=str(self.id)).order_by("-created_at")
        except (ImportError, LookupError) as e:
            logger.warning(f"Could not load Activity model: {e}")
            from django.db.models import QuerySet

            return QuerySet().none()

    def can_contact_now(self) -> bool:
        """Check if it's appropriate to contact this person now."""
        # from datetime import datetime (moved to top level)

        import pytz

        if not self.timezone:
            return True

        try:
            tz = pytz.timezone(self.timezone)
            now = datetime.now(tz).time()

            if self.do_not_contact_before and now < self.do_not_contact_before:
                return False
            return not (self.do_not_contact_after and now > self.do_not_contact_after)
        except (ImportError, pytz.exceptions.UnknownTimeZoneError) as e:
            logger.debug(f"Timezone processing error: {e}")
            return True


class PersonProject(models.Model):
    """Through model for Person-Project relationships."""

    person = models.ForeignKey(Person, on_delete=models.CASCADE)
    project = models.ForeignKey("Project", on_delete=models.CASCADE)
    role = models.CharField(max_length=100, blank=True, null=True)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    is_primary_contact = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        unique_together = [["person", "project", "role"]]
        verbose_name = "Person Project"
        verbose_name_plural = "Person Projects"

    def __str__(self) -> str:
        return f"{self.person.full_name} - {self.project.name} ({self.role})"


class PersonCompany(models.Model):
    """Through model for Person-Company relationships."""

    person = models.ForeignKey(Person, on_delete=models.CASCADE)
    company = models.ForeignKey("authentication.Organization", on_delete=models.CASCADE)
    role = models.CharField(max_length=100, blank=True, null=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    is_primary_contact = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        unique_together = [["person", "company", "role"]]
        verbose_name = "Person Company"
        verbose_name_plural = "Person Companies"

    def __str__(self) -> str:
        return f"{self.person.full_name} - {self.company.name} ({self.role})"


# Keep Stakeholder as an alias for backward compatibility
Stakeholder = Person


class Task(VersionedModelMixin, models.Model, QueryOptimizationMixin):
    """Project tasks and task management."""

    # Status and Priority Choices
    STATUS_CHOICES = [
        ("pending", "Pending"),
        ("in-progress", "In Progress"),
        ("completed", "Completed"),
    ]

    PRIORITY_CHOICES = [
        ("low", "Low"),
        ("medium", "Medium"),
        ("high", "High"),
        ("critical", "Critical"),
    ]

    # Relationships
    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="tasks")
    assigned_to = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="assigned_tasks",
        help_text="Users assigned to this task",
    )
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="created_tasks")
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="tasks",
        help_text="Organization this task belongs to (for multi-tenant isolation)",
    )

    # Scenario Planning
    scenario = models.ForeignKey(
        "Scenario",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="scenario_tasks",
        help_text="Scenario this task belongs to (null for baseline project tasks)",
    )

    # Basic Information
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)

    # Status and Priority
    status = models.CharField(
        max_length=50,
        default="pending",
        choices=STATUS_CHOICES,
    )
    priority = models.CharField(
        max_length=20,
        default="medium",
        choices=PRIORITY_CHOICES,
    )

    # Dates
    due_date = models.DateTimeField(blank=True, null=True)
    start_date = models.DateTimeField(blank=True, null=True)
    completion_date = models.DateTimeField(blank=True, null=True)

    # Time Tracking
    estimated_hours = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    actual_hours = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)
    progress_percentage = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Task completion percentage (0-100)",
    )

    # Additional Data
    tags = models.JSONField(default=list)
    attachments = models.JSONField(default=list)
    dependencies = models.ManyToManyField(
        "self",
        symmetrical=False,
        blank=True,
        related_name="dependent_tasks",
        help_text="Tasks that must be completed before this task can start",
    )

    # Recurring Task Fields
    recurring_pattern = models.ForeignKey(
        "RecurringTaskPattern",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="task_instances",
        help_text="Recurrence pattern for this task if it's part of a recurring series",
    )
    parent_task = models.ForeignKey(
        "self",
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name="recurring_instances",
        help_text="Parent task for recurring task instances",
    )
    is_recurring_template = models.BooleanField(
        default=False,
        help_text="Whether this task serves as a template for generating recurring instances",
    )
    occurrence_number = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Sequence number for recurring task instances (1, 2, 3, etc.)",
    )
    original_due_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Original calculated due date before any manual modifications",
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Versioning configuration
    version_fields = [
        "title",
        "description",
        "status",
        "priority",
        "due_date",
        "start_date",
        "completion_date",
        "estimated_hours",
        "actual_hours",
        "tags",
        "attachments",
        "organization",
        "scenario",
    ]

    def get_log_name(self) -> str:
        """Generate log namespace for task changes."""
        return "tasks.log"

    def clean(self) -> None:
        """Validate task data."""
        super().clean()

        # Validate organization consistency
        if self.project and self.organization:
            if self.project.organization != self.organization:
                raise ValidationError({"organization": "Task organization must match project organization."})

        # Auto-set organization from project if not provided
        if self.project and not self.organization:
            self.organization = self.project.organization

        # Validate status changes with dependencies
        if self.pk:  # Only validate for existing tasks
            self._validate_status_with_dependencies()

    def _validate_status_with_dependencies(self) -> None:
        """Validate status changes respect dependency constraints."""
        if self.status == "completed":
            # Check if all dependencies are completed
            incomplete_dependencies = self.dependencies.exclude(status="completed")
            if incomplete_dependencies.exists():
                incomplete_titles = list(incomplete_dependencies.values_list("title", flat=True))
                incomplete_list = ", ".join(incomplete_titles)
                raise ValidationError(
                    {"status": f"Cannot complete task while dependencies are incomplete: {incomplete_list}"}
                )

        elif self.status == "in-progress":
            # Check if all dependencies are at least in-progress or completed
            blocking_dependencies = self.dependencies.filter(status="pending")
            if blocking_dependencies.exists():
                blocking_titles = list(blocking_dependencies.values_list("title", flat=True))
                raise ValidationError(
                    {"status": f"Cannot start task while dependencies are pending: {', '.join(blocking_titles)}"}
                )

    def validate_dependency_addition(self, dependency_task) -> None:
        """Validate adding a dependency would not create circular references."""
        if dependency_task == self:
            raise ValidationError("Task cannot depend on itself")

        if dependency_task.organization != self.organization:
            raise ValidationError("Task dependencies must be within the same organization")

        if dependency_task.project != self.project:
            raise ValidationError("Task dependencies must be within the same project")

        # Check for circular dependencies using DFS
        if self._would_create_circular_dependency(dependency_task):
            raise ValidationError(f"Adding dependency '{dependency_task.title}' would create a circular dependency")

    def _would_create_circular_dependency(self, new_dependency) -> bool:
        """Check if adding a dependency would create a circular reference using DFS."""
        visited = set()

        def dfs(task_id: int) -> bool:
            if task_id in visited:
                return True  # Found a cycle

            visited.add(task_id)

            # Check all dependencies of the current task
            for dep in Task.objects.filter(dependent_tasks=task_id).values_list("id", flat=True):
                if dfs(dep):
                    return True

            visited.remove(task_id)  # Backtrack
            return False

        # Start DFS from the new dependency and see if we can reach self
        return dfs(new_dependency.id)

    def has_circular_dependencies(self) -> bool:
        """Check if this task is part of any circular dependency chain."""
        visited = set()
        rec_stack = set()

        def dfs(task_id: int) -> bool:
            if task_id in rec_stack:
                return True  # Found a cycle

            if task_id in visited:
                return False

            visited.add(task_id)
            rec_stack.add(task_id)

            # Check all dependencies
            for dep_id in Task.objects.filter(dependent_tasks=task_id).values_list("id", flat=True):
                if dfs(dep_id):
                    return True

            rec_stack.remove(task_id)
            return False

        return dfs(self.id)

    def get_dependency_chain(self, include_self: bool = True) -> list:
        """Get the complete dependency chain for this task using topological sort."""
        visited = set()
        result = []

        def dfs(task):
            if task.id in visited:
                return

            visited.add(task.id)

            # First, visit all dependencies
            for dependency in task.dependencies.all():
                dfs(dependency)

            # Then add this task to result
            result.append(task)

        dfs(self)

        if not include_self and result and result[-1].id == self.id:
            result.pop()

        return result

    def get_all_dependencies(self) -> set:
        """Get all direct and indirect dependencies of this task."""
        all_deps = set()

        def collect_dependencies(task):
            for dep in task.dependencies.all():
                if dep.id not in all_deps:
                    all_deps.add(dep.id)
                    collect_dependencies(dep)

        collect_dependencies(self)
        return Task.objects.filter(id__in=all_deps)

    def get_all_dependents(self) -> set:
        """Get all direct and indirect dependent tasks."""
        all_dependents = set()

        def collect_dependents(task):
            for dependent in task.dependent_tasks.all():
                if dependent.id not in all_dependents:
                    all_dependents.add(dependent.id)
                    collect_dependents(dependent)

        collect_dependents(self)
        return Task.objects.filter(id__in=all_dependents)

    def get_critical_path(self) -> dict:
        """Calculate critical path analysis for this task and its dependencies."""
        dependency_chain = self.get_dependency_chain()

        # Calculate the critical path duration
        sum(task.estimated_hours or 0 for task in dependency_chain)

        # Find the longest sequential path
        longest_path = []
        max_duration = 0

        for task in dependency_chain:
            current_path = [task]
            current_duration = task.estimated_hours or 0

            # Build path from this task backwards
            current_task = task
            while current_task.dependencies.exists():
                # Find the dependency with the longest estimated time
                longest_dep = max(
                    current_task.dependencies.all(),
                    key=lambda dep: dep.estimated_hours or 0,
                    default=None,
                )
                if longest_dep:
                    current_path.insert(0, longest_dep)
                    current_duration += longest_dep.estimated_hours or 0
                    current_task = longest_dep
                else:
                    break

            if current_duration > max_duration:
                max_duration = current_duration
                longest_path = current_path

        return {
            "critical_path": longest_path,
            "total_duration_hours": max_duration,
            "total_tasks": len(longest_path),
            "completion_percentage": self._calculate_path_completion(longest_path),
        }

    def _calculate_path_completion(self, path: list) -> float:
        """Calculate completion percentage for a task path."""
        if not path:
            return 0.0

        completed_tasks = sum(1 for task in path if task.status == "completed")
        return round((completed_tasks / len(path)) * 100, 1)

    def can_start(self) -> bool:
        """Check if this task can be started based on dependency status."""
        return not self.dependencies.filter(status="pending").exists()

    def can_complete(self) -> bool:
        """Check if this task can be completed based on dependency status."""
        return not self.dependencies.exclude(status="completed").exists()

    def get_blocking_dependencies(self):
        """Get dependencies that are blocking this task from starting."""
        return self.dependencies.filter(status="pending")

    def get_incomplete_dependencies(self):
        """Get dependencies that are preventing this task from being completed."""
        return self.dependencies.exclude(status="completed")

    def add_dependency(self, dependency_task):
        """Safely add a dependency with validation."""
        self.validate_dependency_addition(dependency_task)
        self.dependencies.add(dependency_task)

    def remove_dependency(self, dependency_task):
        """Remove a dependency relationship."""
        self.dependencies.remove(dependency_task)

    # Recurring Task Methods
    def is_recurring(self) -> bool:
        """Check if this task is part of a recurring series."""
        return self.recurring_pattern is not None

    def is_recurring_instance(self) -> bool:
        """Check if this task is a generated instance from a recurring pattern."""
        return self.parent_task is not None and self.recurring_pattern is not None

    def get_next_occurrence(self):
        """Get the next scheduled occurrence for this recurring task."""
        if not self.recurring_pattern:
            return None
        return self.recurring_pattern.get_next_occurrence(after=self.due_date)

    def get_recurring_siblings(self):
        """Get all other instances from the same recurring pattern."""
        if not self.recurring_pattern:
            return Task.objects.none()
        return self.recurring_pattern.task_instances.exclude(id=self.id)

    def create_next_occurrence(self, save=True):
        """Create the next occurrence of this recurring task."""
        if not self.recurring_pattern:
            return None

        next_due_date = self.get_next_occurrence()
        if not next_due_date:
            return None

        # Find the highest occurrence number in the series
        max_occurrence = (
            self.recurring_pattern.task_instances.aggregate(max_occurrence=models.Max("occurrence_number"))[
                "max_occurrence"
            ]
            or 0
        )

        # Create new task instance
        new_task = Task(
            title=self.title,
            description=self.description,
            project=self.project,
            organization=self.organization,
            created_by=self.created_by,
            priority=self.priority,
            estimated_hours=self.estimated_hours,
            due_date=next_due_date,
            original_due_date=next_due_date,
            status="pending",
            recurring_pattern=self.recurring_pattern,
            parent_task=self.parent_task or self,
            occurrence_number=max_occurrence + 1,
            tags=self.tags.copy() if self.tags else [],
            attachments=self.attachments.copy() if self.attachments else [],
        )

        if save:
            new_task.save()
            # Copy task assignments
            for user in self.assigned_to.all():
                new_task.assigned_to.add(user)

        return new_task

    def update_recurring_series(self, update_future=False, **kwargs):
        """Update this task and optionally all future occurrences in the series."""
        if not self.recurring_pattern:
            # Not a recurring task, just update this task
            for field, value in kwargs.items():
                setattr(self, field, value)
            self.save()
            return

        # Update this task
        for field, value in kwargs.items():
            setattr(self, field, value)
        self.save()

        if update_future:
            # Update all future occurrences in the series
            future_tasks = self.recurring_pattern.task_instances.filter(
                due_date__gt=self.due_date,
                status="pending",  # Only update tasks that haven't started
            )

            for task in future_tasks:
                for field, value in kwargs.items():
                    if field not in [
                        "due_date",
                        "original_due_date",
                        "occurrence_number",
                    ]:
                        setattr(task, field, value)
                task.save()

    # Optimized manager for performance
    objects = TaskOptimizedManager()

    class Meta:
        app_label = "projects"
        verbose_name = "Task"
        verbose_name_plural = "Tasks"
        indexes = [
            models.Index(fields=["project", "status"]),
            models.Index(fields=["organization", "status"]),
            models.Index(fields=["due_date"]),
            models.Index(fields=["priority", "status"]),
            models.Index(fields=["created_by"]),
            models.Index(fields=["status", "due_date"]),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(estimated_hours__gte=0) | models.Q(estimated_hours__isnull=True),
                name="task_estimated_hours_non_negative",
            ),
            models.CheckConstraint(
                check=models.Q(actual_hours__gte=0) | models.Q(actual_hours__isnull=True),
                name="task_actual_hours_non_negative",
            ),
            models.CheckConstraint(
                check=models.Q(due_date__gte=models.F("start_date"))
                | models.Q(due_date__isnull=True)
                | models.Q(start_date__isnull=True),
                name="task_due_date_after_start_date",
            ),
        ]
        ordering = ["due_date", "priority"]

    def __str__(self) -> str:
        return f"{self.title} ({self.project.name})"

    def get_comment_count(self) -> int:
        """Get total number of comments for this task - optimized to use annotation when available."""
        # Try to use annotation first (from optimized queryset)
        if hasattr(self, "comment_count"):
            return self.comment_count

        # Fallback to individual query if not annotated
        try:
            from django.apps import apps

            Comment = apps.get_model("CLEAR", "Comment")
            return Comment.objects.filter(
                commentable_type="task",
                commentable_id=str(self.id),
                deleted_at__isnull=True,
            ).count()
        except (ImportError, LookupError) as e:
            logger.warning(f"Could not load Comment model: {e}")
            return 0

    def get_recent_comments(self, limit: int = 5):
        """Get recent comments for this task."""
        try:
            from django.apps import apps

            Comment = apps.get_model("CLEAR", "Comment")
            return (
                Comment.objects.filter(
                    commentable_type="task",
                    commentable_id=str(self.id),
                    deleted_at__isnull=True,
                )
                .select_related("user")
                .order_by("-created_at")[:limit]
            )
        except (ImportError, LookupError) as e:
            logger.warning(f"Could not load Comment model: {e}")
            return Comment.objects.none() if "Comment" in locals() else []

    # Object-Level Permission Methods
    def user_has_access(self, user, permission_level: str = "view") -> bool:
        """Check if user has specific access level to this task.

        Permission levels:
        - 'view': Can view task details
        - 'edit': Can edit task (owner, assignee, or project member)
        - 'assign': Can assign task to others
        - 'delete': Can delete task
        """
        if not user or not user.is_authenticated:
            return False

        # Check organization membership first
        if not hasattr(user, "organization") or not user.organization:
            return False

        if user.organization != self.organization:
            return False

        # Check project access first - if no project access, no task access
        if not self.project.user_has_access(user, "view"):
            return False

        # System admins have full access
        if user.is_superuser:
            return True

        # Task creator has full access
        if self.created_by == user:
            return True

        # Task assignee has edit access
        if self.assigned_to.filter(id=user.id).exists() and permission_level in [
            "view",
            "edit",
        ]:
            return True

        # Check project role for more advanced permissions
        project_role = self.project.get_user_role(user)
        if not project_role:
            return False

        # Role-based task permissions
        role_permissions = {
            "manager": ["view", "edit", "assign", "delete"],
            "coordinator": ["view", "edit", "assign"],
            "contributor": ["view", "edit"],
            "viewer": ["view"],
        }

        user_permissions = role_permissions.get(project_role, [])
        return permission_level in user_permissions

    def can_user_edit(self, user) -> bool:
        """Check if user can edit this task."""
        return self.user_has_access(user, "edit")

    def can_user_assign(self, user) -> bool:
        """Check if user can assign this task to others."""
        return self.user_has_access(user, "assign")

    def can_user_delete(self, user) -> bool:
        """Check if user can delete this task."""
        return self.user_has_access(user, "delete")

    def assign_to_user(self, user, assigned_by=None) -> bool:
        """Assign task to a user with permission checking."""
        try:
            from apps.core.models.permissions import PermissionAuditLog
        except ImportError:
            PermissionAuditLog = None

        # Check if the target user has project access
        if not self.project.user_has_access(user, "view"):
            raise PermissionError("User does not have access to project")

        # Check organization membership
        if user.organization != self.organization:
            raise PermissionError("User does not belong to the same organization")

        old_assignees = list(self.assigned_to.all())
        self.assigned_to.add(user)

        # Log the action
        if PermissionAuditLog:
            try:
                PermissionAuditLog.log_permission_change(
                    action_type="permission_modified",
                    resource_type="task",
                    resource_id=self.id,
                    user=user,
                    performed_by=assigned_by,
                    old_value={"assigned_to": [a.id for a in old_assignees]},
                    new_value={"assigned_to": [a.id for a in self.assigned_to.all()]},
                )
            except (ImportError, AttributeError) as e:
                # Log action failed but assignment succeeded
                logger.debug(f"Could not log task assignment: {e}")

        return True

    def remove_assignee(self, user, removed_by=None) -> bool:
        """Remove a user from task assignments."""
        try:
            from apps.core.models.permissions import PermissionAuditLog
        except ImportError:
            PermissionAuditLog = None

        if self.assigned_to.filter(id=user.id).exists():
            old_assignees = list(self.assigned_to.all())
            self.assigned_to.remove(user)

            # Log the action
            if PermissionAuditLog:
                try:
                    PermissionAuditLog.log_permission_change(
                        action_type="permission_modified",
                        resource_type="task",
                        resource_id=self.id,
                        user=user,
                        performed_by=removed_by,
                        old_value={"assigned_to": [a.id for a in old_assignees]},
                        new_value={"assigned_to": [a.id for a in self.assigned_to.all()]},
                    )
                except (ImportError, AttributeError) as e:
                    logger.debug(f"Could not log task assignment removal: {e}")

            return True
        return False

    def get_accessible_to_user(self, user, permission_level="view"):
        """Get all tasks accessible to a user with specified permission level."""
        if not user or not user.is_authenticated:
            return self.objects.none()

        if not hasattr(user, "organization") or not user.organization:
            return self.objects.none()

        # Start with organization filter
        queryset = (
            self.objects.filter(organization=user.organization)
            .select_related(
                "project",
                "created_by",
                "organization",
            )
            .prefetch_related(
                "assigned_to",
            )
        )

        # System admins have access to all tasks in their org
        if user.is_superuser:
            return queryset

        # Filter by accessible projects and task-specific permissions
        accessible_tasks = []
        for task in queryset:
            if task.user_has_access(user, permission_level):
                accessible_tasks.append(task.id)

        return queryset.filter(id__in=accessible_tasks)


class TaskAssignment(models.Model):
    """Enhanced task assignment tracking with full audit trail and notifications.

    Provides comprehensive assignment management with:
    - Direct Task model relationship
    - Assignment roles and permissions
    - Automatic notification triggers
    - Assignment history audit trail
    - Workload tracking integration
    """

    class AssignmentType(models.TextChoices):
        """Types of task assignments."""

        PRIMARY = "primary", _("Primary Assignee")
        SECONDARY = "secondary", _("Secondary Assignee")
        REVIEWER = "reviewer", _("Reviewer")
        OBSERVER = "observer", _("Observer")
        APPROVER = "approver", _("Approver")
        COLLABORATOR = "collaborator", _("Collaborator")

    class Status(models.TextChoices):
        """Assignment status values."""

        PENDING = "pending", _("Pending")
        ACCEPTED = "accepted", _("Accepted")
        DECLINED = "declined", _("Declined")
        COMPLETED = "completed", _("Completed")
        INACTIVE = "inactive", _("Inactive")

    # Core relationships
    task = models.ForeignKey(
        "Task",
        on_delete=models.CASCADE,
        related_name="assignments",
        verbose_name=_("Task"),
        help_text=_("The task this assignment refers to"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="project_task_assignments",
        verbose_name=_("Assigned User"),
        help_text=_("User assigned to this task"),
    )
    assigned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_assignments",
        verbose_name=_("Assigned By"),
        help_text=_("User who created this assignment"),
    )

    # Assignment details
    assignment_type = models.CharField(
        max_length=20,
        choices=AssignmentType.choices,
        default=AssignmentType.PRIMARY,
        verbose_name=_("Assignment Type"),
        help_text=_("Type of assignment role"),
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING,
        verbose_name=_("Status"),
        help_text=_("Current assignment status"),
    )

    # Permissions
    can_edit = models.BooleanField(default=False, verbose_name=_("Can Edit"), help_text=_("Can edit the task"))
    can_reassign = models.BooleanField(
        default=False,
        verbose_name=_("Can Reassign"),
        help_text=_("Can reassign the task to others"),
    )
    can_approve = models.BooleanField(
        default=False,
        verbose_name=_("Can Approve"),
        help_text=_("Can approve task completion"),
    )

    # Timestamps
    assigned_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Assigned At"),
        help_text=_("When the assignment was created"),
    )
    accepted_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Accepted At"),
        help_text=_("When the assignment was accepted"),
    )
    completed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Completed At"),
        help_text=_("When the assignment was completed"),
    )

    # Notification settings
    notify_on_update = models.BooleanField(
        default=True,
        verbose_name=_("Notify on Updates"),
        help_text=_("Send notifications when task is updated"),
    )
    notify_on_comment = models.BooleanField(
        default=True,
        verbose_name=_("Notify on Comments"),
        help_text=_("Send notifications for new comments"),
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = _("Task Assignment")
        verbose_name_plural = _("Task Assignments")
        unique_together = ["task", "user", "assignment_type"]
        indexes = [
            models.Index(fields=["task", "status"]),
            models.Index(fields=["user", "status"]),
            models.Index(fields=["assigned_by", "assigned_at"]),
            models.Index(fields=["assignment_type", "status"]),
        ]
        ordering = ["-assigned_at"]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.task.title} ({self.assignment_type})"

    def accept_assignment(self, user=None):
        """Accept the assignment."""
        if self.status == self.Status.PENDING:
            self.status = self.Status.ACCEPTED
            self.accepted_at = timezone.now()
            self.save(update_fields=["status", "accepted_at"])

            # Trigger notification
            from apps.notifications.compatibility import create_notification

            create_notification(
                recipient=self.assigned_to,
                notification_type="task_assignment",
                title="Assignment Accepted",
                message=f'Your assignment for "{self.project.name}" has been accepted',
                data={"assignment_id": self.id, "project_id": self.project.id},
                related_object=self,
            )

    def complete_assignment(self, user=None):
        """Mark assignment as completed."""
        if self.status in [self.Status.ACCEPTED, self.Status.PENDING]:
            self.status = self.Status.COMPLETED
            self.completed_at = timezone.now()
            self.save(update_fields=["status", "completed_at"])

            # Trigger notification
            from apps.notifications.compatibility import create_notification

            create_notification(
                recipient=self.assigned_to,
                notification_type="task_assignment",
                title="Assignment Completed",
                message=f'Your assignment for "{self.project.name}" has been completed',
                data={"assignment_id": self.id, "project_id": self.project.id},
                related_object=self,
            )

    @property
    def is_active(self):
        """Check if assignment is currently active."""
        return self.status in [self.Status.PENDING, self.Status.ACCEPTED]

    @property
    def duration(self):
        """Get assignment duration if completed."""
        if self.completed_at and self.assigned_at:
            return self.completed_at - self.assigned_at
        return None


class TaskAssignmentHistory(models.Model):
    """Complete audit trail for task assignment changes.

    Tracks all changes to task assignments including:
    - Assignment creation/deletion
    - Status changes
    - Permission changes
    - Metadata updates
    """

    class ActionType(models.TextChoices):
        """Types of assignment history actions."""

        CREATED = "created", _("Assignment Created")
        DELETED = "deleted", _("Assignment Deleted")
        STATUS_CHANGED = "status_changed", _("Status Changed")
        PERMISSIONS_CHANGED = "permissions_changed", _("Permissions Changed")
        TYPE_CHANGED = "type_changed", _("Assignment Type Changed")
        ACCEPTED = "accepted", _("Assignment Accepted")
        DECLINED = "declined", _("Assignment Declined")
        COMPLETED = "completed", _("Assignment Completed")
        REACTIVATED = "reactivated", _("Assignment Reactivated")

    # Core relationships
    assignment = models.ForeignKey(
        "TaskAssignment",
        on_delete=models.CASCADE,
        related_name="history",
        verbose_name=_("Assignment"),
        help_text=_("The assignment this history entry refers to"),
    )
    task = models.ForeignKey(
        "Task",
        on_delete=models.CASCADE,
        related_name="assignment_history",
        verbose_name=_("Task"),
        help_text=_("The task for better querying"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="assignment_history_entries",
        verbose_name=_("Assigned User"),
        help_text=_("User the assignment is for"),
    )

    # Action details
    action_type = models.CharField(
        max_length=30,
        choices=ActionType.choices,
        verbose_name=_("Action Type"),
        help_text=_("Type of assignment action"),
    )
    action_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="performed_assignment_actions",
        verbose_name=_("Action Performed By"),
        help_text=_("User who performed this action"),
    )
    action_timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Action Timestamp"),
        help_text=_("When this action occurred"),
    )

    # Change tracking
    old_value = models.JSONField(
        null=True,
        blank=True,
        verbose_name=_("Old Value"),
        help_text=_("Previous value before change"),
    )
    new_value = models.JSONField(
        null=True,
        blank=True,
        verbose_name=_("New Value"),
        help_text=_("New value after change"),
    )

    # Additional context
    reason = models.TextField(blank=True, verbose_name=_("Reason"), help_text=_("Reason for the change"))
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_("IP Address"),
        help_text=_("IP address of the user who made the change"),
    )
    user_agent = models.TextField(
        blank=True,
        verbose_name=_("User Agent"),
        help_text=_("Browser/client information"),
    )

    # Audit metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        app_label = "projects"
        verbose_name = _("Task Assignment History")
        verbose_name_plural = _("Task Assignment History")
        indexes = [
            models.Index(fields=["assignment", "action_timestamp"]),
            models.Index(fields=["task", "action_timestamp"]),
            models.Index(fields=["user", "action_timestamp"]),
            models.Index(fields=["action_type", "action_timestamp"]),
            models.Index(fields=["action_by", "action_timestamp"]),
        ]
        ordering = ["-action_timestamp"]

    def __str__(self):
        return f"{self.get_action_type_display()} - {self.user.get_full_name()} on {self.task.title}"

    @classmethod
    def create_history_entry(
        cls,
        assignment,
        action_type: str,
        action_by,
        old_value=None,
        new_value=None,
        reason="",
        request=None,
    ):
        """Create a new history entry for an assignment action.

        Args:
            assignment: TaskAssignment instance
            action_type: Type of action from ActionType choices
            action_by: User who performed the action
            old_value: Previous value (for changes)
            new_value: New value (for changes)
            reason: Reason for the change
            request: HTTP request object for IP/user agent tracking
        """
        history_data = {
            "assignment": assignment,
            "task": assignment.task,
            "user": assignment.user,
            "action_type": action_type,
            "action_by": action_by,
            "old_value": old_value,
            "new_value": new_value,
            "reason": reason,
        }

        # Extract request metadata if available
        if request:
            # Get real IP address (considering proxies)
            x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
            if x_forwarded_for:
                ip = x_forwarded_for.split(",")[0].strip()
            else:
                ip = request.META.get("REMOTE_ADDR")

            history_data.update(
                {
                    "ip_address": ip,
                    "user_agent": request.META.get("HTTP_USER_AGENT", "")[:500],  # Limit length
                }
            )

        return cls.objects.create(**history_data)

    @property
    def change_summary(self):
        """Get a human-readable summary of the change."""
        if self.action_type == self.ActionType.STATUS_CHANGED:
            old_status = self.old_value.get("status") if self.old_value else "Unknown"
            new_status = self.new_value.get("status") if self.new_value else "Unknown"
            return f"Status changed from {old_status} to {new_status}"

        elif self.action_type == self.ActionType.TYPE_CHANGED:
            old_type = self.old_value.get("assignment_type") if self.old_value else "Unknown"
            new_type = self.new_value.get("assignment_type") if self.new_value else "Unknown"
            return f"Assignment type changed from {old_type} to {new_type}"

        elif self.action_type == self.ActionType.PERMISSIONS_CHANGED:
            changes = []
            if self.old_value and self.new_value:
                for key, new_val in self.new_value.items():
                    if key.startswith("can_") and self.old_value.get(key) != new_val:
                        changes.append(f"{key}: {self.old_value.get(key)} → {new_val}")
            return f"Permissions changed: {', '.join(changes)}"

        else:
            return self.get_action_type_display()


class ProjectMember(models.Model):
    """Project team member assignments."""

    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="team_members")
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="project_memberships",
    )
    role = models.CharField(
        max_length=50,
        default="contributor",
        choices=[
            ("manager", "Project Manager"),
            ("coordinator", "Coordinator"),
            ("contributor", "Contributor"),
            ("viewer", "Viewer"),
        ],
    )
    permissions = models.JSONField(default=list)
    added_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="added_project_members",
    )
    added_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    # Composite primary key using unique_together instead of CompositePrimaryKey
    # which is not yet available in Django

    class Meta:
        app_label = "projects"
        unique_together = [["project", "user"]]
        verbose_name = "Project Member"
        verbose_name_plural = "Project Members"
        indexes = [
            models.Index(fields=["project", "is_active"]),
            models.Index(fields=["user", "is_active"]),
        ]

    def __str__(self) -> str:
        return f"{self.user.get_full_name()} on {self.project.name} ({self.role})"


class ProjectActivity(models.Model):
    """Project activity tracking for timelines."""

    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="project_activities")
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="project_activities",
    )
    action_type = models.CharField(
        max_length=100,
        choices=[
            ("comment_added", "Comment Added"),
            ("task_created", "Task Created"),
            ("task_updated", "Task Updated"),
            ("task_completed", "Task Completed"),
            ("document_uploaded", "Document Uploaded"),
            ("utility_updated", "Utility Updated"),
            ("conflict_reported", "Conflict Reported"),
            ("conflict_resolved", "Conflict Resolved"),
            ("member_added", "Team Member Added"),
            ("member_removed", "Team Member Removed"),
            ("project_updated", "Project Updated"),
        ],
    )
    description = models.TextField()
    target_type = models.CharField(max_length=50, blank=True, null=True)  # task, utility, conflict, etc.
    target_id = models.CharField(max_length=255, blank=True, null=True)
    metadata = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Project Activity"
        verbose_name_plural = "Project Activities"
        indexes = [
            models.Index(fields=["project", "-timestamp"]),
            models.Index(fields=["user", "-timestamp"]),
            models.Index(fields=["action_type", "-timestamp"]),
        ]
        ordering = ["-timestamp"]

    def __str__(self) -> str:
        return f"{self.project.name}: {self.description} by {self.user.get_full_name()}"


class ProjectPhase(models.Model):
    """Project phase management."""

    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="phases")
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    order = models.IntegerField(default=0)
    start_date = models.DateField(blank=True, null=True)
    end_date = models.DateField(blank=True, null=True)
    status = models.CharField(
        max_length=50,
        default="pending",
        choices=[
            ("pending", "Pending"),
            ("active", "Active"),
            ("completed", "Completed"),
            ("cancelled", "Cancelled"),
        ],
    )
    deliverables = models.JSONField(default=list)
    milestones = models.JSONField(default=list)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Project Phase"
        verbose_name_plural = "Project Phases"
        ordering = ["project", "order"]
        constraints = [
            models.UniqueConstraint(
                fields=["project", "name"],
                name="project_phase_unique_name",
            ),
        ]

    def __str__(self) -> str:
        return f"{self.project.name} - {self.name}"


class Workflow(models.Model):
    """Workflow definitions."""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="workflows",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="core_created_workflows",
    )
    workflow_definition = models.JSONField(default=dict)  # Step definitions, conditions, etc.
    is_active = models.BooleanField(default=True)
    version = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Workflow"
        verbose_name_plural = "Workflows"
        ordering = ["name"]
        constraints = [models.UniqueConstraint(fields=["organization", "name"], name="unique_workflow_per_org")]

    def __str__(self) -> str:
        return f"{self.name} v{self.version}"


class WorkflowExecution(models.Model):
    """Track workflow execution instances."""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workflow = models.ForeignKey("Workflow", on_delete=models.CASCADE, related_name="executions")
    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="workflow_executions")
    started_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="started_workflows",
    )
    current_step = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(
        max_length=50,
        default="running",
        choices=[
            ("pending", "Pending"),
            ("running", "Running"),
            ("completed", "Completed"),
            ("failed", "Failed"),
            ("cancelled", "Cancelled"),
        ],
    )
    execution_data = models.JSONField(default=dict)  # Step results, variables, etc.
    error_message = models.TextField(blank=True, null=True)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Workflow Execution"
        verbose_name_plural = "Workflow Executions"
        ordering = ["-started_at"]

    def __str__(self) -> str:
        return f"{self.workflow.name} - {self.project.name} ({self.status})"


class ProjectLog(models.Model):
    """Project event logging."""

    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="logs")
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=100)
    details = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Project Log"
        verbose_name_plural = "Project Logs"
        indexes = [
            models.Index(fields=["project", "-timestamp"]),
            models.Index(fields=["action", "-timestamp"]),
        ]
        ordering = ["-timestamp"]

    def __str__(self) -> str:
        return f"{self.project.name}: {self.action}"


class Meeting(models.Model):
    """Meeting management for projects and teams."""

    MEETING_TYPES = [
        ("in_person", "In Person"),
        ("virtual", "Virtual"),
        ("hybrid", "Hybrid"),
        ("phone", "Phone Call"),
    ]

    MEETING_STATUS = [
        ("scheduled", "Scheduled"),
        ("in_progress", "In Progress"),
        ("completed", "Completed"),
        ("cancelled", "Cancelled"),
        ("postponed", "Postponed"),
    ]

    # Basic information
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    scheduled_time = models.DateTimeField()
    duration = models.IntegerField(help_text="Duration in minutes", default=60)

    # Meeting details
    location = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Physical location or virtual meeting platform",
    )
    meeting_type = models.CharField(max_length=20, choices=MEETING_TYPES, default="virtual")
    meeting_link = models.URLField(blank=True, null=True, help_text="Virtual meeting link (Zoom, Teams, etc.)")

    # Relationships
    attendees = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name="meetings_attending", blank=True)
    project = models.ForeignKey(
        "Project",
        on_delete=models.CASCADE,
        related_name="meetings",
        blank=True,
        null=True,
    )
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="meetings",
        help_text="Organization context for multi-tenant isolation",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="meetings_created",
    )

    # Status and notes
    status = models.CharField(max_length=20, choices=MEETING_STATUS, default="scheduled")
    notes = models.TextField(blank=True, null=True, help_text="Meeting notes or minutes")
    agenda = models.TextField(blank=True, null=True, help_text="Meeting agenda")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Additional fields
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.JSONField(
        default=dict,
        blank=True,
        help_text="Recurrence pattern for recurring meetings",
    )
    parent_meeting = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="recurring_instances",
    )

    class Meta:
        app_label = "projects"
        verbose_name = "Meeting"
        verbose_name_plural = "Meetings"
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "title", "scheduled_time"],
                name="unique_meeting_per_org_time",
            ),
        ]
        indexes = [
            models.Index(fields=["organization", "scheduled_time"]),
            models.Index(fields=["scheduled_time", "status"]),
            models.Index(fields=["created_by", "scheduled_time"]),
            models.Index(fields=["project", "scheduled_time"]),
        ]
        ordering = ["scheduled_time"]

    def __str__(self) -> str:
        return f"{self.title} - {self.scheduled_time.strftime('%Y-%m-%d %H:%M')}"

    @property
    def end_time(self):
        """Calculate the end time based on scheduled_time and duration."""
        return self.scheduled_time + timedelta(minutes=self.duration)

    @property
    def is_upcoming(self) -> bool:
        """Check if the meeting is upcoming."""
        return self.scheduled_time > timezone.now() and self.status == "scheduled"

    @property
    def is_past(self) -> bool:
        """Check if the meeting is in the past."""
        return self.scheduled_time < timezone.now()

    def get_attendee_count(self) -> int:
        """Get the number of attendees."""
        return self.attendees.count()

    def is_user_attending(self, user) -> bool:
        """Check if a specific user is attending."""
        return self.attendees.filter(id=user.id).exists()


class Invoice(models.Model):
    """Model for project invoices and billing."""

    INVOICE_STATUS_CHOICES = [
        ("draft", "Draft"),
        ("sent", "Sent"),
        ("paid", "Paid"),
        ("overdue", "Overdue"),
        ("cancelled", "Cancelled"),
    ]

    INVOICE_TYPE_CHOICES = [
        ("project", "Project Invoice"),
        ("time_based", "Time-based Invoice"),
        ("milestone", "Milestone Invoice"),
        ("expense", "Expense Invoice"),
    ]

    # Invoice identification
    invoice_number = models.CharField(max_length=50, unique=True, verbose_name="invoice number")

    # Related project
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name="invoices",
        verbose_name="project",
    )

    # Invoice details
    invoice_type = models.CharField(
        max_length=20,
        choices=INVOICE_TYPE_CHOICES,
        default="project",
        verbose_name="invoice type",
    )

    status = models.CharField(
        max_length=20,
        choices=INVOICE_STATUS_CHOICES,
        default="draft",
        verbose_name="status",
    )

    # Dates
    invoice_date = models.DateField(default=timezone.now, verbose_name="invoice date")
    due_date = models.DateField(verbose_name="due date")
    sent_date = models.DateField(null=True, blank=True, verbose_name="sent date")
    paid_date = models.DateField(null=True, blank=True, verbose_name="paid date")

    # Financial details
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name="subtotal")
    tax_rate = models.DecimalField(max_digits=5, decimal_places=4, default=0.0000, verbose_name="tax rate")
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name="tax amount")
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name="discount amount")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name="total amount")

    # Client information
    client_name = models.CharField(max_length=255, verbose_name="client name")
    client_email = models.EmailField(verbose_name="client email")
    client_address = models.TextField(blank=True, verbose_name="client address")

    # Invoice content
    description = models.TextField(blank=True, verbose_name="description")
    notes = models.TextField(blank=True, verbose_name="notes")

    # Payment information
    payment_method = models.CharField(max_length=50, blank=True, verbose_name="payment method")
    payment_reference = models.CharField(max_length=100, blank=True, verbose_name="payment reference")

    # Metadata
    terms_and_conditions = models.TextField(blank=True, verbose_name="terms and conditions")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "invoice"
        verbose_name_plural = "invoices"
        ordering = ["-invoice_date"]
        indexes = [
            models.Index(fields=["project"]),
            models.Index(fields=["status"]),
            models.Index(fields=["invoice_date"]),
            models.Index(fields=["due_date"]),
        ]

    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.project.name}"

    def save(self, *args, **kwargs):
        # Auto-generate invoice number if not provided
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

        # Calculate totals
        self.calculate_totals()

        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate a unique invoice number."""
        from datetime import datetime

        year = datetime.now().year

        # Get the last invoice number for this year
        last_invoice = (
            Invoice.objects.filter(invoice_number__startswith=f"INV-{year}-").order_by("-invoice_number").first()
        )

        if last_invoice:
            # Extract the sequence number
            try:
                last_seq = int(last_invoice.invoice_number.split("-")[-1])
                next_seq = last_seq + 1
            except (ValueError, IndexError):
                next_seq = 1
        else:
            next_seq = 1

        return f"INV-{year}-{next_seq:04d}"

    def calculate_totals(self):
        """Calculate invoice totals."""
        # Calculate tax amount
        self.tax_amount = self.subtotal * self.tax_rate

        # Calculate total amount
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount

    def mark_as_sent(self):
        """Mark invoice as sent."""
        self.status = "sent"
        self.sent_date = timezone.now().date()
        self.save()

    def mark_as_paid(self, payment_method=None, payment_reference=None):
        """Mark invoice as paid."""
        self.status = "paid"
        self.paid_date = timezone.now().date()

        if payment_method:
            self.payment_method = payment_method
        if payment_reference:
            self.payment_reference = payment_reference

        self.save()

    def is_overdue(self):
        """Check if invoice is overdue."""
        if self.status in ["paid", "cancelled"]:
            return False

        return timezone.now().date() > self.due_date

    def days_overdue(self):
        """Get number of days overdue."""
        if not self.is_overdue():
            return 0

        return (timezone.now().date() - self.due_date).days


class SavedProjectFilter(models.Model):
    """Model for saving user-defined project filter configurations."""

    # User who saved the filter
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="saved_project_filters",
        verbose_name="user",
    )

    # Filter details
    name = models.CharField(
        max_length=255,
        verbose_name="filter name",
        help_text="Descriptive name for this filter configuration",
    )

    # Serialized filter parameters
    query_string = models.TextField(
        verbose_name="query string",
        help_text="URL-encoded query parameters for the filter",
    )

    # Optional description
    description = models.TextField(
        blank=True,
        verbose_name="description",
        help_text="Optional description of what this filter shows",
    )

    # Sharing options
    is_shared = models.BooleanField(
        default=False,
        verbose_name="shared with organization",
        help_text="Make this filter available to all organization members",
    )

    # Usage tracking
    use_count = models.PositiveIntegerField(
        default=0,
        verbose_name="use count",
        help_text="Number of times this filter has been used",
    )

    last_used = models.DateTimeField(null=True, blank=True, verbose_name="last used")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "saved project filter"
        verbose_name_plural = "saved project filters"
        ordering = ["-last_used", "-created_at"]
        indexes = [
            models.Index(fields=["user", "-last_used"]),
            models.Index(fields=["user", "is_shared"]),
        ]
        unique_together = [["user", "name"]]

    def __str__(self):
        return f"{self.name} ({self.user.get_full_name() or self.user.username})"

    def increment_use_count(self):
        """Increment usage counter and update last used timestamp."""
        self.use_count = F("use_count") + 1
        self.last_used = timezone.now()
        self.save(update_fields=["use_count", "last_used"])


# Stakeholder is an alias for Person for backward compatibility
Stakeholder = Person


# Signal handlers for task dependency integrity
@receiver(pre_save, sender=Task)
def validate_task_before_save(sender, instance, **kwargs):
    """Pre-save validation for task dependency integrity."""
    if instance.pk:  # Only for existing tasks
        try:
            # Store old status for comparison in post_save
            old_instance = Task.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status

            if old_instance.status != instance.status:
                # Additional validation for status changes
                instance._validate_status_with_dependencies()
        except Task.DoesNotExist:
            instance._old_status = None


@receiver(m2m_changed, sender=Task.dependencies.through)
def validate_task_dependencies_changed(sender, instance, action, pk_set, **kwargs):
    """Validate dependency changes maintain integrity."""
    if action == "pre_add" and pk_set:
        # Validate each dependency being added
        for dep_id in pk_set:
            try:
                dependency_task = Task.objects.get(pk=dep_id)
                instance.validate_dependency_addition(dependency_task)
            except Task.DoesNotExist:
                logger.warning(f"Attempted to add non-existent task {dep_id} as dependency")
                continue
            except ValidationError as e:
                logger.error(f"Dependency validation failed: {e}")
                raise

    elif action == "post_add" and pk_set:
        # Log dependency additions
        for dep_id in pk_set:
            try:
                dependency_task = Task.objects.get(pk=dep_id)
                logger.info(f"Added dependency: {dependency_task.title} -> {instance.title}")

                # Create project activity log
                if hasattr(instance, "project") and instance.project:
                    ProjectActivity.objects.create(
                        project=instance.project,
                        user=getattr(instance, "_current_user", None) or instance.created_by,
                        action_type="task_updated",
                        description=f"Added dependency '{dependency_task.title}' to task '{instance.title}'",
                        target_type="task",
                        target_id=str(instance.id),
                        metadata={
                            "dependency_added": dep_id,
                            "dependency_title": dependency_task.title,
                        },
                    )
            except Task.DoesNotExist:
                pass

    elif action == "post_remove" and pk_set:
        # Log dependency removals
        for dep_id in pk_set:
            try:
                dependency_task = Task.objects.get(pk=dep_id)
                logger.info(f"Removed dependency: {dependency_task.title} -> {instance.title}")

                # Create project activity log
                if hasattr(instance, "project") and instance.project:
                    ProjectActivity.objects.create(
                        project=instance.project,
                        user=getattr(instance, "_current_user", None) or instance.created_by,
                        action_type="task_updated",
                        description=f"Removed dependency '{dependency_task.title}' from task '{instance.title}'",
                        target_type="task",
                        target_id=str(instance.id),
                        metadata={
                            "dependency_removed": dep_id,
                            "dependency_title": dependency_task.title,
                        },
                    )
            except Task.DoesNotExist:
                pass


@receiver(post_save, sender=Task)
def handle_task_status_change(sender, instance, created, **kwargs):
    """Handle task status changes and dependency implications."""
    if not created and instance.pk:
        # Use the stored old status from pre_save
        old_status = getattr(instance, "_old_status", None)
        if old_status and old_status != instance.status:
            # If task was completed, check if any dependent tasks can now start
            if instance.status == "completed":
                dependent_tasks = instance.dependent_tasks.filter(status="pending")
                for dependent in dependent_tasks:
                    if dependent.can_start():
                        logger.info(f"Task '{dependent.title}' can now start - all dependencies completed")

                        # Create activity log for dependent task becoming available
                        ProjectActivity.objects.create(
                            project=dependent.project,
                            user=(getattr(instance, "_current_user", None) or instance.created_by),
                            action_type="task_updated",
                            description=(
                                f"Task '{dependent.title}' is now ready to start - "
                                f"dependency '{instance.title}' completed"
                            ),
                            target_type="task",
                            target_id=str(dependent.id),
                            metadata={
                                "dependency_completed": instance.id,
                                "dependency_title": instance.title,
                                "can_start": True,
                            },
                        )

            # If task was moved to in-progress, validate all dependencies are ready
            elif instance.status == "in-progress":
                blocking_deps = instance.get_blocking_dependencies()
                if blocking_deps.exists():
                    logger.warning(f"Task '{instance.title}' moved to in-progress but has blocking dependencies")

            # Log status change
            ProjectActivity.objects.create(
                project=instance.project,
                user=getattr(instance, "_current_user", None) or instance.created_by,
                action_type="task_updated",
                description=f"Task '{instance.title}' status changed from {old_status} to {instance.status}",
                target_type="task",
                target_id=str(instance.id),
                metadata={
                    "old_status": old_status,
                    "new_status": instance.status,
                    "status_change": True,
                },
            )


class TaskComment(models.Model):
    """Comments on tasks with rich text support and threading."""

    # Relationships
    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name="comments")
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="task_comments")
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="task_comments",
        help_text="Organization for multi-tenant isolation",
    )

    # Threading support
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="replies",
        help_text="Parent comment for threading discussions",
    )

    # Content
    content = models.TextField(help_text="Comment content with Markdown support")
    attachments = models.JSONField(default=list, blank=True, help_text="File attachments as JSON array")

    # Metadata
    mentions = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name="task_comment_mentions",
        help_text="Users mentioned in this comment",
    )
    is_edited = models.BooleanField(default=False)
    edit_count = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["created_at"]
        indexes = [
            models.Index(fields=["task", "created_at"]),
            models.Index(fields=["user", "created_at"]),
            models.Index(fields=["parent"]),
        ]

    def __str__(self):
        return f"Comment by {self.user} on {self.task.title}"

    @property
    def is_reply(self):
        """Check if this is a reply to another comment."""
        return self.parent is not None

    @property
    def reply_count(self):
        """Count of direct replies to this comment."""
        return self.replies.count()

    def clean(self):
        """Validate comment data."""
        super().clean()

        # Validate organization consistency
        if self.task and self.organization:
            if self.task.organization != self.organization:
                raise ValidationError({"organization": "Comment organization must match task organization."})

        # Auto-set organization from task if not provided
        if self.task and not self.organization:
            self.organization = self.task.organization


class TaskCommentReaction(models.Model):
    """Emoji reactions to task comments."""

    # Relationships
    comment = models.ForeignKey(TaskComment, on_delete=models.CASCADE, related_name="reactions")
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="task_comment_reactions",
    )

    # Reaction data
    emoji = models.CharField(max_length=10, help_text="Emoji character or code")
    emoji_name = models.CharField(max_length=50, help_text="Emoji name for accessibility")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ["comment", "user", "emoji"]
        indexes = [
            models.Index(fields=["comment", "emoji"]),
        ]

    def __str__(self):
        return f"{self.emoji} by {self.user} on comment {self.comment.id}"


class TaskActivity(models.Model):
    """Activity tracking for all task changes and events."""

    class ActivityType(models.TextChoices):
        # Task lifecycle
        CREATED = "created", "Task Created"
        STATUS_CHANGED = "status_changed", "Status Changed"
        ASSIGNED = "assigned", "User Assigned"
        UNASSIGNED = "unassigned", "User Unassigned"

        # Content changes
        TITLE_CHANGED = "title_changed", "Title Changed"
        DESCRIPTION_CHANGED = "description_changed", "Description Changed"
        DUE_DATE_CHANGED = "due_date_changed", "Due Date Changed"
        PRIORITY_CHANGED = "priority_changed", "Priority Changed"

        # Dependencies
        DEPENDENCY_ADDED = "dependency_added", "Dependency Added"
        DEPENDENCY_REMOVED = "dependency_removed", "Dependency Removed"

        # Comments and collaboration
        COMMENT_ADDED = "comment_added", "Comment Added"
        COMMENT_EDITED = "comment_edited", "Comment Edited"
        COMMENT_DELETED = "comment_deleted", "Comment Deleted"

        # File management
        ATTACHMENT_ADDED = "attachment_added", "Attachment Added"
        ATTACHMENT_REMOVED = "attachment_removed", "Attachment Removed"

        # Time tracking
        TIME_LOGGED = "time_logged", "Time Logged"
        ESTIMATE_CHANGED = "estimate_changed", "Estimate Changed"

        # Other
        OTHER = "other", "Other Activity"

    # Relationships
    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name="activities")
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="task_activities",
        help_text="User who performed the activity (null for system activities)",
    )
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="task_activities",
        help_text="Organization for multi-tenant isolation",
    )

    # Activity details
    activity_type = models.CharField(
        max_length=50,
        choices=ActivityType.choices,
        help_text="Type of activity performed",
    )
    description = models.TextField(help_text="Human-readable description of the activity")
    details = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional activity-specific data (old/new values, etc.)",
    )

    # Optional reference to related objects
    related_comment = models.ForeignKey(
        TaskComment,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="activities",
        help_text="Related comment if activity is comment-related",
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["task", "-created_at"]),
            models.Index(fields=["activity_type", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
        ]
        verbose_name = "Task Activity"
        verbose_name_plural = "Task Activities"

    def __str__(self):
        actor = self.user or "System"
        return f"{self.get_activity_type_display()} by {actor} on {self.task.title}"

    @classmethod
    def log_activity(
        cls,
        task,
        activity_type,
        user=None,
        description="",
        details=None,
        related_comment=None,
    ):
        """Helper method to log task activities."""
        return cls.objects.create(
            task=task,
            user=user,
            organization=task.organization,
            activity_type=activity_type,
            description=description,
            details=details or {},
            related_comment=related_comment,
        )

    def clean(self):
        """Validate activity data."""
        super().clean()

        # Validate organization consistency
        if self.task and self.organization:
            if self.task.organization != self.organization:
                raise ValidationError({"organization": "Activity organization must match task organization."})

        # Auto-set organization from task if not provided
        if self.task and not self.organization:
            self.organization = self.task.organization


class TaskTemplate(models.Model):
    """Template for creating standardized task structures with predefined dependencies."""

    # Basic Information
    template_name = models.CharField(max_length=255, unique=True)
    description = models.TextField(help_text="Description of what this template creates")
    is_active = models.BooleanField(default=True, help_text="Whether this template is available for use")

    # Categorization
    category = models.CharField(
        max_length=100,
        choices=[
            ("software", "Software Development"),
            ("construction", "Construction Project"),
            ("marketing", "Marketing Campaign"),
            ("event", "Event Planning"),
            ("research", "Research Project"),
            ("product", "Product Launch"),
            ("compliance", "Compliance/Audit"),
            ("maintenance", "Maintenance Work"),
            ("custom", "Custom Template"),
        ],
        default="custom",
    )
    tags = models.JSONField(default=list, blank=True, help_text="Tags for organizing templates")

    # Organizational Context
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="task_templates",
        help_text="Organization this template belongs to",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_task_templates",
    )

    # Template Settings
    estimated_duration_days = models.PositiveIntegerField(
        null=True, blank=True, help_text="Estimated total project duration in days"
    )
    default_priority = models.CharField(
        max_length=20,
        choices=[
            ("low", "Low"),
            ("medium", "Medium"),
            ("high", "High"),
            ("critical", "Critical"),
        ],
        default="medium",
    )

    # Usage Tracking
    usage_count = models.PositiveIntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Task Template"
        verbose_name_plural = "Task Templates"
        indexes = [
            models.Index(fields=["organization", "is_active"]),
            models.Index(fields=["category", "is_active"]),
            models.Index(fields=["usage_count", "-last_used"]),
        ]
        ordering = ["-usage_count", "-updated_at"]

    def __str__(self):
        return f"{self.template_name} ({self.get_category_display()})"

    def clean(self):
        """Validate template data."""
        super().clean()

        # Ensure template name is not empty and properly formatted
        if not self.template_name or not self.template_name.strip():
            raise ValidationError({"template_name": "Template name cannot be empty."})

        # Validate tags are strings
        if self.tags and not all(isinstance(tag, str) for tag in self.tags):
            raise ValidationError({"tags": "All tags must be strings."})

    def apply_to_project(self, project, start_date=None, assigned_user=None):
        """
        Apply this template to create tasks in the specified project.

        Args:
            project: Project instance to create tasks in
            start_date: When the first tasks should start (defaults to today)
            assigned_user: Default user to assign tasks to (optional)

        Returns:
            dict: Summary of created tasks including task mapping
        """
        if not self.is_active:
            raise ValidationError("Cannot apply inactive template.")

        if project.organization != self.organization:
            raise ValidationError("Template and project must be in same organization.")

        from datetime import timedelta

        from django.db import transaction

        start_date = start_date or timezone.now().date()

        with transaction.atomic():
            # Get all template items ordered by dependencies
            template_items = self.template_items.all().order_by("order", "id")

            # Track created tasks and their mapping to template items
            created_tasks = {}
            task_mapping = {}  # template_item_id -> task_id

            # First pass: Create all tasks
            for item in template_items:
                task_start_date = start_date + timedelta(days=item.start_offset_days)
                task_due_date = None
                if item.estimated_hours and item.estimated_hours > 0:
                    # Estimate due date based on 8 hours per day
                    estimated_days = max(1, int(item.estimated_hours / 8))
                    task_due_date = task_start_date + timedelta(days=estimated_days)

                task = Task.objects.create(
                    project=project,
                    organization=project.organization,
                    created_by=assigned_user or project.manager or project.created_by,
                    title=item.title,
                    description=item.description,
                    priority=item.priority or self.default_priority,
                    status="pending",
                    start_date=task_start_date,
                    due_date=task_due_date,
                    estimated_hours=item.estimated_hours,
                    tags=item.tags.copy() if item.tags else [],
                )

                # Assign user if provided
                if assigned_user:
                    task.assigned_to.add(assigned_user)

                created_tasks[item.id] = task
                task_mapping[item.id] = task.id

            # Second pass: Set up dependencies
            for item in template_items:
                if item.dependency_template_item_ids:
                    task = created_tasks[item.id]

                    # Add dependencies based on template item references
                    for dep_item_id in item.dependency_template_item_ids:
                        if dep_item_id in created_tasks:
                            dependency_task = created_tasks[dep_item_id]
                            task.dependencies.add(dependency_task)

            # Update template usage statistics
            self.usage_count += 1
            self.last_used = timezone.now()
            self.save(update_fields=["usage_count", "last_used"])

            return {
                "success": True,
                "tasks_created": len(created_tasks),
                "task_mapping": task_mapping,
                "template_name": self.template_name,
                "project": project.name,
                "created_task_ids": list(task_mapping.values()),
            }

    def get_template_preview(self):
        """Generate a preview of what tasks will be created by this template."""
        items = self.template_items.all().order_by("order", "id")

        preview = {
            "template_name": self.template_name,
            "description": self.description,
            "category": self.get_category_display(),
            "estimated_duration_days": self.estimated_duration_days,
            "total_items": items.count(),
            "items": [],
        }

        for item in items:
            item_preview = {
                "title": item.title,
                "description": item.description,
                "estimated_hours": item.estimated_hours,
                "start_offset_days": item.start_offset_days,
                "priority": (item.get_priority_display() if item.priority else self.get_default_priority_display()),
                "tags": item.tags,
                "has_dependencies": bool(item.dependency_template_item_ids),
                "dependency_count": (
                    len(item.dependency_template_item_ids) if item.dependency_template_item_ids else 0
                ),
            }
            preview["items"].append(item_preview)

        return preview


class TaskTemplateItem(models.Model):
    """Individual task definition within a task template."""

    # Template Relationship
    template = models.ForeignKey(TaskTemplate, on_delete=models.CASCADE, related_name="template_items")

    # Task Definition
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    # Scheduling
    order = models.PositiveIntegerField(default=0, help_text="Order within template (for display)")
    start_offset_days = models.PositiveIntegerField(
        default=0, help_text="Days after project start when this task should begin"
    )
    estimated_hours = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Estimated hours to complete this task",
    )

    # Task Properties
    priority = models.CharField(
        max_length=20,
        choices=[
            ("low", "Low"),
            ("medium", "Medium"),
            ("high", "High"),
            ("critical", "Critical"),
        ],
        null=True,
        blank=True,
        help_text="Priority for this task (defaults to template priority)",
    )
    tags = models.JSONField(default=list, blank=True)

    # Dependencies (relative to other template items)
    dependency_template_item_ids = models.JSONField(
        default=list,
        blank=True,
        help_text="List of template item IDs that this task depends on",
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Task Template Item"
        verbose_name_plural = "Task Template Items"
        indexes = [
            models.Index(fields=["template", "order"]),
            models.Index(fields=["template", "start_offset_days"]),
        ]
        ordering = ["template", "order", "start_offset_days"]

    def __str__(self):
        return f"{self.template.template_name} - {self.title}"

    def clean(self):
        """Validate template item data."""
        super().clean()

        # Validate title
        if not self.title or not self.title.strip():
            raise ValidationError({"title": "Task title cannot be empty."})

        # Validate tags are strings
        if self.tags and not all(isinstance(tag, str) for tag in self.tags):
            raise ValidationError({"tags": "All tags must be strings."})

        # Validate dependency references
        if self.dependency_template_item_ids:
            if not all(isinstance(dep_id, int) for dep_id in self.dependency_template_item_ids):
                raise ValidationError({"dependency_template_item_ids": "All dependency IDs must be integers."})

            # Check for self-dependency
            if self.pk and self.pk in self.dependency_template_item_ids:
                raise ValidationError({"dependency_template_item_ids": "Task cannot depend on itself."})

    def save(self, *args, **kwargs):
        """Override save to perform additional validation."""
        self.clean()
        super().save(*args, **kwargs)

        # After saving, validate dependency references point to existing items
        if self.dependency_template_item_ids and self.template_id:
            valid_item_ids = set(self.template.template_items.values_list("id", flat=True))
            invalid_deps = set(self.dependency_template_item_ids) - valid_item_ids

            if invalid_deps:
                logger.warning(f"Template item {self.id} has invalid dependency references: {invalid_deps}")

    def get_dependency_items(self):
        """Get the actual TaskTemplateItem instances this item depends on."""
        if not self.dependency_template_item_ids:
            return TaskTemplateItem.objects.none()

        return TaskTemplateItem.objects.filter(template=self.template, id__in=self.dependency_template_item_ids)

    def would_create_circular_dependency(self, new_dependency_id):
        """Check if adding a dependency would create a circular reference."""
        if new_dependency_id == self.id:
            return True

        # Use BFS to check for cycles
        visited = set()
        queue = deque([new_dependency_id])

        while queue:
            current_id = queue.popleft()

            if current_id in visited:
                continue

            if current_id == self.id:
                return True

            visited.add(current_id)

            # Get dependencies of current item
            try:
                current_item = TaskTemplateItem.objects.get(template=self.template, id=current_id)
                if current_item.dependency_template_item_ids:
                    queue.extend(current_item.dependency_template_item_ids)
            except TaskTemplateItem.DoesNotExist:
                continue

        return False


class SavedView(models.Model):
    """Model to store user-specific task filter configurations and saved views."""

    # Core Fields
    name = models.CharField(max_length=255, help_text="Name for the saved view")
    description = models.TextField(blank=True, help_text="Optional description of what this view shows")

    # Ownership and Organization
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="saved_task_views",
        help_text="User who created this saved view",
    )
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="saved_task_views",
        help_text="Organization this view belongs to",
    )

    # Filter Configuration (stored as JSON)
    filter_config = models.JSONField(default=dict, help_text="JSON configuration storing all filter parameters")

    # View Properties
    is_shared = models.BooleanField(
        default=False,
        help_text="Whether this view is shared with other organization members",
    )
    is_default = models.BooleanField(default=False, help_text="Whether this is the user's default view")
    sort_order = models.CharField(
        max_length=100,
        default="-created_at",
        help_text="Default sort order for this view",
    )

    # Usage Tracking
    usage_count = models.PositiveIntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "projects_saved_view"
        verbose_name = "Saved View"
        verbose_name_plural = "Saved Views"
        ordering = ["-last_used", "-created_at"]
        constraints = [
            models.UniqueConstraint(
                fields=["user", "name", "organization"],
                name="unique_saved_view_per_user_org",
            )
        ]
        indexes = [
            models.Index(fields=["user", "organization"]),
            models.Index(fields=["organization", "is_shared"]),
            models.Index(fields=["last_used"]),
        ]

    def __str__(self):
        return f"{self.name} ({self.user.username})"

    def clean(self):
        """Validate saved view data."""
        super().clean()

        # Validate name
        if not self.name or not self.name.strip():
            raise ValidationError({"name": "View name cannot be empty."})

        # Validate filter config structure
        if not isinstance(self.filter_config, dict):
            raise ValidationError({"filter_config": "Filter configuration must be a valid JSON object."})

    def save(self, *args, **kwargs):
        """Override save to handle default view logic."""
        self.clean()

        # If this is being set as default, unset other defaults for this user
        if self.is_default:
            SavedView.objects.filter(user=self.user, organization=self.organization, is_default=True).exclude(
                pk=self.pk
            ).update(is_default=False)

        super().save(*args, **kwargs)

    def increment_usage(self):
        """Increment usage count and update last used timestamp."""
        self.usage_count = models.F("usage_count") + 1
        self.last_used = timezone.now()
        self.save(update_fields=["usage_count", "last_used"])

    def get_filter_summary(self):
        """Get a human-readable summary of the filters applied."""
        config = self.filter_config
        summary_parts = []

        if config.get("status"):
            summary_parts.append(f"Status: {', '.join(config['status'])}")

        if config.get("priority"):
            summary_parts.append(f"Priority: {', '.join(config['priority'])}")

        if config.get("assigned_to"):
            summary_parts.append(f"Assigned to: {len(config['assigned_to'])} users")

        if config.get("tags"):
            summary_parts.append(f"Tags: {', '.join(config['tags'][:3])}{'...' if len(config['tags']) > 3 else ''}")

        if config.get("due_date_from") or config.get("due_date_to"):
            summary_parts.append("Date range specified")

        if config.get("search"):
            summary_parts.append(f"Search: '{config['search'][:20]}{'...' if len(config['search']) > 20 else ''}'")

        return "; ".join(summary_parts) if summary_parts else "No filters applied"


class RecurringTaskPattern(models.Model):
    """Pattern definition for recurring tasks with flexible recurrence rules."""

    class RecurrenceType(models.TextChoices):
        DAILY = "daily", "Daily"
        WEEKLY = "weekly", "Weekly"
        MONTHLY = "monthly", "Monthly"
        YEARLY = "yearly", "Yearly"
        CUSTOM = "custom", "Custom"

    # Basic Information
    name = models.CharField(max_length=255, help_text="Descriptive name for this recurrence pattern")
    description = models.TextField(blank=True, help_text="Optional description of the recurring task pattern")

    # Relationships
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="recurring_task_patterns",
        help_text="Organization this pattern belongs to",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_recurring_patterns",
        help_text="User who created this pattern",
    )

    # Recurrence Configuration
    recurrence_type = models.CharField(
        max_length=20,
        choices=RecurrenceType.choices,
        default=RecurrenceType.DAILY,
        help_text="Type of recurrence pattern",
    )
    interval = models.PositiveIntegerField(default=1, help_text="Interval between occurrences (e.g., every 2 weeks)")

    # Weekly recurrence options
    days_of_week = models.JSONField(
        default=list,
        blank=True,
        help_text="Days of week for weekly recurrence (0=Monday, 6=Sunday)",
    )

    # Monthly recurrence options
    day_of_month = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(31)],
        help_text="Day of month for monthly recurrence (1-31)",
    )
    week_of_month = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Week of month (1-5, 5 = last week)",
    )

    # Custom recurrence for complex patterns
    custom_rule = models.JSONField(
        default=dict,
        blank=True,
        help_text="Custom recurrence rule for complex patterns",
    )

    # Schedule Boundaries
    timezone = models.CharField(max_length=50, default="UTC", help_text="Timezone for recurrence calculations")
    start_date = models.DateTimeField(help_text="When the recurrence pattern starts")
    end_date = models.DateTimeField(null=True, blank=True, help_text="When the recurrence pattern ends (optional)")
    max_occurrences = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of task instances to create (optional)",
    )

    # Status and Configuration
    is_active = models.BooleanField(default=True, help_text="Whether this pattern is actively generating tasks")
    last_generated = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time a task was generated from this pattern",
    )
    next_due = models.DateTimeField(null=True, blank=True, help_text="Next calculated due date for task generation")

    # Exception Handling
    exceptions = models.JSONField(default=list, blank=True, help_text="List of dates to skip (ISO format strings)")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = "projects"
        verbose_name = "Recurring Task Pattern"
        verbose_name_plural = "Recurring Task Patterns"
        indexes = [
            models.Index(fields=["organization", "is_active"]),
            models.Index(fields=["recurrence_type", "is_active"]),
            models.Index(fields=["next_due", "is_active"]),
            models.Index(fields=["created_by", "-created_at"]),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "name"],
                name="unique_recurring_pattern_name_per_org",
            )
        ]
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} ({self.get_recurrence_type_display()})"

    def clean(self):
        """Validate recurrence pattern configuration."""
        super().clean()

        if self.end_date and self.start_date and self.end_date <= self.start_date:
            raise ValidationError("End date must be after start date")

        if self.recurrence_type == self.RecurrenceType.WEEKLY and not self.days_of_week:
            raise ValidationError("Weekly recurrence requires at least one day of week")

        if self.recurrence_type == self.RecurrenceType.MONTHLY:
            if not self.day_of_month and not self.week_of_month:
                raise ValidationError("Monthly recurrence requires either day_of_month or week_of_month")

    def get_next_occurrence(self, after=None):
        """Calculate the next occurrence after the given date."""
        from datetime import datetime, timedelta

        import pytz
        from dateutil.relativedelta import relativedelta

        if not self.is_active:
            return None

        if after is None:
            after = timezone.now()

        # Convert to pattern timezone
        tz = pytz.timezone(self.timezone)
        if timezone.is_aware(after):
            after = after.astimezone(tz)
        else:
            after = tz.localize(after)

        # Check if we've exceeded max occurrences
        if self.max_occurrences:
            current_count = self.task_instances.count()
            if current_count >= self.max_occurrences:
                return None

        # Check if we've passed the end date
        if self.end_date:
            if timezone.is_aware(self.end_date):
                end_date = self.end_date.astimezone(tz)
            else:
                end_date = tz.localize(self.end_date)
            if after >= end_date:
                return None

        # Calculate next occurrence based on recurrence type
        if self.recurrence_type == self.RecurrenceType.DAILY:
            next_date = after + timedelta(days=self.interval)

        elif self.recurrence_type == self.RecurrenceType.WEEKLY:
            # Find next occurrence of specified weekdays
            days_ahead = []
            current_weekday = after.weekday()

            for day in self.days_of_week:
                days_ahead.append((day - current_weekday) % 7)

            if days_ahead:
                min_days = min([d for d in days_ahead if d > 0] or [7])
                next_date = after + timedelta(days=min_days)
            else:
                next_date = after + timedelta(days=7 * self.interval)

        elif self.recurrence_type == self.RecurrenceType.MONTHLY:
            if self.day_of_month:
                # Specific day of month
                next_date = after.replace(day=1) + relativedelta(months=self.interval)
                try:
                    next_date = next_date.replace(day=self.day_of_month)
                except ValueError:
                    # Day doesn't exist in target month (e.g., Feb 31)
                    next_date = next_date + relativedelta(day=31)  # Last day of month
            else:
                # Specific week and weekday of month
                next_date = after + relativedelta(months=self.interval)

        elif self.recurrence_type == self.RecurrenceType.YEARLY:
            next_date = after + relativedelta(years=self.interval)

        else:  # CUSTOM
            # Handle custom recurrence rules
            # This would need to be implemented based on custom_rule format
            return None

        # Check if the calculated date is in exceptions
        exception_dates = [
            datetime.fromisoformat(exc).date() if isinstance(exc, str) else exc for exc in self.exceptions
        ]

        while next_date.date() in exception_dates:
            # Skip this date and calculate the next one
            if self.recurrence_type == self.RecurrenceType.DAILY:
                next_date += timedelta(days=self.interval)
            elif self.recurrence_type == self.RecurrenceType.WEEKLY:
                next_date += timedelta(days=7 * self.interval)
            elif self.recurrence_type == self.RecurrenceType.MONTHLY:
                next_date += relativedelta(months=self.interval)
            elif self.recurrence_type == self.RecurrenceType.YEARLY:
                next_date += relativedelta(years=self.interval)

        return next_date

    def get_upcoming_occurrences(self, count=10):
        """Get the next N upcoming occurrences."""
        occurrences = []
        current_date = timezone.now()

        for _ in range(count):
            next_occurrence = self.get_next_occurrence(after=current_date)
            if not next_occurrence:
                break
            occurrences.append(next_occurrence)
            current_date = next_occurrence

        return occurrences

    def add_exception(self, date):
        """Add a date to the exceptions list."""
        date_str = date.isoformat() if hasattr(date, "isoformat") else str(date)
        if date_str not in self.exceptions:
            self.exceptions.append(date_str)
            self.save(update_fields=["exceptions"])

    def remove_exception(self, date):
        """Remove a date from the exceptions list."""
        date_str = date.isoformat() if hasattr(date, "isoformat") else str(date)
        if date_str in self.exceptions:
            self.exceptions.remove(date_str)
            self.save(update_fields=["exceptions"])

    def update_next_due(self):
        """Update the next_due field based on current calculation."""
        self.next_due = self.get_next_occurrence()
        self.save(update_fields=["next_due"])

    def generate_next_task(self, template_task=None):
        """Generate the next task instance from this pattern."""
        if not template_task:
            # Find a template task (either marked as template or the first instance)
            template_task = (
                self.task_instances.filter(is_recurring_template=True).first()
                or self.task_instances.order_by("occurrence_number").first()
            )

        if not template_task:
            return None

        return template_task.create_next_occurrence()


# Re-export for compatibility
__all__ = [
    "Invoice",
    "Meeting",
    "Person",
    "PersonCompany",
    "PersonProject",
    "Project",
    "ProjectActivity",
    "ProjectLog",
    "ProjectMember",
    "ProjectPhase",
    "ProjectTemplate",
    "RecurringTaskPattern",
    "SavedProjectFilter",
    "SavedView",
    "Stakeholder",  # Alias for Person
    "Task",
    "TaskComment",
    "TaskCommentReaction",
    "TaskActivity",
    "TaskTemplate",
    "TaskTemplateItem",
    "Workflow",
    "WorkflowExecution",
]


class MonteCarloSimulation(models.Model):
    """Monte Carlo simulation configuration and results."""

    class DistributionType(models.TextChoices):
        PERT = "pert", "PERT Distribution"
        NORMAL = "normal", "Normal Distribution"
        TRIANGULAR = "triangular", "Triangular Distribution"
        UNIFORM = "uniform", "Uniform Distribution"
        BETA = "beta", "Beta Distribution"

    # Relationships
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="monte_carlo_simulations")
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="monte_carlo_simulations",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_monte_carlo_simulations",
    )

    # Simulation Configuration
    name = models.CharField(max_length=255, help_text="Name for this simulation")
    description = models.TextField(blank=True)
    iterations = models.PositiveIntegerField(default=10000)
    distribution_type = models.CharField(max_length=20, choices=DistributionType.choices, default=DistributionType.PERT)
    variance_factor = models.FloatField(default=0.3)
    use_critical_chain = models.BooleanField(default=True)

    # Simulation Results
    mean_duration = models.FloatField(null=True, blank=True)
    median_duration = models.FloatField(null=True, blank=True)
    std_deviation = models.FloatField(null=True, blank=True)
    min_duration = models.FloatField(null=True, blank=True)
    max_duration = models.FloatField(null=True, blank=True)
    confidence_intervals = models.JSONField(default=dict, blank=True)
    percentiles = models.JSONField(default=dict, blank=True)
    probability_distribution = models.JSONField(default=list, blank=True)
    critical_path_analysis = models.JSONField(default=dict, blank=True)
    risk_metrics = models.JSONField(default=dict, blank=True)
    completion_probabilities = models.JSONField(default=dict, blank=True)

    # Status and metadata
    status = models.CharField(
        max_length=20,
        choices=[
            ("pending", "Pending"),
            ("running", "Running"),
            ("completed", "Completed"),
            ("failed", "Failed"),
        ],
        default="pending",
    )
    error_message = models.TextField(blank=True)
    execution_time = models.DurationField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    simulation_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["project", "status"]),
            models.Index(fields=["organization", "created_at"]),
            models.Index(fields=["created_by", "-created_at"]),
        ]

    def __str__(self):
        return f"Monte Carlo: {self.name} - {self.project.name}"

    @property
    def coefficient_of_variation(self):
        """Calculate coefficient of variation (CV)"""
        if self.mean_duration and self.mean_duration > 0:
            return self.std_deviation / self.mean_duration
        return 0.0

    @property
    def risk_level(self):
        """Determine risk level based on coefficient of variation"""
        cv = self.coefficient_of_variation
        if cv < 0.1:
            return "Low"
        elif cv < 0.2:
            return "Moderate"
        elif cv < 0.3:
            return "High"
        else:
            return "Very High"


class TaskEstimation(models.Model):
    """Task estimation data for Monte Carlo simulation."""

    simulation = models.ForeignKey(MonteCarloSimulation, on_delete=models.CASCADE, related_name="task_estimations")
    task = models.ForeignKey(Task, on_delete=models.CASCADE, related_name="monte_carlo_estimations")

    # Three-point estimation
    optimistic = models.FloatField(help_text="Optimistic estimate in days")
    most_likely = models.FloatField(help_text="Most likely estimate in days")
    pessimistic = models.FloatField(help_text="Pessimistic estimate in days")

    # Distribution parameters
    distribution_type = models.CharField(
        max_length=20,
        choices=MonteCarloSimulation.DistributionType.choices,
        default=MonteCarloSimulation.DistributionType.PERT,
    )
    confidence_level = models.FloatField(default=0.95)

    # Calculated values
    pert_mean = models.FloatField(null=True, blank=True)
    pert_variance = models.FloatField(null=True, blank=True)
    pert_std_dev = models.FloatField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["simulation", "task"]
        indexes = [
            models.Index(fields=["simulation", "task"]),
        ]

    def __str__(self):
        return f"Estimation: {self.task.title} ({self.most_likely} days)"

    def save(self, *args, **kwargs):
        """Calculate PERT values on save."""
        if self.optimistic and self.most_likely and self.pessimistic:
            # PERT formulas
            self.pert_mean = (self.optimistic + 4 * self.most_likely + self.pessimistic) / 6
            self.pert_variance = ((self.pessimistic - self.optimistic) / 6) ** 2
            self.pert_std_dev = (self.pert_variance**0.5) if self.pert_variance else 0
        super().save(*args, **kwargs)


class CriticalChainAnalysis(models.Model):
    """Critical Chain Method analysis results."""

    # Relationships
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="critical_chain_analyses")
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="critical_chain_analyses",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_critical_chain_analyses",
    )

    # Analysis Configuration
    name = models.CharField(max_length=255, help_text="Name for this analysis")
    description = models.TextField(blank=True)
    buffer_percentage = models.FloatField(default=0.5, help_text="Buffer size as percentage of critical chain")

    # Analysis Results
    critical_chain_tasks = models.JSONField(default=list, blank=True)
    critical_chain_length = models.FloatField(null=True, blank=True)
    resource_conflicts = models.JSONField(default=list, blank=True)
    buffer_recommendations = models.JSONField(default=dict, blank=True)
    resource_utilization = models.JSONField(default=dict, blank=True)
    leveling_results = models.JSONField(default=dict, blank=True)

    # Status
    status = models.CharField(
        max_length=20,
        choices=[
            ("pending", "Pending"),
            ("running", "Running"),
            ("completed", "Completed"),
            ("failed", "Failed"),
        ],
        default="pending",
    )
    error_message = models.TextField(blank=True)
    execution_time = models.DurationField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["project", "status"]),
            models.Index(fields=["organization", "created_at"]),
            models.Index(fields=["created_by", "-created_at"]),
        ]

    def __str__(self):
        return f"Critical Chain: {self.name} - {self.project.name}"


class ProjectBuffer(models.Model):
    """Project buffer management for Critical Chain Method."""

    class BufferType(models.TextChoices):
        PROJECT = "project", "Project Buffer"
        FEEDING = "feeding", "Feeding Buffer"
        RESOURCE = "resource", "Resource Buffer"

    class BufferStatus(models.TextChoices):
        GREEN = "green", "Green (< 33% consumed)"
        YELLOW = "yellow", "Yellow (33-66% consumed)"
        RED = "red", "Red (> 66% consumed)"

    critical_chain_analysis = models.ForeignKey(CriticalChainAnalysis, on_delete=models.CASCADE, related_name="buffers")

    buffer_type = models.CharField(max_length=20, choices=BufferType.choices)
    name = models.CharField(max_length=255)
    location = models.CharField(max_length=255, help_text="Where buffer is inserted")

    # Buffer sizing
    original_duration = models.FloatField(help_text="Original duration in days")
    buffer_duration = models.FloatField(help_text="Buffer size in days")
    consumed_duration = models.FloatField(default=0, help_text="Buffer consumed in days")

    # Related tasks
    protected_tasks = models.JSONField(default=list, blank=True)
    feeding_chain_tasks = models.JSONField(default=list, blank=True)

    # Monitoring
    status = models.CharField(max_length=20, choices=BufferStatus.choices, default=BufferStatus.GREEN)
    last_status_change = models.DateTimeField(auto_now_add=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["critical_chain_analysis", "buffer_type"]),
            models.Index(fields=["status", "last_status_change"]),
        ]

    def __str__(self):
        return f"{self.get_buffer_type_display()}: {self.name}"

    @property
    def consumption_percentage(self):
        """Calculate buffer consumption percentage."""
        if self.buffer_duration > 0:
            return min(100, (self.consumed_duration / self.buffer_duration) * 100)
        return 0

    @property
    def consumption_color(self):
        """Get color based on consumption level."""
        percentage = self.consumption_percentage
        if percentage <= 33:
            return "success"
        elif percentage <= 66:
            return "warning"
        else:
            return "danger"

    @property
    def status_color(self):
        """Get Bootstrap color class for status."""
        return {
            self.BufferStatus.GREEN: "success",
            self.BufferStatus.YELLOW: "warning",
            self.BufferStatus.RED: "danger",
        }.get(self.status, "secondary")

    def update_status(self):
        """Update buffer status based on consumption."""
        percentage = self.consumption_percentage
        old_status = self.status

        if percentage <= 33:
            self.status = self.BufferStatus.GREEN
        elif percentage <= 66:
            self.status = self.BufferStatus.YELLOW
        else:
            self.status = self.BufferStatus.RED

        if old_status != self.status:
            self.last_status_change = timezone.now()

        self.save(update_fields=["status", "last_status_change"])


class Scenario(models.Model):
    """
    Scenario planning model for project what-if analysis.

    Scenarios allow project managers to model different project configurations
    and compare their impact on schedule, cost, and resource utilization.
    """

    project = models.ForeignKey(
        "Project", on_delete=models.CASCADE, related_name="scenarios", help_text="The project this scenario belongs to"
    )

    name = models.CharField(max_length=100, help_text="Descriptive name for this scenario")

    description = models.TextField(blank=True, help_text="Detailed description of scenario assumptions")

    is_baseline = models.BooleanField(default=False, help_text="Whether this is the baseline scenario")

    is_active = models.BooleanField(default=True, help_text="Whether this scenario is currently active")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        "authentication.User", on_delete=models.SET_NULL, null=True, related_name="created_scenarios"
    )

    class Meta:
        db_table = "project_scenarios"
        verbose_name = "Scenario"
        verbose_name_plural = "Scenarios"
        indexes = [
            models.Index(fields=["project", "is_baseline"]),
            models.Index(fields=["is_active", "created_at"]),
        ]
        unique_together = [["project", "name"]]

    def __str__(self):
        return f"{self.project} - {self.name}"


class ScenarioVersion(models.Model):
    """
    Version control for scenarios to track changes over time.
    """

    scenario = models.ForeignKey(Scenario, on_delete=models.CASCADE, related_name="versions")

    version_number = models.PositiveIntegerField()

    data = models.JSONField(default=dict, help_text="Scenario configuration data")

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey("authentication.User", on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = "project_scenario_versions"
        verbose_name = "Scenario Version"
        verbose_name_plural = "Scenario Versions"
        unique_together = [["scenario", "version_number"]]
        ordering = ["-version_number"]

    def __str__(self):
        return f"{self.scenario} v{self.version_number}"


class ScenarioComparison(models.Model):
    """
    Comparison between different scenarios to analyze trade-offs.
    """

    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="scenario_comparisons")

    name = models.CharField(max_length=100, help_text="Name for this comparison")

    scenarios = models.ManyToManyField(Scenario, related_name="comparisons", help_text="Scenarios being compared")

    comparison_data = models.JSONField(default=dict, help_text="Results of scenario comparison")

    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey("authentication.User", on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = "project_scenario_comparisons"
        verbose_name = "Scenario Comparison"
        verbose_name_plural = "Scenario Comparisons"

    def __str__(self):
        return f"{self.project} - {self.name}"


# Update __all__ export
__all__ = [
    "Invoice",
    "Meeting",
    "Person",
    "PersonCompany",
    "PersonProject",
    "Project",
    "ProjectActivity",
    "ProjectLog",
    "ProjectMember",
    "ProjectPhase",
    "ProjectTemplate",
    "RecurringTaskPattern",
    "SavedProjectFilter",
    "SavedView",
    "Stakeholder",  # Alias for Person
    "Task",
    "TaskComment",
    "TaskCommentReaction",
    "TaskActivity",
    "TaskTemplate",
    "TaskTemplateItem",
    "Workflow",
    "WorkflowExecution",
    "MonteCarloSimulation",
    "TaskEstimation",
    "CriticalChainAnalysis",
    "ProjectBuffer",
    "Scenario",
    "ScenarioVersion",
    "ScenarioComparison",
]
