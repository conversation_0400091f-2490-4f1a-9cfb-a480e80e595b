{% load humanize %}

<div class="skill-goal-tracker" id="goal-tracker-{{ skill.id }}">
  <div class="card border-0 shadow-sm">
    <div class="card-header bg-light border-0">
      <div class="d-flex justify-content-between align-items-center">
        <h6 class="mb-0">
          <i class="fas fa-bullseye me-2 text-success"></i>Skill Development Goals
        </h6>
        <button class="btn btn-sm btn-outline-primary"
                data-bs-toggle="collapse"
                data-bs-target="#goal-details-{{ skill.id }}"
                aria-expanded="false">
          <i class="fas fa-plus"></i> Set Goal
        </button>
      </div>
    </div>
    <div class="card-body">
      <!-- Current Goals Display -->
      <div class="current-goals mb-3">

        {% if skill.proficiency_level != 'expert' %}
          <div class="goal-item">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="goal-info">
                <div class="fw-bold small">Reach {{ skill.get_next_milestone.level }} Level</div>
                <small class="text-muted">
                  <i class="fas fa-calendar me-1"></i>
                  Target: {% now "Y-m-d"|add:"365" %}
                </small>
              </div>
              <div class="goal-progress">

                {% with milestone=skill.get_next_milestone %}

                  {% if milestone.experience_needed > 0 %}
                    <div class="progress" style="width: 100px; height: 6px;">
                      <div class="progress-bar bg-success"
                           style="width: {% widthratio milestone.experience_needed milestone.experience_needed|add:2 100 %}%">
                      </div>
                    </div>
                    <small class="text-muted d-block text-end">{{ milestone.experience_needed|floatformat:1 }}yr left</small>
                  {% else %}
                    <span class="badge bg-success">Ready!</span>
                  {% endif %}

                {% endwith %}

              </div>
            </div>
          </div>
        {% else %}
          <div class="text-center py-3 text-muted">
            <i class="fas fa-trophy fa-2x mb-2 text-warning"></i>
            <div class="small">Expert level achieved! Focus on maintaining expertise.</div>
          </div>
        {% endif %}

        <!-- Verification Goal -->

        {% if not skill.is_verified %}
          <div class="goal-item border-top pt-3">
            <div class="d-flex justify-content-between align-items-center">
              <div class="goal-info">
                <div class="fw-bold small">Get Skill Verified</div>
                <small class="text-muted">Have a colleague verify this skill</small>
              </div>
              <button class="btn btn-sm btn-outline-primary"
                      onclick="requestVerification({{ skill.id }})">
                <i class="fas fa-paper-plane"></i> Request
              </button>
            </div>
          </div>
        {% endif %}

        <!-- Currency Goal -->

        {% if not skill.is_current %}
          <div class="goal-item border-top pt-3">
            <div class="d-flex justify-content-between align-items-center">
              <div class="goal-info">
                <div class="fw-bold small">Update Recent Usage</div>
                <small class="text-muted">Last used: {{ skill.last_used_date|naturalday|default:"Never" }}</small>
              </div>
              <button class="btn btn-sm btn-outline-success"
                      hx-post="{% url 'profiles:skill_assess' skill.id %}"
                      hx-target="#goal-tracker-{{ skill.id }}"
                      hx-swap="outerHTML"
                      hx-vals='{"last_used_date": "{{ "now"|date:"Y-m-d" }}"}'>
                <i class="fas fa-check"></i> Used Today
              </button>
            </div>
          </div>
        {% endif %}

      </div>
      <!-- Goal Setting Form (Collapsible) -->
      <div class="collapse" id="goal-details-{{ skill.id }}">
        <div class="border-top pt-3">
          <form hx-post="{% url 'profiles:skill_set_goal' skill.id %}"
                hx-target="#goal-tracker-{{ skill.id }}"
                hx-swap="outerHTML"
                class="skill-goal-form">
            <div class="mb-3">
              <label class="form-label small fw-bold">Goal Type</label>
              <select name="goal_type" class="form-select form-select-sm" required>
                <option value="">Select goal type</option>
                <option value="proficiency">Improve Proficiency</option>
                <option value="experience">Gain Experience</option>
                <option value="certification">Obtain Certification</option>
                <option value="project">Complete Project</option>
                <option value="mentoring">Mentor Others</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label small fw-bold">Target Date</label>
              <input type="date"
                     name="target_date"
                     class="form-control form-control-sm"
                     min="{% now "Y-m-d" %}"
                     value="{% now "Y-m-d"|add:"90" %}"
                     required>
            </div>
            <div class="mb-3">
              <label class="form-label small fw-bold">Description</label>
              <textarea name="description"
                        class="form-control form-control-sm"
                        rows="2"
                        placeholder="Describe your goal and how you plan to achieve it..."
                        required></textarea>
            </div>
            <div class="mb-3">
              <label class="form-label small fw-bold">Success Criteria</label>
              <textarea name="success_criteria"
                        class="form-control form-control-sm"
                        rows="2"
                        placeholder="How will you know you've achieved this goal?"
                        required></textarea>
            </div>
            <div class="d-flex justify-content-end gap-2">
              <button type="button"
                      class="btn btn-sm btn-secondary"
                      data-bs-toggle="collapse"
                      data-bs-target="#goal-details-{{ skill.id }}">Cancel</button>
              <button type="submit" class="btn btn-sm btn-primary">
                <i class="fas fa-save me-1"></i>Set Goal
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  <!-- Development Timeline -->
  <div class="card border-0 shadow-sm mt-3">
    <div class="card-header bg-light border-0">
      <h6 class="mb-0">
        <i class="fas fa-history me-2 text-info"></i>Development Timeline
      </h6>
    </div>
    <div class="card-body">
      <div class="timeline">
        <!-- Current Status -->
        <div class="timeline-item active">
          <div class="timeline-marker bg-primary"></div>
          <div class="timeline-content">
            <div class="timeline-date">{{ skill.updated_at|date:"M d, Y" }}</div>
            <div class="timeline-title">Current Level: {{ skill.get_proficiency_level_display }}</div>
            <div class="timeline-desc small text-muted">
              {{ skill.get_experience_description }}

              {% if skill.is_verified %}• Verified{% endif %}

            </div>
          </div>
        </div>
        <!-- Future Milestones -->

        {% with milestone=skill.get_next_milestone %}

          {% if milestone.level != skill.get_proficiency_level_display %}
            <div class="timeline-item future">
              <div class="timeline-marker bg-success"></div>
              <div class="timeline-content">
                <div class="timeline-date">{% now "Y-m-d"|add:"365" %}</div>
                <div class="timeline-title">Target: {{ milestone.level }}</div>
                <div class="timeline-desc small text-muted">Complete development milestones</div>
              </div>
            </div>
          {% endif %}

        {% endwith %}

        <!-- Future Expert Level -->

        {% if skill.proficiency_level != 'expert' %}
          <div class="timeline-item future">
            <div class="timeline-marker bg-warning"></div>
            <div class="timeline-content">
              <div class="timeline-date">{% now "Y-m-d"|add:"1095" %}</div>
              <div class="timeline-title">Goal: Expert Level</div>
              <div class="timeline-desc small text-muted">Master the skill and mentor others</div>
            </div>
          </div>
        {% endif %}

      </div>
    </div>
  </div>
  <!-- Quick Actions -->
  <div class="card border-0 shadow-sm mt-3">
    <div class="card-body py-2">
      <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
        <div class="quick-actions">
          <small class="text-muted me-3">Quick actions:</small>
          <button class="btn btn-sm btn-outline-success me-2"
                  hx-post="{% url 'profiles:skill_assess' skill.id %}"
                  hx-target="#goal-tracker-{{ skill.id }}"
                  hx-swap="outerHTML"
                  hx-vals='{"last_used_date": "{{ "now"|date:"Y-m-d" }}"}'>
            <i class="fas fa-calendar-check"></i> Used Today
          </button>
          <button class="btn btn-sm btn-outline-primary me-2"
                  data-bs-toggle="modal"
                  data-bs-target="#skillModal"
                  hx-get="{% url 'profiles:skill_edit' skill.id %}"
                  hx-target="#skill-modal-content">
            <i class="fas fa-edit"></i> Update Details
          </button>
          <button class="btn btn-sm btn-outline-info"
                  onclick="shareSkillProgress({{ skill.id }})">
            <i class="fas fa-share"></i> Share Progress
          </button>
        </div>
        <div class="skill-score">
          <small class="text-muted me-2">Skill Score:</small>
          <span class="badge bg-primary">{{ skill.get_skill_progress_score }}/100</span>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
function requestVerification(skillId) {
    // Show verification request modal or send notification
    alert('Verification request functionality would be implemented here');
}

function shareSkillProgress(skillId) {
    // Share skill progress functionality
    if (navigator.share) {
        navigator.share({
            title: 'My Skill Progress',
            text: 'Check out my progress on {{ skill.skill_name }}!',
            url: window.location.href
        });
    } else {
        // Fallback to copy to clipboard
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('Progress link copied to clipboard!');
        });
    }
}
</script>
<style>
.goal-item {
    padding: 0.75rem 0;
}

.goal-item:not(:last-child) {
    border-bottom: 1px solid #e9ecef;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem;
    border-left: 3px solid #007bff;
}

.timeline-item.active .timeline-content {
    background: #e7f3ff;
    border-left-color: #0056b3;
}

.timeline-item.future .timeline-content {
    background: #f8f9fa;
    border-left-color: #28a745;
}

.timeline-date {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.timeline-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.timeline-desc {
    margin: 0;
}

.quick-actions .btn {
    margin-bottom: 0.25rem;
}
</style>
