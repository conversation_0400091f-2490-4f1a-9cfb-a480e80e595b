{% extends "base.html" %}

{% load static %}

{% block title %}Conversations - CLEAR{% endblock %}

{% block extra_css %}
  <link href="{% static 'css/messaging.css' %}" rel="stylesheet">
  <style>
.conversation-dashboard {
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

.conversation-sidebar {
    width: 350px;
    min-width: 350px;
    border-right: 1px solid var(--bs-border-color);
    display: flex;
    flex-direction: column;
    background: var(--bs-body-bg);
}

.conversation-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.conversation-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.conversation-sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--bs-border-color);
    flex-shrink: 0;
}

.conversation-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.conversation-tabs {
    border-bottom: 1px solid var(--bs-border-color);
    padding: 0 1rem;
    flex-shrink: 0;
}

.conversation-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    padding: 0.75rem 1rem;
    color: var(--bs-secondary);
}

.conversation-tabs .nav-link.active {
    border-bottom-color: var(--bs-primary);
    color: var(--bs-primary);
    background: none;
}

@media (max-width: 992px) {
    .conversation-sidebar {
        width: 100%;
        min-width: auto;
    }
    
    .conversation-main {
        display: none;
    }
    
    .conversation-main.active {
        display: flex;
    }
    
    .conversation-sidebar.conversation-selected {
        display: none;
    }
}
  </style>
{% endblock %}

{% block content %}
  <div class="container-fluid conversation-dashboard">
    <div class="row h-100">
      <!-- Sidebar -->
      <div class="col-lg-4 col-xl-3 p-0 conversation-sidebar"
           id="conversation-sidebar">
        <!-- Sidebar Header -->
        <div class="conversation-sidebar-header">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
              <i class="fas fa-comments me-2"></i>
              Conversations
            </h5>
            <button class="btn btn-primary btn-sm"
                    data-bs-toggle="modal"
                    data-bs-target="#createConversationModal">
              <i class="fas fa-plus"></i>
            </button>
          </div>
          <!-- Search Bar -->
          <div class="input-group">
            <span class="input-group-text">
              <i class="fas fa-search"></i>
            </span>
            <input type="text"
                   class="form-control"
                   placeholder="Search conversations..."
                   hx-get="{% url 'messaging:conversation_search' %}"
                   hx-trigger="keyup changed delay:300ms"
                   hx-target="#conversation-search-results"
                   hx-indicator="#search-indicator"
                   name="q"
                   autocomplete="off">
          </div>
          <!-- Search Results -->
          <div id="conversation-search-results" class="mt-2"></div>
        </div>
        <!-- Conversation Tabs -->
        <div class="conversation-tabs">
          <ul class="nav nav-pills nav-justified" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active"
                      id="all-conversations-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#all-conversations"
                      type="button"
                      role="tab"
                      hx-get="{% url 'messaging:conversation_list' %}"
                      hx-target="#conversation-list-content">
                All
                <span class="badge bg-primary ms-1" id="all-count">0</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link"
                      id="unread-conversations-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#unread-conversations"
                      type="button"
                      role="tab"
                      hx-get="{% url 'messaging:conversation_list' %}?filter=unread"
                      hx-target="#conversation-list-content">
                Unread
                <span class="badge bg-danger ms-1" id="unread-count">0</span>
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link"
                      id="archived-conversations-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#archived-conversations"
                      type="button"
                      role="tab"
                      hx-get="{% url 'messaging:conversation_list' %}?filter=archived"
                      hx-target="#conversation-list-content">
                <i class="fas fa-archive me-1"></i>
                Archived
              </button>
            </li>
          </ul>
        </div>
        <!-- Conversation List -->
        <div class="conversation-list-container">
          <div id="conversation-list-content"
               hx-get="{% url 'messaging:conversation_list' %}"
               hx-trigger="load"
               hx-indicator="#conversation-loading">
            <div id="conversation-loading" class="text-center p-4">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading conversations...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Main Content -->
      <div class="col-lg-8 col-xl-9 p-0 conversation-main"
           id="conversation-main">
        <div id="conversation-detail" class="h-100 d-flex flex-column">
          <!-- Welcome/Empty State -->
          <div class="conversation-content d-flex align-items-center justify-content-center">
            <div class="text-center">
              <i class="fas fa-comments text-muted mb-3" style="font-size: 4rem;"></i>
              <h4 class="text-muted">Select a conversation</h4>
              <p class="text-muted">Choose a conversation from the sidebar to start messaging</p>
              <button class="btn btn-primary"
                      data-bs-toggle="modal"
                      data-bs-target="#createConversationModal">
                <i class="fas fa-plus me-2"></i>Start New Conversation
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Include the conversation management modal -->
  {% include 'messaging/partials/conversation_management.html' %}
  <!-- Conversation Settings Sidebar (Slide-out) -->
  <div class="offcanvas offcanvas-end"
       tabindex="-1"
       id="conversationSettings">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Conversation Settings</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
      <div id="conversation-settings-content">
        <!-- Settings content will be loaded here -->
      </div>
    </div>
  </div>
  <!-- Real-time notifications -->
  <div id="notification-container"
       class="position-fixed top-0 end-0 p-3"
       style="z-index: 1050">
    <!-- Notifications will appear here -->
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/messaging.js' %}"></script>
  <script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize conversation dashboard
    const conversationDashboard = {
        init: function() {
            this.bindEvents();
            this.setupWebSocket();
            this.updateCounts();
        },
        
        bindEvents: function() {
            // Handle conversation selection
            document.addEventListener('htmx:afterRequest', function(evt) {
                if (evt.detail.target.id === 'conversation-detail') {
                    // Mark conversation as active in sidebar
                    const conversationId = evt.detail.xhr.responseURL.split('/').pop().split('?')[0];
                    this.markConversationActive(conversationId);
                    
                    // Show main content on mobile
                    document.getElementById('conversation-main').classList.add('active');
                    document.getElementById('conversation-sidebar').classList.add('conversation-selected');
                }
            }.bind(this));
            
            // Handle mobile back navigation
            document.addEventListener('click', function(evt) {
                if (evt.target.matches('.mobile-back-btn')) {
                    document.getElementById('conversation-main').classList.remove('active');
                    document.getElementById('conversation-sidebar').classList.remove('conversation-selected');
                }
            });
            
            // Handle conversation list updates
            document.addEventListener('htmx:afterSwap', function(evt) {
                if (evt.detail.target.id === 'conversation-list-content') {
                    this.updateCounts();
                }
            }.bind(this));
        },
        
        markConversationActive: function(conversationId) {
            // Remove active class from all conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to selected conversation
            const activeItem = document.querySelector(`[data-conversation-id="${conversationId}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        },
        
        updateCounts: function() {
            // Update conversation counts in tabs
            const allItems = document.querySelectorAll('.conversation-item').length;
            const unreadItems = document.querySelectorAll('.unread-count-badge').length;
            
            document.getElementById('all-count').textContent = allItems;
            document.getElementById('unread-count').textContent = unreadItems;
        },
        
        setupWebSocket: function() {
            // Setup WebSocket for real-time updates
            // This would connect to your Django Channels WebSocket
            if (window.WebSocket) {
                const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
                const wsUrl = `${wsScheme}://${window.location.host}/ws/messaging/`;
                
                try {
                    const socket = new WebSocket(wsUrl);
                    
                    socket.onmessage = function(event) {
                        const data = JSON.parse(event.data);
                        this.handleWebSocketMessage(data);
                    }.bind(this);
                    
                    socket.onopen = function() {
                        console.log('WebSocket connected');
                    };
                    
                    socket.onerror = function(error) {
                        console.error('WebSocket error:', error);
                    };
                } catch (error) {
                    console.warn('WebSocket not available:', error);
                }
            }
        },
        
        handleWebSocketMessage: function(data) {
            if (data.type === 'new_message') {
                // Update conversation list
                htmx.trigger('#conversation-list-content', 'refresh');
                
                // Show notification if not in active conversation
                if (data.conversation_id !== this.getCurrentConversationId()) {
                    this.showNotification(data);
                }
            } else if (data.type === 'conversation_updated') {
                // Refresh conversation list
                htmx.trigger('#conversation-list-content', 'refresh');
            }
        },
        
        getCurrentConversationId: function() {
            const activeItem = document.querySelector('.conversation-item.active');
            return activeItem ? activeItem.dataset.conversationId : null;
        },
        
        showNotification: function(data) {
            const notification = document.createElement('div');
            notification.className = 'toast show';
            notification.innerHTML = `
                <div class="toast-header">
                    <strong class="me-auto">${data.sender_name}</strong>
                    <small class="text-muted">now</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${data.message.substring(0, 100)}...
                </div>
            `;
            
            document.getElementById('notification-container').appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    };
    
    // Initialize the dashboard
    conversationDashboard.init();
});
  </script>
{% endblock %}
