<!-- Thread Created Template -->
<div class="thread-created-indicator p-1 bg-primary  bg-opacity-10  border-start  border-primary  border mb-2">
  <div class="d-flex align-items-center">
    <i class="bi bi-chats text-primary  me-2"></i>
    <div class="flex-grow-1 ">
      <strong>Thread started</strong>

      {% if thread.title %}<span class="text-muted ">- {{ thread.title|escape }}</span>{% endif %}

      <div class="small text-muted ">Click to view and reply to this conversation thread</div>
    </div>
    <button class="btn btn-sm btn-outline-primary"
            hx-get="{% url 'messaging:htmx:message-thread-view' thread.id %}"
            hx-target="#thread-container-{{ thread.id|escape }}"
            hx-swap="innerHTML"
            type="button"
            aria-label="Loading...  View Thread">
      <div class="htmx-indicator">Loading...</div>
      <i class="fas fa-expand-alt me-1"></i>
      View Thread
    </button>
  </div>
</div>
<!-- Thread Container -->
<div id="thread-container-{{ thread.id|escape }}" class="thread-container">
  <!-- Thread content will be loaded here -->
</div>
