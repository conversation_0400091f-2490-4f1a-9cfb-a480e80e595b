{% load custom_filters %}

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Compliance Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .report-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
            margin-bottom: 20px;
        }
        
        .bulk-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 30px;
            margin-bottom: 40px;
            text-align: center;
        }
        
        .bulk-title {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .bulk-subtitle {
            font-size: 1.2em;
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .date-range {
            background: #e7f3ff;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
        }
        
        .summary-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .section-title {
            font-size: 1.8em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .reports-grid {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .report-summary-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            background: #ffffff;
            border-left: 5px solid #007bff;
        }
        
        .report-summary-title {
            font-weight: bold;
            font-size: 1.2em;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .report-summary-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .meta-item {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .meta-label {
            font-weight: bold;
            color: #495057;
        }
        
        .projects-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .projects-table th,
        .projects-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        
        .projects-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .compliance-score-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .score-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .score-fill.high {
            background: linear-gradient(90deg, #28a745, #20c997);
        }
        
        .score-fill.medium {
            background: linear-gradient(90deg, #ffc107, #fd7e14);
        }
        
        .score-fill.low {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
        }
        
        .standards-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .standard-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            background: #ffffff;
        }
        
        .standard-name {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            display: inline-block;
            margin: 2px;
        }
        
        .badge-primary { background: #cce7ff; color: #004085; }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #ffeaa7; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .badge-info { background: #d1ecf1; color: #0c5460; }
        
        .footer {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
        }
        
        @media print {
            body { background: white; }
            .report-container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
    </style>
  </head>
  <body>
    <div class="report-container">
      <!-- Bulk Report Header -->
      <div class="bulk-header">
        <div class="bulk-title">Bulk Compliance Report</div>
        <div class="bulk-subtitle">Comprehensive Compliance Overview</div>
        <div class="date-range">
          <strong>Report Period:</strong>
          {{ data.date_range.start|date:"F d, Y" }} to {{ data.date_range.end|date:"F d, Y" }}
        </div>
      </div>
      <!-- Summary Statistics -->
      <div class="summary-grid">
        <div class="summary-card">
          <div class="summary-number">{{ data.summary.total_reports }}</div>
          <div class="summary-label">Total Reports</div>
        </div>
        <div class="summary-card">
          <div class="summary-number">{{ data.summary.total_projects }}</div>
          <div class="summary-label">Projects Reviewed</div>
        </div>
        <div class="summary-card">
          <div class="summary-number">{{ data.summary.average_compliance_score|floatformat:1 }}%</div>
          <div class="summary-label">Average Compliance</div>
        </div>
        <div class="summary-card">
          <div class="summary-number">{{ data.summary.standards_coverage|length }}</div>
          <div class="summary-label">Standards Covered</div>
        </div>
      </div>
      <!-- Status Distribution -->

      {% if data.summary.status_distribution %}
        <div class="section">
          <h2 class="section-title">Status Distribution</h2>
          <div class="summary-grid">

            {% for status, count in data.summary.status_distribution.items %}
              <div class="summary-card" style="background: 
                {% if status == 'approved' %}#28a745{% elif status == 'published' %}#17a2b8{% elif status == 'in_review' %}#ffc107{% elif status == 'draft' %}#6c757d{% else %}#007bff{% endif %}
                 ;">
                <div class="summary-number">{{ count }}</div>
                <div class="summary-label">{{ status|title }} Reports</div>
              </div>
            {% endfor %}

          </div>
        </div>
      {% endif %}

      <!-- Standards Coverage -->

      {% if data.summary.standards_coverage %}
        <div class="section">
          <h2 class="section-title">Standards Coverage</h2>
          <div class="standards-breakdown">

            {% for standard, count in data.summary.standards_coverage.items %}
              <div class="standard-card">
                <div class="standard-name">{{ standard }}</div>
                <div>
                  <span class="badge badge-primary">{{ count }} Report{{ count|pluralize }}</span>
                </div>
                <div style="margin-top: 10px;">
                  <div class="compliance-score-bar">
                    <div class="score-fill high"
                         style="width: {% widthratio count data.summary.total_reports 100 %}%"></div>
                  </div>
                  <small class="text-muted">{% widthratio count data.summary.total_reports 100 %}% of total reports</small>
                </div>
              </div>
            {% endfor %}

          </div>
        </div>
      {% endif %}

      <!-- Individual Reports Summary -->

      {% if data.reports %}
        <div class="section">
          <h2 class="section-title">Individual Reports Summary ({{ data.reports|length }})</h2>
          <div class="reports-grid">

            {% for report in data.reports %}
              <div class="report-summary-item">
                <div class="report-summary-title">{{ report.title }}</div>
                <div>{{ report.standard.name }}</div>
                <div class="report-summary-meta">
                  <div class="meta-item">
                    <span class="meta-label">Status:</span>
                    <span class="badge 
                      {% if report.status == 'approved' %}badge-success{% elif report.status == 'published' %}badge-info{% elif report.status == 'in_review' %}badge-warning{% elif report.status == 'draft' %}badge-secondary{% else %}badge-primary{% endif %}
                     ">{{ report.status|title }}</span>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">Period:</span>
                    {{ report.period_start|date:"M d" }} - {{ report.period_end|date:"M d, Y" }}
                  </div>

                  {% if report.compliance_score %}
                    <div class="meta-item">
                      <span class="meta-label">Score:</span>
                      <span class="badge 
                        {% if report.compliance_score >= 80 %}badge-success{% elif report.compliance_score >= 60 %}badge-warning{% else %}badge-danger{% endif %}
                       ">{{ report.compliance_score|floatformat:1 }}%</span>
                    </div>
                  {% endif %}

                  <div class="meta-item">
                    <span class="meta-label">Risk Level:</span>
                    <span class="badge 
                      {% if report.risk_level == 'low' %}badge-success{% elif report.risk_level == 'medium' %}badge-warning{% elif report.risk_level == 'high' %}badge-danger{% else %}badge-info{% endif %}
                     ">{{ report.risk_level|title }}</span>
                  </div>
                </div>
              </div>
            {% endfor %}

          </div>
        </div>
      {% endif %}

      <!-- Projects Overview -->

      {% if data.projects %}
        <div class="section">
          <h2 class="section-title">Projects Compliance Overview ({{ data.projects|length }})</h2>
          <table class="projects-table">
            <thead>
              <tr>
                <th>Project Name</th>
                <th>Compliance Score</th>
                <th>Checks Count</th>
                <th>Score Visualization</th>
              </tr>
            </thead>
            <tbody>

              {% for project_data in data.projects %}
                <tr>
                  <td>{{ project_data.project.name }}</td>
                  <td>

                    {% if project_data.compliance_score %}
                      {{ project_data.compliance_score }}%
                    {% else %}
                      Not calculated
                    {% endif %}

                  </td>
                  <td>{{ project_data.compliance_checks|length }}</td>
                  <td>

                    {% if project_data.compliance_score %}
                      <div class="compliance-score-bar">
                        <div class="score-fill 
                          {% if project_data.compliance_score >= 80 %}high{% elif project_data.compliance_score >= 60 %}medium{% else %}low{% endif %}
                           " style="width: {{ project_data.compliance_score }}%;"></div>
                      </div>
                    {% else %}
                      <span class="badge badge-secondary">No data</span>
                    {% endif %}

                  </td>
                </tr>
              {% endfor %}

            </tbody>
          </table>
        </div>
      {% endif %}

      <!-- Generation Metadata -->
      <div class="section">
        <h2 class="section-title">Report Metadata</h2>
        <div class="meta-item"
             style="background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px">
          <div>
            <strong>Generated At:</strong> {{ data.generated_at|date:"F d, Y \a\t H:i T" }}
          </div>
          <div>
            <strong>Organization ID:</strong> {{ data.organization_id }}
          </div>
          <div>
            <strong>Date Range:</strong> {{ data.date_range.start|date:"Y-m-d" }} to {{ data.date_range.end|date:"Y-m-d" }}
          </div>
          <div>
            <strong>Export Format:</strong> {{ export_format|upper }}
          </div>

          {% if data.generated_by %}
            <div>
              <strong>Generated By:</strong> {{ data.generated_by.get_full_name|default:data.generated_by.username }}
            </div>
          {% endif %}

        </div>
      </div>
      <!-- Footer -->
      <div class="footer">
        <div>Bulk Compliance Report generated on {{ generation_date|date:"F d, Y \a\t H:i T" }}</div>
        <div>
          This report includes {{ data.reports|length }} individual compliance reports and {{ data.projects|length }} project assessments
        </div>
      </div>
    </div>
  </body>
</html>
