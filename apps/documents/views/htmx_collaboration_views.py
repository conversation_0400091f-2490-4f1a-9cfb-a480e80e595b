"""
HTMX-specific collaboration views for converting JavaScript collaboration functionality.

This module provides server-side HTMX views that replace client-side JavaScript
collaboration features while maintaining real-time user experience.
"""

import logging
from datetime import datetime, timedelta

from django.core.cache import cache
from django.core.exceptions import PermissionDenied, ValidationError
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.generic import TemplateView, View

from apps.common.mixins import OrganizationRequiredMixin
from apps.documents.models import Document

logger = logging.getLogger(__name__)


class HTMXCollaborationStatusView(OrganizationRequiredMixin, View):
    """
    HTMX view for real-time collaboration status tracking.

    Provides server-side user presence, typing indicators, and activity status
    without requiring WebSocket connections or client-side JavaScript.
    """

    def get(self, request, document_id):
        """Get collaboration status for document."""
        try:
            document = get_object_or_404(Document, id=document_id, organization=request.user.organization)

            # Check document access permissions
            if not self._can_access_document(request.user, document):
                raise PermissionDenied("No access to this document")

            # Get collaboration data
            collaboration_data = self._get_collaboration_data(document, request.user)

            return render(
                request,
                "documents/partials/collaboration_status.html",
                {
                    "document": document,
                    "collaboration_data": collaboration_data,
                    "current_user": request.user,
                },
            )

        except Exception as e:
            logger.error(f"Collaboration status error: {e}")
            return render(
                request,
                "documents/partials/collaboration_error.html",
                {
                    "error_message": "Unable to load collaboration status",
                    "document_id": document_id,
                },
            )

    def post(self, request, document_id):
        """Update user activity status."""
        try:
            document = get_object_or_404(Document, id=document_id, organization=request.user.organization)

            if not self._can_access_document(request.user, document):
                raise PermissionDenied("No access to this document")

            activity_type = request.POST.get("activity_type", "viewing")
            cursor_position = request.POST.get("cursor_position")
            selection_text = request.POST.get("selection_text", "")

            # Update user activity in cache
            self._update_user_activity(
                document_id,
                request.user,
                activity_type,
                cursor_position,
                selection_text,
            )

            # Return updated collaboration status
            collaboration_data = self._get_collaboration_data(document, request.user)

            return render(
                request,
                "documents/partials/collaboration_status.html",
                {
                    "document": document,
                    "collaboration_data": collaboration_data,
                    "current_user": request.user,
                },
            )

        except Exception as e:
            logger.error(f"Activity update error: {e}")
            return render(
                request,
                "documents/partials/collaboration_error.html",
                {
                    "error_message": "Unable to update activity",
                    "document_id": document_id,
                },
            )

    def _can_access_document(self, user, document):
        """Check if user can access document for collaboration."""
        try:
            # Check basic document access
            if document.organization != user.organization:
                return False

            # Check document permissions if available
            if hasattr(document, "can_user_view"):
                return document.can_user_view(user)

            # Default: check if user is owner or has project access
            if document.uploaded_by == user:
                return True

            if hasattr(document, "project") and document.project:
                try:
                    return document.project.can_user_access(user)
                except AttributeError:
                    pass

            return True  # Default allow for organization members

        except Exception as e:
            logger.error(f"Permission check error: {e}")
            return False

    def _get_collaboration_data(self, document, current_user):
        """Get current collaboration data from cache."""
        cache_key = f"collaboration:{document.id}"
        collaboration_data = cache.get(
            cache_key,
            {
                "active_users": [],
                "typing_users": [],
                "last_activity": {},
                "document_stats": {},
            },
        )

        # Clean up old activity data
        cutoff_time = timezone.now() - timedelta(minutes=5)
        active_users = []

        for user_data in collaboration_data.get("active_users", []):
            last_seen = datetime.fromisoformat(user_data.get("last_seen", ""))
            if timezone.make_aware(last_seen) > cutoff_time:
                active_users.append(user_data)

        collaboration_data["active_users"] = active_users

        # Add document statistics
        collaboration_data["document_stats"] = self._get_document_stats(document)

        return collaboration_data

    def _update_user_activity(self, document_id, user, activity_type, cursor_position=None, selection_text=""):
        """Update user activity in cache."""
        cache_key = f"collaboration:{document_id}"
        collaboration_data = cache.get(
            cache_key,
            {
                "active_users": [],
                "typing_users": [],
                "last_activity": {},
                "document_stats": {},
            },
        )

        # Update or add user activity
        user_data = {
            "user_id": user.id,
            "username": user.get_full_name() or user.username,
            "activity_type": activity_type,
            "last_seen": timezone.now().isoformat(),
            "cursor_position": cursor_position,
            "selection_text": (selection_text[:100] if selection_text else ""),  # Limit selection text
        }

        # Update active users list
        active_users = [u for u in collaboration_data["active_users"] if u["user_id"] != user.id]
        active_users.append(user_data)
        collaboration_data["active_users"] = active_users

        # Update typing status
        typing_users = [u for u in collaboration_data.get("typing_users", []) if u["user_id"] != user.id]
        if activity_type == "typing":
            typing_users.append(
                {
                    "user_id": user.id,
                    "username": user_data["username"],
                    "started_typing": timezone.now().isoformat(),
                }
            )
        collaboration_data["typing_users"] = typing_users

        # Cache for 10 minutes
        cache.set(cache_key, collaboration_data, 600)

    def _get_document_stats(self, document):
        """Get basic document statistics."""
        try:
            # Basic file stats
            stats = {
                "file_size": document.file.size if document.file else 0,
                "last_modified": (document.updated_at.isoformat() if document.updated_at else None),
            }

            # Try to get content stats if text-based
            if hasattr(document, "content") and document.content:
                content = str(document.content)
                stats.update(
                    {
                        "word_count": len(content.split()),
                        "character_count": len(content),
                        "character_count_no_spaces": len(content.replace(" ", "")),
                    }
                )

            return stats

        except Exception as e:
            logger.error(f"Document stats error: {e}")
            return {}


class HTMXDocumentDiscussionView(OrganizationRequiredMixin, View):
    """
    HTMX view for document discussions and comments.

    Handles threaded discussions, comments, and real-time updates
    without requiring JavaScript or WebSocket connections.
    """

    def get(self, request, document_id):
        """Get document discussions."""
        try:
            document = get_object_or_404(Document, id=document_id, organization=request.user.organization)

            # Get discussions for this document
            discussions = self._get_document_discussions(document)

            return render(
                request,
                "documents/partials/document_discussions.html",
                {
                    "document": document,
                    "discussions": discussions,
                    "current_user": request.user,
                },
            )

        except Exception as e:
            logger.error(f"Discussion view error: {e}")
            return render(
                request,
                "documents/partials/collaboration_error.html",
                {
                    "error_message": "Unable to load discussions",
                    "document_id": document_id,
                },
            )

    def post(self, request, document_id):
        """Create new discussion or comment."""
        try:
            document = get_object_or_404(Document, id=document_id, organization=request.user.organization)

            discussion_title = request.POST.get("discussion_title")
            discussion_content = request.POST.get("discussion_content")
            parent_discussion_id = request.POST.get("parent_discussion_id")
            page_number = request.POST.get("page_number")
            coordinates = request.POST.get("coordinates")

            if discussion_title and discussion_content:
                # Create new discussion
                discussion = self._create_discussion(
                    document,
                    request.user,
                    discussion_title,
                    discussion_content,
                    page_number,
                    coordinates,
                )

                return render(
                    request,
                    "documents/partials/discussion_created.html",
                    {
                        "discussion": discussion,
                        "document": document,
                    },
                )

            elif discussion_content and parent_discussion_id:
                # Add comment to existing discussion
                comment = self._create_comment(parent_discussion_id, request.user, discussion_content)

                return render(
                    request,
                    "documents/partials/comment_created.html",
                    {
                        "comment": comment,
                        "document": document,
                    },
                )

            else:
                raise ValidationError("Missing required fields")

        except Exception as e:
            logger.error(f"Discussion creation error: {e}")
            return render(
                request,
                "documents/partials/collaboration_error.html",
                {
                    "error_message": f"Unable to create discussion: {str(e)}",
                    "document_id": document_id,
                },
            )

    def _get_document_discussions(self, document):
        """Get discussions for document."""
        try:
            # Try to get discussions from document model
            if hasattr(document, "discussions"):
                return document.discussions.filter(is_resolved=False).order_by("-created_at")

            # Fallback: Try to import and query DocumentDiscussion
            try:
                from apps.documents.models import DocumentDiscussion

                return DocumentDiscussion.objects.filter(document=document, is_resolved=False).order_by("-created_at")
            except ImportError:
                logger.warning("DocumentDiscussion model not available")
                return []

        except Exception as e:
            logger.error(f"Error getting discussions: {e}")
            return []

    def _create_discussion(self, document, user, title, content, page_number=None, coordinates=None):
        """Create new discussion."""
        try:
            from apps.documents.models import DocumentDiscussion

            discussion_data = {
                "document": document,
                "created_by": user,
                "title": title,
                "description": content,
                "page_number": int(page_number) if page_number else None,
                "coordinates": coordinates,
            }

            discussion = DocumentDiscussion.objects.create(**discussion_data)
            return discussion

        except ImportError:
            logger.error("DocumentDiscussion model not available")
            raise ValidationError("Discussion feature not available")
        except Exception as e:
            logger.error(f"Discussion creation failed: {e}")
            raise ValidationError(f"Failed to create discussion: {str(e)}")

    def _create_comment(self, discussion_id, user, content):
        """Create comment on discussion."""
        try:
            from apps.documents.models import (
                DocumentDiscussion,
                DocumentDiscussionMessage,
            )

            discussion = DocumentDiscussion.objects.get(id=discussion_id)

            comment = DocumentDiscussionMessage.objects.create(
                discussion=discussion,
                created_by=user,
                content=content,
            )

            return comment

        except ImportError:
            logger.error("DocumentDiscussionMessage model not available")
            raise ValidationError("Comment feature not available")
        except Exception as e:
            logger.error(f"Comment creation failed: {e}")
            raise ValidationError(f"Failed to create comment: {str(e)}")


class HTMXDocumentAutoSaveView(OrganizationRequiredMixin, View):
    """
    HTMX view for document auto-save functionality.

    Handles automatic document saving without requiring JavaScript timers
    or client-side state management.
    """

    def post(self, request, document_id):
        """Auto-save document content."""
        try:
            document = get_object_or_404(Document, id=document_id, organization=request.user.organization)

            # Check edit permissions
            if not self._can_edit_document(request.user, document):
                raise PermissionDenied("No edit access to this document")

            content = request.POST.get("content", "")
            save_type = request.POST.get("save_type", "auto")  # auto, manual, draft

            # Save document content
            save_result = self._save_document_content(document, request.user, content, save_type)

            return render(
                request,
                "documents/partials/document_saved.html",
                {
                    "document": document,
                    "save_result": save_result,
                    "save_type": save_type,
                    "timestamp": timezone.now(),
                },
            )

        except Exception as e:
            logger.error(f"Auto-save error: {e}")
            return render(
                request,
                "documents/partials/collaboration_error.html",
                {
                    "error_message": f"Save failed: {str(e)}",
                    "document_id": document_id,
                },
            )

    def _can_edit_document(self, user, document):
        """Check if user can edit document."""
        try:
            if hasattr(document, "can_user_edit"):
                return document.can_user_edit(user)

            # Default: check if user is owner
            return document.uploaded_by == user

        except Exception as e:
            logger.error(f"Edit permission check error: {e}")
            return False

    def _save_document_content(self, document, user, content, save_type):
        """Save document content."""
        try:
            # Create or update document content
            if hasattr(document, "content"):
                previous_content = getattr(document, "content", "")
                document.content = content
                document.save()

                # Create version if significant change
                if save_type == "manual" and len(content) != len(previous_content):
                    self._create_document_version(document, user, "Manual save")

                return {
                    "status": "success",
                    "message": f"Document {save_type} saved",
                    "content_length": len(content),
                    "word_count": len(content.split()) if content else 0,
                }
            else:
                logger.warning("Document model does not support content field")
                return {
                    "status": "warning",
                    "message": "Content saving not supported for this document type",
                }

        except Exception as e:
            logger.error(f"Document save failed: {e}")
            raise ValidationError(f"Save failed: {str(e)}")

    def _create_document_version(self, document, user, change_summary):
        """Create document version for significant changes."""
        try:
            if hasattr(document, "create_version"):
                return document.create_version(user=user, summary=change_summary)
            else:
                logger.info("Document versioning not available")
                return None

        except Exception as e:
            logger.error(f"Version creation failed: {e}")
            return None


class HTMXCollaborationDashboardView(OrganizationRequiredMixin, TemplateView):
    """
    HTMX dashboard view combining all collaboration features.

    Provides a comprehensive collaboration interface that updates
    via HTMX polling without requiring WebSocket connections.
    """

    template_name = "documents/partials/collaboration_dashboard.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        document_id = self.kwargs.get("document_id")

        try:
            document = get_object_or_404(Document, id=document_id, organization=self.request.user.organization)

            context.update(
                {
                    "document": document,
                    "can_edit": self._can_edit_document(self.request.user, document),
                    "collaboration_features": {
                        "discussions": True,
                        "auto_save": True,
                        "user_presence": True,
                        "typing_indicators": True,
                    },
                }
            )

        except Exception as e:
            logger.error(f"Collaboration dashboard error: {e}")
            context["error"] = str(e)

        return context

    def _can_edit_document(self, user, document):
        """Check if user can edit document."""
        try:
            if hasattr(document, "can_user_edit"):
                return document.can_user_edit(user)
            return document.uploaded_by == user
        except Exception:
            return False
