<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Permission Operation Failed</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .content {
            background: #f9f9f9;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .error-summary {
            background: #f8d7da;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
            margin: 10px 0;
            background: #f8d7da;
            color: #721c24;
        }
        .error-details {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #555;
        }
        .detail-value {
            color: #333;
        }
        .error-message {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #f5c6cb;
            font-family: monospace;
            font-size: 14px;
            word-wrap: break-word;
        }
        .action-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #ffc107;
        }
        .button {
            display: inline-block;
            background: #dc3545;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 6px;
            margin: 20px 0;
            font-weight: bold;
            text-align: center;
        }
        .button.secondary {
            background: #6c757d;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
        }
        .icon-error {
            font-size: 48px;
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
        }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>⚠️ Bulk Permission Operation Failed</h1>
      <p>Your operation could not be completed</p>
    </div>
    <div class="content">
      <div class="error-summary">
        <div class="icon-error">❌</div>
        <h2 style="text-align: center; margin-top: 0;">Operation Failed</h2>
        <div class="status-badge">Failed</div>
        <p>
          The bulk permission operation encountered a critical error and could not be completed. No changes have been applied to your documents.
        </p>
      </div>
      <div class="error-details">
        <h3>Operation Details</h3>
        <div class="detail-row">
          <span class="detail-label">Operation Type:</span>
          <span class="detail-value">{{ operation.get_operation_type_display }}</span>
        </div>

        {% if operation.description %}
          <div class="detail-row">
            <span class="detail-label">Description:</span>
            <span class="detail-value">{{ operation.description }}</span>
          </div>
        {% endif %}

        <div class="detail-row">
          <span class="detail-label">Started At:</span>
          <span class="detail-value">{{ operation.started_at|date:"F d, Y g:i A" }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Failed At:</span>
          <span class="detail-value">{{ operation.completed_at|date:"F d, Y g:i A" }}</span>
        </div>

        {% if operation.result_summary.error %}
          <h4>Error Details</h4>
          <div class="error-message">{{ operation.result_summary.error }}</div>
        {% endif %}

      </div>
      <div class="action-section">
        <h4 style="margin-top: 0;">Recommended Actions</h4>
        <ol style="margin: 10px 0; padding-left: 20px;">
          <li>Review the error message above</li>
          <li>Check that all selected documents are accessible</li>
          <li>Verify you have the necessary permissions</li>
          <li>Try running the operation with a smaller set of documents</li>
          <li>Contact your system administrator if the problem persists</li>
        </ol>
      </div>
      <div style="text-align: center; margin: 30px 0;">
        <a href="{{ operation.get_absolute_url|default:'#' }}" class="button">View Operation Details</a>
        <a href="#" class="button secondary" style="margin-left: 10px;">Contact Support</a>
      </div>
    </div>
    <div class="footer">
      <p>This is an automated notification from your document management system.</p>
      <p>If you need assistance, please contact your system administrator with the operation ID below.</p>
      <p>Operation ID: {{ operation.id }}</p>
    </div>
  </body>
</html>
