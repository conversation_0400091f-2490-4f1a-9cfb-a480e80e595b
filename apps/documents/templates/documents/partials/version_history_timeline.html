{% load custom_filters %}

{% for version in page_obj %}
  <div class="timeline-item {{ version.version_type }} 
    {% if version.is_current %}current{% elif version.is_draft %}draft{% endif %}
     ">
    <div class="timeline-card">
      <div class="version-header">
        <div class="version-info">
          <div class="flex items-center space-x-3 mb-2">
            <h3 class="text-xl font-semibold text-gray-900">Version {{ version.get_version_string }}</h3>
            <span class="inline-block px-2 py-1 rounded-full text-xs font-medium 
              {% if version.version_type == 'major' %}bg-red-100 text-red-800 {% elif version.version_type == 'minor' %}bg-blue-100 text-blue-800 {% elif version.version_type == 'patch' %}bg-gray-100 text-gray-800 {% else %}bg-yellow-100 text-yellow-800{% endif %}
             ">{{ version.get_version_type_display }}</span>

            {% if version.is_current %}
              <span class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Current</span>
            {% endif %}

            {% if version.is_draft %}
              <span class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Draft</span>
            {% endif %}

            {% if version.branch_name %}
              <span class="inline-block px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Branch: {{ version.branch_name }}
              </span>
            {% endif %}

          </div>
          <p class="text-gray-700 mb-3">{{ version.change_summary|default:"No description provided" }}</p>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
            <div>
              <i class="fas fa-user mr-1"></i>
              <span>{{ version.uploaded_by.get_full_name|default:version.uploaded_by.username }}</span>
            </div>
            <div>
              <i class="fas fa-clock mr-1"></i>
              <span>{{ version.created_at|date:"M j, Y g:i A" }}</span>
            </div>
            <div>
              <i class="fas fa-file mr-1"></i>
              <span>{{ version.file_size|filesizeformat }}</span>
            </div>

            {% if version.similarity_score %}
              <div>
                <i class="fas fa-chart-line mr-1"></i>
                <span>{{ version.similarity_score|floatformat:1 }}% similar</span>
              </div>
            {% endif %}

          </div>

          {% if version.change_notes %}
            <div class="bg-gray-50 rounded-lg p-3 mb-3">
              <h4 class="font-medium text-gray-900 mb-1">Detailed Notes:</h4>
              <p class="text-sm text-gray-700">{{ version.change_notes|truncatewords:30 }}</p>
            </div>
          {% endif %}

          <!-- Tags -->

          {% if version.metadata.tags %}
            <div class="flex flex-wrap gap-2 mb-3">

              {% for tag in version.metadata.tags %}
                <span class="inline-block bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-xs">
                  <i class="fas fa-tag mr-1"></i>#{{ tag.name }}

                  {% if tag.notes %}<span class="ml-1 text-indigo-600" title="{{ tag.notes }}">ℹ</span>{% endif %}

                </span>
              {% endfor %}

            </div>
          {% endif %}

        </div>
        <div class="version-actions">
          <a href="{% url 'documents:version-detail' version.id %}"
             class="action-btn secondary">
            <i class="fas fa-eye mr-1"></i>View
          </a>

          {% if not version.is_current %}
            <button onclick="compareWithCurrent('{{ version.id }}')"
                    class="action-btn secondary">
              <i class="fas fa-code-compare mr-1"></i>Compare
            </button>
            <button onclick="restoreVersion('{{ version.id }}')"
                    class="action-btn primary">
              <i class="fas fa-undo mr-1"></i>Restore
            </button>
          {% endif %}

          <div class="relative">
            <button onclick="showVersionMenu('{{ version.id }}')"
                    class="action-btn secondary">
              <i class="fas fa-ellipsis-v"></i>
            </button>
            <div id="menu-{{ version.id }}"
                 class="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10 hidden min-w-[150px]">
              <button onclick="tagVersion('{{ version.id }}')"
                      class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50">
                <i class="fas fa-tag mr-2"></i>Add Tag
              </button>
              <button onclick="editVersionNotes('{{ version.id }}')"
                      class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50">
                <i class="fas fa-edit mr-2"></i>Edit Notes
              </button>

              {% if version.file %}
                <a href="{{ version.file.url }}"
                   download
                   class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50">
                  <i class="fas fa-download mr-2"></i>Download
                </a>
              {% endif %}

              <hr class="my-1">
              <button onclick="showVersionInfo('{{ version.id }}')"
                      class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50">
                <i class="fas fa-info-circle mr-2"></i>Details
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- Diff Preview (if comparing with previous version) -->

      {% if forloop.counter0 > 0 %}
        <div class="diff-summary">
          <button onclick="loadDiffPreview('{{ version.id }}', '{{ previous_version.id }}')"
                  class="text-blue-600 hover:text-blue-800 text-sm font-medium">
            <i class="fas fa-code-compare mr-1"></i>
            Show changes from v{{ previous_version.get_version_string }}
          </button>
          <div id="diff-preview-{{ version.id }}" class="mt-2 hidden">
            <!-- Diff content will be loaded here -->
          </div>
        </div>
      {% endif %}

    </div>
  </div>
{% empty %}
  <div class="timeline-item">
    <div class="timeline-card">
      <div class="text-center py-8">
        <i class="fas fa-file-alt text-gray-400 text-3xl mb-3"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No versions found</h3>
        <p class="text-gray-500">No versions match your current filters.</p>
      </div>
    </div>
  </div>
{% endfor %}

{% if page_obj.has_next %}
  <div class="text-center mt-6">
    <button hx-get="{% url 'documents:version_history_htmx' document.id %}?page={{ page_obj.next_page_number }}&include_drafts={{ include_drafts|yesno:'true,false' }}&branch={{ branch_filter }}"
            hx-target="#timelineContainer"
            hx-swap="beforeend"
            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
      Load More Versions
    </button>
  </div>
{% endif %}

<script>
function showVersionMenu(versionId) {
    // Hide all other menus
    document.querySelectorAll('[id^="menu-"]').forEach(menu => {
        if (menu.id !== `menu-${versionId}`) {
            menu.classList.add('hidden');
        }
    });
    
    // Toggle current menu
    const menu = document.getElementById(`menu-${versionId}`);
    menu.classList.toggle('hidden');
}

function compareWithCurrent(versionId) {
    const currentVersionId = '{{ document.versions.filter.is_current.first.id }}';
    if (currentVersionId) {
        window.location.href = `/documents/versions/compare/${versionId}/${currentVersionId}/`;
    } else {
        alert('No current version to compare with');
    }
}

function restoreVersion(versionId) {
    window.location.href = `/documents/versions/${versionId}/restore/`;
}

function tagVersion(versionId) {
    htmx.ajax('GET', `/documents/htmx/version-tag/${versionId}/`, {
        target: '#modalContainer'
    });
    
    // Hide menu
    document.getElementById(`menu-${versionId}`).classList.add('hidden');
}

function editVersionNotes(versionId) {
    htmx.ajax('GET', `/documents/htmx/version-notes/${versionId}/`, {
        target: '#modalContainer'
    });
    
    // Hide menu
    document.getElementById(`menu-${versionId}`).classList.add('hidden');
}

function showVersionInfo(versionId) {
    window.location.href = `/documents/versions/${versionId}/`;
}

function loadDiffPreview(fromId, toId) {
    const previewDiv = document.getElementById(`diff-preview-${fromId}`);
    
    if (previewDiv.classList.contains('hidden')) {
        // Load diff content
        htmx.ajax('GET', `/documents/htmx/version-diff/${toId}/${fromId}/?diff_type=metadata`, {
            target: `#diff-preview-${fromId}`,
            swap: 'innerHTML'
        });
        
        previewDiv.classList.remove('hidden');
    } else {
        previewDiv.classList.add('hidden');
    }
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick*="showVersionMenu"]') && 
        !event.target.closest('[id^="menu-"]')) {
        document.querySelectorAll('[id^="menu-"]').forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});
</script>
