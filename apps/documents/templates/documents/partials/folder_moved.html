{% load static %}

<div class="alert alert-success alert-dismissible fade show" role="alert">
  <i class="fas fa-arrows-alt me-2"></i>
  {{ success_message }}
  <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<script>
// Refresh the folder tree to reflect the move
if (typeof refreshFolderTree === 'function') {
    refreshFolderTree();
}

// Update breadcrumbs if necessary
if (typeof updateBreadcrumbs === 'function' && '{{ folder.id }}') {
    updateBreadcrumbs('{{ folder.id }}');
}
</script>
