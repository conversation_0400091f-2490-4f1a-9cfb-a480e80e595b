"""
Document Permission System

Provides permission checking decorators, mixins, and utility functions
for the document access control system.
"""

import functools
from typing import Any, Dict, List, Optional, Union

from django.contrib.auth.models import Group
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import PermissionDenied

from apps.authentication.models import PermissionAuditLog, Role, User

# DocumentPermission with fallback for circular imports
try:
    from .models import DocumentPermission
except ImportError:
    # Fallback for cases where models aren't available yet
    DocumentPermission = None


class PermissionMixin:
    """Mixin for models that support the document permission system."""

    def get_permissions_for_user(self, user: User, include_inherited: bool = True) -> Dict[str, bool]:
        """
        Get effective permissions for a user on this object.

        Args:
            user: User to check permissions for
            include_inherited: Whether to include inherited permissions from parent folders

        Returns:
            Dictionary of permission names and boolean values
        """
        if not user or not user.is_authenticated:
            return self._get_default_permissions()

        # Superusers have all permissions
        if user.is_superuser:
            return self._get_admin_permissions()

        # Check organization access
        if hasattr(self, "organization") and user.organization != self.organization:
            return self._get_default_permissions()

        # Get direct permissions
        permissions = self._get_user_permissions(user)

        # Get group permissions
        group_permissions = self._get_group_permissions(user)
        permissions = self._merge_permissions(permissions, group_permissions)

        # Get role permissions
        role_permissions = self._get_role_permissions(user)
        permissions = self._merge_permissions(permissions, role_permissions)

        # Get inherited permissions if enabled
        if include_inherited:
            inherited_permissions = self._get_inherited_permissions(user)
            # Inherited permissions only apply if no direct permissions are set
            if not any(permissions.values()):
                permissions = self._merge_permissions(permissions, inherited_permissions)

        # Check if user is owner/creator
        if self._is_user_owner(user):
            owner_permissions = self._get_owner_permissions()
            permissions = self._merge_permissions(permissions, owner_permissions)

        # Apply public permissions as fallback
        public_permissions = self._get_public_permissions()
        permissions = self._merge_permissions(public_permissions, permissions)

        return permissions

    def user_has_permission(self, user: User, permission: str, include_inherited: bool = True) -> bool:
        """
        Check if user has a specific permission on this object.

        Args:
            user: User to check permissions for
            permission: Permission name to check
            include_inherited: Whether to include inherited permissions

        Returns:
            Boolean indicating if user has the permission
        """
        permissions = self.get_permissions_for_user(user, include_inherited)
        return permissions.get(permission, False)

    def _get_user_permissions(self, user: User) -> Dict[str, bool]:
        """Get permissions directly assigned to the user."""
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)
        try:
            permission = DocumentPermission.objects.get(
                content_type=content_type,
                object_id=self.pk,
                user=user,
                permission_type="user",
            )
            if not permission.is_expired():
                return permission.get_effective_permissions()
        except DocumentPermission.DoesNotExist:
            pass

        return {}

    def _get_group_permissions(self, user: User) -> Dict[str, bool]:
        """Get permissions from user's groups."""
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)
        user_groups = user.groups.all()

        permissions = {}
        for group in user_groups:
            try:
                permission = DocumentPermission.objects.get(
                    content_type=content_type,
                    object_id=self.pk,
                    group=group,
                    permission_type="group",
                )
                if not permission.is_expired():
                    group_perms = permission.get_effective_permissions()
                    permissions = self._merge_permissions(permissions, group_perms)
            except DocumentPermission.DoesNotExist:
                continue

        return permissions

    def _get_role_permissions(self, user: User) -> Dict[str, bool]:
        """Get permissions from user's roles."""
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)
        user_roles = user.user_roles.filter(organization=getattr(self, "organization", None)).select_related("role")

        permissions = {}
        for user_role in user_roles:
            if not user_role.is_active():
                continue

            try:
                permission = DocumentPermission.objects.get(
                    content_type=content_type,
                    object_id=self.pk,
                    role=user_role.role,
                    permission_type="role",
                )
                if not permission.is_expired():
                    role_perms = permission.get_effective_permissions()
                    permissions = self._merge_permissions(permissions, role_perms)
            except DocumentPermission.DoesNotExist:
                continue

        return permissions

    def _get_inherited_permissions(self, user: User) -> Dict[str, bool]:
        """Get permissions inherited from parent folders."""
        from .models import DocumentPermissionInheritance

        content_type = ContentType.objects.get_for_model(self)

        # Check if inheritance is enabled for this object
        try:
            inheritance = DocumentPermissionInheritance.objects.get(
                child_content_type=content_type,
                child_object_id=self.pk,
                inherits_permissions=True,
                break_inheritance=False,
            )

            # Get permissions from parent
            parent = inheritance.parent_object
            if parent and hasattr(parent, "get_permissions_for_user"):
                return parent.get_permissions_for_user(user, include_inherited=True)
        except DocumentPermissionInheritance.DoesNotExist:
            # Try to inherit from folder if this is a document
            if hasattr(self, "folder") and self.folder:
                return self.folder.get_permissions_for_user(user, include_inherited=True)

        return {}

    def _get_public_permissions(self) -> Dict[str, bool]:
        """Get public permissions for this object."""
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)
        try:
            permission = DocumentPermission.objects.get(
                content_type=content_type, object_id=self.pk, permission_type="public"
            )
            if not permission.is_expired():
                return permission.get_effective_permissions()
        except DocumentPermission.DoesNotExist:
            pass

        # Check if object has is_public flag
        if hasattr(self, "is_public") and self.is_public:
            return {"view": True, "view_metadata": True, "download": True}

        return {}

    def _is_user_owner(self, user: User) -> bool:
        """Check if user is the owner/creator of this object."""
        owner_fields = ["created_by", "uploaded_by", "owner"]
        for field in owner_fields:
            if hasattr(self, field):
                owner = getattr(self, field)
                if owner == user:
                    return True
        return False

    def _get_owner_permissions(self) -> Dict[str, bool]:
        """Get permissions for object owner."""
        return self._get_admin_permissions()

    def _get_admin_permissions(self) -> Dict[str, bool]:
        """Get full admin permissions."""
        return {
            "view": True,
            "edit": True,
            "delete": True,
            "share": True,
            "admin": True,
            "view_metadata": True,
            "view_versions": True,
            "create_versions": True,
            "download": True,
            "print": True,
            "comment": True,
        }

    def _get_default_permissions(self) -> Dict[str, bool]:
        """Get default (no access) permissions."""
        return {
            "view": False,
            "edit": False,
            "delete": False,
            "share": False,
            "admin": False,
            "view_metadata": False,
            "view_versions": False,
            "create_versions": False,
            "download": False,
            "print": False,
            "comment": False,
        }

    def _merge_permissions(self, base: Dict[str, bool], override: Dict[str, bool]) -> Dict[str, bool]:
        """Merge two permission dictionaries, with override taking precedence."""
        result = base.copy()
        for permission, value in override.items():
            if value:  # Only override if the new value is True
                result[permission] = value
        return result

    def grant_permission(
        self,
        target: Union[User, Group, Role],
        permission_level: str,
        granted_by: Optional[User] = None,
        **kwargs,
    ) -> "DocumentPermission":
        from .models import DocumentPermission
        """
        Grant permission to a user, group, or role.

        Args:
            target: User, Group, or Role to grant permission to
            permission_level: Level of permission to grant
            granted_by: User granting the permission
            **kwargs: Additional permission settings

        Returns:
            Created DocumentPermission instance
        """
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)

        # Determine permission type and target
        if isinstance(target, User):
            permission_type = "user"
            kwargs["user"] = target
        elif isinstance(target, Group):
            permission_type = "group"
            kwargs["group"] = target
        elif isinstance(target, Role):
            permission_type = "role"
            kwargs["role"] = target
        else:
            raise ValueError("Target must be User, Group, or Role")

        # Create or update permission
        permission, created = DocumentPermission.objects.update_or_create(
            content_type=content_type,
            object_id=self.pk,
            permission_type=permission_type,
            **{k: v for k, v in kwargs.items() if k in ["user", "group", "role"]},
            defaults={
                "permission_level": permission_level,
                "granted_by": granted_by,
                "organization": getattr(self, "organization", None),
                **kwargs,
            },
        )

        return permission

    def revoke_permission(self, target: Union[User, Group, Role], revoked_by: Optional[User] = None) -> bool:
        """
        Revoke permission from a user, group, or role.

        Args:
            target: User, Group, or Role to revoke permission from
            revoked_by: User revoking the permission

        Returns:
            Boolean indicating if permission was revoked
        """
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)

        # Determine permission type and target
        filter_kwargs = {"content_type": content_type, "object_id": self.pk}

        if isinstance(target, User):
            filter_kwargs.update({"permission_type": "user", "user": target})
        elif isinstance(target, Group):
            filter_kwargs.update({"permission_type": "group", "group": target})
        elif isinstance(target, Role):
            filter_kwargs.update({"permission_type": "role", "role": target})
        else:
            raise ValueError("Target must be User, Group, or Role")

        try:
            permission = DocumentPermission.objects.get(**filter_kwargs)

            # Log the revocation
            if revoked_by:
                PermissionAuditLog.log_permission_event(
                    event_type="object_permission_removed",
                    organization=getattr(self, "organization", None),
                    user=revoked_by,
                    description=f"Permission revoked for {permission.get_permission_target_display()} on {self}",
                    severity="warning",
                    permission_name=f"document.{permission.permission_level}",
                    content_object=self,
                    metadata={
                        "permission_id": str(permission.id),
                        "permission_type": permission.permission_type,
                        "permission_level": permission.permission_level,
                        "target": permission.get_permission_target_display(),
                    },
                )

            permission.delete()
            return True
        except DocumentPermission.DoesNotExist:
            return False

    def get_permission_holders(self) -> List[Dict[str, Any]]:
        """Get all users, groups, and roles with permissions on this object."""
        from .models import DocumentPermission

        content_type = ContentType.objects.get_for_model(self)
        permissions = DocumentPermission.objects.filter(content_type=content_type, object_id=self.pk).select_related(
            "user", "group", "role"
        )

        holders = []
        for permission in permissions:
            if permission.is_expired():
                continue

            holder_data = {
                "permission_id": permission.id,
                "permission_type": permission.permission_type,
                "permission_level": permission.permission_level,
                "effective_permissions": permission.get_effective_permissions(),
                "granted_by": permission.granted_by,
                "granted_at": permission.granted_at,
                "expires_at": permission.expires_at,
            }

            if permission.user:
                holder_data.update(
                    {
                        "target_type": "user",
                        "target_id": permission.user.id,
                        "target_name": permission.user.get_full_name() or permission.user.email,
                        "target_email": permission.user.email,
                    }
                )
            elif permission.group:
                holder_data.update(
                    {
                        "target_type": "group",
                        "target_id": permission.group.id,
                        "target_name": permission.group.name,
                    }
                )
            elif permission.role:
                holder_data.update(
                    {
                        "target_type": "role",
                        "target_id": permission.role.id,
                        "target_name": permission.role.name,
                    }
                )
            else:
                holder_data.update(
                    {
                        "target_type": "public",
                        "target_name": "Public Access",
                    }
                )

            holders.append(holder_data)

        return holders


def require_permission(
    permission: str,
    model_param: str = "pk",
    permission_denied_message: Optional[str] = None,
):
    """
    Decorator to require specific permission on a model object.

    Args:
        permission: Permission name to check
        model_param: Parameter name that contains the model object or its ID
        permission_denied_message: Custom message for permission denied
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Get the model object
            obj = kwargs.get(model_param)
            if obj is None and hasattr(request, "resolver_match"):
                obj = request.resolver_match.kwargs.get(model_param)

            if obj is None:
                raise PermissionDenied("Object not found")

            # If obj is an ID, we need to get the actual object
            # This requires the view to provide the model class
            if not hasattr(obj, "user_has_permission"):
                raise PermissionDenied("Object does not support permission checking")

            # Check permission
            if not obj.user_has_permission(request.user, permission):
                # Log permission denial
                PermissionAuditLog.log_permission_denial(
                    permission_name=f"document.{permission}",
                    user=request.user,
                    organization=getattr(request.user, "organization", None),
                    content_object=obj,
                    request=request,
                    denial_reason=permission_denied_message or f"Insufficient {permission} permission",
                )

                raise PermissionDenied(
                    permission_denied_message or f"You don't have {permission} permission for this object"
                )

            # Log successful access
            PermissionAuditLog.log_access_attempt(
                permission_name=f"document.{permission}",
                user=request.user,
                organization=getattr(request.user, "organization", None),
                content_object=obj,
                request=request,
                is_granted=True,
            )

            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def require_any_permission(
    *permissions,
    model_param: str = "pk",
    permission_denied_message: Optional[str] = None,
):
    """
    Decorator to require any one of the specified permissions.

    Args:
        *permissions: Permission names to check (user needs at least one)
        model_param: Parameter name that contains the model object or its ID
        permission_denied_message: Custom message for permission denied
    """

    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(request, *args, **kwargs):
            obj = kwargs.get(model_param)
            if obj is None and hasattr(request, "resolver_match"):
                obj = request.resolver_match.kwargs.get(model_param)

            if obj is None:
                raise PermissionDenied("Object not found")

            if not hasattr(obj, "user_has_permission"):
                raise PermissionDenied("Object does not support permission checking")

            # Check if user has any of the required permissions
            has_permission = False
            granted_permission = None

            for permission in permissions:
                if obj.user_has_permission(request.user, permission):
                    has_permission = True
                    granted_permission = permission
                    break

            if not has_permission:
                # Log permission denial
                PermissionAuditLog.log_permission_denial(
                    permission_name=f"document.{permissions[0]}",  # Log first permission for simplicity
                    user=request.user,
                    organization=getattr(request.user, "organization", None),
                    content_object=obj,
                    request=request,
                    denial_reason=permission_denied_message or f"Insufficient permissions: {', '.join(permissions)}",
                )

                raise PermissionDenied(
                    permission_denied_message or f"You need one of these permissions: {', '.join(permissions)}"
                )

            # Log successful access
            PermissionAuditLog.log_access_attempt(
                permission_name=f"document.{granted_permission}",
                user=request.user,
                organization=getattr(request.user, "organization", None),
                content_object=obj,
                request=request,
                is_granted=True,
            )

            return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


class PermissionViewMixin:
    """Mixin for views that need to check object permissions."""

    required_permission = None
    required_permissions = None  # For multiple permissions (any)
    permission_denied_message = None

    def dispatch(self, request, *args, **kwargs):
        """Check permissions before dispatching to the view method."""
        if self.required_permission or self.required_permissions:
            obj = self.get_permission_object()

            if self.required_permissions:
                # Check if user has any of the required permissions
                has_permission = False
                for permission in self.required_permissions:
                    if obj.user_has_permission(request.user, permission):
                        has_permission = True
                        break

                if not has_permission:
                    self._handle_permission_denied(request, obj, self.required_permissions)

            elif self.required_permission:
                if not obj.user_has_permission(request.user, self.required_permission):
                    self._handle_permission_denied(request, obj, [self.required_permission])

        return super().dispatch(request, *args, **kwargs)

    def get_permission_object(self):
        """Get the object to check permissions against."""
        if hasattr(self, "get_object"):
            return self.get_object()
        raise NotImplementedError("Must implement get_permission_object or provide get_object method")

    def _handle_permission_denied(self, request, obj, permissions):
        """Handle permission denied scenario."""
        # Log permission denial
        PermissionAuditLog.log_permission_denial(
            permission_name=f"document.{permissions[0]}",
            user=request.user,
            organization=getattr(request.user, "organization", None),
            content_object=obj,
            request=request,
            denial_reason=self.permission_denied_message or f"Insufficient permissions: {', '.join(permissions)}",
        )

        raise PermissionDenied(
            self.permission_denied_message or f"You need one of these permissions: {', '.join(permissions)}"
        )


def filter_by_permission(queryset, user: User, permission: str):
    """
    Filter a queryset to only include objects the user has permission to access.

    Args:
        queryset: Django QuerySet to filter
        user: User to check permissions for
        permission: Permission name to check

    Returns:
        Filtered QuerySet
    """
    if not user or not user.is_authenticated:
        return queryset.none()

    if user.is_superuser:
        return queryset

    # For now, implement basic filtering - this could be optimized with database queries
    filtered_ids = []
    for obj in queryset:
        if hasattr(obj, "user_has_permission") and obj.user_has_permission(user, permission):
            filtered_ids.append(obj.pk)

    return queryset.filter(pk__in=filtered_ids)


def get_user_accessible_objects(model_class, user: User, permission: str = "view"):
    """
    Get all objects of a model class that a user has permission to access.

    Args:
        model_class: Django model class
        user: User to check permissions for
        permission: Permission name to check

    Returns:
        QuerySet of accessible objects
    """
    queryset = model_class.objects.all()

    # Filter by organization if the model supports it
    if hasattr(model_class, "organization") and hasattr(user, "organization") and user.organization:
        queryset = queryset.filter(organization=user.organization)

    return filter_by_permission(queryset, user, permission)
