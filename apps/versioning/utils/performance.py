"""
Performance monitoring utilities for versioning views.

Provides decorators and utilities for tracking performance metrics,
slow query detection, and optimization recommendations.
"""

import logging
import time
from contextlib import contextmanager
from functools import wraps
from typing import Any, Callable, Dict

from django.core.cache import cache
from django.db import connection
from django.http import HttpRequest, HttpResponse
from django.utils import timezone

logger = logging.getLogger("versioning.performance")


class PerformanceMonitor:
    """Performance monitoring context manager and utilities"""

    def __init__(self, operation_name: str, threshold_ms: int = 1000):
        self.operation_name = operation_name
        self.threshold_ms = threshold_ms
        self.start_time = None
        self.end_time = None
        self.query_count_start = None
        self.query_count_end = None

    def __enter__(self):
        self.start_time = time.time()
        self.query_count_start = len(connection.queries)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.query_count_end = len(connection.queries)

        execution_time_ms = (self.end_time - self.start_time) * 1000
        query_count = self.query_count_end - self.query_count_start

        # Log performance metrics
        if execution_time_ms > self.threshold_ms:
            logger.warning(
                f"Slow operation: {self.operation_name} took {execution_time_ms:.2f}ms with {query_count} queries"
            )
        else:
            logger.info(f"Operation: {self.operation_name} took {execution_time_ms:.2f}ms with {query_count} queries")

        # Store metrics for analysis
        self._store_metrics(execution_time_ms, query_count)

    def _store_metrics(self, execution_time_ms: float, query_count: int):
        """Store performance metrics in cache for analysis"""
        cache_key = f"perf_metrics_{self.operation_name}"
        metrics = cache.get(cache_key, [])

        # Keep last 100 measurements
        metrics.append(
            {
                "timestamp": timezone.now().isoformat(),
                "execution_time_ms": execution_time_ms,
                "query_count": query_count,
            }
        )

        if len(metrics) > 100:
            metrics = metrics[-100:]

        cache.set(cache_key, metrics, timeout=3600)  # 1 hour


def monitor_performance(operation_name: str = None, threshold_ms: int = 1000):
    """
    Decorator to monitor view performance.

    Args:
        operation_name: Name of the operation for logging
        threshold_ms: Threshold in milliseconds to log as slow operation
    """

    def decorator(view_func: Callable) -> Callable:
        nonlocal operation_name
        if operation_name is None:
            operation_name = view_func.__name__

        @wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
            with PerformanceMonitor(operation_name, threshold_ms):
                return view_func(request, *args, **kwargs)

        return wrapper

    return decorator


def cache_view_result(cache_key_func: Callable = None, timeout: int = 300):
    """
    Decorator to cache view results for performance.

    Args:
        cache_key_func: Function to generate cache key
        timeout: Cache timeout in seconds
    """

    def decorator(view_func: Callable) -> Callable:
        @wraps(view_func)
        def wrapper(request: HttpRequest, *args, **kwargs) -> HttpResponse:
            # Generate cache key
            if cache_key_func:
                cache_key = cache_key_func(request, *args, **kwargs)
            else:
                cache_key = f"view_{view_func.__name__}_{hash(str(args))}"

            # Try to get from cache
            cached_response = cache.get(cache_key)
            if cached_response:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_response

            # Execute view and cache result
            response = view_func(request, *args, **kwargs)

            # Only cache successful responses
            if response.status_code == 200:
                cache.set(cache_key, response, timeout=timeout)
                logger.debug(f"Cached response for {cache_key}")

            return response

        return wrapper

    return decorator


@contextmanager
def query_counter():
    """Context manager to count database queries"""
    query_count_start = len(connection.queries)
    yield
    query_count_end = len(connection.queries)
    query_count = query_count_end - query_count_start

    if query_count > 10:
        logger.warning(f"High query count: {query_count} queries executed")


def optimize_queryset(queryset, select_related_fields=None, prefetch_related_fields=None):
    """
    Optimize a queryset with select_related and prefetch_related.

    Args:
        queryset: Django queryset to optimize
        select_related_fields: Fields for select_related
        prefetch_related_fields: Fields for prefetch_related
    """
    if select_related_fields:
        queryset = queryset.select_related(*select_related_fields)

    if prefetch_related_fields:
        queryset = queryset.prefetch_related(*prefetch_related_fields)

    return queryset


class SlowQueryDetector:
    """Utility to detect and log slow database queries"""

    @staticmethod
    def analyze_queries(threshold_ms: float = 100):
        """Analyze recent queries for slow ones"""
        slow_queries = []

        for query in connection.queries[-20:]:  # Check last 20 queries
            time_taken = float(query.get("time", 0)) * 1000  # Convert to ms

            if time_taken > threshold_ms:
                slow_queries.append(
                    {
                        "sql": (query["sql"][:200] + "..." if len(query["sql"]) > 200 else query["sql"]),
                        "time_ms": time_taken,
                    }
                )

        if slow_queries:
            logger.warning(f"Detected {len(slow_queries)} slow queries")
            for query in slow_queries:
                logger.warning(f"Slow query ({query['time_ms']:.2f}ms): {query['sql']}")

        return slow_queries


def get_performance_metrics(operation_name: str = None) -> Dict[str, Any]:
    """
    Get performance metrics for analysis.

    Args:
        operation_name: Specific operation to get metrics for

    Returns:
        Dictionary containing performance metrics
    """
    if operation_name:
        cache_key = f"perf_metrics_{operation_name}"
        metrics = cache.get(cache_key, [])

        if metrics:
            execution_times = [m["execution_time_ms"] for m in metrics]
            query_counts = [m["query_count"] for m in metrics]

            return {
                "operation": operation_name,
                "total_measurements": len(metrics),
                "avg_execution_time_ms": sum(execution_times) / len(execution_times),
                "max_execution_time_ms": max(execution_times),
                "min_execution_time_ms": min(execution_times),
                "avg_query_count": sum(query_counts) / len(query_counts),
                "max_query_count": max(query_counts),
                "recent_metrics": metrics[-10:],  # Last 10 measurements
            }
    else:
        # Get metrics for all operations
        all_metrics = {}

        # This would require scanning cache keys, which is not efficient
        # In production, use a dedicated metrics store
        logger.info("Use specific operation_name for detailed metrics")

        return all_metrics


def performance_report() -> str:
    """Generate a performance report"""
    operations = [
        "universal_history",
        "version_creation",
        "branch_creation",
        "branch_merge",
        "advanced_search",
    ]

    report = ["Performance Report", "=" * 50]

    for operation in operations:
        metrics = get_performance_metrics(operation)
        if metrics and metrics.get("total_measurements", 0) > 0:
            report.append(f"\n{operation.upper()}")
            report.append(f"  Measurements: {metrics['total_measurements']}")
            report.append(f"  Avg time: {metrics['avg_execution_time_ms']:.2f}ms")
            report.append(f"  Max time: {metrics['max_execution_time_ms']:.2f}ms")
            report.append(f"  Avg queries: {metrics['avg_query_count']:.1f}")
            report.append(f"  Max queries: {metrics['max_query_count']}")

    return "\n".join(report)
