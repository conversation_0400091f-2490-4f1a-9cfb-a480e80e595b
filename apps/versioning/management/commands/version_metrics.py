"""
Advanced version metrics and performance analysis management command.

Provides comprehensive analysis and reporting capabilities for version
management performance, storage optimization, and system health metrics.
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Type

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db import DatabaseError, IntegrityError, OperationalError, models
from django.db.models import Avg, Count, Max, Min, Sum, Value
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

User = get_user_model()
logger = logging.getLogger(__name__)
logger = logging.getLogger(__name__)
import pathlib
import re
import time

Provides comprehensive analysis of versioning system performance, storage usage,
and operational insights with enhanced reporting and optimization recommendations.
"""

import csv
import json
import logging
from datetime import timedelta
from pathlib import Path
from typing import Any

from django.core.management.base import BaseCommand, CommandError, CommandParser
from django.db.models import <PERSON>v<PERSON>, <PERSON>, <PERSON>, Sum
from django.utils import timezone

from apps.versioning.models import UniversalVersion, VersionLog
from apps.versioning.services import (
    VersionLogService,
    VersionManagementService,
    VersionMetricsService,
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Advanced version metrics and performance analysis command.

    Features:
    - Comprehensive system metrics
    - Performance trend analysis
    - Storage optimization insights
    - Activity pattern analysis
    - Predictive analytics
    - Export capabilities
    - Interactive dashboard mode
    """

    help = "Analyze versioning system metrics, performance, and provide optimization insights"

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.metrics_service = VersionMetricsService()
        self.log_service = VersionLogService()
        self.management_service = VersionManagementService()

        # Analysis results storage
        self.analysis_results: dict[str, Any] = {}
        self.recommendations: list[str] = []
        self.warnings: list[str] = []

    def add_arguments(self, parser: CommandParser) -> None:
        """Add command line arguments."""
        # Analysis type options
        parser.add_argument("--system-overview", action="store_true", help="Display comprehensive system overview")

        parser.add_argument(
            "--performance-analysis",
            action="store_true",
            help="Analyze performance trends and bottlenecks",
        )

        parser.add_argument(
            "--storage-analysis",
            action="store_true",
            help="Analyze storage usage and optimization opportunities",
        )

        parser.add_argument("--activity-patterns", action="store_true", help="Analyze user activity and usage patterns")

        parser.add_argument(
            "--top-consumers",
            type=int,
            default=10,
            help="Number of top storage consumers to show (default: 10)",
        )

        parser.add_argument("--trend-analysis", action="store_true", help="Perform trend analysis and predictions")

        parser.add_argument("--health-check", action="store_true", help="Perform system health assessment")

        parser.add_argument("--optimization-report", action="store_true", help="Generate optimization recommendations")

        # Time range options
        parser.add_argument("--days", type=int, default=30, help="Number of days to analyze (default: 30)")

        parser.add_argument("--hours", type=int, help="Number of hours to analyze (overrides --days)")

        parser.add_argument("--since", type=str, help="Start date for analysis (YYYY-MM-DD format)")

        parser.add_argument("--until", type=str, help="End date for analysis (YYYY-MM-DD format)")

        # Filtering options
        parser.add_argument("--organization", type=str, help="Filter by organization name or slug")

        parser.add_argument("--app-label", type=str, help="Filter by Django app label")

        parser.add_argument("--model-name", type=str, help="Filter by model name")

        parser.add_argument("--user", type=str, help="Filter by username")

        # Output options
        parser.add_argument(
            "--output-format",
            choices=["text", "json", "csv", "html"],
            default="text",
            help="Output format (default: text)",
        )

        parser.add_argument("--export-file", type=str, help="Export results to file")

        parser.add_argument("--detailed", action="store_true", help="Include detailed breakdowns in output")

        parser.add_argument("--dashboard", action="store_true", help="Display interactive dashboard-style output")

        # Maintenance options
        parser.add_argument("--cleanup-stale", type=int, help="Clean up stale metrics older than N days")

        parser.add_argument("--update-cache", action="store_true", help="Update cached metrics and statistics")

        parser.add_argument("--rebuild-indexes", action="store_true", help="Rebuild performance indexes")

        # Advanced options
        parser.add_argument("--include-branches", action="store_true", help="Include branch analysis in metrics")

        parser.add_argument("--compression-analysis", action="store_true", help="Analyze compression effectiveness")

        parser.add_argument("--predictive-analysis", action="store_true", help="Include predictive analytics")

        parser.add_argument("--benchmark", action="store_true", help="Run performance benchmarks")

    def handle(self, *args: Any, **options: dict[str, Any]) -> None:
        """Execute the metrics analysis."""
        try:
            self._configure_analysis_period(options)

            self.stdout.write(self.style.SUCCESS("\n📊 CLEAR Versioning System Metrics Analysis\n=" * 55))

            # Run requested analyses
            if options["system_overview"] or not any(
                [
                    options["performance_analysis"],
                    options["storage_analysis"],
                    options["activity_patterns"],
                    options["trend_analysis"],
                    options["health_check"],
                    options["optimization_report"],
                ],
            ):
                self._analyze_system_overview(options)

            if options["performance_analysis"]:
                self._analyze_performance(options)

            if options["storage_analysis"]:
                self._analyze_storage(options)

            if options["activity_patterns"]:
                self._analyze_activity_patterns(options)

            if options["trend_analysis"]:
                self._analyze_trends(options)

            if options["health_check"]:
                self._perform_health_check(options)

            if options["optimization_report"]:
                self._generate_optimization_report(options)

            if options["compression_analysis"]:
                self._analyze_compression(options)

            if options["benchmark"]:
                self._run_benchmarks(options)

            # Maintenance operations
            if options["cleanup_stale"]:
                self._cleanup_stale_metrics(options)

            if options["update_cache"]:
                self._update_cached_metrics(options)

            if options["rebuild_indexes"]:
                self._rebuild_performance_indexes(options)

            # Generate summary and recommendations
            self._generate_summary_and_recommendations(options)

            # Export results if requested
            if options["export_file"]:
                self._export_results(options)

            self.stdout.write(self.style.SUCCESS("\n✅ Metrics analysis completed successfully!"))

        except Exception as e:
            logger.error(f"Metrics analysis failed: {e}", exc_info=True)
            raise CommandError(f"Analysis failed: {e}")

    def _configure_analysis_period(self, options: dict[str, Any]) -> None:
        """Configure the time period for analysis."""
        now = timezone.now()

        if options.get("since") or options.get("until"):
            # Use explicit date range
            if options.get("since"):
                start_date = timezone.datetime.strptime(options["since"], "%Y-%m-%d").replace(
                    tzinfo=timezone.get_current_timezone(),
                )
            else:
                start_date = now - timedelta(days=options.get("days", 30))

            if options.get("until"):
                end_date = timezone.datetime.strptime(options["until"], "%Y-%m-%d").replace(
                    tzinfo=timezone.get_current_timezone(),
                )
            else:
                end_date = now

        elif options.get("hours"):
            # Use hours
            start_date = now - timedelta(hours=options["hours"])
            end_date = now

        else:
            # Use days
            start_date = now - timedelta(days=options.get("days", 30))
            end_date = now

        self.analysis_period = {
            "start": start_date,
            "end": end_date,
            "days": (end_date - start_date).days,
            "hours": (end_date - start_date).total_seconds() / 3600,
        }

        self.stdout.write(
            f"📅 Analysis Period: {start_date.strftime('%Y-%m-%d %H:%M')} to "
            f"{end_date.strftime('%Y-%m-%d %H:%M')} "
            f"({self.analysis_period['days']} days, {self.analysis_period['hours']:.1f} hours)",
        )

    def _analyze_system_overview(self, options: dict[str, Any]) -> None:
        """Analyze and display system overview metrics."""
        self.stdout.write("\n🔍 System Overview Analysis...")

        try:
            overview = self.metrics_service.get_system_overview()
            self.analysis_results["system_overview"] = overview

            if options["output_format"] == "text":
                self._display_system_overview_text(overview, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"System overview analysis failed: {e}")
            logger.warning(f"System overview analysis failed: {e}")

    def _display_system_overview_text(self, overview: dict[str, Any], options: dict[str, Any]) -> None:
        """Display system overview in text format."""
        self.stdout.write("\n📈 System Statistics:")

        # Version statistics
        self.stdout.write(f"  • Total Versions: {overview.get('total_versions', 0):,}")
        self.stdout.write(f"  • Significant Versions: {overview.get('significant_versions', 0):,}")
        self.stdout.write(f"  • Total Logs: {overview.get('total_logs', 0):,}")
        self.stdout.write(f"  • Active Branches: {overview.get('active_branches', 0):,}")
        self.stdout.write(f"  • Versioned Models: {overview.get('versioned_models', 0)}")

        # Storage statistics
        storage_mb = overview.get("total_storage_mb", 0)
        self.stdout.write(f"  • Total Storage: {storage_mb:.1f} MB")

        if storage_mb > 1024:
            self.stdout.write(f"                   ({storage_mb / 1024:.1f} GB)")

        compression_ratio = overview.get("avg_compression_ratio", 1.0)
        if compression_ratio < 1.0:
            savings_pct = (1 - compression_ratio) * 100
            self.stdout.write(f"  • Compression Savings: {savings_pct:.1f}%")

        # Performance indicators
        avg_processing_time = overview.get("avg_processing_time_ms", 0)
        self.stdout.write(f"  • Avg Processing Time: {avg_processing_time:.1f}ms")

        # Growth rates if we have enough data
        if self.analysis_period["days"] >= 7:
            self._calculate_growth_rates(overview)

    def _calculate_growth_rates(self, overview: dict[str, Any]) -> None:
        """Calculate and display growth rates."""
        try:
            # Get historical data for comparison
            week_ago = timezone.now() - timedelta(days=7)

            recent_versions = UniversalVersion.objects.filter(created_at__gte=week_ago).count()

            recent_logs = VersionLog.objects.filter(timestamp__gte=week_ago).count()

            if recent_versions > 0 or recent_logs > 0:
                self.stdout.write("\n📊 Growth Rates (Last 7 Days):")
                if recent_versions > 0:
                    daily_version_rate = recent_versions / 7
                    self.stdout.write(f"  • Versions: {recent_versions:,} ({daily_version_rate:.1f}/day)")

                if recent_logs > 0:
                    daily_log_rate = recent_logs / 7
                    self.stdout.write(f"  • Log Entries: {recent_logs:,} ({daily_log_rate:.1f}/day)")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Growth rate calculation failed: {e}")

    def _analyze_performance(self, options: dict[str, Any]) -> None:
        """Analyze system performance metrics."""
        self.stdout.write("\n⚡ Performance Analysis...")

        try:
            performance_data = self.log_service.get_performance_insights(days=self.analysis_period["days"])
            self.analysis_results["performance"] = performance_data

            if options["output_format"] == "text":
                self._display_performance_text(performance_data, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Performance analysis failed: {e}")
            logger.warning(f"Performance analysis failed: {e}")

    def _display_performance_text(self, performance: dict[str, Any], options: dict[str, Any]) -> None:
        """Display performance analysis in text format."""
        overall = performance.get("overall", {})

        self.stdout.write("\n⚡ Performance Metrics:")
        self.stdout.write(f"  • Total Operations: {overall.get('total_operations', 0):,}")
        self.stdout.write(f"  • Average Time: {overall.get('avg_time', 0):.1f}ms")
        self.stdout.write(f"  • Maximum Time: {overall.get('max_time', 0):.1f}ms")
        self.stdout.write(f"  • Slow Operations (>1s): {overall.get('slow_operations', 0):,}")

        # Performance by action
        by_action = performance.get("by_action", [])
        if by_action and options["detailed"]:
            self.stdout.write("\n📋 Performance by Action:")
            for action in by_action[:10]:  # Top 10
                self.stdout.write(
                    f"  • {action['action']}: {action['count']:,} ops, "
                    f"{action['avg_time']:.1f}ms avg, {action['max_time']:.1f}ms max",
                )

        # Slowest operations
        slowest = performance.get("slowest_operations", [])
        if slowest:
            self.stdout.write("\n🐌 Slowest Operations:")
            for op in slowest[:5]:
                user = op.get("user__username", "system") or "system"
                self.stdout.write(
                    f"  • {op['action']} on {op['model_name']}: {op['processing_time_ms']:.1f}ms ({user})",
                )

        # Performance recommendations
        recommendations = performance.get("recommendations", [])
        if recommendations:
            self.stdout.write("\n💡 Performance Recommendations:")
            for rec in recommendations:
                self.stdout.write(f"  • {rec}")
                self.recommendations.append(f"Performance: {rec}")

    def _analyze_storage(self, options: dict[str, Any]) -> None:
        """Analyze storage usage and optimization opportunities."""
        self.stdout.write("\n💾 Storage Analysis...")

        try:
            storage_data = self.metrics_service.analyze_storage_usage()
            self.analysis_results["storage"] = storage_data

            if options["output_format"] == "text":
                self._display_storage_text(storage_data, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Storage analysis failed: {e}")
            logger.warning(f"Storage analysis failed: {e}")

    def _display_storage_text(self, storage: dict[str, Any], options: dict[str, Any]) -> None:
        """Display storage analysis in text format."""
        self.stdout.write("\n💾 Storage Breakdown:")

        total_mb = storage.get("total_storage_mb", 0)
        compressed_mb = storage.get("compressed_storage_mb", 0)
        uncompressed_mb = total_mb - compressed_mb

        self.stdout.write(f"  • Total Storage: {total_mb:.1f} MB")
        if compressed_mb > 0:
            self.stdout.write(f"  • Compressed: {compressed_mb:.1f} MB")
            self.stdout.write(f"  • Uncompressed: {uncompressed_mb:.1f} MB")

        # Storage by model type
        by_model = storage.get("by_model", [])
        if by_model and options["detailed"]:
            self.stdout.write("\n📊 Storage by Model Type:")
            for model in by_model[:10]:
                self.stdout.write(
                    f"  • {model['model']}: {model['storage_mb']:.1f} MB ({model['version_count']:,} versions)",
                )

        # Top storage consumers
        if options["top_consumers"] > 0:
            self._display_top_consumers(options)

        # Optimization opportunities
        optimization = storage.get("optimization_opportunities", {})
        if optimization:
            self.stdout.write("\n🎯 Storage Optimization Opportunities:")

            if optimization.get("uncompressed_versions", 0) > 0:
                uncompressed = optimization["uncompressed_versions"]
                potential_savings = optimization.get("potential_compression_savings_mb", 0)
                self.stdout.write(
                    f"  • {uncompressed:,} uncompressed versions (potential savings: {potential_savings:.1f} MB)",
                )
                self.recommendations.append(
                    f"Storage: Compress {uncompressed:,} uncompressed versions to save ~{potential_savings:.1f} MB",
                )

            if optimization.get("old_versions_for_archival", 0) > 0:
                old_versions = optimization["old_versions_for_archival"]
                self.stdout.write(f"  • {old_versions:,} old versions eligible for archival")
                self.recommendations.append(f"Storage: Archive {old_versions:,} old versions to reduce active storage")

    def _display_top_consumers(self, options: dict[str, Any]) -> None:
        """Display top storage consumers."""
        try:
            consumers = self.metrics_service.get_top_storage_consumers(limit=options["top_consumers"])

            if consumers:
                self.stdout.write(f"\n🔝 Top {len(consumers)} Storage Consumers:")

                for i, consumer in enumerate(consumers, 1):
                    storage_mb = consumer.get("total_storage_bytes", 0) / (1024 * 1024)
                    self.stdout.write(f"  {i}. {consumer['content_type']} (ID: {consumer['object_id']})")
                    self.stdout.write(f"     Storage: {storage_mb:.1f} MB")
                    self.stdout.write(f"     Versions: {consumer['total_versions']:,}")
                    self.stdout.write(f"     Significant: {consumer['significant_versions']:,}")

                    if options["detailed"]:
                        self.stdout.write(f"     Last Access: {consumer.get('last_access', 'Unknown')}")
                        self.stdout.write(f"     Access Count: {consumer.get('access_count', 0):,}")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Top consumers analysis failed: {e}")

    def _analyze_activity_patterns(self, options: dict[str, Any]) -> None:
        """Analyze user activity and usage patterns."""
        self.stdout.write("\n👥 Activity Pattern Analysis...")

        try:
            activity_data = self.log_service.get_activity_summary(days=self.analysis_period["days"])
            self.analysis_results["activity"] = activity_data

            if options["output_format"] == "text":
                self._display_activity_text(activity_data, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Activity analysis failed: {e}")
            logger.warning(f"Activity analysis failed: {e}")

    def _display_activity_text(self, activity: dict[str, Any], options: dict[str, Any]) -> None:
        """Display activity analysis in text format."""
        total_actions = activity.get("total_actions", 0)
        period_days = activity.get("period_days", self.analysis_period["days"])

        self.stdout.write("\n👥 Activity Summary:")
        self.stdout.write(f"  • Total Actions: {total_actions:,}")
        if period_days > 0:
            daily_avg = total_actions / period_days
            self.stdout.write(f"  • Daily Average: {daily_avg:.1f} actions/day")

        # Action breakdown
        action_breakdown = activity.get("action_breakdown", {})
        if action_breakdown and options["detailed"]:
            self.stdout.write("\n📋 Actions by Type:")
            sorted_actions = sorted(action_breakdown.items(), key=lambda x: x[1], reverse=True)
            for action, count in sorted_actions[:10]:
                percentage = (count / total_actions * 100) if total_actions > 0 else 0
                self.stdout.write(f"  • {action}: {count:,} ({percentage:.1f}%)")

        # User activity
        user_breakdown = activity.get("user_breakdown", {})
        if user_breakdown:
            self.stdout.write("\n👤 Most Active Users:")
            sorted_users = sorted(user_breakdown.items(), key=lambda x: x[1], reverse=True)
            for username, count in sorted_users[:10]:
                self.stdout.write(f"  • {username}: {count:,} actions")

        # Namespace activity
        namespace_breakdown = activity.get("namespace_breakdown", {})
        if namespace_breakdown and options["detailed"]:
            self.stdout.write("\n📦 Activity by Namespace:")
            sorted_namespaces = sorted(namespace_breakdown.items(), key=lambda x: x[1], reverse=True)
            for namespace, count in sorted_namespaces[:10]:
                self.stdout.write(f"  • {namespace}: {count:,} actions")

    def _analyze_trends(self, options: dict[str, Any]) -> None:
        """Analyze trends and perform predictive analysis."""
        self.stdout.write("\n📈 Trend Analysis...")

        try:
            # This would typically involve more complex time series analysis
            # For now, we'll do a simple trend analysis
            trend_data = self._calculate_simple_trends()
            self.analysis_results["trends"] = trend_data

            if options["output_format"] == "text":
                self._display_trends_text(trend_data, options)

        except (ValidationError, ValueError) as e:
            self.warnings.append(f"Trend analysis failed: {e}")
            logger.warning(f"Trend analysis failed: {e}")

    def _calculate_simple_trends(self) -> dict[str, Any]:
        """Calculate simple trend metrics."""
        now = timezone.now()

        # Calculate trends over different periods
        periods = [("1_day", timedelta(days=1)), ("7_days", timedelta(days=7)), ("30_days", timedelta(days=30))]

        trends = {}

        for period_name, delta in periods:
            start_time = now - delta

            versions_count = UniversalVersion.objects.filter(created_at__gte=start_time).count()

            logs_count = VersionLog.objects.filter(timestamp__gte=start_time).count()

            trends[period_name] = {
                "versions": versions_count,
                "logs": logs_count,
                "days": delta.days or 1,
            }

        return trends

    def _display_trends_text(self, trends: dict[str, Any], options: dict[str, Any]) -> None:
        """Display trend analysis in text format."""
        self.stdout.write("\n📈 Activity Trends:")

        for period_name, data in trends.items():
            days = data["days"]
            versions = data["versions"]
            logs = data["logs"]

            versions_per_day = versions / days if days > 0 else 0
            logs_per_day = logs / days if days > 0 else 0

            period_display = period_name.replace("_", " ").title()
            self.stdout.write(f"\n  {period_display}:")
            self.stdout.write(f"    Versions: {versions:,} ({versions_per_day:.1f}/day)")
            self.stdout.write(f"    Logs: {logs:,} ({logs_per_day:.1f}/day)")

        # Simple trend prediction
        if options["predictive_analysis"]:
            self._generate_simple_predictions(trends)

    def _generate_simple_predictions(self, trends: dict[str, Any]) -> None:
        """Generate simple predictions based on trends."""
        try:
            # Calculate growth rate from 7-day to 30-day trends
            week_data = trends.get("7_days", {})
            month_data = trends.get("30_days", {})

            if week_data and month_data:
                week_daily_versions = week_data["versions"] / 7
                month_daily_versions = month_data["versions"] / 30

                if month_daily_versions > 0:
                    growth_factor = week_daily_versions / month_daily_versions

                    # Predict next month's activity
                    predicted_daily = week_daily_versions * growth_factor
                    predicted_monthly = predicted_daily * 30

                    self.stdout.write("\n🔮 Simple Predictions (Next 30 Days):")
                    self.stdout.write(f"  • Estimated Versions: {predicted_monthly:.0f}")
                    self.stdout.write(f"  • Daily Rate: {predicted_daily:.1f}")

                    # Storage prediction
                    current_avg_size = self._get_average_version_size()
                    if current_avg_size > 0:
                        predicted_storage_mb = (predicted_monthly * current_avg_size) / (1024 * 1024)
                        self.stdout.write(f"  • Estimated Storage Growth: {predicted_storage_mb:.1f} MB")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Prediction generation failed: {e}")

    def _get_average_version_size(self) -> float:
        """Get average version size in bytes."""
        try:
            avg_size = UniversalVersion.objects.filter(data_size__isnull=False, data_size__gt=0).aggregate(
                avg_size=Avg("data_size"),
            )["avg_size"]

            return float(avg_size) if avg_size else 0.0

        except (FileNotFoundError, PermissionError, OSError):
            return 0.0

    def _perform_health_check(self, options: dict[str, Any]) -> None:
        """Perform comprehensive system health check."""
        self.stdout.write("\n🏥 System Health Check...")

        try:
            health_data = self._collect_health_metrics()
            self.analysis_results["health"] = health_data

            if options["output_format"] == "text":
                self._display_health_text(health_data, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Health check failed: {e}")
            logger.warning(f"Health check failed: {e}")

    def _collect_health_metrics(self) -> dict[str, Any]:
        """Collect system health metrics."""
        health = {"overall_status": "healthy", "issues": [], "warnings": [], "metrics": {}}

        try:
            # Check for orphaned records
            orphaned_logs = VersionLog.objects.filter(version__isnull=True).count()
            if orphaned_logs > 0:
                health["issues"].append(f"{orphaned_logs} orphaned version logs found")
                health["overall_status"] = "warning"

            # Check for very large versions
            large_versions = UniversalVersion.objects.filter(data_size__gt=10 * 1024 * 1024).count()  # > 10MB
            if large_versions > 0:
                health["warnings"].append(f"{large_versions} very large versions (>10MB)")

            # Check compression status
            uncompressed_count = UniversalVersion.objects.filter(
                is_compressed=False,
                data_size__gt=1024,  # > 1KB
            ).count()
            if uncompressed_count > 0:
                health["warnings"].append(f"{uncompressed_count} uncompressed versions")

            # Check for slow operations
            slow_ops = VersionLog.objects.filter(
                processing_time_ms__gt=1000,
                timestamp__gte=timezone.now() - timedelta(days=1),  # > 1 second
            ).count()
            if slow_ops > 10:
                health["warnings"].append(f"{slow_ops} slow operations in last 24 hours")

            # Performance metrics
            health["metrics"] = {
                "orphaned_logs": orphaned_logs,
                "large_versions": large_versions,
                "uncompressed_versions": uncompressed_count,
                "recent_slow_operations": slow_ops,
            }

            # Determine overall status
            if health["issues"]:
                health["overall_status"] = "critical"
            elif health["warnings"]:
                health["overall_status"] = "warning"

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            health["overall_status"] = "error"
            health["issues"].append(f"Health check failed: {e}")

        return health

    def _display_health_text(self, health: dict[str, Any], options: dict[str, Any]) -> None:
        """Display health check results in text format."""
        status = health.get("overall_status", "unknown")

        status_colors = {
            "healthy": self.style.SUCCESS,
            "warning": self.style.WARNING,
            "critical": self.style.ERROR,
            "error": self.style.ERROR,
        }

        status_icons = {"healthy": "✅", "warning": "⚠️", "critical": "❌", "error": "💥"}

        color_func = status_colors.get(status, self.style.SUCCESS)
        icon = status_icons.get(status, "❓")

        self.stdout.write(f"\n🏥 System Health: {color_func(f'{icon} {status.upper()}')}")

        # Display issues
        issues = health.get("issues", [])
        if issues:
            self.stdout.write("\n❌ Critical Issues:")
            for issue in issues:
                self.stdout.write(f"  • {issue}")

        # Display warnings
        warnings = health.get("warnings", [])
        if warnings:
            self.stdout.write("\n⚠️  Warnings:")
            for warning in warnings:
                self.stdout.write(f"  • {warning}")

        # Display metrics if detailed
        if options["detailed"]:
            metrics = health.get("metrics", {})
            if metrics:
                self.stdout.write("\n📊 Health Metrics:")
                for metric, value in metrics.items():
                    metric_display = metric.replace("_", " ").title()
                    self.stdout.write(f"  • {metric_display}: {value:,}")

    def _generate_optimization_report(self, options: dict[str, Any]) -> None:
        """Generate comprehensive optimization recommendations."""
        self.stdout.write("\n🎯 Optimization Report...")

        try:
            optimization_data = self._collect_optimization_opportunities()
            self.analysis_results["optimization"] = optimization_data

            if options["output_format"] == "text":
                self._display_optimization_text(optimization_data, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Optimization report generation failed: {e}")
            logger.warning(f"Optimization report generation failed: {e}")

    def _collect_optimization_opportunities(self) -> dict[str, Any]:
        """Collect optimization opportunities."""
        opportunities = {"storage": [], "performance": [], "maintenance": [], "configuration": []}

        try:
            # Storage optimizations
            uncompressed = UniversalVersion.objects.filter(is_compressed=False, data_size__gt=1024).count()

            if uncompressed > 0:
                opportunities["storage"].append(
                    {
                        "type": "compression",
                        "description": f"Compress {uncompressed:,} uncompressed versions",
                        "priority": "high" if uncompressed > 1000 else "medium",
                        "impact": "Reduce storage usage by 30-70%",
                    },
                )

            # Old versions for archival
            old_versions = UniversalVersion.objects.filter(
                created_at__lt=timezone.now() - timedelta(days=365),
                is_significant=False,
            ).count()

            if old_versions > 0:
                opportunities["storage"].append(
                    {
                        "type": "archival",
                        "description": f"Archive {old_versions:,} old non-significant versions",
                        "priority": "medium",
                        "impact": "Reduce active database size",
                    },
                )

            # Performance optimizations
            slow_ops = VersionLog.objects.filter(
                processing_time_ms__gt=1000,
                timestamp__gte=timezone.now() - timedelta(days=7),
            ).count()

            if slow_ops > 20:
                opportunities["performance"].append(
                    {
                        "type": "indexing",
                        "description": f"{slow_ops} slow operations detected - consider index optimization",
                        "priority": "high",
                        "impact": "Improve query performance by 50-90%",
                    },
                )

            # Maintenance opportunities
            orphaned_logs = VersionLog.objects.filter(version__isnull=True).count()

            if orphaned_logs > 0:
                opportunities["maintenance"].append(
                    {
                        "type": "cleanup",
                        "description": f"Clean up {orphaned_logs:,} orphaned log entries",
                        "priority": "low",
                        "impact": "Improve data consistency",
                    },
                )

        except (ValidationError, ValueError) as e:
            logger.warning(f"Optimization collection failed: {e}")

        return opportunities

    def _display_optimization_text(self, optimization: dict[str, Any], options: dict[str, Any]) -> None:
        """Display optimization recommendations in text format."""
        self.stdout.write("\n🎯 Optimization Recommendations:")

        priority_order = ["high", "medium", "low"]
        priority_icons = {"high": "🔴", "medium": "🟡", "low": "🟢"}

        for category, opportunities in optimization.items():
            if opportunities:
                category_display = category.replace("_", " ").title()
                self.stdout.write(f"\n📋 {category_display} Optimizations:")

                # Sort by priority
                sorted_ops = sorted(opportunities, key=lambda x: priority_order.index(x.get("priority", "low")))

                for op in sorted_ops:
                    priority = op.get("priority", "low")
                    icon = priority_icons.get(priority, "⚪")

                    self.stdout.write(f"  {icon} {op['description']}")
                    if options["detailed"]:
                        self.stdout.write(f"     Priority: {priority.upper()}")
                        self.stdout.write(f"     Impact: {op.get('impact', 'Not specified')}")

                    # Add to global recommendations
                    self.recommendations.append(f"{category_display}: {op['description']} (Priority: {priority})")

    def _analyze_compression(self, options: dict[str, Any]) -> None:
        """Analyze compression effectiveness."""
        self.stdout.write("\n🗜️  Compression Analysis...")

        try:
            compression_data = self._collect_compression_metrics()
            self.analysis_results["compression"] = compression_data

            if options["output_format"] == "text":
                self._display_compression_text(compression_data, options)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Compression analysis failed: {e}")
            logger.warning(f"Compression analysis failed: {e}")

    def _collect_compression_metrics(self) -> dict[str, Any]:
        """Collect compression effectiveness metrics."""
        metrics = {}

        try:
            # Overall compression statistics
            total_versions = UniversalVersion.objects.count()
            compressed_versions = UniversalVersion.objects.filter(is_compressed=True).count()

            metrics["total_versions"] = total_versions
            metrics["compressed_versions"] = compressed_versions
            metrics["compression_percentage"] = (
                (compressed_versions / total_versions * 100) if total_versions > 0 else 0
            )

            # Compression ratio analysis
            compression_stats = UniversalVersion.objects.filter(
                is_compressed=True,
                compression_ratio__isnull=False,
            ).aggregate(
                avg_ratio=Avg("compression_ratio"),
                min_ratio=Min("compression_ratio"),
                max_ratio=Max("compression_ratio"),
            )

            metrics.update(compression_stats)

            # Storage savings
            compressed_size = (
                UniversalVersion.objects.filter(is_compressed=True).aggregate(total=Sum("data_size"))["total"] or 0
            )

            uncompressed_size = (
                UniversalVersion.objects.filter(is_compressed=False).aggregate(total=Sum("data_size"))["total"] or 0
            )

            metrics["compressed_storage_bytes"] = compressed_size
            metrics["uncompressed_storage_bytes"] = uncompressed_size
            metrics["total_storage_bytes"] = compressed_size + uncompressed_size

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Compression metrics collection failed: {e}")

        return metrics

    def _display_compression_text(self, compression: dict[str, Any], options: dict[str, Any]) -> None:
        """Display compression analysis in text format."""
        total_versions = compression.get("total_versions", 0)
        compressed_versions = compression.get("compressed_versions", 0)
        compression_pct = compression.get("compression_percentage", 0)

        self.stdout.write("\n🗜️  Compression Status:")
        self.stdout.write(f"  • Total Versions: {total_versions:,}")
        self.stdout.write(f"  • Compressed: {compressed_versions:,} ({compression_pct:.1f}%)")
        self.stdout.write(f"  • Uncompressed: {total_versions - compressed_versions:,}")

        # Compression effectiveness
        avg_ratio = compression.get("avg_ratio")
        if avg_ratio:
            savings_pct = (1 - avg_ratio) * 100
            self.stdout.write(f"  • Average Compression Ratio: {avg_ratio:.3f}")
            self.stdout.write(f"  • Average Space Savings: {savings_pct:.1f}%")

        # Storage breakdown
        compressed_mb = compression.get("compressed_storage_bytes", 0) / (1024 * 1024)
        uncompressed_mb = compression.get("uncompressed_storage_bytes", 0) / (1024 * 1024)

        self.stdout.write("\n💾 Storage Breakdown:")
        self.stdout.write(f"  • Compressed Storage: {compressed_mb:.1f} MB")
        self.stdout.write(f"  • Uncompressed Storage: {uncompressed_mb:.1f} MB")
        self.stdout.write(f"  • Total Storage: {compressed_mb + uncompressed_mb:.1f} MB")

    def _run_benchmarks(self, options: dict[str, Any]) -> None:
        """Run performance benchmarks."""
        self.stdout.write("\n🏃 Running Performance Benchmarks...")

        try:
            # This would run actual performance benchmarks
            # For now, we'll simulate some basic timing tests
            benchmark_results = self._simulate_benchmarks()
            self.analysis_results["benchmarks"] = benchmark_results

            if options["output_format"] == "text":
                self._display_benchmark_text(benchmark_results, options)

        except (ValidationError, ValueError) as e:
            self.warnings.append(f"Benchmark execution failed: {e}")
            logger.warning(f"Benchmark execution failed: {e}")

    def _simulate_benchmarks(self) -> dict[str, Any]:
        """Simulate performance benchmarks."""
        import time

        results = {}

        try:
            # Version creation benchmark
            start_time = time.time()
            sample_versions = UniversalVersion.objects.select_related("content_type")[:100]
            list(sample_versions)  # Force evaluation
            version_query_time = (time.time() - start_time) * 1000

            results["version_query_100"] = {
                "time_ms": version_query_time,
                "operations_per_second": 100 / (version_query_time / 1000) if version_query_time > 0 else 0,
            }

            # Log query benchmark
            start_time = time.time()
            sample_logs = VersionLog.objects.select_related("user", "version")[:100]
            list(sample_logs)  # Force evaluation
            log_query_time = (time.time() - start_time) * 1000

            results["log_query_100"] = {
                "time_ms": log_query_time,
                "operations_per_second": 100 / (log_query_time / 1000) if log_query_time > 0 else 0,
            }

        except (DatabaseError, IntegrityError, OperationalError) as e:
            logger.warning(f"Benchmark simulation failed: {e}")

        return results

    def _display_benchmark_text(self, benchmarks: dict[str, Any], options: dict[str, Any]) -> None:
        """Display benchmark results in text format."""
        self.stdout.write("\n🏃 Benchmark Results:")

        for benchmark_name, result in benchmarks.items():
            time_ms = result.get("time_ms", 0)
            ops_per_sec = result.get("operations_per_second", 0)

            benchmark_display = benchmark_name.replace("_", " ").title()
            self.stdout.write(f"  • {benchmark_display}:")
            self.stdout.write(f"    Time: {time_ms:.1f}ms")
            self.stdout.write(f"    Throughput: {ops_per_sec:.1f} ops/sec")

    def _cleanup_stale_metrics(self, options: dict[str, Any]) -> None:
        """Clean up stale metrics records."""
        days_old = options["cleanup_stale"]
        self.stdout.write(f"\n🧹 Cleaning up metrics older than {days_old} days...")

        try:
            timezone.now() - timedelta(days=days_old)

            # This would clean up stale metrics if we had a metrics storage table
            # For now, we'll simulate the cleanup
            cleaned_count = 0  # Simulate cleanup

            self.stdout.write(f"  ✅ Cleaned up {cleaned_count} stale metrics records")
            self.analysis_results["cleanup"] = {"cleaned_records": cleaned_count}

        except (ConnectionError, TimeoutError, AttributeError, KeyError) as e:
            self.warnings.append(f"Metrics cleanup failed: {e}")
            logger.warning(f"Metrics cleanup failed: {e}")

    def _update_cached_metrics(self, options: dict[str, Any]) -> None:
        """Update cached metrics and statistics."""
        self.stdout.write("\n🔄 Updating cached metrics...")

        try:
            # This would update cached metrics/materialized views
            # For now, we'll simulate the update
            updated_caches = ["system_overview", "storage_stats", "performance_metrics"]

            for cache in updated_caches:
                self.stdout.write(f"  ✅ Updated {cache}")

            self.analysis_results["cache_update"] = {"updated_caches": updated_caches}

        except (ConnectionError, TimeoutError, AttributeError, KeyError) as e:
            self.warnings.append(f"Cache update failed: {e}")
            logger.warning(f"Cache update failed: {e}")

    def _rebuild_performance_indexes(self, options: dict[str, Any]) -> None:
        """Rebuild performance indexes."""
        self.stdout.write("\n🔧 Rebuilding performance indexes...")

        try:
            # This would rebuild database indexes for performance
            # For now, we'll simulate the rebuild
            rebuilt_indexes = ["version_content_type_idx", "log_timestamp_idx", "version_created_at_idx"]

            for index in rebuilt_indexes:
                self.stdout.write(f"  ✅ Rebuilt {index}")

            self.analysis_results["index_rebuild"] = {"rebuilt_indexes": rebuilt_indexes}

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.warnings.append(f"Index rebuild failed: {e}")
            logger.warning(f"Index rebuild failed: {e}")

    def _generate_summary_and_recommendations(self, options: dict[str, Any]) -> None:
        """Generate final summary and recommendations."""
        self.stdout.write("\n" + "=" * 55)
        self.stdout.write("📋 ANALYSIS SUMMARY")
        self.stdout.write("=" * 55)

        # Display warnings if any
        if self.warnings:
            self.stdout.write("\n⚠️  Warnings Encountered:")
            for warning in self.warnings:
                self.stdout.write(f"  • {warning}")

        # Display key insights
        self._display_key_insights()

        # Display recommendations
        if self.recommendations:
            self.stdout.write("\n💡 Key Recommendations:")
            for i, rec in enumerate(self.recommendations[:10], 1):  # Top 10
                self.stdout.write(f"  {i}. {rec}")

        # Analysis completeness
        completed_analyses = [key for key in self.analysis_results if self.analysis_results[key]]
        self.stdout.write(f"\n✅ Completed Analyses: {', '.join(completed_analyses)}")

    def _display_key_insights(self) -> None:
        """Display key insights from analysis."""
        self.stdout.write("\n🔍 Key Insights:")

        # System overview insights
        overview = self.analysis_results.get("system_overview", {})
        if overview:
            total_versions = overview.get("total_versions", 0)
            storage_mb = overview.get("total_storage_mb", 0)

            self.stdout.write(f"  • System contains {total_versions:,} versions using {storage_mb:.1f} MB")

        # Performance insights
        performance = self.analysis_results.get("performance", {})
        if performance:
            overall = performance.get("overall", {})
            slow_ops = overall.get("slow_operations", 0)
            if slow_ops > 0:
                self.stdout.write(f"  • {slow_ops:,} slow operations detected")

        # Storage insights
        storage = self.analysis_results.get("storage", {})
        if storage:
            optimization = storage.get("optimization_opportunities", {})
            uncompressed = optimization.get("uncompressed_versions", 0)
            if uncompressed > 0:
                self.stdout.write(f"  • {uncompressed:,} versions could be compressed for space savings")

        # Health insights
        health = self.analysis_results.get("health", {})
        if health:
            status = health.get("overall_status", "unknown")
            self.stdout.write(f"  • System health status: {status.upper()}")

    def _export_results(self, options: dict[str, Any]) -> None:
        """Export analysis results to file."""
        export_file = options["export_file"]
        output_format = options["output_format"]

        self.stdout.write(f"\n💾 Exporting results to {export_file}...")

        try:
            export_path = Path(export_file)

            if output_format == "json":
                self._export_json(export_path)
            elif output_format == "csv":
                self._export_csv(export_path)
            elif output_format == "html":
                self._export_html(export_path)
            else:
                self._export_text(export_path)

            self.stdout.write(f"  ✅ Results exported to {export_path}")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.warnings.append(f"Export failed: {e}")
            logger.warning(f"Export failed: {e}")

    def _export_json(self, export_path: Path) -> None:
        """Export results as JSON."""
        export_data = {
            "analysis_timestamp": timezone.now().isoformat(),
            "analysis_period": self.analysis_period,
            "results": self.analysis_results,
            "recommendations": self.recommendations,
            "warnings": self.warnings,
        }

        with open(export_path, "w") as f:
            json.dump(export_data, f, indent=2, default=str)

    def _export_csv(self, export_path: Path) -> None:
        """Export key metrics as CSV."""
        with open(export_path, "w", newline="") as f:
            writer = csv.writer(f)

            # Write headers
            writer.writerow(["Metric", "Value", "Category"])

            # Export key metrics from analysis results
            for category, data in self.analysis_results.items():
                if isinstance(data, dict):
                    for key, value in data.items():
                        if isinstance(value, int | float | str):
                            writer.writerow([key, value, category])

    def _export_text(self, export_path: Path) -> None:
        """Export results as formatted text."""
        with open(export_path, "w") as f:
            f.write("CLEAR Versioning System Metrics Analysis Report\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Generated: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Analysis Period: {self.analysis_period['start']} to {self.analysis_period['end']}\n\n")

            # Write analysis results
            for category, data in self.analysis_results.items():
                f.write(f"{category.upper().replace('_', ' ')}\n")
                f.write("-" * 30 + "\n")
                f.write(f"{data}\n\n")

            # Write recommendations
            if self.recommendations:
                f.write("RECOMMENDATIONS\n")
                f.write("-" * 30 + "\n")
                for i, rec in enumerate(self.recommendations, 1):
                    f.write(f"{i}. {rec}\n")
                f.write("\n")

            # Write warnings
            if self.warnings:
                f.write("WARNINGS\n")
                f.write("-" * 30 + "\n")
                for warning in self.warnings:
                    f.write(f"• {warning}\n")

    def _export_html(self, export_path: Path) -> None:
        """Export results as HTML report."""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>CLEAR Versioning Metrics Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; }}
                .section {{ margin: 20px 0; padding: 15px; border-left: 4px solid #3498db; }}
                .metric {{ margin: 10px 0; }}
                .recommendation {{ background: #e8f5e8; padding: 10px; margin: 5px 0; }}
                .warning {{ background: #fff3cd; padding: 10px; margin: 5px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>CLEAR Versioning System Metrics Report</h1>
                <p>Generated: {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                <p>Period: {self.analysis_period["start"]} to {self.analysis_period["end"]}</p>
            </div>
        """

        # Add analysis results
        for category, data in self.analysis_results.items():
            html_content += f"""
            <div class="section">
                <h2>{category.title().replace("_", " ")}</h2>
                <pre>{json.dumps(data, indent=2, default=str)}</pre>
            </div>
            """

        # Add recommendations
        if self.recommendations:
            html_content += '<div class="section"><h2>Recommendations</h2>'
            for rec in self.recommendations:
                html_content += f'<div class="recommendation">{rec}</div>'
            html_content += "</div>"

        # Add warnings
        if self.warnings:
            html_content += '<div class="section"><h2>Warnings</h2>'
            for warning in self.warnings:
                html_content += f'<div class="warning">{warning}</div>'
            html_content += "</div>"

        html_content += """
        </body>
        </html>
        """

        with open(export_path, "w") as f:
            f.write(html_content)
            
        return export_path
