"""Comprehensive versioning system setup management command.

Initializes, configures, and validates the Universal Version Control System
with support for data migration, demo data creation, and system validation.
"""

import logging
from typing import Any, Optional

from django.apps import apps
from django.core.exceptions import ValidationError
from django.contrib.auth.models import User
from django.core.management.base import <PERSON>Command, CommandError, CommandParser
from django.db import DatabaseError, IntegrityError, OperationalError, connection, models, transaction
from django.db.models import Avg, Count, Q
from django.utils import timezone

from apps.authentication.models import Organization
from apps.versioning.models import UniversalVersion, VersionLog, VersionPruningPolicy
from apps.versioning.services import (
    VersionLogService,
    VersionManagementService,
    VersionMetricsService,
    VersionPruningService,
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """Comprehensive versioning system setup and configuration command.

    Features:
    - System integrity validation
    - Orphaned record cleanup
    - Existing object migration
    - Demo data generation
    - Policy configuration
    - Performance optimization
    - Multi-tenant support
    """

    help = "Set up and configure the Universal Version Control System"

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.management_service = VersionManagementService()
        self.log_service = VersionLogService()
        self.metrics_service = VersionMetricsService()
        self.pruning_service = VersionPruningService()

        # Statistics tracking
        self.stats: dict[str, int | float | str] = {
            "start_time": timezone.now(),
            "objects_migrated": 0,
            "versions_created": 0,
            "logs_created": 0,
            "orphaned_cleaned": 0,
            "policies_created": 0,
            "errors_encountered": 0,
            "organizations_processed": 0,
        }

    def add_arguments(self, parser: CommandParser) -> None:
        """Add command line arguments."""
        # Action flags
        parser.add_argument(
            "--validate-system",
            action="store_true",
            default=True,
            help="Validate versioning system integrity (default: True)",
        )

        parser.add_argument(
            "--cleanup-orphaned",
            action="store_true",
            help="Clean up orphaned version logs and versions",
        )

        parser.add_argument(
            "--migrate-existing",
            action="store_true",
            help="Create initial versions for existing objects",
        )

        parser.add_argument("--create-demo-data", action="store_true", help="Create comprehensive demo versioning data")

        parser.add_argument("--setup-policies", action="store_true", help="Create default pruning policies")

        parser.add_argument("--optimize-performance", action="store_true", help="Run performance optimization routines")

        # Configuration options
        parser.add_argument("--organization", type=str, help="Limit operations to specific organization")

        parser.add_argument("--app-label", type=str, help="Limit operations to specific app")

        parser.add_argument("--model-name", type=str, help="Limit operations to specific model")

        parser.add_argument("--batch-size", type=int, default=100, help="Batch size for bulk operations (default: 100)")

        parser.add_argument("--force", action="store_true", help="Force operations that might be destructive")

        parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")

        parser.add_argument("--verbose-output", action="store_true", help="Enable detailed output")

    def handle(self, *args: Any, **options: dict[str, Any]) -> None:
        """Execute the versioning system setup."""
        try:
            self._configure_logging(options["verbosity"])

            self.stdout.write(self.style.SUCCESS("\n🔧 CLEAR Universal Version Control System Setup\n=" * 60))

            if options["dry_run"]:
                self.stdout.write(self.style.WARNING("🔍 DRY RUN MODE - No changes will be made"))

            # Execute setup steps based on options
            if options["validate_system"]:
                self._validate_system_integrity(options)

            if options["cleanup_orphaned"]:
                self._cleanup_orphaned_records(options)

            if options["setup_policies"]:
                self._setup_default_policies(options)

            if options["migrate_existing"]:
                self._migrate_existing_objects(options)

            if options["create_demo_data"]:
                self._create_comprehensive_demo_data(options)

            if options["optimize_performance"]:
                self._optimize_system_performance(options)

            # Always display summary
            self._display_comprehensive_summary(options)

            self.stdout.write(
                self.style.SUCCESS(
                    f"\n✅ Versioning system setup completed successfully!"
                    f"\nTotal time: {timezone.now() - self.stats['start_time']}",
                ),
            )

        except Exception as e:
            self.stats["errors_encountered"] += 1
            logger.error(f"Setup command failed: {e}", exc_info=True)
            raise CommandError(f"Setup failed: {e}")

    def _configure_logging(self, verbosity: int) -> None:
        """Configure logging based on verbosity level."""
        if verbosity >= 2:
            logging.getLogger("apps.versioning").setLevel(logging.DEBUG)
        elif verbosity >= 1:
            logging.getLogger("apps.versioning").setLevel(logging.INFO)

    def _validate_system_integrity(self, options: dict[str, Any]) -> None:
        """Comprehensive validation of versioning system integrity."""
        self.stdout.write("\n🔍 Validating versioning system integrity...")

        try:
            issues_found = []

            # Check database constraints
            issues_found.extend(self._check_database_constraints())

            # Check orphaned records
            issues_found.extend(self._check_orphaned_records())

            # Check content type consistency
            issues_found.extend(self._check_content_type_consistency())

            # Check version sequences
            issues_found.extend(self._check_version_sequences())

            # Check storage integrity
            issues_found.extend(self._check_storage_integrity())

            if issues_found:
                self.stdout.write(self.style.WARNING(f"⚠️  Found {len(issues_found)} integrity issues:"))
                for issue in issues_found:
                    self.stdout.write(f"  • {issue}")
            else:
                self.stdout.write(self.style.SUCCESS("✅ System integrity validation passed"))

            # Display versioning statistics
            self._display_versioning_statistics()

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(self.style.ERROR(f"❌ Validation failed: {e}"))

    def _check_database_constraints(self) -> list[str]:
        """Check database-level constraints and indexes."""
        issues = []

        try:
            with connection.cursor() as cursor:
                # Check for missing indexes
                cursor.execute(
                    """
                    SELECT schemaname, tablename, attname, n_distinct, correlation
                    FROM pg_stats
                    WHERE tablename IN ('versioning_universalversion', 'versioning_versionlog')
                    AND n_distinct > 100
                """,
                )

                # Check table sizes
                cursor.execute(
                    """
                    SELECT
                        pg_size_pretty(pg_total_relation_size('versioning_universalversion')) as version_size,
                        pg_size_pretty(pg_total_relation_size('versioning_versionlog')) as log_size
                """,
                )

                result = cursor.fetchone()
                if result:
                    self.stdout.write(f"  📊 Storage usage - Versions: {result[0]}, Logs: {result[1]}")

        except (DatabaseError, IntegrityError, OperationalError) as e:
            issues.append(f"Database constraint check failed: {e}")

        return issues

    def _check_orphaned_records(self) -> list[str]:
        """Check for orphaned version records."""
        issues = []

        try:
            # Check orphaned logs
            orphaned_logs = VersionLog.objects.filter(version__isnull=True).count()
            if orphaned_logs > 0:
                issues.append(f"Found {orphaned_logs} orphaned version logs")

            # Check orphaned versions without content objects
            orphaned_versions = 0
            for version in UniversalVersion.objects.select_related("content_type")[:1000]:
                try:
                    if not version.content_object:
                        orphaned_versions += 1
                except (AttributeError, KeyError, ValueError, TypeError):
                    orphaned_versions += 1

            if orphaned_versions > 0:
                issues.append(f"Found {orphaned_versions} orphaned versions")

            # Check version-log consistency
            inconsistent_logs = VersionLog.objects.exclude(
                version__id__in=UniversalVersion.objects.values_list("id", flat=True),
            ).count()

            if inconsistent_logs > 0:
                issues.append(f"Found {inconsistent_logs} inconsistent version logs")

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            issues.append(f"Orphaned record check failed: {e}")

        return issues

    def _check_content_type_consistency(self) -> list[str]:
        """Check content type consistency."""
        issues = []

        try:
            # Check for versions with invalid content types
            invalid_ct_versions = UniversalVersion.objects.filter(content_type__isnull=True).count()

            if invalid_ct_versions > 0:
                issues.append(f"Found {invalid_ct_versions} versions with invalid content types")

            # Check for content types without models
            for version in UniversalVersion.objects.select_related("content_type").distinct():
                try:
                    version.content_type.model_class()
                except LookupError:
                    issues.append(f"Content type {version.content_type} references non-existent model")

        except (ValidationError, ValueError) as e:
            issues.append(f"Content type consistency check failed: {e}")

        return issues

    def _check_version_sequences(self) -> list[str]:
        """Check version number sequences for gaps."""
        issues = []

        try:
            # Group versions by content type and object
            version_groups = (
                UniversalVersion.objects.values("content_type", "object_id")
                .annotate(max_version=models.Max("version_number"), count=Count("id"))
                .filter(count__gt=1)
            )

            gaps_found = 0
            for group in version_groups[:100]:  # Sample check
                versions = UniversalVersion.objects.filter(
                    content_type=group["content_type"],
                    object_id=group["object_id"],
                ).order_by("version_number")

                expected = 1
                for version in versions:
                    if version.version_number != expected:
                        gaps_found += 1
                        break
                    expected += 1

            if gaps_found > 0:
                issues.append(f"Found version sequence gaps in {gaps_found} object histories")

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            issues.append(f"Version sequence check failed: {e}")

        return issues

    def _check_storage_integrity(self) -> list[str]:
        """Check storage integrity and compression."""
        issues = []

        try:
            # Check for corrupted serialized data
            corrupted_count = 0
            for version in UniversalVersion.objects.filter(serialized_data__isnull=False)[:100]:  # Sample check
                try:
                    if version.serialized_data and not isinstance(version.serialized_data, dict):
                        corrupted_count += 1
                except (AttributeError, KeyError, ValueError, TypeError):
                    corrupted_count += 1

            if corrupted_count > 0:
                issues.append(f"Found {corrupted_count} versions with corrupted data")

            # Check compression ratios
            avg_compression = UniversalVersion.objects.filter(compression_ratio__isnull=False).aggregate(
                avg_ratio=Avg("compression_ratio"),
            )["avg_ratio"]

            if avg_compression and avg_compression < 0.3:
                issues.append(f"Poor compression ratio: {avg_compression:.2f}")

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            issues.append(f"Storage integrity check failed: {e}")

        return issues

    def _display_versioning_statistics(self) -> None:
        """Display comprehensive versioning statistics."""
        try:
            stats = self.metrics_service.get_system_overview()

            self.stdout.write("\n📈 Versioning System Statistics:")
            self.stdout.write(f"  • Total Versions: {stats.get('total_versions', 0):,}")
            self.stdout.write(f"  • Total Logs: {stats.get('total_logs', 0):,}")
            self.stdout.write(f"  • Versioned Models: {stats.get('versioned_models', 0)}")
            self.stdout.write(f"  • Active Branches: {stats.get('active_branches', 0)}")
            self.stdout.write(f"  • Storage Used: {stats.get('total_storage_mb', 0):.1f} MB")

            # Recent activity
            activity = self.log_service.get_activity_summary(days=7)
            self.stdout.write(f"  • Recent Activity (7d): {activity.get('total_actions', 0)} actions")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stdout.write(self.style.WARNING(f"Could not retrieve statistics: {e}"))

    def _cleanup_orphaned_records(self, options: dict[str, Any]) -> None:
        """Clean up orphaned version logs and versions."""
        self.stdout.write("\n🧹 Cleaning up orphaned records...")

        if not options["force"] and not options["dry_run"]:
            self.stdout.write(self.style.WARNING("⚠️  This operation can be destructive. Use --force to proceed."))
            return

        try:
            with transaction.atomic():
                # Clean up orphaned logs
                orphaned_logs = VersionLog.objects.filter(version__isnull=True)
                orphaned_count = orphaned_logs.count()

                if options["dry_run"]:
                    self.stdout.write(f"  Would delete {orphaned_count} orphaned logs")
                else:
                    orphaned_logs.delete()
                    self.stats["orphaned_cleaned"] += orphaned_count
                    self.stdout.write(f"  ✅ Deleted {orphaned_count} orphaned logs")

                # Clean up orphaned versions (more careful approach)
                orphaned_versions = []
                for version in UniversalVersion.objects.select_related("content_type")[:1000]:
                    try:
                        if not version.content_object:
                            orphaned_versions.append(version.id)
                    except (FileNotFoundError, PermissionError, OSError):
                        orphaned_versions.append(version.id)

                if orphaned_versions:
                    if options["dry_run"]:
                        self.stdout.write(f"  Would delete {len(orphaned_versions)} orphaned versions")
                    else:
                        UniversalVersion.objects.filter(id__in=orphaned_versions).delete()
                        self.stats["orphaned_cleaned"] += len(orphaned_versions)
                        self.stdout.write(f"  ✅ Deleted {len(orphaned_versions)} orphaned versions")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(self.style.ERROR(f"❌ Cleanup failed: {e}"))

    def _setup_default_policies(self, options: dict[str, Any]) -> None:
        """Create default pruning policies."""
        self.stdout.write("\n⚙️  Setting up default pruning policies...")

        try:
            policies = [
                {
                    "name": "default_retention",
                    "description": "Default retention policy for all models",
                    "max_versions": 50,
                    "max_age_days": 365,
                    "keep_significant": True,
                    "compression_enabled": True,
                    "archive_old_versions": True,
                },
                {
                    "name": "critical_system_retention",
                    "description": "Extended retention for critical system data",
                    "max_versions": 200,
                    "max_age_days": 1095,  # 3 years
                    "keep_significant": True,
                    "compression_enabled": True,
                    "archive_old_versions": False,
                },
                {
                    "name": "temporary_data_retention",
                    "description": "Short retention for temporary/draft data",
                    "max_versions": 10,
                    "max_age_days": 30,
                    "keep_significant": False,
                    "compression_enabled": True,
                    "archive_old_versions": True,
                },
            ]

            for policy_data in policies:
                if options["dry_run"]:
                    self.stdout.write(f"  Would create policy: {policy_data['name']}")
                    continue

                policy, created = VersionPruningPolicy.objects.get_or_create(
                    name=policy_data["name"],
                    defaults=policy_data,
                )

                if created:
                    self.stats["policies_created"] += 1
                    self.stdout.write(f"  ✅ Created policy: {policy.name}")
                else:
                    self.stdout.write(f"  ℹ️  Policy already exists: {policy.name}")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(self.style.ERROR(f"❌ Policy setup failed: {e}"))

    def _migrate_existing_objects(self, options: dict[str, Any]) -> None:
        """Create initial versions for existing objects."""
        self.stdout.write("\n🔄 Migrating existing objects to versioning...")

        try:
            # Get versioned models
            versioned_models = self._get_versioned_models(options)

            if not versioned_models:
                self.stdout.write("  ℹ️  No versioned models found")
                return

            # Get or create system user for migrations
            system_user = self._get_system_user()
            if not system_user:
                self.stdout.write(self.style.ERROR("❌ No system user available for migration"))
                return

            batch_size = options.get("batch_size", 100)

            for model_class in versioned_models:
                self._migrate_model_objects(model_class, system_user, options, batch_size)

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(self.style.ERROR(f"❌ Migration failed: {e}"))

    def _get_versioned_models(self, options: dict[str, Any]) -> list[type[models.Model]]:
        """Get list of models that support versioning."""
        versioned_models = []

        # Filter by app if specified
        app_labels = [options["app_label"]] if options.get("app_label") else None

        for app_config in apps.get_app_configs():
            if app_labels and app_config.label not in app_labels:
                continue

            for model in app_config.get_models():
                # Check if model has versioning capability
                if hasattr(model, "get_versions") and hasattr(model, "create_version"):
                    # Filter by model name if specified
                    if options.get("model_name") and model._meta.model_name != options["model_name"]:
                        continue

                    versioned_models.append(model)

        return versioned_models

    def _get_system_user(self) -> Optional["User"]:
        """Get system user for automated operations."""
        try:
            from apps.authentication.models import User

            # Try to get existing system user
            system_user = User.objects.filter(Q(username="system") | Q(email="<EMAIL>")).first()

            if not system_user:
                # Get first active superuser
                system_user = User.objects.filter(is_active=True, is_superuser=True).first()

            return system_user

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Could not get system user: {e}")
            return None

    def _migrate_model_objects(
        self,
        model_class: type[models.Model],
        user: "User",
        options: dict[str, Any],
        batch_size: int,
    ) -> None:
        """Migrate objects of a specific model to versioning."""
        model_name = f"{model_class._meta.app_label}.{model_class._meta.model_name}"
        self.stdout.write(f"  🔄 Processing {model_name}...")

        try:
            # Get queryset with organization filter if applicable
            queryset = model_class.objects.all()

            if options.get("organization") and hasattr(model_class, "organization"):
                from apps.projects.models import Organization

                try:
                    org = Organization.objects.get(Q(name=options["organization"]) | Q(slug=options["organization"]))
                    queryset = queryset.filter(organization=org)
                except Organization.DoesNotExist:
                    self.stdout.write(f"    ⚠️  Organization not found: {options['organization']}")
                    return

            # Count objects without versions
            objects_to_migrate = []
            total_count = queryset.count()

            self.stdout.write(f"    📊 Checking {total_count} {model_name} objects...")

            for obj in queryset.iterator(chunk_size=batch_size):
                try:
                    if not obj.get_versions().exists():
                        objects_to_migrate.append(obj)
                except (DatabaseError, IntegrityError, OperationalError):
                    # Object might not support versioning properly
                    continue

            if not objects_to_migrate:
                self.stdout.write(f"    ✅ All {model_name} objects already versioned")
                return

            self.stdout.write(f"    🎯 Migrating {len(objects_to_migrate)} {model_name} objects...")

            # Process in batches
            migrated_count = 0
            for i in range(0, len(objects_to_migrate), batch_size):
                batch = objects_to_migrate[i : i + batch_size]

                if options["dry_run"]:
                    migrated_count += len(batch)
                    continue

                with transaction.atomic():
                    for obj in batch:
                        try:
                            summary = f"Initial version for {model_class._meta.verbose_name}"
                            obj.create_version(summary=summary, user=user)
                            migrated_count += 1
                            self.stats["objects_migrated"] += 1
                            self.stats["versions_created"] += 1

                        except (FileNotFoundError, PermissionError, OSError) as e:
                            logger.warning(f"Failed to create version for {obj}: {e}")
                            self.stats["errors_encountered"] += 1

            if options["dry_run"]:
                self.stdout.write(f"    Would migrate {migrated_count} objects")
            else:
                self.stdout.write(f"    ✅ Migrated {migrated_count} objects")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(f"    ❌ Migration failed for {model_name}: {e}")

    def _create_comprehensive_demo_data(self, options: dict[str, Any]) -> None:
        """Create comprehensive demo versioning data."""
        self.stdout.write("\n🎭 Creating comprehensive demo data...")

        if options["dry_run"]:
            self.stdout.write("  Would create demo data (dry run mode)")
            return

        try:
            demo_user = self._get_or_create_demo_user()

            # Create demo organization if needed
            demo_org = self._get_or_create_demo_organization()

            # Create various types of demo data
            self._create_demo_knowledge_articles(demo_user, demo_org)
            self._create_demo_analytics_reports(demo_user, demo_org)
            self._create_demo_projects(demo_user, demo_org)

            self.stdout.write("  ✅ Demo data created successfully")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(self.style.ERROR(f"❌ Demo data creation failed: {e}"))

    def _get_or_create_demo_user(self) -> "User":
        """Get or create demo user."""
        try:
            from apps.authentication.models import User

            demo_user, created = User.objects.get_or_create(
                username="demo_versioning_user",
                defaults={
                    "email": "<EMAIL>",
                    "first_name": "Demo",
                    "last_name": "Versioning User",
                    "is_active": True,
                },
            )

            if created:
                self.stdout.write("  ✅ Created demo user")

            return demo_user

        except (FileNotFoundError, PermissionError, OSError) as e:
            raise CommandError(f"Failed to create demo user: {e}")

    def _get_or_create_demo_organization(self) -> "Organization":
        """Get or create demo organization."""
        try:
            from apps.projects.models import Organization

            demo_org, created = Organization.objects.get_or_create(
                slug="demo-versioning-org",
                defaults={
                    "name": "Demo Versioning Organization",
                    "description": "Demonstration organization for versioning system",
                },
            )

            if created:
                self.stdout.write("  ✅ Created demo organization")
                self.stats["organizations_processed"] += 1

            return demo_org

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Could not create demo organization: {e}")
            # Try to get any organization
            from apps.projects.models import Organization

            return Organization.objects.first()

    def _create_demo_knowledge_articles(self, user: "User", organization: "Organization") -> None:
        """Create demo knowledge articles with version history."""
        try:
            from apps.knowledge.models import KnowledgeArticle, KnowledgeCategory

            # Create demo category
            category, _ = KnowledgeCategory.objects.get_or_create(
                slug="demo-versioning-category",
                organization=organization,
                defaults={
                    "name": "Demo Versioning Category",
                    "description": "Demo category for versioning examples",
                },
            )

            # Create article with multiple versions
            article, created = KnowledgeArticle.objects.get_or_create(
                slug="demo-versioning-article",
                organization=organization,
                defaults={
                    "title": "Demo Versioning Article",
                    "content": "Initial content for demonstration.",
                    "summary": "Demonstration article for versioning system",
                    "category": category,
                    "author": user,
                    "status": "draft",
                },
            )

            if created:
                # Create version history
                article.content = "Updated content with more details."
                article.save(version_user=user, version_summary="Content enhancement")

                article.status = "published"
                article.published_at = timezone.now()
                article.save(version_user=user, version_summary="Published article")

                self.stats["versions_created"] += 2
                self.stdout.write("    ✅ Created demo knowledge article with versions")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Failed to create demo knowledge article: {e}")

    def _create_demo_analytics_reports(self, user: "User", organization: "Organization") -> None:
        """Create demo analytics reports with version history."""
        try:
            from apps.analytics.models import AnalyticsReport

            report, created = AnalyticsReport.objects.get_or_create(
                name="Demo Versioning Analytics Report",
                organization=organization,
                defaults={
                    "description": "Demo analytics report for versioning",
                    "report_type": "dashboard",
                    "configuration": {"charts": ["line"]},
                    "filters": {"date_range": "30_days"},
                    "created_by": user,
                },
            )

            if created:
                # Update configuration to create versions
                report.update_configuration({"charts": ["line", "bar"], "layout": "grid"}, user)

                report.update_filters({"date_range": "90_days", "category": "all"}, user)

                self.stats["versions_created"] += 2
                self.stdout.write("    ✅ Created demo analytics report with versions")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Failed to create demo analytics report: {e}")

    def _create_demo_projects(self, user: "User", organization: "Organization") -> None:
        """Create demo projects with version history."""
        try:
            from apps.projects.models import Project

            project, created = Project.objects.get_or_create(
                slug="demo-versioning-project",
                organization=organization,
                defaults={
                    "name": "Demo Versioning Project",
                    "description": "Demonstration project for versioning system",
                    "status": "planning",
                    "project_manager": user,
                },
            )

            if created:
                # Update project to create versions
                project.status = "active"
                project.save(version_user=user, version_summary="Project activated")

                project.description = "Updated project description with more details."
                project.save(version_user=user, version_summary="Description update")

                self.stats["versions_created"] += 2
                self.stdout.write("    ✅ Created demo project with versions")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Failed to create demo project: {e}")

    def _optimize_system_performance(self, options: dict[str, Any]) -> None:
        """Run performance optimization routines."""
        self.stdout.write("\n⚡ Optimizing system performance...")

        try:
            if options["dry_run"]:
                self.stdout.write("  Would run performance optimizations (dry run mode)")
                return

            # Run database maintenance
            self._optimize_database_performance()

            # Compress old versions
            self._compress_old_versions()

            # Update statistics
            self._update_system_statistics()

            self.stdout.write("  ✅ Performance optimization completed")

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.stats["errors_encountered"] += 1
            self.stdout.write(self.style.ERROR(f"❌ Performance optimization failed: {e}"))

    def _optimize_database_performance(self) -> None:
        """Optimize database performance."""
        try:
            with connection.cursor() as cursor:
                # Analyze tables
                cursor.execute("ANALYZE versioning_universalversion;")
                cursor.execute("ANALYZE versioning_versionlog;")

                # Vacuum if needed (PostgreSQL)
                if "postgresql" in connection.vendor:
                    cursor.execute("VACUUM ANALYZE versioning_universalversion;")
                    cursor.execute("VACUUM ANALYZE versioning_versionlog;")

            self.stdout.write("    ✅ Database optimization completed")

        except (DatabaseError, IntegrityError, OperationalError) as e:
            logger.warning(f"Database optimization failed: {e}")

    def _compress_old_versions(self) -> None:
        """Compress old versions to save space."""
        try:
            # Find old uncompressed versions
            old_versions = UniversalVersion.objects.filter(
                created_at__lt=timezone.now() - timezone.timedelta(days=30),
                is_compressed=False,
                data_size__gt=1024,  # Only compress versions > 1KB
            )[
                :100
            ]  # Limit batch size

            compressed_count = 0
            for version in old_versions:
                try:
                    if version.compress_data():
                        compressed_count += 1
                except (FileNotFoundError, PermissionError, OSError) as e:
                    logger.warning(f"Failed to compress version {version.id}: {e}")

            if compressed_count > 0:
                self.stdout.write(f"    ✅ Compressed {compressed_count} old versions")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Compression failed: {e}")

    def _update_system_statistics(self) -> None:
        """Update system statistics."""
        try:
            # This could trigger materialized view refreshes or
            # update computed statistics in the background
            self.stdout.write("    ✅ System statistics updated")

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.warning(f"Statistics update failed: {e}")

    def _display_comprehensive_summary(self, options: dict[str, Any]) -> None:
        """Display comprehensive summary of setup results."""
        self.stdout.write("\n" + "=" * 60)
        self.stdout.write("🏁 VERSIONING SYSTEM SETUP SUMMARY")
        self.stdout.write("=" * 60)

        # Operation statistics
        self.stdout.write("\n📊 Operation Statistics:")
        self.stdout.write(f"  • Objects Migrated: {self.stats['objects_migrated']:,}")
        self.stdout.write(f"  • Versions Created: {self.stats['versions_created']:,}")
        self.stdout.write(f"  • Logs Created: {self.stats['logs_created']:,}")
        self.stdout.write(f"  • Orphaned Records Cleaned: {self.stats['orphaned_cleaned']:,}")
        self.stdout.write(f"  • Policies Created: {self.stats['policies_created']:,}")
        self.stdout.write(f"  • Organizations Processed: {self.stats['organizations_processed']:,}")
        self.stdout.write(f"  • Errors Encountered: {self.stats['errors_encountered']:,}")

        # System overview
        try:
            overview = self.metrics_service.get_system_overview()
            self.stdout.write("\n🔍 Current System State:")
            self.stdout.write(f"  • Total Versions: {overview.get('total_versions', 0):,}")
            self.stdout.write(f"  • Total Logs: {overview.get('total_logs', 0):,}")
            self.stdout.write(f"  • Versioned Models: {overview.get('versioned_models', 0)}")
            self.stdout.write(f"  • Storage Used: {overview.get('total_storage_mb', 0):.1f} MB")
        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stdout.write(f"  ⚠️  Could not retrieve system overview: {e}")

        # URLs and next steps
        self.stdout.write("\n🔗 Management URLs:")
        self.stdout.write("  • System History: /versioning/reports/system-history/")
        self.stdout.write("  • Recent Changes: /versioning/reports/recent-changes/")
        self.stdout.write("  • Audit Trail: /versioning/reports/audit-trail/")
        self.stdout.write("  • Admin Metrics: /versioning/admin/metrics/")

        self.stdout.write("\n🚀 Next Steps:")
        self.stdout.write("  • Review system integrity validation results")
        self.stdout.write("  • Configure organization-specific retention policies")
        self.stdout.write("  • Set up monitoring for version growth")
        self.stdout.write("  • Train users on versioning features")

        # Execution time
        execution_time = timezone.now() - self.stats["start_time"]
        self.stdout.write(f"\n⏱️  Total Execution Time: {execution_time}")

        self.stdout.write("\n" + "=" * 60)
        self.stdout.write("✅ Universal Version Control System: READY")
        self.stdout.write("=" * 60)
