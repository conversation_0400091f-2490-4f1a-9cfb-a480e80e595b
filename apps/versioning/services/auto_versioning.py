"""Auto-Versioning Service

Provides automated version creation based on model state changes with:
- Configurable triggers for different model types
- Workflow-aware version creation
- Automatic cleanup and retention policies
- Signal-based integration with Django models

The service supports multiple model types with specific workflow patterns:
- Documents: Track publish status changes and file modifications
- Budgets: Monitor financial amounts and approval status
- Invoices: Track payment status and amount changes
- GIS Layers: Monitor construction progress and verification
- Tasks: Track status changes and assignments
"""

from __future__ import annotations

import logging
from typing import TYPE_CHECKING, Any

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models.signals import post_save, pre_save
from django.utils import timezone
from django.utils.functional import cached_property

if TYPE_CHECKING:
    from django.contrib.auth.models import AbstractUser

    from apps.versioning.models import UniversalVersion

User = get_user_model()
logger = logging.getLogger(__name__)


class AutoVersioningService:
    """Service for handling automated version creation based on model state changes.

    This service monitors specific fields for changes and automatically creates
    versions when significant changes occur or workflow transitions happen.
    """

    # Versioning triggers configuration for each model type
    VERSIONING_TRIGGERS: dict[str, dict[str, Any]] = {
        "document": {
            "status_fields": ["publish_status"],
            "significant_fields": [
                "file_path",
                "file_size",
                "name",
                "description",
                "tags",
            ],
            "workflow_transitions": {
                "draft": ["pending_review", "published"],
                "pending_review": ["approved", "rejected", "draft"],
                "approved": ["published", "draft"],
                "published": ["archived", "draft"],
                "archived": ["draft"],
            },
            "milestone_statuses": ["published", "approved", "archived"],
        },
        "budget": {
            "status_fields": ["status"],
            "significant_fields": [
                "total_budget",
                "allocated_amount",
                "spent_amount",
                "category_allocations",
                "fiscal_year",
            ],
            "workflow_transitions": {
                "draft": ["proposal"],
                "proposal": ["under_review", "draft"],
                "under_review": ["approved", "rejected", "proposal"],
                "approved": ["active", "proposal"],
                "active": ["closed"],
                "rejected": ["draft", "proposal"],
                "closed": [],  # Terminal state
            },
            "milestone_statuses": ["approved", "active", "closed"],
        },
        "invoice": {
            "status_fields": ["status"],
            "significant_fields": [
                "amount",
                "tax_amount",
                "total_amount",
                "invoice_date",
                "due_date",
                "payment_terms",
            ],
            "workflow_transitions": {
                "draft": ["proposal"],
                "proposal": ["under_review", "draft"],
                "under_review": ["approved", "rejected", "proposal"],
                "approved": ["sent", "proposal"],
                "sent": ["paid", "overdue", "cancelled"],
                "paid": [],  # Terminal state
                "overdue": ["paid", "cancelled"],
                "cancelled": ["draft"],
                "rejected": ["draft", "proposal"],
            },
            "milestone_statuses": ["approved", "sent", "paid"],
        },
        "gislayer": {
            "status_fields": ["layout_status"],
            "significant_fields": [
                "data_source",
                "style_config",
                "visibility",
                "opacity",
                "layer_type",
                "geometry",
            ],
            "workflow_transitions": {
                "planned": ["proposed"],
                "proposed": ["under_review", "planned"],
                "under_review": ["approved", "rejected", "proposed"],
                "approved": ["construction", "proposed"],
                "construction": ["actual", "approved"],
                "actual": ["verified", "construction"],
                "verified": [],  # Terminal state
                "rejected": ["planned", "proposed"],
            },
            "milestone_statuses": ["approved", "actual", "verified"],
        },
        "task": {
            "status_fields": ["status"],
            "significant_fields": [
                "title",
                "description",
                "priority",
                "due_date",
                "assigned_to",
                "estimated_hours",
            ],
            "workflow_transitions": {
                "pending": ["in_progress", "completed", "cancelled"],
                "in_progress": ["completed", "pending", "cancelled", "blocked"],
                "completed": ["in_progress"],  # Can reopen
                "cancelled": ["pending", "in_progress"],
                "blocked": ["in_progress", "pending"],
            },
            "milestone_statuses": ["completed"],
        },
    }

    def __init__(self, model_instance: models.Model) -> None:
        """Initialize auto-versioning service for a model instance.

        Args:
        ----
            model_instance: The Django model instance to track

        """
        self.model_instance = model_instance
        self.model_type = model_instance._meta.model_name
        self.app_label = model_instance._meta.app_label

    @cached_property
    def config(self) -> dict[str, Any]:
        """Get configuration for this model type."""
        return self.VERSIONING_TRIGGERS.get(self.model_type, {})

    def should_auto_version(self, changed_fields: list[str]) -> bool:
        """Determine if auto-versioning should occur based on changed fields.

        Args:
        ----
            changed_fields: List of field names that changed

        Returns:
        -------
            True if auto-versioning should occur, False otherwise

        """
        if not self.config:
            logger.debug(f"No auto-versioning config for model type: {self.model_type}")
            return False

        if not hasattr(self.model_instance, "create_version"):
            logger.debug(f"Model {self.model_type} does not support versioning")
            return False

        # Check if versioning is disabled for this instance
        if hasattr(self.model_instance, "auto_version") and not self.model_instance.auto_version:
            logger.debug(f"Auto-versioning disabled for {self.model_type} instance")
            return False

        # Check if any status fields changed
        status_fields = self.config.get("status_fields", [])
        if any(field in changed_fields for field in status_fields):
            logger.info(f"Status field change detected for {self.model_type}: {changed_fields}")
            return True

        # Check if any significant fields changed
        significant_fields = self.config.get("significant_fields", [])
        significant_changes = [field for field in changed_fields if field in significant_fields]
        if significant_changes:
            logger.info(f"Significant field changes for {self.model_type}: {significant_changes}")
            return True

        logger.debug(f"No auto-versioning triggers met for {self.model_type}")
        return False

    def create_auto_version(
        self,
        changed_fields: list[str],
        user: AbstractUser | None = None,
    ) -> UniversalVersion | None:
        """Create an automated version based on model changes.

        Args:
        ----
            changed_fields: List of field names that changed
            user: User who made the change (if available)

        Returns:
        -------
            Created version instance or None if creation failed

        """
        if not self.config:
            logger.warning(f"Cannot create auto-version for unconfigured model: {self.model_type}")
            return None

        try:
            with transaction.atomic():
                # Generate version metadata
                summary = self._generate_version_summary(changed_fields)
                branch_name = self._determine_branch_name(changed_fields)
                metadata = self._generate_version_metadata(changed_fields, user)

                # Create the version
                version = self.model_instance.create_version(
                    summary=summary,
                    user=user,
                    branch_name=branch_name,
                    metadata=metadata,
                )

                logger.info(
                    f"Auto-version created for {self.model_type} "
                    f"(ID: {self.model_instance.pk}): v{version.version_number}",
                )
                return version

        except Exception as e:
            logger.error(
                f"Auto-versioning failed for {self.model_type} (ID: {self.model_instance.pk}): {e}",
                exc_info=True,
            )
            return None

    def _generate_version_summary(self, changed_fields: list[str]) -> str:
        """Generate a descriptive summary for the auto-created version."""
        status_fields = self.config.get("status_fields", [])
        significant_fields = self.config.get("significant_fields", [])

        # Check for status changes first
        status_changes = []
        for field in status_fields:
            if field in changed_fields:
                old_value = getattr(self.model_instance, f"_original_{field}", "unknown")
                new_value = getattr(self.model_instance, field, "unknown")
                if old_value != new_value:
                    status_changes.append(f"{field}: {old_value} → {new_value}")

        if status_changes:
            return f"Status change: {', '.join(status_changes)}"

        # Check for significant field changes
        significant_changes = [field for field in changed_fields if field in significant_fields]
        if significant_changes:
            if len(significant_changes) == 1:
                return f"Updated {significant_changes[0]}"
            if len(significant_changes) <= 3:
                return f"Updated {', '.join(significant_changes)}"
            return f"Updated {len(significant_changes)} fields: {', '.join(significant_changes[:3])}..."

        # Default summary
        return f"Auto-version: {self.model_type} updated ({len(changed_fields)} fields)"

    def _determine_branch_name(self, changed_fields: list[str]) -> str:
        """Determine the appropriate branch name for the version."""
        status_fields = self.config.get("status_fields", [])

        # Check if any status field changed
        for field in status_fields:
            if field in changed_fields:
                current_status = getattr(self.model_instance, field, None)
                if current_status:
                    return f"auto-{current_status}"

        # Check if this is a milestone version
        current_status = self._get_current_status()
        milestone_statuses = self.config.get("milestone_statuses", [])
        if current_status in milestone_statuses:
            return f"milestone-{current_status}"

        # Default branch name
        return f"auto-{self.model_type}"

    def _generate_version_metadata(self, changed_fields: list[str], user: AbstractUser | None) -> dict[str, Any]:
        """Generate metadata for the auto-created version."""
        return {
            "auto_created": True,
            "trigger_fields": changed_fields,
            "workflow_state": self._get_current_status(),
            "created_by_service": "AutoVersioningService",
            "created_at": timezone.now().isoformat(),
            "user_id": user.pk if user else None,
            "user_email": user.email if user else None,
            "model_type": self.model_type,
            "app_label": self.app_label,
        }

    def _get_current_status(self) -> str:
        """Get the current workflow status of the model instance."""
        status_fields = self.config.get("status_fields", [])

        for field in status_fields:
            if hasattr(self.model_instance, field):
                return str(getattr(self.model_instance, field, "unknown"))

        return "unknown"

    def get_workflow_next_states(self) -> list[str]:
        """Get possible next workflow states for the current instance."""
        if not self.config:
            return []

        current_status = self._get_current_status()
        workflow_transitions = self.config.get("workflow_transitions", {})

        return workflow_transitions.get(current_status, [])

    def can_transition_to_status(self, target_status: str) -> bool:
        """Check if the instance can transition to a specific status."""
        next_states = self.get_workflow_next_states()
        return target_status in next_states

    def get_workflow_history(self) -> list[dict[str, Any]]:
        """Get workflow history for the model instance."""
        if not hasattr(self.model_instance, "get_versions"):
            return []

        versions = self.model_instance.get_versions().order_by("-created_at")
        history = []

        for version in versions:
            metadata = version.metadata or {}
            workflow_state = metadata.get("workflow_state", "unknown")

            history.append(
                {
                    "version_number": version.version_number,
                    "created_at": version.created_at,
                    "created_by": (version.created_by.username if version.created_by else None),
                    "summary": version.change_summary,
                    "branch_name": version.branch_name,
                    "workflow_state": workflow_state,
                    "is_auto_version": metadata.get("auto_created", False),
                    "is_milestone": version.branch_name.startswith("milestone-"),
                    "trigger_fields": metadata.get("trigger_fields", []),
                },
            )

        return history


class ModelStateTracker:
    """Track model state changes to enable auto-versioning.

    This class provides signal handlers that capture model state before
    and after save operations to detect field changes.
    """

    @classmethod
    def track_pre_save(cls, sender: type[models.Model], instance: models.Model, **kwargs: Any) -> None:
        """Track original values before save operation.

        Args:
        ----
            sender: Model class that sent the signal
            instance: Model instance being saved
            **kwargs: Additional signal arguments

        """
        # Only track existing instances (not new ones)
        if not hasattr(instance, "pk") or instance.pk is None:
            return

        try:
            # Get the original instance from database
            original = sender.objects.get(pk=instance.pk)

            # Store original values for comparison
            for field in instance._meta.get_fields():
                if hasattr(field, "name") and hasattr(original, field.name):
                    original_value = getattr(original, field.name)
                    setattr(instance, f"_original_{field.name}", original_value)

        except sender.DoesNotExist:
            # Instance was deleted between operations
            logger.debug(f"Original instance not found for {sender._meta.label}: {instance.pk}")
        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.warning(f"Failed to track pre-save state for {sender._meta.label}: {e}")

    @classmethod
    def track_post_save(
        cls,
        sender: type[models.Model],
        instance: models.Model,
        created: bool,
        **kwargs: Any,
    ) -> None:
        """Handle auto-versioning after save operation.

        Args:
        ----
            sender: Model class that sent the signal
            instance: Model instance that was saved
            created: True if this was a create operation
            **kwargs: Additional signal arguments

        """
        # Skip new instances
        if created:
            return

        # Skip if auto-versioning is disabled for this instance
        if hasattr(instance, "auto_version") and not instance.auto_version:
            return

        try:
            # Get changed fields by comparing original and current values
            changed_fields = cls._get_changed_fields(instance)

            if not changed_fields:
                logger.debug(f"No field changes detected for {sender._meta.label}: {instance.pk}")
                return

            # Create auto-versioning service
            service = AutoVersioningService(instance)

            # Check if auto-versioning should occur
            if service.should_auto_version(changed_fields):
                # Get user from request context if available
                user = getattr(instance, "_current_user", None)

                # Create auto-version
                service.create_auto_version(changed_fields, user)

        except Exception as e:
            logger.error(
                f"Auto-versioning post-save failed for {sender._meta.label}: {e}",
                exc_info=True,
            )

    @classmethod
    def _get_changed_fields(cls, instance: models.Model) -> list[str]:
        """Get list of fields that changed since pre_save."""
        changed_fields = []

        for field in instance._meta.get_fields():
            if not hasattr(field, "name"):
                continue

            field_name = field.name
            original_attr = f"_original_{field_name}"

            if hasattr(instance, original_attr):
                original_value = getattr(instance, original_attr)
                current_value = getattr(instance, field_name, None)

                # Compare values, handling special cases
                if cls._values_differ(original_value, current_value):
                    changed_fields.append(field_name)

                # Clean up the original value attribute
                delattr(instance, original_attr)

        return changed_fields

    @classmethod
    def _values_differ(cls, original: Any, current: Any) -> bool:
        """Check if two values are different, handling edge cases."""
        # Handle None values
        if original is None and current is None:
            return False
        if original is None or current is None:
            return True

        # Handle timezone-aware datetime objects
        if hasattr(original, "replace") and hasattr(current, "replace"):
            try:
                # Remove microseconds for comparison
                orig_normalized = original.replace(microsecond=0)
                curr_normalized = current.replace(microsecond=0)
                return orig_normalized != curr_normalized
            except (AttributeError, TypeError):
                pass

        # Standard comparison
        return original != current


class AutoVersioningConfig:
    """Configuration settings for auto-versioning behavior."""

    # Global enable/disable flag
    ENABLED: bool = True

    # Models that should use auto-versioning
    ENABLED_MODELS: list[str] = [
        "documents.Document",
        "financial.Budget",
        "financial.Invoice",
        "infrastructure.GISLayer",
        "projects.Task",
    ]

    # Retention policies for auto-created versions
    RETENTION_POLICIES: dict[str, Any] = {
        "auto_versions_max_age_days": 90,
        "auto_versions_max_count": 100,
        "keep_workflow_milestone_versions": True,
        "compress_old_auto_versions": True,
        "cleanup_interval_hours": 24,
    }

    @classmethod
    def is_enabled_for_model(cls, model_instance: models.Model) -> bool:
        """Check if auto-versioning is enabled for a specific model."""
        if not cls.ENABLED:
            return False

        model_path = f"{model_instance._meta.app_label}.{model_instance._meta.model_name}"
        return model_path in cls.ENABLED_MODELS

    @classmethod
    def get_retention_policy(cls, model_instance: models.Model) -> dict[str, Any]:
        """Get retention policy configuration for a model."""
        # Could be customized per model type in the future
        return cls.RETENTION_POLICIES.copy()

    @classmethod
    def register_model(cls, app_label: str, model_name: str) -> None:
        """Register a model for auto-versioning."""
        model_path = f"{app_label}.{model_name}"
        if model_path not in cls.ENABLED_MODELS:
            cls.ENABLED_MODELS.append(model_path)
            logger.info(f"Registered model for auto-versioning: {model_path}")

    @classmethod
    def unregister_model(cls, app_label: str, model_name: str) -> None:
        """Unregister a model from auto-versioning."""
        model_path = f"{app_label}.{model_name}"
        if model_path in cls.ENABLED_MODELS:
            cls.ENABLED_MODELS.remove(model_path)
            logger.info(f"Unregistered model from auto-versioning: {model_path}")


class VersionCleanupService:
    """Service for cleaning up old auto-created versions."""

    def __init__(self, model_instance: models.Model) -> None:
        """Initialize cleanup service for a model instance.

        Args:
        ----
            model_instance: Model instance to clean up versions for

        """
        self.model_instance = model_instance
        self.config = AutoVersioningConfig.get_retention_policy(model_instance)

    def cleanup_old_versions(self) -> dict[str, int]:
        """Clean up old versions based on retention policy.

        Returns
        -------
            Dictionary with cleanup statistics

        """
        if not hasattr(self.model_instance, "get_versions"):
            logger.warning(f"Model {self.model_instance._meta.label} does not support versioning")
            return {"error": "Model does not support versioning"}

        try:
            with transaction.atomic():
                # Get all auto-created versions
                all_versions = self.model_instance.get_versions()
                auto_versions = all_versions.filter(
                    models.Q(branch_name__startswith="auto-") | models.Q(metadata__auto_created=True),
                ).order_by("-created_at")

                results = {
                    "total_auto_versions": auto_versions.count(),
                    "deleted_by_age": 0,
                    "deleted_by_count": 0,
                    "compressed": 0,
                    "kept_milestones": 0,
                    "errors": 0,
                }

                # Apply age-based cleanup
                results["deleted_by_age"] = self._cleanup_by_age(auto_versions)

                # Apply count-based cleanup
                results["deleted_by_count"] = self._cleanup_by_count(auto_versions)

                # Compress old versions
                results["compressed"] = self._compress_old_versions(auto_versions)

                logger.info(f"Version cleanup completed for {self.model_instance._meta.label}: {results}")
                return results

        except Exception as e:
            logger.error(
                f"Version cleanup failed for {self.model_instance._meta.label}: {e}",
                exc_info=True,
            )
            return {"error": str(e)}

    def _cleanup_by_age(self, versions) -> int:
        """Clean up versions older than max age."""
        max_age_days = self.config.get("auto_versions_max_age_days", 90)
        if max_age_days <= 0:
            return 0

        cutoff_date = timezone.now() - timezone.timedelta(days=max_age_days)
        old_versions = versions.filter(created_at__lt=cutoff_date)

        # Exclude milestone versions if configured to keep them
        if self.config.get("keep_workflow_milestone_versions", True):
            old_versions = old_versions.exclude(branch_name__startswith="milestone-")

        count = old_versions.count()
        old_versions.delete()
        return count

    def _cleanup_by_count(self, versions) -> int:
        """Clean up excess versions beyond max count."""
        max_count = self.config.get("auto_versions_max_count", 100)
        if max_count <= 0:
            return 0

        current_count = versions.count()
        if current_count <= max_count:
            return 0

        # Get excess versions (oldest first, excluding milestones)
        excess_versions = versions
        if self.config.get("keep_workflow_milestone_versions", True):
            excess_versions = excess_versions.exclude(branch_name__startswith="milestone-")

        excess_versions = excess_versions[max_count:]
        count = len(excess_versions)

        for version in excess_versions:
            version.delete()

        return count

    def _compress_old_versions(self, versions) -> int:
        """Compress old versions to save space."""
        if not self.config.get("compress_old_auto_versions", True):
            return 0

        # Compress versions older than 30 days
        compress_cutoff = timezone.now() - timezone.timedelta(days=30)
        old_versions = versions.filter(created_at__lt=compress_cutoff, metadata__compressed__isnull=True)

        compressed_count = 0
        for version in old_versions:
            try:
                if not version.metadata:
                    version.metadata = {}

                version.metadata["compressed"] = True
                version.metadata["compressed_at"] = timezone.now().isoformat()

                # Simplify serialized data
                if version.serialized_data:
                    version.serialized_data = self._compress_version_data(version.serialized_data)

                version.save()
                compressed_count += 1

            except (ValidationError, ValueError) as e:
                logger.warning(f"Failed to compress version {version.id}: {e}")

        return compressed_count

    def _compress_version_data(self, data: dict[str, Any]) -> dict[str, Any]:
        """Compress version data by keeping only essential fields."""
        # Define essential fields per model type
        essential_fields_map = {
            "document": ["name", "file_path", "publish_status"],
            "budget": ["name", "total_budget", "status"],
            "invoice": ["invoice_number", "total_amount", "status"],
            "gislayer": ["name", "layer_type", "layout_status"],
            "task": ["title", "status"],
        }

        model_type = self.model_instance._meta.model_name
        essential_fields = essential_fields_map.get(model_type, ["name", "status"])

        # Keep only essential fields
        compressed_data = {field: data.get(field) for field in essential_fields if field in data}

        # Add compression metadata
        compressed_data.update(
            {
                "_compressed": True,
                "_original_field_count": len(data),
                "_compressed_field_count": len(compressed_data),
            },
        )

        return compressed_data


class AutoVersioningMiddleware:
    """Middleware to track current user for auto-versioning."""

    def __init__(self, get_response) -> None:
        """Initialize middleware."""
        self.get_response = get_response

    def __call__(self, request):
        """Process request and track user context."""
        # Store current user in thread-local storage or request context
        # This would need proper implementation with thread-local storage
        # For now, we rely on models setting _current_user attribute

        return self.get_response(request)


def connect_auto_versioning_signals() -> None:
    """Connect Django signals for auto-versioning."""
    from django.apps import apps

    connected_models = []
    failed_models = []

    for model_path in AutoVersioningConfig.ENABLED_MODELS:
        try:
            app_label, model_name = model_path.split(".")
            model_class = apps.get_model(app_label, model_name)

            # Connect pre_save signal
            pre_save.connect(
                ModelStateTracker.track_pre_save,
                sender=model_class,
                dispatch_uid=f"auto_version_pre_{model_path}",
            )

            # Connect post_save signal
            post_save.connect(
                ModelStateTracker.track_post_save,
                sender=model_class,
                dispatch_uid=f"auto_version_post_{model_path}",
            )

            connected_models.append(model_path)
            logger.info(f"Connected auto-versioning signals for {model_path}")

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            failed_models.append((model_path, str(e)))
            logger.error(f"Failed to connect auto-versioning signals for {model_path}: {e}")

    logger.info(f"Auto-versioning signals connected for {len(connected_models)} models")
    if failed_models:
        logger.warning(f"Failed to connect signals for {len(failed_models)} models: {failed_models}")
