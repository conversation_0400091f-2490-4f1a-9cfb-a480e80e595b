"""Knowledge Management Models for CLEAR Platform

Comprehensive models for knowledge base, articles, categories, search, and collaboration
features with Django 5.2 compliance and organization-based multi-tenancy.
"""

import uuid
from typing import Any, ClassVar

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.postgres.search import SearchVector
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from apps.common.models import BaseModel, OrganizationOwnedModel
from apps.versioning.mixins import VersionedModelMixin

User = get_user_model()


# ============================================================================
# Core Knowledge Models
# ============================================================================


class ArticleCategory(OrganizationOwnedModel):
    """Categories for organizing knowledge articles with hierarchical structure."""

    name = models.CharField(_("name"), max_length=255, help_text=_("Display name for the category"))
    description = models.TextField(
        _("description"),
        blank=True,
        help_text=_("Optional description of the category purpose"),
    )
    slug = models.SlugField(
        _("slug"),
        max_length=255,
        help_text=_("URL-friendly identifier for the category"),
    )

    # Hierarchical structure
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="subcategories",
        verbose_name=_("parent category"),
        help_text=_("Parent category for hierarchical organization"),
    )

    # Display and organization
    order = models.PositiveIntegerField(_("sort order"), default=0, help_text=_("Sort order within parent category"))
    icon = models.CharField(_("icon"), max_length=50, blank=True, help_text=_("CSS icon class for display"))
    color = models.CharField(
        _("color"),
        max_length=7,
        blank=True,
        help_text=_("Hex color code for category theming"),
    )

    # Access control
    is_public = models.BooleanField(
        _("is public"),
        default=True,
        help_text=_("Whether category is visible to all organization members"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_category"
        verbose_name = _("Article Category")
        verbose_name_plural = _("Article Categories")
        ordering = ["order", "name"]
        constraints = [
            models.UniqueConstraint(fields=["organization", "slug"], name="unique_category_slug_per_org"),
        ]
        indexes = [
            models.Index(fields=["organization", "parent", "order"]),
            models.Index(fields=["slug"]),
            models.Index(fields=["is_public", "order"]),
        ]

    def __str__(self) -> str:
        return self.name

    def get_full_path(self) -> str:
        """Get the full category path with parent hierarchy."""
        if self.parent:
            return f"{self.parent.get_full_path()} > {self.name}"
        return self.name

    def get_article_count(self) -> int:
        """Get total number of published articles in this category and subcategories."""
        descendant_ids = self._get_descendant_ids()
        return Article.objects.filter(category_id__in=descendant_ids, is_published=True).count()

    def _get_descendant_ids(self) -> list[uuid.UUID]:
        """Get IDs of this category and all descendants."""
        ids = [self.id]
        for child in self.subcategories.all():
            ids.extend(child._get_descendant_ids())
        return ids


class Article(OrganizationOwnedModel, VersionedModelMixin):
    """Knowledge base articles with versioning and comprehensive metadata."""

    title = models.CharField(_("title"), max_length=255, help_text=_("Article title for display and search"))
    slug = models.SlugField(
        _("slug"),
        max_length=255,
        help_text=_("URL-friendly identifier for the article"),
    )
    summary = models.TextField(_("summary"), blank=True, help_text=_("Brief summary or excerpt of the article"))
    content = models.TextField(_("content"), help_text=_("Full article content in markdown or HTML"))

    # Organization and categorization
    category = models.ForeignKey(
        ArticleCategory,
        on_delete=models.CASCADE,
        related_name="articles",
        verbose_name=_("category"),
        help_text=_("Category for organizing the article"),
    )

    # Authoring and modification
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="authored_articles",
        verbose_name=_("author"),
        help_text=_("Original author of the article"),
    )
    last_modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="modified_articles",
        verbose_name=_("last modified by"),
        help_text=_("User who last modified the article"),
    )

    # Publication and status
    status = models.CharField(
        _("status"),
        max_length=20,
        default="draft",
        choices=[
            ("draft", _("Draft")),
            ("review", _("Under Review")),
            ("published", _("Published")),
            ("archived", _("Archived")),
        ],
        help_text=_("Current status of the article"),
    )
    is_published = models.BooleanField(
        _("is published"),
        default=False,
        help_text=_("Whether article is publicly visible"),
    )
    published_at = models.DateTimeField(
        _("published at"),
        blank=True,
        null=True,
        help_text=_("When the article was first published"),
    )

    # Content metadata
    tags = models.JSONField(
        _("tags"),
        default=list,
        blank=True,
        help_text=_("Tags for categorization and search"),
    )
    difficulty_level = models.CharField(
        _("difficulty level"),
        max_length=20,
        default="beginner",
        choices=[
            ("beginner", _("Beginner")),
            ("intermediate", _("Intermediate")),
            ("advanced", _("Advanced")),
            ("expert", _("Expert")),
        ],
        help_text=_("Target audience difficulty level"),
    )
    reading_time_minutes = models.PositiveIntegerField(
        _("reading time (minutes)"),
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(480)],
        help_text=_("Estimated reading time in minutes"),
    )

    # Access control
    is_public = models.BooleanField(
        _("is public"),
        default=True,
        help_text=_("Whether article is visible to all organization members"),
    )
    allowed_roles = models.JSONField(
        _("allowed roles"),
        default=list,
        blank=True,
        help_text=_("Role slugs that can access this article"),
    )

    # Engagement metrics
    view_count = models.PositiveIntegerField(
        _("view count"),
        default=0,
        help_text=_("Number of times article has been viewed"),
    )
    helpful_votes = models.PositiveIntegerField(
        _("helpful votes"),
        default=0,
        help_text=_("Number of helpful votes received"),
    )
    not_helpful_votes = models.PositiveIntegerField(
        _("not helpful votes"),
        default=0,
        help_text=_("Number of not helpful votes received"),
    )

    # SEO and metadata
    meta_description = models.CharField(
        _("meta description"),
        max_length=160,
        blank=True,
        help_text=_("SEO meta description for search engines"),
    )
    featured_image = models.URLField(
        _("featured image"),
        blank=True,
        help_text=_("URL to featured image for the article"),
    )

    # Search vector for PostgreSQL full-text search
    # Using Django 5.2 GeneratedField for optimal search performance
    search_vector = models.GeneratedField(
        expression=SearchVector("title", weight="A")
        + SearchVector("content", weight="B")
        + SearchVector("summary", weight="C"),
        output_field=models.TextField(),
        db_persist=True,
        help_text=_("PostgreSQL search vector for full-text search"),
    )

    # Versioning configuration
    version_fields = [
        "title",
        "content",
        "summary",
        "category",
        "status",
        "is_published",
        "tags",
        "difficulty_level",
        "is_public",
        "allowed_roles",
        "slug",
        "meta_description",
        "featured_image",
    ]
    auto_version = True
    version_summary_templates = {
        "created": _("Created article: {title}"),
        "updated": _("Updated article: {title}"),
        "published": _("Published article: {title}"),
        "archived": _("Archived article: {title}"),
    }

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article"
        verbose_name = _("Article")
        verbose_name_plural = _("Articles")
        ordering = ["-updated_at"]
        constraints = [
            models.UniqueConstraint(fields=["organization", "slug"], name="unique_article_slug_per_org"),
        ]
        indexes = [
            models.Index(fields=["organization", "status", "-updated_at"]),
            models.Index(fields=["category", "status", "-updated_at"]),
            models.Index(fields=["author", "-created_at"]),
            models.Index(fields=["slug"]),
            models.Index(fields=["-view_count"]),
            models.Index(fields=["status", "-published_at"]),
            models.Index(fields=["is_published", "-updated_at"]),
            # GIN index for search_vector (PostgreSQL full-text search)
            # Note: Actual GIN index will be created via custom SQL in migration
        ]
        permissions = [
            ("publish_article", _("Can publish articles")),
            ("approve_article", _("Can approve articles")),
            ("manage_categories", _("Can manage categories")),
            ("view_analytics", _("Can view article analytics")),
        ]

    def __str__(self) -> str:
        return self.title

    def save(self, *args, **kwargs):
        """Override save to handle publication timestamp and versioning."""
        # Set published_at when first published
        if self.pk:
            old_instance = Article.objects.get(pk=self.pk)
            if old_instance.status != "published" and self.status == "published":
                if not self.published_at:
                    self.published_at = timezone.now()
                self.is_published = True

        super().save(*args, **kwargs)

    def get_helpfulness_ratio(self) -> float | None:
        """Calculate helpfulness ratio (0.0-1.0)."""
        total_votes = self.helpful_votes + self.not_helpful_votes
        if total_votes == 0:
            return None
        return self.helpful_votes / total_votes

    def get_reading_time(self) -> int:
        """Get or estimate reading time in minutes."""
        if self.reading_time_minutes:
            return self.reading_time_minutes

        # Estimate: 200 words per minute
        word_count = len(self.content.split())
        return max(1, round(word_count / 200))

    def is_accessible_by_user(self, user) -> bool:
        """Check if user can access this article based on roles."""
        if not self.is_published:
            return False

        if self.is_public:
            return True

        if not self.allowed_roles:
            return True

        # Check user's role in organization
        user_role = getattr(user, "user_roles", None)
        if user_role:
            user_role_slugs = [ur.role.slug for ur in user_role.filter(organization=self.organization)]
            return any(role_slug in self.allowed_roles for role_slug in user_role_slugs)

        return False


# ============================================================================
# Article Interaction Models
# ============================================================================


class ArticleView(BaseModel):
    """Track article views for analytics and engagement metrics."""

    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="views",
        verbose_name=_("article"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="article_views",
        verbose_name=_("user"),
    )

    # Request metadata
    ip_address = models.GenericIPAddressField(_("IP address"), help_text=_("IP address of the viewer"))
    user_agent = models.TextField(_("user agent"), blank=True, help_text=_("Browser user agent string"))
    referrer = models.URLField(_("referrer"), blank=True, help_text=_("Referring page URL"))

    # Reading behavior
    time_spent_seconds = models.PositiveIntegerField(
        _("time spent (seconds)"),
        blank=True,
        null=True,
        help_text=_("Time spent reading the article"),
    )
    scroll_percentage = models.PositiveIntegerField(
        _("scroll percentage"),
        blank=True,
        null=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text=_("Percentage of article scrolled through"),
    )

    viewed_at = models.DateTimeField(_("viewed at"), auto_now_add=True, help_text=_("When the article was viewed"))

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_view"
        verbose_name = _("Article View")
        verbose_name_plural = _("Article Views")
        ordering = ["-viewed_at"]
        indexes = [
            models.Index(fields=["article", "-viewed_at"]),
            models.Index(fields=["user", "-viewed_at"]),
            models.Index(fields=["ip_address", "-viewed_at"]),
        ]

    def __str__(self) -> str:
        return f"View of {self.article.title}"


class ArticleVote(BaseModel):
    """Track helpful/not helpful votes on articles."""

    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="votes",
        verbose_name=_("article"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="article_votes",
        verbose_name=_("user"),
    )

    is_helpful = models.BooleanField(
        _("is helpful"),
        help_text=_("Whether the vote is helpful (True) or not helpful (False)"),
    )
    feedback = models.TextField(_("feedback"), blank=True, help_text=_("Optional feedback about the article"))

    voted_at = models.DateTimeField(_("voted at"), auto_now_add=True, help_text=_("When the vote was cast"))

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_vote"
        verbose_name = _("Article Vote")
        verbose_name_plural = _("Article Votes")
        ordering = ["-voted_at"]
        constraints = [
            models.UniqueConstraint(fields=["article", "user"], name="unique_vote_per_user_article"),
        ]
        indexes = [
            models.Index(fields=["article", "is_helpful"]),
            models.Index(fields=["user", "-voted_at"]),
        ]

    def __str__(self) -> str:
        vote_type = _("helpful") if self.is_helpful else _("not helpful")
        return f"{self.user.username} voted {vote_type} on {self.article.title}"


class ArticleComment(BaseModel):
    """Comments on knowledge articles with moderation support."""

    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="comments",
        verbose_name=_("article"),
    )
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="article_comments",
        verbose_name=_("author"),
    )

    # Comment content and threading
    content = models.TextField(_("content"), help_text=_("Comment content"))
    parent = models.ForeignKey(
        "self",
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="replies",
        verbose_name=_("parent comment"),
        help_text=_("Parent comment for threading"),
    )

    # Moderation
    is_approved = models.BooleanField(
        _("is approved"),
        default=True,
        help_text=_("Whether comment is approved for display"),
    )
    is_flagged = models.BooleanField(
        _("is flagged"),
        default=False,
        help_text=_("Whether comment has been flagged for review"),
    )

    # Engagement
    helpful_votes = models.PositiveIntegerField(
        _("helpful votes"),
        default=0,
        help_text=_("Number of helpful votes received"),
    )
    not_helpful_votes = models.PositiveIntegerField(
        _("not helpful votes"),
        default=0,
        help_text=_("Number of not helpful votes received"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_comment"
        verbose_name = _("Article Comment")
        verbose_name_plural = _("Article Comments")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["article", "-created_at"]),
            models.Index(fields=["author", "-created_at"]),
            models.Index(fields=["parent", "-created_at"]),
            models.Index(fields=["is_approved", "-created_at"]),
        ]

    def __str__(self) -> str:
        return f"Comment on {self.article.title} by {self.author.username}"


# ============================================================================
# Article Management Models
# ============================================================================


class ArticleRevision(BaseModel):
    """Track article revision history with detailed change tracking."""

    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="revisions",
        verbose_name=_("article"),
    )
    revision_number = models.PositiveIntegerField(_("revision number"), help_text=_("Sequential revision number"))

    # Snapshot of content at revision
    title = models.CharField(_("title"), max_length=255, help_text=_("Article title at this revision"))
    content = models.TextField(_("content"), help_text=_("Article content at this revision"))
    summary = models.TextField(_("summary"), blank=True, help_text=_("Article summary at this revision"))

    # Change metadata
    changed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="article_revisions",
        verbose_name=_("changed by"),
    )
    change_summary = models.TextField(
        _("change summary"),
        blank=True,
        help_text=_("Summary of changes made in this revision"),
    )
    change_type = models.CharField(
        _("change type"),
        max_length=20,
        choices=[
            ("created", _("Created")),
            ("content", _("Content Update")),
            ("metadata", _("Metadata Update")),
            ("status", _("Status Change")),
            ("major", _("Major Revision")),
        ],
        help_text=_("Type of change made"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_revision"
        verbose_name = _("Article Revision")
        verbose_name_plural = _("Article Revisions")
        ordering = ["-revision_number"]
        constraints = [
            models.UniqueConstraint(
                fields=["article", "revision_number"],
                name="unique_article_revision_number",
            ),
        ]
        indexes = [
            models.Index(fields=["article", "-revision_number"]),
            models.Index(fields=["changed_by", "-created_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.article.title} revision {self.revision_number}"


class ArticleAttachment(BaseModel):
    """File attachments for knowledge articles."""

    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="attachments",
        verbose_name=_("article"),
    )

    # File details
    name = models.CharField(_("name"), max_length=255, help_text=_("Display name for the attachment"))
    file_path = models.CharField(_("file path"), max_length=500, help_text=_("Path to the stored file"))
    file_size = models.BigIntegerField(_("file size"), help_text=_("File size in bytes"))
    mime_type = models.CharField(_("MIME type"), max_length=100, help_text=_("MIME type of the file"))

    # Metadata
    description = models.TextField(
        _("description"),
        blank=True,
        help_text=_("Optional description of the attachment"),
    )
    uploaded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="article_attachments",
        verbose_name=_("uploaded by"),
    )

    # Organization
    order = models.PositiveIntegerField(
        _("sort order"),
        default=0,
        help_text=_("Sort order within article attachments"),
    )
    is_featured = models.BooleanField(
        _("is featured"),
        default=False,
        help_text=_("Whether attachment is featured for the article"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_attachment"
        verbose_name = _("Article Attachment")
        verbose_name_plural = _("Article Attachments")
        ordering = ["order", "name"]
        indexes = [
            models.Index(fields=["article", "order"]),
            models.Index(fields=["uploaded_by", "-created_at"]),
            models.Index(fields=["mime_type"]),
        ]

    def __str__(self) -> str:
        return f"{self.name} for {self.article.title}"

    def get_file_extension(self) -> str:
        """Get file extension from name."""
        return self.name.split(".")[-1].lower() if "." in self.name else ""

    def is_image(self) -> bool:
        """Check if attachment is an image file."""
        image_extensions = {"jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"}
        return self.get_file_extension() in image_extensions


class ArticleWorkflow(BaseModel):
    """Track workflow state changes and approvals for articles."""

    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        related_name="workflow_history",
        verbose_name=_("article"),
    )

    # Workflow transition
    from_status = models.CharField(_("from status"), max_length=20, help_text=_("Previous status"))
    to_status = models.CharField(_("to status"), max_length=20, help_text=_("New status"))
    action = models.CharField(
        _("action"),
        max_length=50,
        choices=[
            ("created", _("Created")),
            ("submitted", _("Submitted for Review")),
            ("reviewed", _("Reviewed")),
            ("approved", _("Approved")),
            ("rejected", _("Rejected")),
            ("published", _("Published")),
            ("archived", _("Archived")),
            ("restored", _("Restored")),
        ],
        help_text=_("Action that caused the status change"),
    )

    # Actors
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="article_workflow_actions",
        verbose_name=_("performed by"),
    )
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="assigned_article_workflows",
        verbose_name=_("assigned to"),
        help_text=_("User assigned to handle this workflow step"),
    )

    # Details
    comments = models.TextField(_("comments"), blank=True, help_text=_("Comments about the workflow action"))
    due_date = models.DateTimeField(
        _("due date"),
        blank=True,
        null=True,
        help_text=_("Due date for assigned workflow step"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_article_workflow"
        verbose_name = _("Article Workflow")
        verbose_name_plural = _("Article Workflows")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["article", "-created_at"]),
            models.Index(fields=["performed_by", "-created_at"]),
            models.Index(fields=["assigned_to", "due_date"]),
            models.Index(fields=["action", "-created_at"]),
        ]

    def __str__(self) -> str:
        return f"{self.article.title}: {self.from_status} → {self.to_status}"


# ============================================================================
# Search and Analytics Models
# ============================================================================


# All search-related models have been moved to models_search.py to consolidate functionality
# and resolve import conflicts. Import from apps.knowledge.models_search for:
# - SavedKnowledgeSearch
# - KnowledgeSearchAnalytics
# - KnowledgeSearchAlert
# - KnowledgeSearchExport
# - KnowledgeSearchFacet
# - KnowledgeTeam
# - KnowledgeTeamMembership


# ============================================================================
# Contextual Help System Models
# ============================================================================


class HelpTopic(OrganizationOwnedModel):
    """Contextual help topics that can be triggered from UI elements."""

    title = models.CharField(_("title"), max_length=255, help_text=_("Help topic title"))
    slug = models.SlugField(_("slug"), max_length=255, help_text=_("URL-friendly identifier"))

    # Content sources
    content_type = models.CharField(
        _("content type"),
        max_length=20,
        choices=[
            ("markdown", _("Markdown Content")),
            ("article", _("Knowledge Article")),
            ("external", _("External Documentation")),
            ("mixed", _("Mixed Content")),
        ],
        default="markdown",
        help_text=_("Type of content source"),
    )

    # Content fields
    markdown_content = models.TextField(
        _("markdown content"),
        blank=True,
        help_text=_("Direct markdown content for help topic"),
    )
    article = models.ForeignKey(
        Article,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="help_topics",
        verbose_name=_("linked article"),
        help_text=_("Knowledge base article to display as help"),
    )
    external_url = models.URLField(_("external URL"), blank=True, help_text=_("External documentation URL"))
    mkdocs_path = models.CharField(
        _("MkDocs path"),
        max_length=500,
        blank=True,
        help_text=_("Path to MkDocs content (e.g., 'users/getting-started.md')"),
    )

    # Contextual mapping
    trigger_contexts = models.JSONField(
        _("trigger contexts"),
        default=list,
        help_text=_("List of UI contexts where this help appears (view names, form fields, etc.)"),
    )
    page_selectors = models.JSONField(
        _("page selectors"),
        default=list,
        help_text=_("CSS selectors for UI elements that trigger this help"),
    )

    # Targeting and permissions
    target_audience = models.CharField(
        _("target audience"),
        max_length=20,
        choices=[
            ("all", _("All Users")),
            ("users", _("End Users")),
            ("admin", _("Administrators")),
            ("dev", _("Developers")),
        ],
        default="all",
        help_text=_("Target audience for this help topic"),
    )
    required_permissions = models.JSONField(
        _("required permissions"),
        default=list,
        blank=True,
        help_text=_("Permissions required to see this help topic"),
    )

    # Display configuration
    display_type = models.CharField(
        _("display type"),
        max_length=20,
        choices=[
            ("modal", _("Modal Dialog")),
            ("sidebar", _("Sidebar Panel")),
            ("tooltip", _("Tooltip")),
            ("inline", _("Inline Expansion")),
            ("overlay", _("Overlay")),
        ],
        default="modal",
        help_text=_("How the help content should be displayed"),
    )
    icon = models.CharField(
        _("icon"),
        max_length=50,
        default="help-circle",
        help_text=_("Icon class for help trigger button"),
    )
    priority = models.PositiveIntegerField(
        _("priority"),
        default=50,
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text=_("Priority for help topic display order"),
    )

    # Status and lifecycle
    is_active = models.BooleanField(
        _("is active"),
        default=True,
        help_text=_("Whether help topic is currently active"),
    )
    auto_trigger = models.BooleanField(
        _("auto trigger"),
        default=False,
        help_text=_("Whether help should appear automatically on page load"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_help_topic"
        verbose_name = _("Help Topic")
        verbose_name_plural = _("Help Topics")
        ordering = ["priority", "title"]
        constraints = [
            models.UniqueConstraint(fields=["organization", "slug"], name="unique_help_topic_slug_per_org"),
        ]
        indexes = [
            models.Index(fields=["organization", "is_active", "priority"]),
            models.Index(fields=["target_audience", "is_active"]),
            models.Index(fields=["slug"]),
        ]

    def __str__(self) -> str:
        return self.title

    def get_content(self) -> dict[str, Any]:
        """Get the help content based on content type."""
        content = {
            "title": self.title,
            "type": self.content_type,
            "display_type": self.display_type,
            "content": "",
            "source": None,
        }

        if self.content_type == "markdown" and self.markdown_content:
            content["content"] = self.markdown_content
            content["source"] = "direct"
        elif self.content_type == "article" and self.article:
            content["content"] = self.article.content
            content["title"] = self.article.title
            content["source"] = "article"
        elif self.content_type == "external" and self.external_url:
            content["content"] = f"See: [{self.external_url}]({self.external_url})"
            content["source"] = "external"
        elif self.content_type == "mixed" and self.mkdocs_path:
            # This would load from MkDocs build output
            content["content"] = f"Loading content from: {self.mkdocs_path}"
            content["source"] = "mkdocs"

        return content

    def is_accessible_by_user(self, user) -> bool:
        """Check if user can access this help topic."""
        if not self.is_active:
            return False

        # Check organization access
        if user.organization != self.organization:
            return False

        # Check required permissions
        if self.required_permissions:
            user_permissions = user.get_all_permissions()
            required_perms = set(self.required_permissions)
            if not required_perms.issubset(user_permissions):
                return False

        # Check target audience
        if self.target_audience == "admin":
            return user.is_staff or user.has_perm("knowledge.manage_help")
        elif self.target_audience == "dev":
            return user.has_perm("api.access_api") or user.has_perm("knowledge.dev_access")

        return True

    def matches_context(self, context: str, selector: str = None) -> bool:
        """Check if help topic matches the given context/selector."""
        if context in self.trigger_contexts:
            return True

        if selector and selector in self.page_selectors:
            return True

        return False


class HelpTopicView(BaseModel):
    """Track help topic views for analytics."""

    help_topic = models.ForeignKey(
        HelpTopic,
        on_delete=models.CASCADE,
        related_name="views",
        verbose_name=_("help topic"),
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="help_topic_views",
        verbose_name=_("user"),
    )

    # Context information
    page_url = models.URLField(_("page URL"), help_text=_("URL where help was accessed"))
    trigger_context = models.CharField(
        _("trigger context"),
        max_length=255,
        help_text=_("Context that triggered the help display"),
    )
    trigger_selector = models.CharField(
        _("trigger selector"),
        max_length=500,
        blank=True,
        help_text=_("CSS selector that triggered help"),
    )

    # Interaction tracking
    time_spent_seconds = models.PositiveIntegerField(
        _("time spent (seconds)"),
        blank=True,
        null=True,
        help_text=_("Time spent viewing help content"),
    )
    was_helpful = models.BooleanField(
        _("was helpful"),
        blank=True,
        null=True,
        help_text=_("User feedback on help usefulness"),
    )
    feedback = models.TextField(_("feedback"), blank=True, help_text=_("Optional user feedback"))

    # Request metadata
    ip_address = models.GenericIPAddressField(_("IP address"))
    user_agent = models.TextField(_("user agent"), blank=True)

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_help_topic_view"
        verbose_name = _("Help Topic View")
        verbose_name_plural = _("Help Topic Views")
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["help_topic", "-created_at"]),
            models.Index(fields=["user", "-created_at"]),
            models.Index(fields=["trigger_context", "-created_at"]),
        ]

    def __str__(self) -> str:
        return f"View of {self.help_topic.title}"


class HelpTopicMapping(BaseModel):
    """Maps help topics to specific Django views and forms."""

    help_topic = models.ForeignKey(
        HelpTopic,
        on_delete=models.CASCADE,
        related_name="mappings",
        verbose_name=_("help topic"),
    )

    # Django-specific mapping
    view_name = models.CharField(
        _("view name"),
        max_length=255,
        blank=True,
        help_text=_("Django URL name or view class name"),
    )
    form_class = models.CharField(
        _("form class"),
        max_length=255,
        blank=True,
        help_text=_("Django form class name"),
    )
    model_name = models.CharField(_("model name"), max_length=255, blank=True, help_text=_("Django model name"))

    # Field-specific help
    field_name = models.CharField(
        _("field name"),
        max_length=100,
        blank=True,
        help_text=_("Specific form field name"),
    )
    css_selector = models.CharField(
        _("CSS selector"),
        max_length=500,
        blank=True,
        help_text=_("CSS selector for UI element"),
    )

    # Conditional display
    conditions = models.JSONField(
        _("conditions"),
        default=dict,
        blank=True,
        help_text=_("Conditions for when help should appear"),
    )

    # Display configuration
    position = models.CharField(
        _("position"),
        max_length=20,
        choices=[
            ("top", _("Top")),
            ("bottom", _("Bottom")),
            ("left", _("Left")),
            ("right", _("Right")),
            ("center", _("Center")),
        ],
        default="right",
        help_text=_("Position relative to trigger element"),
    )

    class Meta:
        app_label = "knowledge"
        db_table: ClassVar[str] = "knowledge_help_topic_mapping"
        verbose_name = _("Help Topic Mapping")
        verbose_name_plural = _("Help Topic Mappings")
        ordering = ["view_name", "field_name"]
        indexes = [
            models.Index(fields=["view_name"]),
            models.Index(fields=["form_class", "field_name"]),
            models.Index(fields=["model_name"]),
        ]

    def __str__(self) -> str:
        if self.field_name:
            return f"{self.help_topic.title} → {self.view_name}.{self.field_name}"
        return f"{self.help_topic.title} → {self.view_name}"

    def matches_request(
        self,
        view_name: str = None,
        form_class: str = None,
        field_name: str = None,
        selector: str = None,
    ) -> bool:
        """Check if mapping matches the current request context."""
        if self.view_name and view_name and self.view_name == view_name:
            if not self.field_name or self.field_name == field_name:
                return True

        if self.form_class and form_class and self.form_class == form_class:
            if not self.field_name or self.field_name == field_name:
                return True

        if self.css_selector and selector and self.css_selector == selector:
            return True

        return False
