"""Knowledge Model Compliance Check for Django 5.2.

This module provides comprehensive model validation for the knowledge management app,
ensuring compliance with Django 5.2 specifications, performance best practices,
and security requirements.

Validates:
- Search performance optimization
- Content security and access control
- Organization isolation patterns
- Database indexing strategies
- Django 5.2 GeneratedField usage
- PostgreSQL full-text search configuration
- Knowledge graph relationships
- Version control integrity
"""

from django.conf import settings
from django.core.checks import Error, Info, Tags, Warning, register
from django.db import models

# Knowledge-specific check tags
KNOWLEDGE_TAGS = [Tags.models, Tags.security, Tags.database]


@register(*KNOWLEDGE_TAGS)
def check_knowledge_model_compliance(app_configs, **kwargs):
    """Comprehensive knowledge model compliance check.

    Runs automatically on Django startup to validate critical configuration
    and performance settings for the knowledge management system.
    """
    errors = []

    # Only check if knowledge app is available
    knowledge_app = None
    if app_configs:
        knowledge_app = next((app for app in app_configs if app.name == "apps.knowledge"), None)

    if not knowledge_app:
        return errors

    # Import models for checking
    try:
        from apps.knowledge.models import (
            Article,
            ArticleAttachment,
            ArticleCategory,
            ArticleComment,
            ArticleRevision,
            ArticleView,
            ArticleVote,
            ArticleWorkflow,
            KnowledgeSearchAlert,
            KnowledgeSearchAnalytics,
            KnowledgeSearchExport,
            KnowledgeSearchFacet,
            KnowledgeTeam,
            KnowledgeTeamMembership,
            SavedKnowledgeSearch,
        )
    except ImportError as e:
        return [
            Error(
                f"Cannot import knowledge models: {e}",
                hint="Ensure knowledge app models are properly defined and migrated",
                id="knowledge.models.E001",
            ),
        ]

    # Perform comprehensive model checks
    errors.extend(_check_search_performance(Article))
    errors.extend(_check_content_security(Article, ArticleCategory))
    errors.extend(_check_organization_isolation(Article, ArticleCategory))
    errors.extend(_check_database_indexes(Article))
    errors.extend(_check_django52_features(Article))
    errors.extend(_check_search_model_integrity(SavedKnowledgeSearch, KnowledgeSearchAlert))
    errors.extend(_check_postgresql_configuration())
    errors.extend(_check_knowledge_relationships(Article, ArticleRevision))
    errors.extend(_check_team_collaboration(KnowledgeTeam, KnowledgeTeamMembership))
    errors.extend(_check_analytics_configuration(KnowledgeSearchAnalytics))

    # Add informational message if issues found
    if errors:
        error_count = len([e for e in errors if e.level == Error.level])
        warning_count = len([e for e in errors if e.level == Warning.level])

        errors.append(
            Info(
                f"Knowledge model check found {error_count} errors and {warning_count} warnings. "
                "Review the messages above for recommended improvements.",
                hint="Address errors first, then consider implementing warning suggestions",
                id="knowledge.models.I001",
            ),
        )

    return errors


def _check_search_performance(article_model):
    """Check search performance configuration and indexing."""
    errors = []

    # Check for full-text search fields
    search_fields = ["title", "content", "summary", "tags"]
    model_indexes = [index.fields for index in article_model._meta.indexes]

    for field in search_fields:
        if hasattr(article_model, field):
            # Check if field has appropriate indexing
            field_indexed = any(field in index for index in model_indexes)
            if not field_indexed:
                errors.append(
                    Warning(
                        f'Article model missing index on search field "{field}"',
                        hint=f"Add database index on {field} for better search performance",
                        obj=article_model,
                        id="knowledge.models.W001",
                    ),
                )

    # Check for search vector field (Django 5.2 GeneratedField)
    has_search_vector = any(f.name.endswith("_search_vector") for f in article_model._meta.get_fields())

    if not has_search_vector:
        errors.append(
            Info(
                "Consider adding search vector GeneratedField for PostgreSQL full-text search",
                hint="Use GeneratedField with SearchVector for optimal search performance",
                obj=article_model,
                id="knowledge.models.I002",
            ),
        )

    # Check for search ranking fields
    ranking_fields = ["view_count", "vote_score", "relevance_score"]
    for field_name in ranking_fields:
        if not hasattr(article_model, field_name):
            errors.append(
                Info(
                    f'Consider adding "{field_name}" field for search ranking',
                    hint="Search ranking fields improve result relevance",
                    obj=article_model,
                    id="knowledge.models.I003",
                ),
            )

    return errors


def _check_content_security(article_model, category_model):
    """Check content security and access control configuration."""
    errors = []

    # Check for organization isolation
    required_org_models = [article_model, category_model]
    for model in required_org_models:
        if not hasattr(model, "organization"):
            errors.append(
                Error(
                    f"{model.__name__} missing organization field for multi-tenant isolation",
                    hint="Add organization ForeignKey for proper content isolation",
                    obj=model,
                    id="knowledge.models.E002",
                ),
            )

    # Check for access control fields
    access_control_fields = ["is_public", "allowed_roles", "visibility_level"]
    for field_name in access_control_fields:
        if not hasattr(article_model, field_name):
            errors.append(
                Warning(
                    f'Article missing "{field_name}" field for access control',
                    hint=f"Add {field_name} field to control article visibility",
                    obj=article_model,
                    id="knowledge.models.W002",
                ),
            )

    # Check for content validation
    if hasattr(article_model, "content"):
        content_field = article_model._meta.get_field("content")
        if not hasattr(content_field, "validators") or not content_field.validators:
            errors.append(
                Warning(
                    "Article content field missing validation",
                    hint="Add content validators to prevent XSS and ensure data quality",
                    obj=article_model,
                    id="knowledge.models.W003",
                ),
            )

    # Check for moderation fields
    moderation_fields = ["is_approved", "approval_status", "reviewed_by"]
    missing_moderation = [field for field in moderation_fields if not hasattr(article_model, field)]

    if len(missing_moderation) > 2:
        errors.append(
            Info(
                "Consider adding content moderation fields for workflow management",
                hint="Moderation fields help manage content quality and approval processes",
                obj=article_model,
                id="knowledge.models.I004",
            ),
        )

    return errors


def _check_organization_isolation(article_model, category_model):
    """Check multi-tenant organization isolation patterns."""
    errors = []

    models_to_check = [article_model, category_model]

    for model in models_to_check:
        # Check for organization-scoped unique constraints
        constraints = model._meta.constraints
        org_constraints = [c for c in constraints if hasattr(c, "fields") and "organization" in c.fields]

        if not org_constraints:
            errors.append(
                Warning(
                    f"{model.__name__} missing organization-scoped unique constraints",
                    hint="Add unique constraints that include organization field",
                    obj=model,
                    id="knowledge.models.W004",
                ),
            )

        # Check for proper indexing on organization field
        org_indexed = any("organization" in index.fields for index in model._meta.indexes)

        if not org_indexed:
            errors.append(
                Warning(
                    f"{model.__name__} missing index on organization field",
                    hint="Add database index on organization for query performance",
                    obj=model,
                    id="knowledge.models.W005",
                ),
            )

    return errors


def _check_database_indexes(article_model):
    """Check database indexing strategy for performance."""
    errors = []

    model_indexes = [index.fields for index in article_model._meta.indexes]

    # Check for essential composite indexes
    recommended_composite_indexes = [
        ("organization", "status", "-updated_at"),
        ("category", "is_published", "-created_at"),
        ("author", "-created_at"),
        ("status", "-published_at"),
    ]

    for recommended_index in recommended_composite_indexes:
        index_exists = any(all(field in index for field in recommended_index) for index in model_indexes)

        if not index_exists:
            errors.append(
                Warning(
                    f"Article missing recommended composite index: {recommended_index}",
                    hint="Add composite index for better query performance",
                    obj=article_model,
                    id="knowledge.models.W006",
                ),
            )

    # Check for JSON field indexing (if using PostgreSQL)
    json_fields = [f for f in article_model._meta.get_fields() if f.get_internal_type() == "JSONField"]

    if json_fields:
        # Look for GIN indexes on JSON fields
        gin_indexes = [
            idx
            for idx in article_model._meta.indexes
            if hasattr(idx, "opclasses") and "gin" in str(idx.opclasses).lower()
        ]

        if not gin_indexes:
            errors.append(
                Info(
                    "Consider using GIN indexes on JSONField columns",
                    hint="GIN indexes improve performance for JSON field queries",
                    obj=article_model,
                    id="knowledge.models.I005",
                ),
            )

    return errors


def _check_django52_features(article_model):
    """Check utilization of Django 5.2 features."""
    errors = []

    # Check for GeneratedField usage
    generated_fields = [f for f in article_model._meta.get_fields() if hasattr(f, "generated") and f.generated]

    if not generated_fields:
        errors.append(
            Info(
                "Consider using Django 5.2 GeneratedField for computed values",
                hint="GeneratedFields can improve performance for calculated fields",
                obj=article_model,
                id="knowledge.models.I006",
            ),
        )

    # Check for potential computed fields
    potential_computed_fields = [
        "reading_time_minutes",
        "word_count",
        "complexity_score",
        "search_rank",
    ]

    for field_name in potential_computed_fields:
        if hasattr(article_model, field_name):
            field = article_model._meta.get_field(field_name)
            if not (hasattr(field, "generated") and field.generated):
                errors.append(
                    Info(
                        f'Field "{field_name}" could benefit from GeneratedField',
                        hint="Convert computed fields to GeneratedField for better performance",
                        obj=article_model,
                        id="knowledge.models.I007",
                    ),
                )

    return errors


def _check_search_model_integrity(saved_search_model, search_alert_model):
    """Check search-related model integrity and relationships."""
    errors = []

    # Check foreign key relationships
    if hasattr(search_alert_model, "saved_search"):
        search_field = search_alert_model._meta.get_field("saved_search")
        if not isinstance(search_field, models.ForeignKey):
            errors.append(
                Error(
                    "SearchAlert.saved_search should be a ForeignKey",
                    hint="Use ForeignKey for proper relationship integrity",
                    obj=search_alert_model,
                    id="knowledge.models.E003",
                ),
            )

    # Check for search parameter validation
    if hasattr(saved_search_model, "query"):
        query_field = saved_search_model._meta.get_field("query")
        if not hasattr(query_field, "validators") or not query_field.validators:
            errors.append(
                Warning(
                    "SavedSearch query field missing validation",
                    hint="Add query validation to prevent injection attacks",
                    obj=saved_search_model,
                    id="knowledge.models.W007",
                ),
            )

    # Check for organization isolation in search models
    search_models = [saved_search_model, search_alert_model]
    for model in search_models:
        if not hasattr(model, "organization"):
            errors.append(
                Error(
                    f"{model.__name__} missing organization field",
                    hint="Add organization field for multi-tenant isolation",
                    obj=model,
                    id="knowledge.models.E004",
                ),
            )

    return errors


def _check_postgresql_configuration():
    """Check PostgreSQL configuration for optimal search performance."""
    errors = []

    # Check database engine
    db_config = settings.DATABASES.get("default", {})
    db_engine = db_config.get("ENGINE", "")

    if "postgresql" not in db_engine:
        errors.append(
            Warning(
                "PostgreSQL recommended for optimal knowledge search performance",
                hint="Use PostgreSQL for full-text search and advanced indexing",
                id="knowledge.models.W008",
            ),
        )
        return errors

    # Check for PostgreSQL search extensions
    try:
        from django.contrib.postgres.search import SearchQuery, SearchRank, SearchVector
    except ImportError:
        errors.append(
            Warning(
                "PostgreSQL search extensions not available",
                hint="Install psycopg[binary] for full-text search capabilities",
                id="knowledge.models.W009",
            ),
        )

    # Check database configuration options
    db_options = db_config.get("OPTIONS", {})
    recommended_options = {
        "shared_preload_libraries": "pg_stat_statements",
        "track_activity_query_size": 2048,
    }

    for option, value in recommended_options.items():
        if option not in db_options:
            errors.append(
                Info(
                    f"Consider adding PostgreSQL option: {option}={value}",
                    hint="Database options can improve search performance monitoring",
                    id="knowledge.models.I008",
                ),
            )

    return errors


def _check_knowledge_relationships(article_model, revision_model):
    """Check knowledge graph relationships and version control."""
    errors = []

    # Check revision tracking relationship
    if hasattr(revision_model, "article"):
        article_field = revision_model._meta.get_field("article")
        if not isinstance(article_field, models.ForeignKey):
            errors.append(
                Error(
                    "ArticleRevision.article should be a ForeignKey",
                    hint="Use ForeignKey for proper version tracking relationship",
                    obj=revision_model,
                    id="knowledge.models.E005",
                ),
            )

    # Check for version control fields
    version_fields = ["revision_number", "change_summary", "changed_by"]
    for field_name in version_fields:
        if not hasattr(revision_model, field_name):
            errors.append(
                Warning(
                    f'ArticleRevision missing "{field_name}" field',
                    hint=f"Add {field_name} for comprehensive version tracking",
                    obj=revision_model,
                    id="knowledge.models.W010",
                ),
            )

    # Check for article relationships (tags, categories, related articles)
    relationship_fields = ["tags", "related_articles", "parent_article"]
    missing_relationships = [field for field in relationship_fields if not hasattr(article_model, field)]

    if len(missing_relationships) > 1:
        errors.append(
            Info(
                "Consider adding relationship fields for knowledge graph connectivity",
                hint="Article relationships improve discoverability and navigation",
                obj=article_model,
                id="knowledge.models.I009",
            ),
        )

    return errors


def _check_team_collaboration(team_model, membership_model):
    """Check team collaboration model configuration."""
    errors = []

    # Check team-membership relationship
    if hasattr(membership_model, "team"):
        team_field = membership_model._meta.get_field("team")
        if not isinstance(team_field, models.ForeignKey):
            errors.append(
                Error(
                    "KnowledgeTeamMembership.team should be a ForeignKey",
                    hint="Use ForeignKey for proper team relationship",
                    obj=membership_model,
                    id="knowledge.models.E006",
                ),
            )

    # Check for role-based permissions in team membership
    permission_fields = ["role", "permissions", "access_level"]
    missing_permissions = [field for field in permission_fields if not hasattr(membership_model, field)]

    if len(missing_permissions) > 2:
        errors.append(
            Warning(
                "Consider adding role-based permission fields to team membership",
                hint="Role fields enable fine-grained access control within teams",
                obj=membership_model,
                id="knowledge.models.W011",
            ),
        )

    # Check for organization isolation in teams
    if not hasattr(team_model, "organization"):
        errors.append(
            Error(
                "KnowledgeTeam missing organization field",
                hint="Add organization field for multi-tenant team isolation",
                obj=team_model,
                id="knowledge.models.E007",
            ),
        )

    return errors


def _check_analytics_configuration(analytics_model):
    """Check analytics model configuration for performance tracking."""
    errors = []

    # Check for essential analytics fields
    required_fields = ["search_term", "result_count", "click_through", "user"]
    missing_fields = [field for field in required_fields if not hasattr(analytics_model, field)]

    if missing_fields:
        errors.append(
            Warning(
                f"KnowledgeSearchAnalytics missing fields: {missing_fields}",
                hint="Add missing fields for comprehensive search analytics",
                obj=analytics_model,
                id="knowledge.models.W012",
            ),
        )

    # Check for performance tracking fields
    performance_fields = ["search_time_ms", "result_relevance_score"]
    missing_performance = [field for field in performance_fields if not hasattr(analytics_model, field)]

    if missing_performance:
        errors.append(
            Info(
                f"Consider adding performance tracking fields: {missing_performance}",
                hint="Performance fields help optimize search functionality",
                obj=analytics_model,
                id="knowledge.models.I010",
            ),
        )

    # Check for proper indexing on analytics
    if hasattr(analytics_model, "_meta"):
        indexes = [index.fields for index in analytics_model._meta.indexes]
        essential_indexes = [("search_term", "-created_at"), ("user", "-created_at")]

        for index_fields in essential_indexes:
            if not any(all(field in idx for field in index_fields) for idx in indexes):
                errors.append(
                    Warning(
                        f"KnowledgeSearchAnalytics missing index: {index_fields}",
                        hint="Add indexes for efficient analytics queries",
                        obj=analytics_model,
                        id="knowledge.models.W013",
                    ),
                )

    return errors
