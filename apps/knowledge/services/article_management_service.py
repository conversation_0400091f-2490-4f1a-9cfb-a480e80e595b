"""
Article Management Service for CLEAR Knowledge Platform

Provides comprehensive article management functionality including:
- Article creation, editing, and deletion
- Comment management and moderation
- Attachment handling and versioning
- Workflow and approval processes
- Content categorization and organization
"""

import logging
import os
import re
import uuid
from datetime import datetime, timedelta
from typing import Any, List, Optional
from uuid import uuid4

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied, ValidationError
from django.db import models, transaction
from django.db.models import Count
from django.utils import timezone
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _

from rest_framework import authentication, permissions, status

# Add missing model imports
try:
    from apps.knowledge.models import (
        Article,
        ArticleAttachment,
        ArticleCategory,
        ArticleComment,
        ArticleRevision,
        ArticleWorkflow,
    )
except ImportError:
    # Fallback classes for missing models
    class Article:
        """Fallback Article model"""
        objects = None
        
    class ArticleComment:
        """Fallback ArticleComment model"""
        objects = None
        
    class ArticleAttachment:
        """Fallback ArticleAttachment model"""
        objects = None
        
    class ArticleRevision:
        """Fallback ArticleRevision model"""
        objects = None
        
    class ArticleWorkflow:
        """Fallback ArticleWorkflow model"""
        objects = None
        
    class ArticleCategory:
        """Fallback ArticleCategory model"""
        objects = None

User = get_user_model()
logger = logging.getLogger(__name__)

"""
Comprehensive service for managing knowledge articles throughout their lifecycle
including creation, editing, review workflows, publication, archiving, and collaboration
features with Django 5.2 compliance and organization-based multi-tenancy.
"""

import logging
import mimetypes
import os
from datetime import timedelta
from typing import Any, Optional

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied, ValidationError
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import transaction
from django.db.models import Count
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from apps.authentication.models import Organization

User = get_user_model()

# Set up logging
logger = logging.getLogger(__name__)


class ArticleManagementService:
    """Comprehensive service for managing knowledge articles with full lifecycle support.

    Features:
    - Article creation, editing, and deletion
    - Review and approval workflows
    - Revision tracking and versioning
    - Comment and attachment management
    - Workflow state management
    - Analytics and statistics
    - Permission-based access control
    - Organization-based multi-tenancy
    """

    def __init__(self, user: User | None = None, organization: Organization | None = None):
        """Initialize the article management service.

        Args:
        ----
            user: The user performing operations (required for most operations)
            organization: The organization context (will be inferred from user if not provided)

        """
        self.user = user
        self.organization = organization or (user.organization if user else None)

        if not self.organization:
            raise ValueError(_("Organization context is required for article management"))

    # ============================================================================
    # Article CRUD Operations
    # ============================================================================

    def create_article(self, title: str, content: str, category_id: str, **kwargs) -> "Article":
        """Create a new knowledge article.

        Args:
        ----
            title: Article title
            content: Article content in markdown or HTML
            category_id: UUID of the article category
            **kwargs: Additional article fields

        Returns:
        -------
            Created Article instance

        Raises:
        ------
            ValidationError: If required fields are missing or invalid
            PermissionDenied: If user doesn't have permission to create articles

        """
        if not self.user:
            raise PermissionDenied(_("User context required for article creation"))

        from apps.knowledge.models import Article, ArticleCategory

        # Validate category exists and belongs to organization
        try:
            category = ArticleCategory.objects.get(id=category_id, organization=self.organization)
        except ArticleCategory.DoesNotExist:
            raise ValidationError(_("Invalid category or category not found"))

        # Set default values
        article_data = {
            "title": title.strip(),
            "content": content,
            "category": category,
            "author": self.user,
            "organization": self.organization,
            "status": "draft",
            "difficulty_level": kwargs.get("difficulty_level", "beginner"),
            "tags": kwargs.get("tags", []),
            "summary": kwargs.get("summary", ""),
            "is_public": kwargs.get("is_public", True),
            "slug": kwargs.get("slug", ""),
        }

        # Auto-generate slug if not provided
        if not article_data["slug"]:
            article_data["slug"] = self._generate_slug(title, category)

        try:
            with transaction.atomic():
                # Create the article
                article = Article.objects.create(**article_data)

                # Create initial revision
                self._create_revision(
                    article=article,
                    title=title,
                    content=content,
                    change_type="created",
                    change_summary=_("Initial article creation"),
                    revision_number=1,
                )

                # Create workflow entry
                self._create_workflow_entry(
                    article=article,
                    from_status="",
                    to_status="draft",
                    action="created",
                    notes=_("Article created"),
                )

                logger.info(
                    f"Article created: {article.title} by {self.user.email} in organization {self.organization.name}",
                )

                return article

        except (ValidationError, ValueError) as e:
            logger.error(f"Error creating article: {e!s}")
            raise ValidationError(_("Failed to create article: {error}").format(error=str(e)))

    def update_article(self, article: "Article", **updates) -> "Article":
        """Update an existing article with revision tracking.

        Args:
        ----
            article: Article instance to update
            **updates: Fields to update

        Returns:
        -------
            Updated Article instance

        Raises:
        ------
            PermissionDenied: If user doesn't have permission to edit
            ValidationError: If update data is invalid

        """
        if not self._can_edit_article(article):
            raise PermissionDenied(_("You don't have permission to edit this article"))

        from apps.knowledge.models import ArticleRevision

        # Store original values for comparison
        original_content = article.content
        original_title = article.title

        # Prepare updates
        valid_fields = {
            "title",
            "content",
            "summary",
            "difficulty_level",
            "tags",
            "is_public",
            "category",
            "featured_image",
        }
        filtered_updates = {k: v for k, v in updates.items() if k in valid_fields}

        try:
            with transaction.atomic():
                # Update the article
                for field, value in filtered_updates.items():
                    setattr(article, field, value)

                article.updated_by = self.user
                article.save()

                # Create revision if content or title changed
                content_changed = original_content != article.content
                title_changed = original_title != article.title

                if content_changed or title_changed:
                    latest_revision = (
                        ArticleRevision.objects.filter(article=article).order_by("-revision_number").first()
                    )

                    new_revision_number = (latest_revision.revision_number + 1) if latest_revision else 1

                    change_type = "content" if content_changed else "metadata"

                    self._create_revision(
                        article=article,
                        title=article.title,
                        content=article.content,
                        change_type=change_type,
                        change_summary=updates.get("change_summary", _("Article updated")),
                        revision_number=new_revision_number,
                    )

                logger.info(f"Article updated: {article.title} by {self.user.email}")
                return article

        except (ValidationError, ValueError) as e:
            logger.error(f"Error updating article {article.id}: {e!s}")
            raise ValidationError(_("Failed to update article: {error}").format(error=str(e)))

    def delete_article(self, article: "Article", permanent: bool = False) -> bool:
        """Delete or soft-delete an article.

        Args:
        ----
            article: Article to delete
            permanent: Whether to permanently delete (hard delete)

        Returns:
        -------
            True if deleted successfully

        Raises:
        ------
            PermissionDenied: If user doesn't have permission to delete

        """
        if not self._can_delete_article(article):
            raise PermissionDenied(_("You don't have permission to delete this article"))

        try:
            with transaction.atomic():
                if permanent:
                    # Hard delete - remove all related data
                    article_title = article.title
                    article.delete()
                    logger.warning(f"Article permanently deleted: {article_title} by {self.user.email}")
                else:
                    # Soft delete - archive the article
                    self.archive_article(article, notes=_("Article deleted (soft delete)"))

                return True

        except (ValidationError, ValueError) as e:
            logger.error(f"Error deleting article {article.id}: {e!s}")
            raise ValidationError(_("Failed to delete article: {error}").format(error=str(e)))

    # ============================================================================
    # Workflow Management
    # ============================================================================

    def submit_for_review(self, article: "Article", notes: str = "") -> None:
        """Submit an article for review.

        Args:
        ----
            article: Article to submit
            notes: Optional notes for the submission

        Raises:
        ------
            ValidationError: If article is not in draft status
            PermissionDenied: If user doesn't have permission

        """
        if not self._can_edit_article(article):
            raise PermissionDenied(_("You don't have permission to submit this article"))

        if article.status != "draft":
            raise ValidationError(_("Only draft articles can be submitted for review"))

        try:
            with transaction.atomic():
                old_status = article.status
                article.status = "review"
                article.save()

                self._create_workflow_entry(
                    article=article,
                    from_status=old_status,
                    to_status="review",
                    action="submitted",
                    notes=notes or _("Submitted for review"),
                )

                logger.info(f"Article submitted for review: {article.title} by {self.user.email}")

        except (ValidationError, ValueError) as e:
            logger.error(f"Error submitting article {article.id} for review: {e!s}")
            raise ValidationError(_("Failed to submit article for review"))

    def approve_article(self, article: "Article", notes: str = "") -> None:
        """Approve an article for publication.

        Args:
        ----
            article: Article to approve
            notes: Optional approval notes

        Raises:
        ------
            PermissionDenied: If user doesn't have permission to approve
            ValidationError: If article is not ready for approval

        """
        if not self._can_approve_article(article):
            raise PermissionDenied(_("You don't have permission to approve articles"))

        if article.status != "review":
            raise ValidationError(_("Only articles under review can be approved"))

        try:
            with transaction.atomic():
                old_status = article.status
                article.status = "published"
                article.is_published = True
                article.published_at = timezone.now()
                article.approved_by = self.user
                article.save()

                self._create_workflow_entry(
                    article=article,
                    from_status=old_status,
                    to_status="published",
                    action="approved",
                    notes=notes or _("Article approved for publication"),
                )

                logger.info(f"Article approved: {article.title} by {self.user.email}")

        except (ValidationError, ValueError) as e:
            logger.error(f"Error approving article {article.id}: {e!s}")
            raise ValidationError(_("Failed to approve article"))

    def reject_article(self, article: "Article", notes: str = "") -> None:
        """Reject an article and return it to draft status.

        Args:
        ----
            article: Article to reject
            notes: Rejection reason/notes

        Raises:
        ------
            PermissionDenied: If user doesn't have permission to reject
            ValidationError: If article is not under review

        """
        if not self._can_approve_article(article):
            raise PermissionDenied(_("You don't have permission to reject articles"))

        if article.status != "review":
            raise ValidationError(_("Only articles under review can be rejected"))

        if not notes:
            raise ValidationError(_("Rejection reason is required"))

        try:
            with transaction.atomic():
                old_status = article.status
                article.status = "draft"
                article.save()

                self._create_workflow_entry(
                    article=article,
                    from_status=old_status,
                    to_status="draft",
                    action="rejected",
                    notes=notes,
                )

                logger.info(f"Article rejected: {article.title} by {self.user.email}")

        except (ValidationError, ValueError) as e:
            logger.error(f"Error rejecting article {article.id}: {e!s}")
            raise ValidationError(_("Failed to reject article"))

    def archive_article(self, article: "Article", notes: str = "") -> None:
        """Archive an article.

        Args:
        ----
            article: Article to archive
            notes: Optional archival notes

        Raises:
        ------
            PermissionDenied: If user doesn't have permission

        """
        if not self._can_edit_article(article):
            raise PermissionDenied(_("You don't have permission to archive this article"))

        try:
            with transaction.atomic():
                old_status = article.status
                article.status = "archived"
                article.is_published = False
                article.archived_at = timezone.now()
                article.archived_by = self.user
                article.save()

                self._create_workflow_entry(
                    article=article,
                    from_status=old_status,
                    to_status="archived",
                    action="archived",
                    notes=notes or _("Article archived"),
                )

                logger.info(f"Article archived: {article.title} by {self.user.email}")

        except (ValidationError, ValueError) as e:
            logger.error(f"Error archiving article {article.id}: {e!s}")
            raise ValidationError(_("Failed to archive article"))

    def restore_article(self, article: "Article", notes: str = "") -> None:
        """Restore an archived article to draft status.

        Args:
        ----
            article: Article to restore
            notes: Optional restoration notes

        Raises:
        ------
            PermissionDenied: If user doesn't have permission
            ValidationError: If article is not archived

        """
        if not self._can_edit_article(article):
            raise PermissionDenied(_("You don't have permission to restore this article"))

        if article.status != "archived":
            raise ValidationError(_("Only archived articles can be restored"))

        try:
            with transaction.atomic():
                old_status = article.status
                article.status = "draft"
                article.archived_at = None
                article.archived_by = None
                article.save()

                self._create_workflow_entry(
                    article=article,
                    from_status=old_status,
                    to_status="draft",
                    action="restored",
                    notes=notes or _("Article restored from archive"),
                )

                logger.info(f"Article restored: {article.title} by {self.user.email}")

        except (ValidationError, ValueError) as e:
            logger.error(f"Error restoring article {article.id}: {e!s}")
            raise ValidationError(_("Failed to restore article"))

    # ============================================================================
    # Comment Management
    # ============================================================================

    def add_comment(self, article: "Article", content: str, parent_id: str | None = None) -> "ArticleComment":
        """Add a comment to an article.

        Args:
        ----
            article: Article to comment on
            content: Comment content
            parent_id: Optional parent comment ID for replies

        Returns:
        -------
            Created ArticleComment instance

        Raises:
        ------
            ValidationError: If comment content is invalid
            PermissionDenied: If user can't comment on this article

        """
        if not self.user:
            raise PermissionDenied(_("User context required for commenting"))

        if not content.strip():
            raise ValidationError(_("Comment content cannot be empty"))

        from apps.knowledge.models import ArticleComment

        parent_comment = None
        if parent_id:
            try:
                parent_comment = ArticleComment.objects.get(
                    id=parent_id,
                    article=article,
                    organization=self.organization,
                )
            except ArticleComment.DoesNotExist:
                raise ValidationError(_("Parent comment not found"))

        try:
            comment = ArticleComment.objects.create(
                article=article,
                author=self.user,
                organization=self.organization,
                content=content.strip(),
                parent=parent_comment,
                is_approved=True,  # Auto-approve for now
            )

            logger.info(f"Comment added to article {article.title} by {self.user.email}")
            return comment

        except (ValidationError, ValueError) as e:
            logger.error(f"Error adding comment to article {article.id}: {e!s}")
            raise ValidationError(_("Failed to add comment"))

    def moderate_comment(self, comment: "ArticleComment", action: str, notes: str = "") -> Optional["ArticleComment"]:
        """Moderate a comment (approve, flag, delete).

        Args:
        ----
            comment: Comment to moderate
            action: Action to take ('approve', 'flag', 'delete')
            notes: Optional moderation notes

        Returns:
        -------
            Updated comment or None if deleted

        Raises:
        ------
            PermissionDenied: If user doesn't have moderation permission
            ValidationError: If action is invalid

        """
        if not self._can_moderate_comments():
            raise PermissionDenied(_("You don't have permission to moderate comments"))

        valid_actions = ["approve", "flag", "delete"]
        if action not in valid_actions:
            raise ValidationError(_("Invalid moderation action"))

        try:
            with transaction.atomic():
                if action == "approve":
                    comment.is_approved = True
                    comment.is_flagged = False
                    comment.moderated_by = self.user
                    comment.moderated_at = timezone.now()
                    comment.save()

                elif action == "flag":
                    comment.is_flagged = True
                    comment.is_approved = False
                    comment.moderated_by = self.user
                    comment.moderated_at = timezone.now()
                    comment.save()

                elif action == "delete":
                    comment_id = comment.id
                    comment.delete()
                    logger.info(f"Comment {comment_id} deleted by {self.user.email}")
                    return None

                logger.info(f"Comment {comment.id} moderated ({action}) by {self.user.email}")
                return comment

        except (ValidationError, ValueError) as e:
            logger.error(f"Error moderating comment {comment.id}: {e!s}")
            raise ValidationError(_("Failed to moderate comment"))

    # ============================================================================
    # Attachment Management
    # ============================================================================

    def add_attachment(self, article: "Article", file_obj, name: str, description: str = "") -> "ArticleAttachment":
        """Add an attachment to an article.

        Args:
        ----
            article: Article to attach file to
            file_obj: File object or path
            name: Attachment name
            description: Optional description

        Returns:
        -------
            Created ArticleAttachment instance

        Raises:
        ------
            ValidationError: If file is invalid or too large
            PermissionDenied: If user can't add attachments

        """
        if not self._can_edit_article(article):
            raise PermissionDenied(_("You don't have permission to add attachments"))

        from apps.knowledge.models import ArticleAttachment

        # Validate file size
        max_file_size = getattr(settings, "KNOWLEDGE_MAX_ATTACHMENT_SIZE", 10 * 1024 * 1024)  # 10MB default

        if hasattr(file_obj, "size") and file_obj.size > max_file_size:
            raise ValidationError(_("File size exceeds maximum allowed size"))

        try:
            # Generate unique filename
            file_path = self._generate_attachment_path(article, name)

            # Save file
            if hasattr(file_obj, "read"):
                # File object
                saved_path = default_storage.save(file_path, file_obj)
                file_size = file_obj.size
            else:
                # File path
                with open(file_obj, "rb") as f:
                    content = f.read()
                    saved_path = default_storage.save(file_path, ContentFile(content))
                    file_size = len(content)

            # Create attachment record
            attachment = ArticleAttachment.objects.create(
                article=article,
                organization=self.organization,
                name=name,
                file_path=saved_path,
                file_size=file_size,
                mime_type=self._get_mime_type(name),
                description=description,
                uploaded_by=self.user,
            )

            logger.info(f"Attachment added to article {article.title}: {name}")
            return attachment

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.error(f"Error adding attachment to article {article.id}: {e!s}")
            raise ValidationError(_("Failed to add attachment"))

    def remove_attachment(self, attachment: "ArticleAttachment") -> bool:
        """Remove an attachment from an article.

        Args:
        ----
            attachment: Attachment to remove

        Returns:
        -------
            True if removed successfully

        Raises:
        ------
            PermissionDenied: If user doesn't have permission

        """
        if not self._can_edit_article(attachment.article):
            raise PermissionDenied(_("You don't have permission to remove attachments"))

        try:
            # Delete file from storage
            if attachment.file_path and default_storage.exists(attachment.file_path):
                default_storage.delete(attachment.file_path)

            # Delete record
            attachment_name = attachment.name
            attachment.delete()

            logger.info(f"Attachment removed: {attachment_name}")
            return True

        except (FileNotFoundError, PermissionError, OSError) as e:
            logger.error(f"Error removing attachment {attachment.id}: {e!s}")
            raise ValidationError(_("Failed to remove attachment"))

    # ============================================================================
    # Analytics and Statistics
    # ============================================================================

    def get_article_statistics(self, article: "Article") -> dict[str, Any]:
        """Get comprehensive statistics for an article.

        Args:
        ----
            article: Article to get statistics for

        Returns:
        -------
            Dictionary containing article statistics

        """
        from apps.knowledge.models import (
            ArticleAttachment,
            ArticleComment,
            ArticleRevision,
            ArticleView,
            ArticleVote,
        )

        try:
            # Basic counts
            view_count = ArticleView.objects.filter(article=article).count()
            comment_count = ArticleComment.objects.filter(article=article, is_approved=True).count()
            revision_count = ArticleRevision.objects.filter(article=article).count()
            attachment_count = ArticleAttachment.objects.filter(article=article).count()

            # Vote statistics
            votes = ArticleVote.objects.filter(article=article)
            helpful_votes = votes.filter(is_helpful=True).count()
            not_helpful_votes = votes.filter(is_helpful=False).count()
            total_votes = helpful_votes + not_helpful_votes

            helpfulness_ratio = (helpful_votes / total_votes) if total_votes > 0 else 0.0

            # Recent activity (last 30 days)
            recent_date = timezone.now() - timedelta(days=30)
            recent_views = ArticleView.objects.filter(article=article, created_at__gte=recent_date).count()
            recent_comments = ArticleComment.objects.filter(
                article=article,
                created_at__gte=recent_date,
                is_approved=True,
            ).count()

            return {
                "view_count": view_count,
                "comment_count": comment_count,
                "revision_count": revision_count,
                "attachment_count": attachment_count,
                "helpful_votes": helpful_votes,
                "not_helpful_votes": not_helpful_votes,
                "total_votes": total_votes,
                "helpfulness_ratio": round(helpfulness_ratio, 2),
                "recent_views": recent_views,
                "recent_comments": recent_comments,
                "last_viewed_at": ArticleView.objects.filter(article=article).order_by("-created_at").first(),
                "last_commented_at": ArticleComment.objects.filter(article=article).order_by("-created_at").first(),
            }

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"Error getting statistics for article {article.id}: {e!s}")
            return {}

    def get_workflow_history(self, article: "Article") -> list["ArticleWorkflow"]:
        """Get workflow history for an article.

        Args:
        ----
            article: Article to get workflow history for

        Returns:
        -------
            List of ArticleWorkflow entries ordered by creation date

        """
        from apps.knowledge.models import ArticleWorkflow

        return list(
            ArticleWorkflow.objects.filter(article=article).select_related("performed_by").order_by("-created_at"),
        )

    def get_organization_statistics(self) -> dict[str, Any]:
        """Get comprehensive statistics for the organization.

        Returns
        -------
            Dictionary containing organization-wide article statistics

        """
        from apps.knowledge.models import Article, ArticleComment, ArticleView

        try:
            articles = Article.objects.filter(organization=self.organization)

            # Status breakdown
            status_counts = {}
            for status, _ in Article.STATUS_CHOICES:
                status_counts[status] = articles.filter(status=status).count()

            # Category breakdown
            category_stats = articles.values("category__name").annotate(count=Count("id")).order_by("-count")[:10]

            # Author breakdown
            author_stats = (
                articles.values("author__email", "author__first_name", "author__last_name")
                .annotate(count=Count("id"))
                .order_by("-count")[:10]
            )

            # Recent activity (last 30 days)
            recent_date = timezone.now() - timedelta(days=30)
            recent_articles = articles.filter(created_at__gte=recent_date).count()
            recent_views = ArticleView.objects.filter(
                article__organization=self.organization,
                created_at__gte=recent_date,
            ).count()
            recent_comments = ArticleComment.objects.filter(
                article__organization=self.organization,
                created_at__gte=recent_date,
            ).count()

            return {
                "total_articles": articles.count(),
                "published_articles": status_counts.get("published", 0),
                "draft_articles": status_counts.get("draft", 0),
                "review_articles": status_counts.get("review", 0),
                "archived_articles": status_counts.get("archived", 0),
                "status_breakdown": status_counts,
                "category_breakdown": list(category_stats),
                "author_breakdown": list(author_stats),
                "recent_articles": recent_articles,
                "recent_views": recent_views,
                "recent_comments": recent_comments,
            }

        except (AttributeError, KeyError, ValueError, TypeError) as e:
            logger.error(f"Error getting organization statistics: {e!s}")
            return {}

    # ============================================================================
    # Helper Methods
    # ============================================================================

    def _create_revision(
        self,
        article: "Article",
        title: str,
        content: str,
        change_type: str,
        change_summary: str,
        revision_number: int,
    ) -> "ArticleRevision":
        """Create a new article revision."""
        from apps.knowledge.models import ArticleRevision

        return ArticleRevision.objects.create(
            article=article,
            organization=self.organization,
            title=title,
            content=content,
            changed_by=self.user,
            change_type=change_type,
            change_summary=change_summary,
            revision_number=revision_number,
        )

    def _create_workflow_entry(
        self,
        article: "Article",
        from_status: str,
        to_status: str,
        action: str,
        notes: str,
    ) -> "ArticleWorkflow":
        """Create a new workflow entry."""
        from apps.knowledge.models import ArticleWorkflow

        return ArticleWorkflow.objects.create(
            article=article,
            organization=self.organization,
            from_status=from_status,
            to_status=to_status,
            action=action,
            performed_by=self.user,
            notes=notes,
        )

    def _generate_slug(self, title: str, category: "ArticleCategory") -> str:
        """Generate a unique slug for an article."""
        from django.utils.text import slugify

        from apps.knowledge.models import Article

        base_slug = slugify(title)[:50]
        slug = base_slug
        counter = 1

        while Article.objects.filter(organization=self.organization, slug=slug).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1

        return slug

    def _generate_attachment_path(self, article: "Article", filename: str) -> str:
        """Generate a unique path for an article attachment."""
        import uuid

        from django.utils.text import slugify

        clean_filename = slugify(os.path.splitext(filename)[0])
        ext = os.path.splitext(filename)[1]
        unique_id = str(uuid.uuid4())[:8]

        return f"knowledge/articles/{article.id}/attachments/{clean_filename}_{unique_id}{ext}"

    def _get_mime_type(self, filename: str) -> str:
        """Get MIME type for a file."""
        mime_type, _ = mimetypes.guess_type(filename)
        return mime_type or "application/octet-stream"

    def _can_edit_article(self, article: "Article") -> bool:
        """Check if user can edit an article."""
        if not self.user:
            return False

        # Organization check
        if article.organization != self.organization:
            return False

        # Author can always edit their own articles
        if article.author == self.user:
            return True

        # Check user roles (this would depend on your role system)
        # For now, allow users with staff status
        return self.user.is_staff

    def _can_delete_article(self, article: "Article") -> bool:
        """Check if user can delete an article."""
        if not self.user:
            return False

        # Same permissions as editing for now
        return self._can_edit_article(article)

    def _can_approve_article(self, article: "Article") -> bool:
        """Check if user can approve articles."""
        if not self.user:
            return False

        # Organization check
        if article.organization != self.organization:
            return False

        # Only staff/managers can approve
        return self.user.is_staff

    def _can_moderate_comments(self) -> bool:
        """Check if user can moderate comments."""
        if not self.user:
            return False

        # Only staff can moderate comments
        return self.user.is_staff


# ============================================================================
# Factory Functions
# ============================================================================


def get_article_management_service(
    user: User | None = None,
    organization: Organization | None = None,
) -> ArticleManagementService:
    """Factory function to get an article management service instance.

    Args:
    ----
        user: User context for the service
        organization: Organization context (will be inferred from user if not provided)

    Returns:
    -------
        ArticleManagementService instance

    """
    return ArticleManagementService(user=user, organization=organization)


def create_article_management_service(**kwargs) -> ArticleManagementService:
    """Alternative factory function with keyword arguments.

    Returns
    -------
        ArticleManagementService instance

    """
    return ArticleManagementService(**kwargs)
