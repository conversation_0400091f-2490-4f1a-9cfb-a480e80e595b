"""Comprehensive test suite for knowledge management app

Tests for models, views, services, search functionality, collaboration features,
analytics, HTMX interactions, and performance with Django 5.2 compliance
and organization-based multi-tenancy.
"""

import time
from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import IntegrityError
from django.test import TestCase, TransactionTestCase
from django.urls import reverse

import pytest

from apps.authentication.models import Organization

User = get_user_model()


# ============================================================================
# Test Mixins and Base Classes
# ============================================================================


class KnowledgeTestMixin:
    """Common test utilities for knowledge app tests."""

    @classmethod
    def setUpTestData(cls):
        """Create test data for knowledge tests."""
        cls.organization = Organization.objects.create(
            name="Test Knowledge Org",
            slug="test-knowledge-org",
            description="Test organization for knowledge management",
        )

        cls.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Knowledge",
            last_name="User",
            organization=cls.organization,
        )

        cls.admin_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Admin",
            last_name="User",
            organization=cls.organization,
            is_staff=True,
        )

    def setUp(self):
        """Set up for each test."""
        cache.clear()

    def create_test_category(self, name="Test Category", parent=None):
        """Create a test article category."""
        from .models import ArticleCategory

        return ArticleCategory.objects.create(
            name=name,
            description=f"Test category: {name}",
            slug=name.lower().replace(" ", "-"),
            organization=self.organization,
            parent=parent,
            order=1,
        )

    def create_test_article(self, title="Test Article", category=None, author=None):
        """Create a test knowledge article."""
        from .models import Article

        if category is None:
            category = self.create_test_category()

        if author is None:
            author = self.user

        return Article.objects.create(
            title=title,
            content=f"Test content for {title}",
            summary=f"Test summary for {title}",
            category=category,
            author=author,
            organization=self.organization,
            status="published",
            is_published=True,
            difficulty_level="beginner",
            tags=["test", "knowledge"],
        )


# ============================================================================
# Model Tests
# ============================================================================


class ArticleCategoryModelTest(KnowledgeTestMixin, TestCase):
    """Test ArticleCategory model functionality."""

    def test_category_creation(self):
        """Test basic category creation."""
        category = self.create_test_category("Programming")

        assert category.name == "Programming"
        assert category.slug == "programming"
        assert category.organization == self.organization
        assert category.parent is None

    def test_hierarchical_categories(self):
        """Test parent-child category relationships."""
        parent = self.create_test_category("Technology")
        child = self.create_test_category("Python", parent=parent)

        assert child.parent == parent
        assert child in parent.subcategories.all()

    def test_category_full_path(self):
        """Test full path generation for nested categories."""
        parent = self.create_test_category("Technology")
        child = self.create_test_category("Python", parent=parent)
        grandchild = self.create_test_category("Django", parent=child)

        assert grandchild.get_full_path() == "Technology > Python > Django"

    def test_organization_constraint(self):
        """Test organization-based unique constraint."""
        self.create_test_category("Unique Category")

        # Should raise IntegrityError for duplicate slug in same organization
        with pytest.raises(IntegrityError):
            self.create_test_category("Unique Category")


class ArticleModelTest(KnowledgeTestMixin, TestCase):
    """Test Article model functionality."""

    def test_article_creation(self):
        """Test basic article creation."""
        article = self.create_test_article("Django Best Practices")

        assert article.title == "Django Best Practices"
        assert article.author == self.user
        assert article.organization == self.organization
        assert article.status == "published"
        assert article.is_published

    def test_article_slug_generation(self):
        """Test automatic slug generation."""
        article = self.create_test_article("How to Test Django Apps")

        expected_slug = "how-to-test-django-apps"
        assert article.slug == expected_slug

    def test_article_view_tracking(self):
        """Test article view count functionality."""
        from .models import ArticleView

        article = self.create_test_article()

        # Create article view
        ArticleView.objects.create(
            article=article,
            user=self.user,
            organization=self.organization,
            ip_address="***********",
            user_agent="Test Browser",
        )

        assert article.articleview_set.count() == 1

    def test_article_voting(self):
        """Test article voting functionality."""
        from .models import ArticleVote

        article = self.create_test_article()

        # Create helpful vote
        vote = ArticleVote.objects.create(
            article=article,
            user=self.user,
            organization=self.organization,
            is_helpful=True,
            feedback="Very helpful article!",
        )

        assert vote.is_helpful
        assert article.articlevote_set.count() == 1

    def test_article_status_choices(self):
        """Test article status validation."""
        valid_statuses = ["draft", "review", "published", "archived"]

        for status in valid_statuses:
            article = self.create_test_article(f"Article {status}")
            article.status = status
            article.save()
            assert article.status == status


class SearchModelsTest(KnowledgeTestMixin, TestCase):
    """Test search-related models."""

    def test_saved_search_creation(self):
        """Test saved search functionality."""
        from .models_search import SavedKnowledgeSearch

        saved_search = SavedKnowledgeSearch.objects.create(
            name="Django Articles",
            description="All articles about Django",
            query="django framework",
            search_type="simple",
            creator=self.user,
            organization=self.organization,
            filters={"category": "programming"},
            sort_criteria="relevance",
        )

        assert saved_search.name == "Django Articles"
        assert saved_search.search_type == "simple"
        assert saved_search.usage_count == 0

    def test_search_analytics_creation(self):
        """Test search analytics tracking."""
        from .models_search import KnowledgeSearchAnalytics

        analytics = KnowledgeSearchAnalytics.objects.create(
            user=self.user,
            organization=self.organization,
            query="test search",
            search_type="simple",
            results_count=5,
            search_time_ms=150,
            filters_applied={"category": "test"},
        )

        assert analytics.query == "test search"
        assert analytics.results_count == 5
        assert analytics.search_time_ms == 150


# ============================================================================
# Service Tests
# ============================================================================


class ArticleManagementServiceTest(KnowledgeTestMixin, TestCase):
    """Test ArticleManagementService functionality."""

    def setUp(self):
        super().setUp()
        from .services.article_management_service import ArticleManagementService

        self.service = ArticleManagementService(user=self.user, organization=self.organization)

    def test_create_article(self):
        """Test article creation through service."""
        category = self.create_test_category()

        article = self.service.create_article(
            title="Service Test Article",
            content="Article created through service",
            category_id=str(category.id),
            summary="Test summary",
            difficulty_level="intermediate",
        )

        assert article.title == "Service Test Article"
        assert article.author == self.user
        assert article.status == "draft"

    def test_update_article(self):
        """Test article updates through service."""
        article = self.create_test_article()

        updated_article = self.service.update_article(
            article,
            title="Updated Title",
            content="Updated content",
        )

        assert updated_article.title == "Updated Title"

    def test_article_workflow(self):
        """Test article workflow transitions."""
        article = self.create_test_article()
        article.status = "draft"
        article.save()

        # Submit for review
        self.service.submit_for_review(article, "Ready for review")
        assert article.status == "review"

        # Approve article (using admin user)
        admin_service = self.service.__class__(user=self.admin_user, organization=self.organization)
        admin_service.approve_article(article, "Approved for publication")
        assert article.status == "published"

    def test_article_statistics(self):
        """Test article statistics generation."""
        article = self.create_test_article()

        # Create some test data
        from .models import ArticleView, ArticleVote

        ArticleView.objects.create(
            article=article,
            user=self.user,
            organization=self.organization,
            ip_address="***********",
        )

        ArticleVote.objects.create(
            article=article,
            user=self.user,
            organization=self.organization,
            is_helpful=True,
        )

        stats = self.service.get_article_statistics(article)

        assert stats["view_count"] == 1
        assert stats["helpful_votes"] == 1
        assert stats["total_votes"] == 1


class KnowledgeSearchServiceTest(KnowledgeTestMixin, TestCase):
    """Test knowledge search functionality."""

    def setUp(self):
        super().setUp()
        from .services.knowledge_search_service import KnowledgeSearchService

        self.search_service = KnowledgeSearchService(user=self.user, organization=self.organization)

    def test_basic_search(self):
        """Test basic article search."""
        # Create test articles
        self.create_test_article("Django Testing Guide")
        self.create_test_article("Python Best Practices")

        results = self.search_service.search_articles(
            query="django",
            search_type="simple",
        )

        assert isinstance(results, dict)
        assert "articles" in results
        assert "total_count" in results

    def test_advanced_search(self):
        """Test advanced search with filters."""
        category = self.create_test_category("Programming")
        self.create_test_article("Django Article", category=category)

        results = self.search_service.search_articles(
            query="django",
            search_type="advanced",
            filters={"category": str(category.id)},
        )

        assert isinstance(results, dict)

    def test_save_search(self):
        """Test saving search queries."""
        saved_search = self.search_service.save_search(
            name="Django Searches",
            query="django",
            search_type="simple",
            filters={},
        )

        assert saved_search.name == "Django Searches"
        assert saved_search.creator == self.user


# ============================================================================
# View Tests
# ============================================================================


class KnowledgeViewsTest(KnowledgeTestMixin, TestCase):
    """Test knowledge app views."""

    def test_knowledge_dashboard_view(self):
        """Test knowledge dashboard access."""
        self.client.login(email="<EMAIL>", password="testpass123")

        url = reverse("knowledge:dashboard")
        response = self.client.get(url)

        assert response.status_code == 200
        self.assertContains(response, "Knowledge Dashboard")

    def test_article_list_view(self):
        """Test article list view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create test articles
        self.create_test_article("Test Article 1")
        self.create_test_article("Test Article 2")

        url = reverse("knowledge:article-list")
        response = self.client.get(url)

        assert response.status_code == 200
        self.assertContains(response, "Test Article 1")
        self.assertContains(response, "Test Article 2")

    def test_article_detail_view(self):
        """Test article detail view."""
        self.client.login(email="<EMAIL>", password="testpass123")

        article = self.create_test_article("Detailed Article")

        url = reverse("knowledge:article-detail", kwargs={"pk": article.id})
        response = self.client.get(url)

        assert response.status_code == 200
        self.assertContains(response, "Detailed Article")

    def test_unauthorized_access(self):
        """Test unauthorized access to knowledge views."""
        url = reverse("knowledge:dashboard")
        response = self.client.get(url)

        # Should redirect to login
        assert response.status_code == 302


class HTMXViewsTest(KnowledgeTestMixin, TestCase):
    """Test HTMX-specific views."""

    def setUp(self):
        super().setUp()
        self.client.login(email="<EMAIL>", password="testpass123")

    def test_htmx_search_view(self):
        """Test HTMX search endpoint."""
        self.create_test_article("Searchable Article")

        url = reverse("knowledge:search-htmx")
        response = self.client.get(
            url,
            {"q": "searchable"},
            HTTP_HX_REQUEST="true",
        )

        assert response.status_code == 200
        self.assertContains(response, "Searchable Article")

    def test_htmx_article_vote(self):
        """Test HTMX article voting."""
        article = self.create_test_article("Votable Article")

        url = reverse("knowledge:article-vote-htmx", kwargs={"article_id": article.id})
        response = self.client.post(
            url,
            {"is_helpful": "true", "feedback": "Great article!"},
            HTTP_HX_REQUEST="true",
        )

        assert response.status_code == 200

        # Check vote was created
        from .models import ArticleVote

        vote = ArticleVote.objects.filter(article=article, user=self.user).first()
        assert vote is not None
        assert vote.is_helpful

    def test_htmx_category_filter(self):
        """Test HTMX category filtering."""
        category = self.create_test_category("Filter Category")
        self.create_test_article("Filtered Article", category=category)

        url = reverse("knowledge:category-filter-htmx")
        response = self.client.get(
            url,
            {"category": str(category.id)},
            HTTP_HX_REQUEST="true",
        )

        assert response.status_code == 200
        self.assertContains(response, "Filtered Article")


# ============================================================================
# Performance Tests
# ============================================================================


class KnowledgePerformanceTest(KnowledgeTestMixin, TransactionTestCase):
    """Test performance of knowledge operations."""

    def test_bulk_article_creation(self):
        """Test performance with many articles."""
        from .models import Article

        category = self.create_test_category()

        # Create 100 articles
        articles = []
        for i in range(100):
            articles.append(
                Article(
                    title=f"Article {i}",
                    content=f"Content for article {i}",
                    category=category,
                    author=self.user,
                    organization=self.organization,
                    status="published",
                ),
            )

        Article.objects.bulk_create(articles)

        # Test search performance
        from .services.knowledge_search_service import KnowledgeSearchService

        search_service = KnowledgeSearchService(user=self.user, organization=self.organization)

        import time

        start_time = time.time()
        results = search_service.search_articles(query="article")
        search_time = time.time() - start_time

        # Search should complete in under 1 second
        assert search_time < 1.0
        assert results["total_count"] > 0

    def test_search_index_performance(self):
        """Test search index performance."""
        # Create articles with varying content
        articles_data = [
            ("Python Programming", "Complete guide to Python programming"),
            ("Django Framework", "Web development with Django framework"),
            ("Testing Strategies", "Comprehensive testing for Python applications"),
            ("Database Design", "Designing efficient database schemas"),
            ("API Development", "Building RESTful APIs with Django REST Framework"),
        ]

        for title, _content in articles_data:
            self.create_test_article(title)

        from .services.knowledge_search_service import KnowledgeSearchService

        search_service = KnowledgeSearchService(user=self.user, organization=self.organization)

        # Test multiple search queries
        queries = ["python", "django", "testing", "database", "api"]

        for query in queries:
            start_time = time.time()
            search_service.search_articles(query=query)
            search_time = time.time() - start_time

            # Each search should complete quickly
            assert search_time < 0.5


# ============================================================================
# Integration Tests
# ============================================================================


class KnowledgeIntegrationTest(KnowledgeTestMixin, TestCase):
    """Test integration between knowledge components."""

    def test_article_workflow_integration(self):
        """Test complete article workflow."""
        self.client.login(email="<EMAIL>", password="testpass123")

        category = self.create_test_category()

        # Create article through web interface
        create_url = reverse("knowledge:article-create")
        response = self.client.post(
            create_url,
            {
                "title": "Integration Test Article",
                "content": "Content for integration testing",
                "summary": "Test summary",
                "category": str(category.id),
                "difficulty_level": "beginner",
                "tags": ["integration", "test"],
            },
        )

        # Check article was created
        from .models import Article

        article = Article.objects.filter(title="Integration Test Article").first()
        assert article is not None

        # Test article view tracking
        detail_url = reverse("knowledge:article-detail", kwargs={"pk": article.id})
        response = self.client.get(detail_url)
        assert response.status_code == 200

        # Check view was recorded
        from .models import ArticleView

        view = ArticleView.objects.filter(article=article, user=self.user).first()
        assert view is not None

    def test_search_and_analytics_integration(self):
        """Test search functionality with analytics tracking."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create searchable content
        self.create_test_article("Django Tutorial")
        self.create_test_article("Python Guide")

        # Perform search
        search_url = reverse("knowledge:search")
        response = self.client.get(search_url, {"q": "django"})

        assert response.status_code == 200
        self.assertContains(response, "Django Tutorial")

        # Check analytics were recorded
        from .models_search import KnowledgeSearchAnalytics

        analytics = KnowledgeSearchAnalytics.objects.filter(
            user=self.user,
            query="django",
        ).first()
        assert analytics is not None

    def test_collaboration_features(self):
        """Test collaboration features integration."""
        self.client.login(email="<EMAIL>", password="testpass123")

        # Create article
        article = self.create_test_article("Collaborative Article")

        # Add comment
        from .models import ArticleComment

        ArticleComment.objects.create(
            article=article,
            author=self.user,
            organization=self.organization,
            content="This is a test comment",
            is_approved=True,
        )

        # View article with comment
        detail_url = reverse("knowledge:article-detail", kwargs={"pk": article.id})
        response = self.client.get(detail_url)

        assert response.status_code == 200
        self.assertContains(response, "This is a test comment")


# ============================================================================
# Security Tests
# ============================================================================


class KnowledgeSecurityTest(KnowledgeTestMixin, TestCase):
    """Test security aspects of knowledge app."""

    def test_organization_isolation(self):
        """Test that users can only access their organization's content."""
        # Create another organization and user
        other_org = Organization.objects.create(
            name="Other Org",
            slug="other-org",
            description="Another organization",
        )

        User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            organization=other_org,
        )

        # Create article in first organization
        article = self.create_test_article("Org 1 Article")

        # Login as user from other organization
        self.client.login(email="<EMAIL>", password="testpass123")

        # Try to access article
        detail_url = reverse("knowledge:article-detail", kwargs={"pk": article.id})
        response = self.client.get(detail_url)

        # Should be forbidden or not found
        assert response.status_code in [403, 404]

    def test_permission_enforcement(self):
        """Test permission enforcement for different user roles."""
        # Test with regular user
        self.client.login(email="<EMAIL>", password="testpass123")

        article = self.create_test_article()

        # Regular user should be able to view
        detail_url = reverse("knowledge:article-detail", kwargs={"pk": article.id})
        response = self.client.get(detail_url)
        assert response.status_code == 200

        # But not access admin features
        admin_url = reverse("knowledge:admin-dashboard")
        response = self.client.get(admin_url)
        assert response.status_code in [302, 403, 404]

    def test_input_validation(self):
        """Test input validation and sanitization."""
        self.client.login(email="<EMAIL>", password="testpass123")

        category = self.create_test_category()

        # Test with malicious input
        create_url = reverse("knowledge:article-create")
        response = self.client.post(
            create_url,
            {
                "title": "<script>alert('xss')</script>",
                "content": "Content with <script>malicious code</script>",
                "category": str(category.id),
                "difficulty_level": "beginner",
            },
        )

        # Check that script tags are handled appropriately
        if response.status_code == 302:  # Redirect on success
            from .models import Article

            article = Article.objects.filter(
                title__icontains="script",
            ).first()

            if article:
                # Script tags should be escaped or removed
                assert "<script>" not in article.title
                assert "<script>" not in article.content


# ============================================================================
# Cache Tests
# ============================================================================


class KnowledgeCacheTest(KnowledgeTestMixin, TestCase):
    """Test caching functionality."""

    def test_search_result_caching(self):
        """Test search result caching."""
        self.create_test_article("Cacheable Article")

        from .services.knowledge_search_service import KnowledgeSearchService

        search_service = KnowledgeSearchService(user=self.user, organization=self.organization)

        # First search
        results1 = search_service.search_articles(query="cacheable")

        # Second search (should use cache)
        results2 = search_service.search_articles(query="cacheable")

        assert results1["total_count"] == results2["total_count"]

    def test_cache_invalidation(self):
        """Test cache invalidation when content changes."""
        article = self.create_test_article("Cache Test Article")

        from .services.knowledge_search_service import KnowledgeSearchService

        search_service = KnowledgeSearchService(user=self.user, organization=self.organization)

        # Search and cache results
        search_service.search_articles(query="cache")

        # Update article
        article.title = "Updated Cache Test Article"
        article.save()

        # Search again (cache should be invalidated)
        results2 = search_service.search_articles(query="updated")

        # Should find the updated article
        assert results2["total_count"] > 0


# ============================================================================
# Signal Tests
# ============================================================================


class KnowledgeSignalTest(KnowledgeTestMixin, TestCase):
    """Test Django signals in knowledge app."""

    def test_article_save_signal(self):
        """Test signals fired on article save."""
        with patch("apps.knowledge.signals.cache.delete") as mock_cache_delete:
            self.create_test_article("Signal Test Article")

            # Cache delete should be called to invalidate caches
            mock_cache_delete.assert_called()

    def test_search_analytics_signal(self):
        """Test signals for search analytics."""
        from .models_search import KnowledgeSearchAnalytics

        # Create search analytics entry
        analytics = KnowledgeSearchAnalytics.objects.create(
            user=self.user,
            organization=self.organization,
            query="signal test",
            search_type="simple",
            results_count=1,
            search_time_ms=100,
        )

        # Check that appropriate signals were fired
        assert analytics.created_at is not None


# ============================================================================
# Management Command Tests
# ============================================================================


class ManagementCommandTest(KnowledgeTestMixin, TestCase):
    """Test knowledge management commands."""

    def test_setup_knowledge_command(self):
        """Test setup_knowledge management command."""
        from io import StringIO

        from django.core.management import call_command

        out = StringIO()
        call_command("setup_knowledge", "--validate-only", stdout=out)

        output = out.getvalue()
        assert "validation" in output.lower()

    def test_setup_knowledge_with_sample_data(self):
        """Test setup command with sample data creation."""
        from io import StringIO

        from django.core.management import call_command

        out = StringIO()
        call_command("setup_knowledge", "--create-sample-data", "--dry-run", stdout=out)

        output = out.getvalue()
        assert "sample" in output.lower()
