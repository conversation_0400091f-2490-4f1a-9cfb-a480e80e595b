"""Test cases for realtime app views.

This module provides comprehensive test coverage for the realtime app views:
- RealtimeDashboardView: Central dashboard for real-time features
- WebSocketTestView: WebSocket connection testing interface
- NotificationListView: User notifications display
- WebRTCRoomView: WebRTC room interface
- NotificationSSEView: Server-Sent Events for notifications
- UpdateSSEView: Server-Sent Events for updates

Test Coverage Areas:
- View rendering and template usage
- Authentication and permission checks
- HTTP response handling
- SSE stream functionality
- Error handling and edge cases
- Security validation
"""

from unittest.mock import patch

from django.contrib.auth.models import AnonymousUser
from django.http import StreamingHttpResponse
from django.test import RequestFactory, TestCase

from apps.authentication.models import User
from apps.realtime.models import ConnectionStats, UserPresence
from apps.realtime.views import (
    NotificationListView,
    NotificationSSEView,
    RealtimeDashboardView,
    UpdateSSEView,
    WebRTCRoomView,
    WebSocketTestView,
)


class BaseRealtimeViewTest(TestCase):
    """Base class for realtime view tests."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
        )

        # Create user presence
        self.user_presence = UserPresence.objects.create(user=self.user, status="online", is_online=True)

        # Create some connection stats
        self.connection_stats = ConnectionStats.objects.create(
            user=self.user,
            session_id="test-session-123",
            connection_type="websocket",
            status="connected",
        )

    def create_authenticated_request(self, method="GET", path="/", data=None):
        """Create an authenticated request."""
        if method.upper() == "GET":
            request = self.factory.get(path, data or {})
        elif method.upper() == "POST":
            request = self.factory.post(path, data or {})
        else:
            request = getattr(self.factory, method.lower())(path, data or {})

        request.user = self.user
        return request

    def create_unauthenticated_request(self, method="GET", path="/", data=None):
        """Create an unauthenticated request."""
        if method.upper() == "GET":
            request = self.factory.get(path, data or {})
        elif method.upper() == "POST":
            request = self.factory.post(path, data or {})
        else:
            request = getattr(self.factory, method.lower())(path, data or {})

        request.user = AnonymousUser()
        return request


class RealtimeDashboardViewTest(BaseRealtimeViewTest):
    """Test cases for RealtimeDashboardView."""

    def test_dashboard_view_authenticated_access(self):
        """Test dashboard view with authenticated user."""
        request = self.create_authenticated_request()
        view = RealtimeDashboardView()
        view.setup(request)

        response = view.get(request)

        self.assertEqual(response.status_code, 200)
        self.assertIn("realtime/dashboard.html", [t.name for t in response.templates])

    def test_dashboard_view_unauthenticated_redirect(self):
        """Test dashboard view redirects unauthenticated users."""
        request = self.create_unauthenticated_request()
        view = RealtimeDashboardView()
        view.setup(request)

        response = view.dispatch(request)

        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_dashboard_view_context_data(self):
        """Test dashboard view context data."""
        request = self.create_authenticated_request()
        view = RealtimeDashboardView()
        view.setup(request)

        response = view.get(request)
        context = response.context_data

        # Should include user presence and connection stats
        self.assertIn("user_presence", context)
        self.assertIn("connection_stats", context)
        self.assertIn("online_users_count", context)
        self.assertIn("active_connections_count", context)

    def test_dashboard_view_with_stats(self):
        """Test dashboard view includes statistics."""
        # Create additional test data
        user2 = User.objects.create_user(email="<EMAIL>", password="testpass123")
        UserPresence.objects.create(user=user2, status="busy", is_online=True)

        request = self.create_authenticated_request()
        view = RealtimeDashboardView()
        view.setup(request)

        response = view.get(request)
        context = response.context_data

        self.assertGreaterEqual(context["online_users_count"], 2)
        self.assertGreaterEqual(context["active_connections_count"], 1)


class WebSocketTestViewTest(BaseRealtimeViewTest):
    """Test cases for WebSocketTestView."""

    def test_websocket_test_view_authenticated_access(self):
        """Test WebSocket test view with authenticated user."""
        request = self.create_authenticated_request()
        view = WebSocketTestView()
        view.setup(request)

        response = view.get(request)

        self.assertEqual(response.status_code, 200)
        self.assertIn("realtime/websocket_test.html", [t.name for t in response.templates])

    def test_websocket_test_view_context_includes_urls(self):
        """Test WebSocket test view includes WebSocket URLs."""
        request = self.create_authenticated_request()
        view = WebSocketTestView()
        view.setup(request)

        response = view.get(request)
        context = response.context_data

        # Should include WebSocket endpoint URLs
        self.assertIn("notification_ws_url", context)
        self.assertIn("update_ws_url", context)
        self.assertIn("webrtc_ws_url", context)

    def test_websocket_test_view_debug_info(self):
        """Test WebSocket test view includes debug information."""
        request = self.create_authenticated_request()
        view = WebSocketTestView()
        view.setup(request)

        response = view.get(request)
        context = response.context_data

        # Should include debug/testing utilities
        self.assertIn("user", context)
        self.assertIn("session_key", context)


class NotificationListViewTest(BaseRealtimeViewTest):
    """Test cases for NotificationListView."""

    def test_notification_list_view_authenticated_access(self):
        """Test notification list view with authenticated user."""
        request = self.create_authenticated_request()
        view = NotificationListView()
        view.setup(request)

        response = view.get(request)

        self.assertEqual(response.status_code, 200)
        self.assertIn("realtime/notification_list.html", [t.name for t in response.templates])

    def test_notification_list_view_user_specific(self):
        """Test notification list view shows only user's notifications."""
        # Create notifications for different users would go here
        # For now, test that the view filters by user

        request = self.create_authenticated_request()
        view = NotificationListView()
        view.setup(request)

        response = view.get(request)
        context = response.context_data

        # Should filter notifications for current user
        # In a real implementation, this would check notification objects
        self.assertIn("notifications", context)

    def test_notification_list_view_pagination(self):
        """Test notification list view pagination."""
        request = self.create_authenticated_request(data={"page": "1"})
        view = NotificationListView()
        view.setup(request)

        response = view.get(request)

        # Should handle pagination
        self.assertEqual(response.status_code, 200)


class WebRTCRoomViewTest(BaseRealtimeViewTest):
    """Test cases for WebRTCRoomView."""

    def test_webrtc_room_view_authenticated_access(self):
        """Test WebRTC room view with authenticated user."""
        request = self.create_authenticated_request()
        view = WebRTCRoomView()
        view.setup(request)

        # Set room name
        view.kwargs = {"room_name": "test_room"}

        response = view.get(request)

        self.assertEqual(response.status_code, 200)
        self.assertIn("realtime/webrtc_room.html", [t.name for t in response.templates])

    def test_webrtc_room_view_context_includes_room_info(self):
        """Test WebRTC room view includes room information."""
        request = self.create_authenticated_request()
        view = WebRTCRoomView()
        view.setup(request)
        view.kwargs = {"room_name": "meeting_room_123"}

        response = view.get(request)
        context = response.context_data

        # Should include room information
        self.assertIn("room_name", context)
        self.assertEqual(context["room_name"], "meeting_room_123")
        self.assertIn("user", context)
        self.assertIn("webrtc_config", context)

    def test_webrtc_room_view_invalid_room_name(self):
        """Test WebRTC room view with invalid room name."""
        request = self.create_authenticated_request()
        view = WebRTCRoomView()
        view.setup(request)
        view.kwargs = {"room_name": "invalid-room!@#$"}

        # Should handle invalid room names gracefully
        response = view.get(request)

        # May return error or sanitized room name
        self.assertIn(response.status_code, [200, 400, 404])


class NotificationSSEViewTest(BaseRealtimeViewTest):
    """Test cases for NotificationSSEView."""

    def test_notification_sse_view_authenticated_access(self):
        """Test notification SSE view with authenticated user."""
        request = self.create_authenticated_request()
        view = NotificationSSEView()
        view.setup(request)

        response = view.get(request)

        self.assertIsInstance(response, StreamingHttpResponse)
        self.assertEqual(response["Content-Type"], "text/event-stream")
        self.assertEqual(response["Cache-Control"], "no-cache")

    def test_notification_sse_view_unauthenticated_access(self):
        """Test notification SSE view rejects unauthenticated users."""
        request = self.create_unauthenticated_request()
        view = NotificationSSEView()
        view.setup(request)

        response = view.dispatch(request)

        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_notification_sse_view_stream_format(self):
        """Test notification SSE view stream format."""
        request = self.create_authenticated_request()
        view = NotificationSSEView()
        view.setup(request)

        response = view.get(request)

        # Get first chunk of stream
        stream_iter = iter(response.streaming_content)
        first_chunk = next(stream_iter, None)

        if first_chunk:
            # Should be valid SSE format
            chunk_str = first_chunk.decode("utf-8") if isinstance(first_chunk, bytes) else first_chunk
            self.assertTrue(chunk_str.startswith("data:") or "event:" in chunk_str)

    @patch("apps.realtime.views.time.sleep")
    def test_notification_sse_view_heartbeat(self, mock_sleep):
        """Test notification SSE view sends heartbeat."""
        request = self.create_authenticated_request()
        view = NotificationSSEView()
        view.setup(request)

        response = view.get(request)

        # Should include heartbeat mechanism
        # The view should send periodic keepalive messages
        self.assertIsInstance(response, StreamingHttpResponse)


class UpdateSSEViewTest(BaseRealtimeViewTest):
    """Test cases for UpdateSSEView."""

    def test_update_sse_view_authenticated_access(self):
        """Test update SSE view with authenticated user."""
        request = self.create_authenticated_request()
        view = UpdateSSEView()
        view.setup(request)

        response = view.get(request)

        self.assertIsInstance(response, StreamingHttpResponse)
        self.assertEqual(response["Content-Type"], "text/event-stream")

    def test_update_sse_view_headers(self):
        """Test update SSE view sets correct headers."""
        request = self.create_authenticated_request()
        view = UpdateSSEView()
        view.setup(request)

        response = view.get(request)

        # Check SSE headers
        self.assertEqual(response["Content-Type"], "text/event-stream")
        self.assertEqual(response["Cache-Control"], "no-cache")
        self.assertEqual(response["Connection"], "keep-alive")

    def test_update_sse_view_stream_content(self):
        """Test update SSE view stream content."""
        request = self.create_authenticated_request()
        view = UpdateSSEView()
        view.setup(request)

        response = view.get(request)

        # Should be a streaming response
        self.assertTrue(hasattr(response, "streaming_content"))


class RealtimeViewIntegrationTest(BaseRealtimeViewTest):
    """Integration tests for realtime views."""

    def test_dashboard_to_websocket_test_flow(self):
        """Test navigation from dashboard to WebSocket test interface."""
        # Access dashboard
        dashboard_request = self.create_authenticated_request()
        dashboard_view = RealtimeDashboardView()
        dashboard_view.setup(dashboard_request)
        dashboard_response = dashboard_view.get(dashboard_request)

        self.assertEqual(dashboard_response.status_code, 200)

        # Access WebSocket test
        test_request = self.create_authenticated_request()
        test_view = WebSocketTestView()
        test_view.setup(test_request)
        test_response = test_view.get(test_request)

        self.assertEqual(test_response.status_code, 200)

    def test_notification_list_to_sse_flow(self):
        """Test notification list view and SSE integration."""
        # Access notification list
        list_request = self.create_authenticated_request()
        list_view = NotificationListView()
        list_view.setup(list_request)
        list_response = list_view.get(list_request)

        self.assertEqual(list_response.status_code, 200)

        # Access notification SSE
        sse_request = self.create_authenticated_request()
        sse_view = NotificationSSEView()
        sse_view.setup(sse_request)
        sse_response = sse_view.get(sse_request)

        self.assertIsInstance(sse_response, StreamingHttpResponse)

    def test_webrtc_room_creation_flow(self):
        """Test WebRTC room creation and access."""
        room_name = "integration_test_room"

        # Access WebRTC room
        room_request = self.create_authenticated_request()
        room_view = WebRTCRoomView()
        room_view.setup(room_request)
        room_view.kwargs = {"room_name": room_name}

        room_response = room_view.get(room_request)

        self.assertEqual(room_response.status_code, 200)
        self.assertEqual(room_response.context_data["room_name"], room_name)


class RealtimeViewSecurityTest(BaseRealtimeViewTest):
    """Security tests for realtime views."""

    def test_all_views_require_authentication(self):
        """Test that all realtime views require authentication."""
        view_classes = [
            RealtimeDashboardView,
            WebSocketTestView,
            NotificationListView,
            NotificationSSEView,
            UpdateSSEView,
        ]

        for view_class in view_classes:
            request = self.create_unauthenticated_request()
            view = view_class()
            view.setup(request)

            response = view.dispatch(request)

            # Should redirect to login (302) or return unauthorized (401/403)
            self.assertIn(
                response.status_code,
                [302, 401, 403],
                f"{view_class.__name__} should require authentication",
            )

    def test_webrtc_room_name_validation(self):
        """Test WebRTC room name validation."""
        malicious_room_names = [
            "../../../etc/passwd",
            "<script>alert('xss')</script>",
            "room'; DROP TABLE users; --",
            "room\x00null",
            "room\n\rinjection",
        ]

        for room_name in malicious_room_names:
            request = self.create_authenticated_request()
            view = WebRTCRoomView()
            view.setup(request)
            view.kwargs = {"room_name": room_name}

            # Should handle malicious room names safely
            try:
                response = view.get(request)
                # Should either sanitize the name or return an error
                self.assertIn(response.status_code, [200, 400, 404])
            except Exception as e:
                # Should not cause unhandled exceptions
                self.fail(f"Room name '{room_name}' caused exception: {e}")

    def test_sse_view_connection_limits(self):
        """Test SSE view connection handling."""
        # This would test connection limits and resource management
        # For now, just ensure multiple connections don't cause errors

        requests = [self.create_authenticated_request() for _ in range(3)]
        views = [NotificationSSEView() for _ in range(3)]

        for request, view in zip(requests, views):
            view.setup(request)
            response = view.get(request)
            self.assertIsInstance(response, StreamingHttpResponse)


class RealtimeViewPerformanceTest(BaseRealtimeViewTest):
    """Performance tests for realtime views."""

    def test_dashboard_view_database_queries(self):
        """Test dashboard view database query optimization."""
        with self.assertNumQueries(4):  # Should be optimized with select_related/prefetch_related
            request = self.create_authenticated_request()
            view = RealtimeDashboardView()
            view.setup(request)
            view.get(request)

    def test_notification_list_pagination_performance(self):
        """Test notification list pagination performance."""
        # Create multiple notifications (would need notification model)
        request = self.create_authenticated_request(data={"page": "1"})
        view = NotificationListView()
        view.setup(request)

        with self.assertNumQueries(3):  # Should use pagination efficiently
            view.get(request)

    def test_sse_view_memory_usage(self):
        """Test SSE view memory efficiency."""
        request = self.create_authenticated_request()
        view = NotificationSSEView()
        view.setup(request)

        response = view.get(request)

        # Should use streaming response to avoid loading everything in memory
        self.assertTrue(hasattr(response, "streaming_content"))
        self.assertIsInstance(response, StreamingHttpResponse)
