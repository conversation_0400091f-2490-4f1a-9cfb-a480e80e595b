/**
 * TypeScript type definitions for the CLEAR API SDK
 */

// Base types
export type ResourceId = string;
export type ISO8601DateTime = string;
export type URLString = string;

// Configuration types
export interface ClearAPIConfig {
  /** Base URL for the CLEAR API */
  baseURL: string;
  /** API key for authentication (optional if using session auth) */
  apiKey?: string;
  /** Username for session authentication (optional) */
  username?: string;
  /** Password for session authentication (optional) */
  password?: string;
  /** Organization slug for multi-tenant operations */
  organization?: string;
  /** API version to use */
  version?: string;
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Maximum number of retry attempts */
  maxRetries?: number;
  /** Backoff factor for retries */
  retryBackoff?: number;
  /** Custom user agent string */
  userAgent?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Filter types
export interface ProjectFilters extends PaginationParams {
  status?: ProjectStatus;
  owner?: ResourceId;
  tags?: string[];
  search?: string;
}

export interface TaskFilters extends PaginationParams {
  projectId?: ResourceId;
  status?: TaskStatus;
  assignee?: ResourceId;
  dueBefore?: ISO8601DateTime;
  search?: string;
}

export interface DocumentFilters extends PaginationParams {
  projectId?: ResourceId;
  documentType?: DocumentType;
  search?: string;
}

export interface TimeEntryFilters extends PaginationParams {
  projectId?: ResourceId;
  taskId?: ResourceId;
  userId?: ResourceId;
  startDate?: ISO8601DateTime;
  endDate?: ISO8601DateTime;
  billable?: boolean;
}

export interface UserFilters extends PaginationParams {
  search?: string;
  isActive?: boolean;
  role?: string;
}

// Enum types
export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  REVIEW = 'review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum DocumentType {
  TECHNICAL_DRAWING = 'technical_drawing',
  SPECIFICATION = 'specification',
  REPORT = 'report',
  CONTRACT = 'contract',
  PERMIT = 'permit',
  PHOTO = 'photo',
  OTHER = 'other',
}

// Model types
export interface BaseModel {
  id: ResourceId;
  createdAt: ISO8601DateTime;
  updatedAt: ISO8601DateTime;
}

export interface User extends BaseModel {
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  isActive: boolean;
  isStaff: boolean;
  isSuperuser: boolean;
  lastLogin: ISO8601DateTime | null;
  dateJoined: ISO8601DateTime;
  organization: string;
  role: string;
  profilePicture: URLString | null;
  phone: string | null;
  timezone: string;
}

export interface Organization extends BaseModel {
  name: string;
  slug: string;
  description: string;
  website: URLString | null;
  logo: URLString | null;
  address: string | null;
  phone: string | null;
  email: string | null;
  isActive: boolean;
  subscriptionPlan: string | null;
  memberCount: number;
}

export interface Project extends BaseModel {
  title: string;
  description: string;
  status: ProjectStatus;
  priority: string;
  location: string | null;
  startDate: ISO8601DateTime | null;
  endDate: ISO8601DateTime | null;
  dueDate: ISO8601DateTime | null;
  budget: number | null;
  budgetCurrency: string;
  progressPercentage: number;
  organization: string;
  owner: User;
  members: User[];
  tags: string[];
  customFields: Record<string, any>;
  geometry: GeoJSON.Geometry | null;
  bbox: [number, number, number, number] | null;
}

export interface Task extends BaseModel {
  title: string;
  description: string;
  status: TaskStatus;
  priority: string;
  startDate: ISO8601DateTime | null;
  dueDate: ISO8601DateTime | null;
  completedAt: ISO8601DateTime | null;
  estimatedHours: number | null;
  actualHours: number | null;
  projectId: ResourceId;
  project: Project | null;
  assignee: User | null;
  reporter: User | null;
  parentTaskId: ResourceId | null;
  dependsOn: ResourceId[];
  blocks: ResourceId[];
  tags: string[];
  customFields: Record<string, any>;
}

export interface Document extends BaseModel {
  title: string;
  description: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  mimeType: string;
  documentType: DocumentType;
  version: string;
  isLatestVersion: boolean;
  fileUrl: URLString;
  downloadUrl: URLString;
  thumbnailUrl: URLString | null;
  projectId: ResourceId;
  project: Project | null;
  uploadedBy: User;
  tags: string[];
  customFields: Record<string, any>;
  reviewStatus: string | null;
  reviewDueDate: ISO8601DateTime | null;
}

export interface TimeEntry extends BaseModel {
  description: string;
  startTime: ISO8601DateTime | null;
  endTime: ISO8601DateTime | null;
  durationHours: number | null;
  billable: boolean;
  hourlyRate: number | null;
  totalAmount: number | null;
  projectId: ResourceId;
  project: Project | null;
  taskId: ResourceId | null;
  task: Task | null;
  user: User;
  workType: string | null;
  category: string | null;
  invoiceId: ResourceId | null;
  billedAt: ISO8601DateTime | null;
}

export interface ApiKey extends BaseModel {
  name: string;
  key?: string; // Only shown during creation
  prefix: string;
  isActive: boolean;
  lastUsedAt: ISO8601DateTime | null;
  expiresAt: ISO8601DateTime | null;
  scopes: string[];
  ipWhitelist: string[];
  requestCount: number;
  rateLimitExceededCount: number;
}

export interface OAuth2Application extends BaseModel {
  name: string;
  clientId: string;
  clientSecret?: string; // Only shown during creation
  clientType: 'confidential' | 'public';
  authorizationGrantType: string;
  redirectUris: string[];
  websiteUrl: URLString | null;
  description: string;
  logoUrl: URLString | null;
  scopes: string[];
  activeTokens: number;
  totalAuthorizations: number;
}

// Request/Response types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  sessionToken?: string;
  csrfToken?: string;
  expiresAt: ISO8601DateTime;
}

export interface ProjectCreateRequest {
  title: string;
  description?: string;
  status?: ProjectStatus;
  priority?: string;
  location?: string;
  startDate?: ISO8601DateTime;
  endDate?: ISO8601DateTime;
  dueDate?: ISO8601DateTime;
  budget?: number;
  budgetCurrency?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  geometry?: GeoJSON.Geometry;
}

export interface TaskCreateRequest {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: string;
  startDate?: ISO8601DateTime;
  dueDate?: ISO8601DateTime;
  estimatedHours?: number;
  projectId: ResourceId;
  assigneeId?: ResourceId;
  parentTaskId?: ResourceId;
  dependsOn?: ResourceId[];
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface DocumentUploadRequest {
  file: File | Blob;
  projectId: ResourceId;
  title?: string;
  description?: string;
  documentType?: DocumentType;
  metadata?: Record<string, any>;
}

export interface TimeEntryCreateRequest {
  description: string;
  startTime?: ISO8601DateTime;
  endTime?: ISO8601DateTime;
  durationHours?: number;
  billable?: boolean;
  hourlyRate?: number;
  projectId: ResourceId;
  taskId?: ResourceId;
  workType?: string;
  category?: string;
}

export interface ApiKeyCreateRequest {
  name: string;
  scopes?: string[];
  expiresAt?: ISO8601DateTime;
  ipWhitelist?: string[];
}

export interface OAuth2ApplicationCreateRequest {
  name: string;
  clientType: 'confidential' | 'public';
  authorizationGrantType: string;
  redirectUris: string[];
  websiteUrl?: URLString;
  description?: string;
  scopes?: string[];
}

// Error types
export interface APIErrorResponse {
  error: string;
  message: string;
  details?: Record<string, any>;
  requestId?: string;
  timestamp: ISO8601DateTime;
}

export interface ValidationErrorDetails {
  field: string;
  message: string;
  code: string;
}

export interface RateLimitErrorDetails {
  limit: number;
  window: string;
  retryAfter: number;
  resetTime: number;
}

// File upload progress callback
export type UploadProgressCallback = (progress: {
  loaded: number;
  total: number;
  percentage: number;
}) => void;

// HTTP request options
export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  onUploadProgress?: UploadProgressCallback;
  signal?: AbortSignal;
}

// Rate limit state
export interface RateLimitState {
  remaining: number | null;
  reset: number | null;
  limit: number | null;
}

// WebSocket types (for future real-time features)
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: ISO8601DateTime;
}

export interface WebSocketOptions {
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  protocols?: string[];
}