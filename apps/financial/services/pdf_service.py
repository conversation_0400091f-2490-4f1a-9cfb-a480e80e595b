"""
PDF generation service for financial documents.

Provides comprehensive PDF generation capabilities for invoices, timesheets,
and financial reports using WeasyPrint for high-quality output.
"""

import logging
from decimal import Decimal
from io import BytesIO
from typing import Dict, List, Optional

from django.conf import settings
from django.http import HttpResponse
from django.template.loader import render_to_string

# WeasyPrint imports with fallback
try:
    from weasyprint import HTML, CSS
    from weasyprint.fonts import FontConfiguration
except ImportError:
    # Fallback if WeasyPrint is not available
    class FontConfiguration:
        def __init__(self):
            pass
    
    class HTML:
        def __init__(self, *args, **kwargs):
            pass
        
        def write_pdf(self, *args, **kwargs):
            return b"PDF generation not available"
    
    class CSS:
        def __init__(self, *args, **kwargs):
            pass
from django.urls import reverse
from django.utils import timezone

from ..models import Invoice, TimeEntry

logger = logging.getLogger(__name__)


class PDFGenerationError(Exception):
    """Custom exception for PDF generation errors."""

    pass


class InvoicePDFGenerator:
    """
    Service class for generating invoice PDFs using WeasyPrint.

    Provides flexible PDF generation with multiple template options,
    custom styling, and optimization for both screen and print output.
    """

    DEFAULT_TEMPLATE = "financial/pdf/invoice_base.html"
    SIMPLE_TEMPLATE = "financial/pdf/invoice_simple.html"

    def __init__(self, invoice: Invoice):
        """
        Initialize PDF generator for an invoice.

        Args:
            invoice: The Invoice instance to generate PDF for
        """
        self.invoice = invoice
        self.font_config = FontConfiguration()

    def generate_pdf(
        self,
        template_name: Optional[str] = None,
        include_line_items: bool = True,
        custom_css: Optional[str] = None,
        attachment: bool = True,
    ) -> HttpResponse:
        """
        Generate PDF for the invoice.

        Args:
            template_name: Custom template to use (defaults to DEFAULT_TEMPLATE)
            include_line_items: Whether to include detailed line items
            custom_css: Additional CSS to apply
            attachment: Whether to serve as download attachment

        Returns:
            HttpResponse with PDF content

        Raises:
            PDFGenerationError: If PDF generation fails
        """
        try:
            # Use default template if none specified
            template_name = template_name or self.DEFAULT_TEMPLATE

            # Prepare context data
            context = self._prepare_context(include_line_items)

            # Render HTML template
            html_content = render_to_string(template_name, context)

            # Generate PDF
            pdf_bytes = self._generate_pdf_bytes(html_content, custom_css)

            # Create HTTP response
            response = self._create_response(pdf_bytes, attachment)

            logger.info(f"Successfully generated PDF for invoice {self.invoice.invoice_number}")

            return response

        except Exception as e:
            logger.error(f"Failed to generate PDF for invoice {self.invoice.invoice_number}: {e}")
            raise PDFGenerationError(f"PDF generation failed: {e}") from e

    def _prepare_context(self, include_line_items: bool) -> Dict:
        """
        Prepare template context for PDF generation.

        Args:
            include_line_items: Whether to include line items

        Returns:
            Dictionary with template context
        """
        context = {
            "invoice": self.invoice,
            "organization": self.invoice.project.organization,
            "project": self.invoice.project,
            "generated_date": timezone.now(),
            "include_line_items": include_line_items,
        }

        if include_line_items:
            # Optimize line items query
            line_items = self.invoice.line_items.select_related(
                "time_entry", "time_entry__user", "time_entry__work_type"
            ).order_by("line_order", "id")
            context["line_items"] = line_items

            # Calculate totals
            context.update(self._calculate_totals(line_items))

        return context

    def _calculate_totals(self, line_items) -> Dict:
        """
        Calculate invoice totals from line items.

        Args:
            line_items: QuerySet of InvoiceLineItem objects

        Returns:
            Dictionary with calculated totals
        """
        subtotal = Decimal("0.00")
        billable_hours = Decimal("0.00")

        for item in line_items:
            if item.is_billable:
                subtotal += item.amount or Decimal("0.00")
                if item.quantity:
                    billable_hours += item.quantity

        tax_amount = self.invoice.tax_amount or Decimal("0.00")
        total_amount = subtotal + tax_amount

        return {
            "calculated_subtotal": subtotal,
            "calculated_tax": tax_amount,
            "calculated_total": total_amount,
            "total_billable_hours": billable_hours,
        }

    def _generate_pdf_bytes(self, html_content: str, custom_css: Optional[str] = None) -> bytes:
        """
        Generate PDF bytes from HTML content.

        Args:
            html_content: HTML string to convert
            custom_css: Optional additional CSS

        Returns:
            PDF content as bytes
        """
        # Create HTML document
        html_doc = HTML(string=html_content, base_url=settings.STATIC_URL, encoding="utf-8")

        # Prepare CSS stylesheets
        css_list = []

        # Add custom CSS if provided
        if custom_css:
            css_list.append(CSS(string=custom_css, font_config=self.font_config))

        # Generate PDF
        pdf_buffer = BytesIO()
        html_doc.write_pdf(
            pdf_buffer,
            stylesheets=css_list,
            font_config=self.font_config,
            optimize_images=True,
        )

        return pdf_buffer.getvalue()

    def _create_response(self, pdf_bytes: bytes, attachment: bool = True) -> HttpResponse:
        """
        Create HTTP response with PDF content.

        Args:
            pdf_bytes: PDF content as bytes
            attachment: Whether to serve as attachment

        Returns:
            HttpResponse with appropriate headers
        """
        filename = f"invoice_{self.invoice.invoice_number}_{timezone.now().strftime('%Y%m%d')}.pdf"

        response = HttpResponse(pdf_bytes, content_type="application/pdf")

        if attachment:
            response["Content-Disposition"] = f'attachment; filename="{filename}"'
        else:
            response["Content-Disposition"] = f'inline; filename="{filename}"'

        response["Content-Length"] = len(pdf_bytes)
        response["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response["Pragma"] = "no-cache"
        response["Expires"] = "0"

        return response


class TimesheetPDFGenerator:
    """
    Service class for generating timesheet PDFs.

    Handles timesheet PDF generation with flexible formatting options
    and support for various time period views.
    """

    def __init__(self, timesheet_entries: List[TimeEntry], period_name: str):
        """
        Initialize timesheet PDF generator.

        Args:
            timesheet_entries: List of TimeEntry objects
            period_name: Name of the time period
        """
        self.timesheet_entries = timesheet_entries
        self.period_name = period_name
        self.font_config = FontConfiguration()

    def generate_pdf(
        self,
        template_name: str = "financial/pdf/timesheet_base.html",
        group_by_project: bool = True,
        include_details: bool = True,
    ) -> HttpResponse:
        """
        Generate timesheet PDF.

        Args:
            template_name: Template to use for PDF
            group_by_project: Whether to group entries by project
            include_details: Whether to include detailed descriptions

        Returns:
            HttpResponse with PDF content
        """
        try:
            # Prepare context
            context = {
                "timesheet_entries": self.timesheet_entries,
                "period_name": self.period_name,
                "generated_date": timezone.now(),
                "group_by_project": group_by_project,
                "include_details": include_details,
            }

            if group_by_project:
                context["grouped_entries"] = self._group_by_project()

            # Calculate totals
            context.update(self._calculate_timesheet_totals())

            # Render and generate PDF
            html_content = render_to_string(template_name, context)
            pdf_bytes = self._generate_pdf_bytes(html_content)

            # Create response
            filename = f"timesheet_{self.period_name}_{timezone.now().strftime('%Y%m%d')}.pdf"
            response = HttpResponse(pdf_bytes, content_type="application/pdf")
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            logger.error(f"Failed to generate timesheet PDF: {e}")
            raise PDFGenerationError(f"Timesheet PDF generation failed: {e}") from e

    def _group_by_project(self) -> Dict:
        """Group timesheet entries by project."""
        grouped = {}
        for entry in self.timesheet_entries:
            project_name = entry.project.name if entry.project else "No Project"
            if project_name not in grouped:
                grouped[project_name] = []
            grouped[project_name].append(entry)
        return grouped

    def _calculate_timesheet_totals(self) -> Dict:
        """Calculate timesheet totals."""
        total_hours = Decimal("0.00")
        billable_hours = Decimal("0.00")
        total_amount = Decimal("0.00")

        for entry in self.timesheet_entries:
            if entry.duration_hours:
                total_hours += entry.duration_hours
                if entry.is_billable:
                    billable_hours += entry.duration_hours
                    if entry.hourly_rate:
                        total_amount += entry.duration_hours * entry.hourly_rate

        return {
            "total_hours": total_hours,
            "billable_hours": billable_hours,
            "non_billable_hours": total_hours - billable_hours,
            "total_amount": total_amount,
        }

    def _generate_pdf_bytes(self, html_content: str) -> bytes:
        """Generate PDF bytes from HTML content."""
        html_doc = HTML(string=html_content, base_url=settings.STATIC_URL, encoding="utf-8")

        pdf_buffer = BytesIO()
        html_doc.write_pdf(pdf_buffer, font_config=self.font_config, optimize_images=True)

        return pdf_buffer.getvalue()


class PDFService:
    """
    Main service class for all PDF generation operations.

    Provides a unified interface for generating various types of
    financial PDFs with consistent error handling and logging.
    """

    @staticmethod
    def generate_invoice_pdf(invoice: Invoice, template_name: Optional[str] = None, **kwargs) -> HttpResponse:
        """
        Generate invoice PDF using InvoicePDFGenerator.

        Args:
            invoice: Invoice instance
            template_name: Optional template override
            **kwargs: Additional options for PDF generation

        Returns:
            HttpResponse with PDF content
        """
        generator = InvoicePDFGenerator(invoice)
        return generator.generate_pdf(template_name=template_name, **kwargs)

    @staticmethod
    def generate_timesheet_pdf(timesheet_entries: List[TimeEntry], period_name: str, **kwargs) -> HttpResponse:
        """
        Generate timesheet PDF using TimesheetPDFGenerator.

        Args:
            timesheet_entries: List of TimeEntry objects
            period_name: Name of the time period
            **kwargs: Additional options for PDF generation

        Returns:
            HttpResponse with PDF content
        """
        generator = TimesheetPDFGenerator(timesheet_entries, period_name)
        return generator.generate_pdf(**kwargs)

    @staticmethod
    def get_invoice_download_url(invoice: Invoice) -> str:
        """
        Get URL for downloading invoice PDF.

        Args:
            invoice: Invoice instance

        Returns:
            URL string for PDF download
        """
        return reverse("financial:invoice_pdf", kwargs={"pk": invoice.pk})

    @staticmethod
    def validate_pdf_generation(invoice: Invoice) -> bool:
        """
        Validate that invoice can be converted to PDF.

        Args:
            invoice: Invoice instance to validate

        Returns:
            True if PDF can be generated, False otherwise
        """
        try:
            # Check required fields
            if not invoice.invoice_number:
                logger.warning(f"Invoice {invoice.pk} missing invoice_number")
                return False

            if not invoice.project:
                logger.warning(f"Invoice {invoice.pk} missing project")
                return False

            if not invoice.project.organization:
                logger.warning(f"Invoice {invoice.pk} missing organization")
                return False

            # Check if invoice has line items or amount
            has_line_items = invoice.line_items.exists()
            has_amount = invoice.amount and invoice.amount > 0

            if not has_line_items and not has_amount:
                logger.warning(f"Invoice {invoice.pk} has no line items or amount")
                return False

            return True

        except Exception as e:
            logger.error(f"PDF validation failed for invoice {invoice.pk}: {e}")
            return False
