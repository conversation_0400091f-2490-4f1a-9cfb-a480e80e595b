{% extends "base.html" %}

{% load i18n %}
{% load static %}

{% block title %}
  {% trans "Expense Reports" %} - CLEAR Platform
{% endblock %}

{% block extra_head %}
  <style>
    .stats-card {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      color: white;
      border-radius: 0.5rem;
      padding: 1.5rem;
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .stats-card h3 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    .stats-card p {
      margin-bottom: 0;
      opacity: 0.9;
      font-weight: 500;
    }

    .chart-container {
      background: #ffffff;
      border-radius: 0.5rem;
      padding: 1.5rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.07);
      margin-bottom: 2rem;
    }

    .chart-container h5 {
      color: #495057;
      margin-bottom: 1rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .export-section {
      background: #f8f9fa;
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    .export-section h5 {
      color: #495057;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .filter-section {
      background: #f8f9fa;
      border-radius: 0.5rem;
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    .filter-section h5 {
      color: #495057;
      margin-bottom: 1rem;
      font-weight: 600;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="container-fluid mt-4" role="main">
    <!-- Page Header -->
    <div class="row">
      <div class="col-12">
        <nav aria-label="{% trans 'Breadcrumb' %}">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <a href="{% url 'financial:dashboard' %}"
                 class="text-decoration-none"
                 role="link">{% trans "Financial" %}</a>
            </li>
            <li class="breadcrumb-item">
              <a href="{% url 'financial:expenses' %}"
                 class="text-decoration-none"
                 role="link">{% trans "Expenses" %}</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{% trans "Reports" %}</li>
          </ol>
        </nav>
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h1 class="h3 mb-2">
              <i class="bi bi-graph-up me-2" aria-hidden="true"></i>
              {% trans "Expense Reports" %}
            </h1>
            <p class="text-muted mb-0">{% trans "Analyze expense data and generate reports" %}</p>
          </div>
          <div class="d-flex gap-2">
            <a href="{% url 'financial:expenses' %}"
               class="btn btn-outline-primary"
               role="link">
              <i class="bi bi-arrow-left me-1" aria-hidden="true"></i>
              {% trans "Back to Expenses" %}
            </a>
          </div>
        </div>
      </div>
    </div>
    <!-- Summary Statistics -->
    <div class="row">
      <div class="col-md-3">
        <div class="stats-card">
          <h3>${{ total_expenses|floatformat:0 }}</h3>
          <p>{% trans "Total Expenses" %}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card"
             style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
                    color: #212529">
          <h3>{{ pending_expenses }}</h3>
          <p>{% trans "Pending Approval" %}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card"
             style="background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%)">
          <h3>{{ approved_expenses }}</h3>
          <p>{% trans "Approved Expenses" %}</p>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card"
             style="background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%)">
          <h3>{{ paid_expenses|default:0 }}</h3>
          <p>{% trans "Paid Expenses" %}</p>
        </div>
      </div>
    </div>
    <!-- Filters -->
    <div class="row">
      <div class="col-12">
        <div class="filter-section">
          <h5>
            <i class="bi bi-funnel" aria-hidden="true"></i>
            {% trans "Filter Reports" %}
          </h5>
          <form method="get" class="row g-3">
            <div class="col-md-3">
              <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
              <input type="date"
                     class="form-control"
                     id="start_date"
                     name="start_date"
                     value="{{ request.GET.start_date }}">
            </div>
            <div class="col-md-3">
              <label for="end_date" class="form-label">{% trans "End Date" %}</label>
              <input type="date"
                     class="form-control"
                     id="end_date"
                     name="end_date"
                     value="{{ request.GET.end_date }}">
            </div>
            <div class="col-md-3">
              <label for="category" class="form-label">{% trans "Category" %}</label>
              <select class="form-select" id="category" name="category">
                <option value="">{% trans "All Categories" %}</option>
                <option value="travel" 
                  {% if request.GET.category == 'travel' %}selected{% endif %}
                  >{% trans "Travel" %}</option>
                <option value="meals" 
                  {% if request.GET.category == 'meals' %}selected{% endif %}
                  >{% trans "Meals" %}</option>
                <option value="materials" 
                  {% if request.GET.category == 'materials' %}selected{% endif %}
                  >{% trans "Materials" %}</option>
                <option value="equipment" 
                  {% if request.GET.category == 'equipment' %}selected{% endif %}
                  >{% trans "Equipment" %}</option>
                <option value="office" 
                  {% if request.GET.category == 'office' %}selected{% endif %}
                  >{% trans "Office Supplies" %}</option>
                <option value="other" 
                  {% if request.GET.category == 'other' %}selected{% endif %}
                  >{% trans "Other" %}</option>
              </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button type="submit" class="btn btn-primary w-100">
                <i class="bi bi-search me-1" aria-hidden="true"></i>
                {% trans "Apply Filters" %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    <!-- Charts Row -->
    <div class="row">
      <div class="col-md-6">
        <div class="chart-container">
          <h5>
            <i class="bi bi-pie-chart" aria-hidden="true"></i>
            {% trans "Expenses by Category" %}
          </h5>
          <div class="text-center">
            <canvas id="categoryChart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="chart-container">
          <h5>
            <i class="bi bi-bar-chart" aria-hidden="true"></i>
            {% trans "Monthly Expense Trend" %}
          </h5>
          <div class="text-center">
            <canvas id="monthlyChart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>
    <!-- Export Section -->
    <div class="row">
      <div class="col-12">
        <div class="export-section">
          <h5>
            <i class="bi bi-download" aria-hidden="true"></i>
            {% trans "Export Reports" %}
          </h5>
          <div class="row g-3">
            <div class="col-md-4">
              <a href="{% url 'financial:export_expenses_csv' %} 
                {% if request.GET %}?{{ request.GET.urlencode }}{% endif %}
                 " class="btn btn-outline-success w-100">
                <i class="bi bi-file-earmark-spreadsheet me-1" aria-hidden="true"></i>
                {% trans "Export CSV" %}
              </a>
            </div>
            <div class="col-md-4">
              <a href="{% url 'financial:export_expenses_json' %} 
                {% if request.GET %}?{{ request.GET.urlencode }}{% endif %}
                 " class="btn btn-outline-primary w-100">
                <i class="bi bi-file-earmark-code me-1" aria-hidden="true"></i>
                {% trans "Export JSON" %}
              </a>
            </div>
            <div class="col-md-4">
              <button class="btn btn-outline-info w-100" onclick="window.print()">
                <i class="bi bi-printer me-1" aria-hidden="true"></i>
                {% trans "Print Report" %}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Quick Links -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              <i class="bi bi-link-45deg me-2" aria-hidden="true"></i>
              {% trans "Quick Actions" %}
            </h5>
          </div>
          <div class="card-body">
            <div class="row g-3">
              <div class="col-md-3">
                <a href="{% url 'financial:expense_category_summary' %}"
                   class="btn btn-outline-primary w-100">
                  <i class="bi bi-tags me-1" aria-hidden="true"></i>
                  {% trans "Category Summary" %}
                </a>
              </div>
              <div class="col-md-3">
                <a href="{% url 'financial:expense_monthly_summary' %}"
                   class="btn btn-outline-info w-100">
                  <i class="bi bi-calendar3 me-1" aria-hidden="true"></i>
                  {% trans "Monthly Summary" %}
                </a>
              </div>
              <div class="col-md-3">
                <a href="{% url 'financial:create_expense' %}"
                   class="btn btn-outline-success w-100">
                  <i class="bi bi-plus me-1" aria-hidden="true"></i>
                  {% trans "Add New Expense" %}
                </a>
              </div>
              <div class="col-md-3">
                <a href="{% url 'financial:expenses' %}"
                   class="btn btn-outline-secondary w-100">
                  <i class="bi bi-list me-1" aria-hidden="true"></i>
                  {% trans "View All Expenses" %}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Sample data - in real implementation, this would come from the backend
      const categoryData = {
        labels: ['Travel', 'Meals', 'Materials', 'Equipment', 'Office Supplies', 'Other'],
        datasets: [{
          data: [30, 25, 20, 15, 7, 3],
          backgroundColor: [
            '#007bff',
            '#28a745',
            '#ffc107',
            '#dc3545',
            '#17a2b8',
            '#6c757d'
          ]
        }]
      };

      const monthlyData = {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          label: 'Expenses ($)',
          data: [1200, 1900, 800, 1700, 1400, 2100],
          backgroundColor: 'rgba(0, 123, 255, 0.1)',
          borderColor: '#007bff',
          borderWidth: 2,
          fill: true
        }]
      };

      // Category Pie Chart
      const categoryCtx = document.getElementById('categoryChart');
      if (categoryCtx) {
        new Chart(categoryCtx, {
          type: 'pie',
          data: categoryData,
          options: {
            responsive: true,
            plugins: {
              legend: {
                position: 'bottom',
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return context.label + ': ' + context.parsed + '%';
                  }
                }
              }
            }
          }
        });
      }

      // Monthly Trend Chart
      const monthlyCtx = document.getElementById('monthlyChart');
      if (monthlyCtx) {
        new Chart(monthlyCtx, {
          type: 'line',
          data: monthlyData,
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return '$' + value;
                  }
                }
              }
            },
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return 'Amount: $' + context.parsed.y;
                  }
                }
              }
            }
          }
        });
      }

      // Date range validation
      const startDate = document.getElementById('start_date');
      const endDate = document.getElementById('end_date');

      function validateDateRange() {
        if (startDate.value && endDate.value) {
          if (new Date(startDate.value) > new Date(endDate.value)) {
            endDate.setCustomValidity('{% trans "End date must be after start date" %}');
          } else {
            endDate.setCustomValidity('');
          }
        }
      }

      if (startDate && endDate) {
        startDate.addEventListener('change', validateDateRange);
        endDate.addEventListener('change', validateDateRange);
      }
    });
  </script>
{% endblock %}
