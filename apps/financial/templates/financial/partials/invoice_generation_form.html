{% load i18n %}

<div class="modal-header">
  <h5 class="modal-title">{% trans "Generate Invoice from Timesheet" %}</h5>
  <button type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="{% trans 'Close' %}"></button>
</div>
<form hx-post="{% url 'financial:generate_invoice_from_timesheet' %}"
      hx-target="#invoice-modal-content"
      hx-swap="innerHTML">
  <div class="modal-body">
    {% csrf_token %}
    <div class="row g-3">
      <!-- Project Selection -->
      <div class="col-12">
        <label class="form-label">{% trans "Project" %} *</label>
        <select class="form-select"
                name="project_id"
                required
                hx-get="{% url 'financial:project_time_entries_preview' %}"
                hx-target="#time-entries-preview"
                hx-trigger="change">
          <option value="">{% trans "Select a project" %}</option>

          {% for project in projects %}<option value="{{ project.id }}">{{ project.name }}</option>{% endfor %}

        </select>
      </div>
      <!-- Date Range -->
      <div class="col-md-6">
        <label class="form-label">{% trans "From Date" %} *</label>
        <input type="date"
               class="form-control"
               name="date_from"
               required
               hx-get="{% url 'financial:project_time_entries_preview' %}"
               hx-target="#time-entries-preview"
               hx-trigger="change"
               hx-include="[name='project_id'], [name='date_to']">
      </div>
      <div class="col-md-6">
        <label class="form-label">{% trans "To Date" %} *</label>
        <input type="date"
               class="form-control"
               name="date_to"
               required
               hx-get="{% url 'financial:project_time_entries_preview' %}"
               hx-target="#time-entries-preview"
               hx-trigger="change"
               hx-include="[name='project_id'], [name='date_from']">
      </div>
      <!-- Preview Section -->
      <div class="col-12">
        <hr class="my-3">
        <h6>{% trans "Time Entries Preview" %}</h6>
        <div id="time-entries-preview" class="border rounded p-3 bg-light">
          <div class="text-center text-muted">
            <i class="fas fa-clock fa-2x mb-2"></i>
            <p>{% trans "Select a project and date range to preview time entries" %}</p>
          </div>
        </div>
      </div>
      <!-- Invoice Options -->
      <div class="col-12">
        <hr class="my-3">
        <h6>{% trans "Invoice Options" %}</h6>
      </div>
      <div class="col-12">
        <div class="form-check">
          <input class="form-check-input"
                 type="checkbox"
                 name="group_by_task"
                 id="group_by_task"
                 checked>
          <label class="form-check-label" for="group_by_task">{% trans "Group line items by task" %}</label>
        </div>
      </div>
      <div class="col-12">
        <div class="form-check">
          <input class="form-check-input"
                 type="checkbox"
                 name="include_descriptions"
                 id="include_descriptions"
                 checked>
          <label class="form-check-label" for="include_descriptions">{% trans "Include detailed descriptions" %}</label>
        </div>
      </div>
      <div class="col-12">
        <div class="form-check">
          <input class="form-check-input"
                 type="checkbox"
                 name="round_hours"
                 id="round_hours">
          <label class="form-check-label" for="round_hours">{% trans "Round hours to nearest quarter" %}</label>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
    <button type="submit" class="btn btn-primary" disabled id="generate-btn">
      <span class="spinner-border spinner-border-sm me-2 htmx-indicator"
            role="status"></span>
      {% trans "Generate Invoice" %}
    </button>
  </div>
</form>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const generateBtn = document.getElementById('generate-btn');
    
    // Enable/disable generate button based on form validity
    function checkFormValidity() {
        const projectId = form.querySelector('[name="project_id"]').value;
        const dateFrom = form.querySelector('[name="date_from"]').value;
        const dateTo = form.querySelector('[name="date_to"]').value;
        
        generateBtn.disabled = !(projectId && dateFrom && dateTo);
    }
    
    // Listen for form changes
    form.addEventListener('change', checkFormValidity);
    form.addEventListener('input', checkFormValidity);
    
    // Initial check
    checkFormValidity();
});
</script>
