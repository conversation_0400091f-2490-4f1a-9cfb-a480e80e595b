"""Performance monitoring for CLEAR platform.

This module provides performance tracking, metrics collection, and monitoring
capabilities for the CLEAR application.
"""

import logging
import time
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Performance monitoring and metrics collection."""

    def __init__(self):
        """Initialize the performance monitor."""
        self.metrics = {}
        self.active_operations = {}

    def start_operation(self, operation_name: str) -> str:
        """Start tracking a performance operation.

        Args:
            operation_name: Name of the operation to track

        Returns:
            Operation ID for stopping the tracking
        """
        operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        self.active_operations[operation_id] = {
            "name": operation_name,
            "start_time": time.time(),
            "start_memory": self._get_memory_usage(),
        }
        return operation_id

    def stop_operation(self, operation_id: str) -> dict:
        """Stop tracking a performance operation.

        Args:
            operation_id: ID returned from start_operation

        Returns:
            Performance metrics for the operation
        """
        if operation_id not in self.active_operations:
            return {}

        operation = self.active_operations.pop(operation_id)
        end_time = time.time()
        end_memory = self._get_memory_usage()

        metrics = {
            "name": operation["name"],
            "duration": end_time - operation["start_time"],
            "memory_delta": end_memory - operation["start_memory"],
            "start_time": operation["start_time"],
            "end_time": end_time,
        }

        # Store metrics
        if operation["name"] not in self.metrics:
            self.metrics[operation["name"]] = []
        self.metrics[operation["name"]].append(metrics)

        return metrics

    @contextmanager
    def track_operation(self, operation_name: str):
        """Context manager for tracking operations.

        Args:
            operation_name: Name of the operation to track

        Yields:
            Performance metrics dictionary
        """
        operation_id = self.start_operation(operation_name)
        try:
            yield
        finally:
            metrics = self.stop_operation(operation_id)
            logger.debug(f"Operation {operation_name} completed in {metrics.get('duration', 0):.3f}s")

    def get_metrics(self, operation_name: str | None = None) -> dict:
        """Get performance metrics.

        Args:
            operation_name: Specific operation to get metrics for, or None for all

        Returns:
            Performance metrics data
        """
        if operation_name:
            return self.metrics.get(operation_name, [])
        return self.metrics.copy()

    def clear_metrics(self, operation_name: str | None = None):
        """Clear stored metrics.

        Args:
            operation_name: Specific operation to clear, or None for all
        """
        if operation_name:
            self.metrics.pop(operation_name, None)
        else:
            self.metrics.clear()

    def _get_memory_usage(self) -> float:
        """Get current memory usage.

        Returns:
            Memory usage in MB
        """
        try:
            import os

            import psutil

            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            return 0.0


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
