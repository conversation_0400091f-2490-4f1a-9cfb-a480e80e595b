"""Django App Configuration for Common App

Configures the common app which provides shared utilities, decorators, base classes,
and cross-cutting concerns for the CLEAR (Comprehensive Location-based Engineering
and Analysis Resource) platform.

This app serves as the foundational layer for:
- Multi-tenant role-based access control (RBAC)
- HTMX security and enhanced CSRF protection
- User activity tracking and audit logging
- Security middleware and monitoring services
- Spatial utilities and PostGIS integration helpers
- Performance optimization and caching strategies

Key Features:
- Flexible role-based access control with tenant-specific roles
- Enhanced HTMX security and response handling
- Comprehensive user activity tracking and audit logging
- Security monitoring and threat detection
- Multi-tenant organization access patterns
- Spatial utilities for PostGIS operations
- Performance monitoring and optimization tools
"""

from __future__ import annotations

import contextlib
import logging
import sys
from typing import Any, ClassVar

from django.apps import AppConfig, apps
from django.core.exceptions import ImproperlyConfigured
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class CommonConfig(AppConfig):
    """Django app configuration for the common app.

    Provides essential infrastructure for the CLEAR platform including:
    - Flexible role-based access control with tenant-specific roles
    - Enhanced HTMX security and response handling
    - Comprehensive user activity tracking and audit logging
    - Security monitoring and threat detection
    - Multi-tenant organization access patterns
    - Spatial utilities for PostGIS operations
    - Performance monitoring and optimization tools

    Attributes:
        default_auto_field (str): Default primary key field type
        name (str): Python package name for the app
        verbose_name (str): Human-readable app name
        default_permissions (tuple): Default model permissions
        is_security_critical (bool): Whether to raise errors on security failures

    """

    # Django 5.2+ configuration with modern type hints
    default_auto_field: ClassVar[str] = "django.db.models.BigAutoField"
    name: ClassVar[str] = "apps.common"
    verbose_name: ClassVar[Any] = _("Common Utilities & Infrastructure")

    # Django 5.2 enhanced app configuration
    default_permissions: ClassVar[tuple[str, ...]] = ("add", "change", "delete", "view")

    # Security configuration
    is_security_critical: ClassVar[bool] = True

    def ready(self) -> None:
        """Initialize the app when Django starts.

        Sets up signal handlers, registers custom system checks, initializes
        security monitoring, and configures performance optimization services.

        This method is called by Django after all models are loaded and the
        registry is populated. It performs the following initialization steps:
        1. Verifies the app is ready for initialization
        2. Sets up signal handlers for event processing
        3. Registers custom system checks
        4. Initializes security and performance monitoring

        Raises:
            ImproperlyConfigured: If required configuration is missing or
                initialization fails and is_security_critical is True

        """
        if not self._is_app_ready():
            logger.debug("Skipping initialization - app not ready")
            return

        try:
            # Import and register signal handlers
            self._setup_signal_handlers()

            # Register custom Django system checks
            self._register_system_checks()

            # Initialize core services
            self._initialize_services()

            logger.info("Common app initialized successfully")

        except Exception as e:
            logger.exception("Failed to initialize common app: %s", str(e))
            if self.is_security_critical:
                raise ImproperlyConfigured(f"Common app initialization failed: {e}")

    def _is_app_ready(self) -> bool:
        """Check if the app is in a state where it can be initialized.

        Verifies that:
        1. Not running migrations or makemigrations
        2. Django model registry is ready
        3. Required dependencies are available

        Returns:
            bool: True if the app can be initialized, False otherwise

        """
        # Skip initialization during migrations
        if any(cmd in sys.argv for cmd in ["migrate", "makemigrations"]):
            return False

        # Check if Django model registry is ready
        try:
            if not apps.models_ready:
                return False
        except (ImportError, AttributeError):
            return False

        return True

    def _setup_signal_handlers(self) -> None:
        """Import and connect signal handlers.

        Imports the signals module which automatically registers all signal
        handlers defined there. Uses contextlib.suppress to gracefully handle
        missing signal handlers.

        Logs:
            debug: When signal handlers are successfully connected
            warning: If signal handler registration fails
        """
        try:
            with contextlib.suppress(ImportError):
                from . import signals

                logger.debug("Signal handlers connected successfully")
        except (ImportError, ModuleNotFoundError) as e:
            logger.warning("Failed to register signal handlers: %s", str(e))

    def _register_system_checks(self) -> None:
        """Register custom Django system checks.

        Imports the checks module which automatically registers all system
        checks defined there. Uses contextlib.suppress to gracefully handle
        missing check definitions.

        Logs:
            debug: When system checks are successfully registered
            warning: If check registration fails
        """
        try:
            with contextlib.suppress(ImportError):
                # Import checks module to register system checks
                logger.debug("System checks registered successfully")
        except (ImportError, ModuleNotFoundError) as e:
            logger.warning("Failed to register system checks: %s", str(e))

    def _initialize_services(self) -> None:
        """Initialize core services and monitoring.

        Sets up:
        1. Security monitoring and threat detection
        2. Performance monitoring and optimization

        Each service is initialized independently to prevent failures in one
        service from affecting others.
        """
        self._setup_security_monitoring()
        self._setup_performance_monitoring()

    def _setup_security_monitoring(self) -> None:
        """Initialize security monitoring and threat detection.

        Configures real-time security monitoring including:
        - User activity tracking
        - Suspicious behavior detection
        - Access control violations
        - Security event logging

        The actual monitoring implementation will be added incrementally.

        Logs:
            debug: When monitoring is initialized
            warning: If monitoring setup fails
        """
        from django.conf import settings

        if not getattr(settings, "ENABLE_SECURITY_MONITORING", True):
            logger.debug("Security monitoring disabled via settings")
            return

        try:
            # Security monitoring will be implemented incrementally
            logger.debug("Security monitoring initialized")
        except (ImportError, ModuleNotFoundError) as e:
            logger.warning("Security monitoring not available: %s", str(e))

    def _setup_performance_monitoring(self) -> None:
        """Initialize performance monitoring and optimization.

        Configures performance tracking including:
        - Query optimization monitoring
        - Cache hit rate tracking
        - Response time analysis
        - Resource usage monitoring

        The actual monitoring implementation will be added incrementally.

        Logs:
            debug: When monitoring is initialized
            warning: If monitoring setup fails
        """
        from django.conf import settings

        if not getattr(settings, "ENABLE_PERFORMANCE_MONITORING", True):
            logger.debug("Performance monitoring disabled via settings")
            return

        try:
            # Performance monitoring will be implemented incrementally
            logger.debug("Performance monitoring initialized")
        except (ImportError, ModuleNotFoundError) as e:
            logger.warning("Performance monitoring not available: %s", str(e))
