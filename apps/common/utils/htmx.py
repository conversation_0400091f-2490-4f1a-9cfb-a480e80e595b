"""
HTMX Utilities and Helpers
==========================

Advanced HTMX patterns and utilities for common use cases:
- Polling with exponential backoff
- Infinite scroll pagination
- Modal/drawer management
- Toast notification system
- Performance monitoring
"""

import json
import time
from typing import Any, Dict, Optional
from uuid import uuid4

from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone


class HTMXPollingMixin:
    """Mixin for HTMX polling with exponential backoff."""

    polling_interval = 5000  # milliseconds
    max_polling_interval = 30000
    polling_enabled = True
    backoff_multiplier = 1.5

    def get_htmx_polling_headers(self, attempt: int = 1) -> Dict[str, str]:
        """Generate polling headers with exponential backoff."""
        if not self.polling_enabled:
            return {}

        # Calculate backoff interval
        interval = min(
            self.polling_interval * (self.backoff_multiplier ** (attempt - 1)),
            self.max_polling_interval,
        )

        return {
            "HX-Trigger-After-Settle": f"poll-update delay:{int(interval)}ms",
            "HX-Poll-Attempt": str(attempt),
            "HX-Poll-Interval": str(int(interval)),
        }

    def should_continue_polling(self, context: Dict[str, Any]) -> bool:
        """Determine if polling should continue based on context."""
        # Override in subclasses for custom logic
        return True


class HTMXInfiniteScrollMixin:
    """Mixin for infinite scroll pagination."""

    page_size = 20
    scroll_threshold = 100  # pixels from bottom
    loading_template = "common/partials/loading.html"

    def get_htmx_scroll_headers(self, page_obj) -> Dict[str, str]:
        """Generate infinite scroll headers."""
        if not page_obj.has_next():
            return {"HX-Trigger": "scroll-end"}

        next_url = self.get_next_page_url(page_obj.next_page_number())
        return {
            "HX-Trigger": "revealed",
            "HX-Get": next_url,
            "HX-Target": "#scroll-container",
            "HX-Swap": "beforeend",
            "HX-Indicator": f"#{self.get_loading_indicator_id()}",
        }

    def get_next_page_url(self, page_number: int) -> str:
        """Get URL for next page."""
        return f"{self.request.path}?page={page_number}"

    def get_loading_indicator_id(self) -> str:
        """Get loading indicator element ID."""
        return "scroll-loading-indicator"

    def render_loading_indicator(self) -> str:
        """Render loading indicator for infinite scroll."""
        return render_to_string(
            self.loading_template,
            {
                "indicator_id": self.get_loading_indicator_id(),
                "message": "Loading more items...",
            },
        )


class HTMXModalMixin:
    """Mixin for HTMX modal management."""

    modal_template = "common/partials/modal.html"
    modal_size = "lg"  # sm, md, lg, xl
    modal_backdrop = "static"
    modal_keyboard = True

    def render_modal_response(
        self,
        content: str,
        title: Optional[str] = None,
        size: Optional[str] = None,
        **kwargs,
    ) -> HttpResponse:
        """Render content in modal wrapper."""
        modal_context = {
            "modal_content": content,
            "modal_title": title or self.get_modal_title(),
            "modal_size": size or self.modal_size,
            "modal_id": f"modal-{uuid4().hex[:8]}",
            "modal_backdrop": self.modal_backdrop,
            "modal_keyboard": self.modal_keyboard,
            **kwargs,
        }

        modal_html = render_to_string(self.modal_template, modal_context)

        response = HttpResponse(modal_html)
        response["HX-Trigger"] = json.dumps(
            {
                "show-modal": {
                    "id": modal_context["modal_id"],
                    "size": modal_context["modal_size"],
                }
            }
        )

        return response

    def get_modal_title(self) -> str:
        """Get modal title - override in subclasses."""
        return getattr(self, "modal_title", "Modal")

    def close_modal_response(self, message: Optional[str] = None) -> HttpResponse:
        """Response to close current modal."""
        response = HttpResponse("")
        trigger_data = {"close-modal": True}

        if message:
            trigger_data["show-toast"] = {"message": message, "level": "success"}

        response["HX-Trigger"] = json.dumps(trigger_data)
        return response


class HTMXToastMixin:
    """Mixin for HTMX toast notifications."""

    default_timeout = 5000  # milliseconds
    toast_positions = ["top-right", "top-left", "bottom-right", "bottom-left"]

    def add_htmx_toast(
        self,
        message: str,
        level: str = "info",
        timeout: Optional[int] = None,
        position: str = "top-right",
        dismissible: bool = True,
    ) -> Dict[str, str]:
        """Add toast notification via HTMX headers."""
        toast_data = {
            "message": message,
            "level": level,
            "timeout": timeout or self.default_timeout,
            "position": position,
            "dismissible": dismissible,
            "timestamp": timezone.now().isoformat(),
            "id": f"toast-{uuid4().hex[:8]}",
        }

        return {"HX-Trigger": json.dumps({"show-toast": toast_data})}

    def success_toast(self, message: str, **kwargs) -> Dict[str, str]:
        """Add success toast."""
        return self.add_htmx_toast(message, level="success", **kwargs)

    def error_toast(self, message: str, **kwargs) -> Dict[str, str]:
        """Add error toast."""
        return self.add_htmx_toast(message, level="error", **kwargs)

    def warning_toast(self, message: str, **kwargs) -> Dict[str, str]:
        """Add warning toast."""
        return self.add_htmx_toast(message, level="warning", **kwargs)

    def info_toast(self, message: str, **kwargs) -> Dict[str, str]:
        """Add info toast."""
        return self.add_htmx_toast(message, level="info", **kwargs)


class HTMXPerformanceMixin:
    """Mixin for HTMX performance monitoring."""

    performance_threshold_ms = 1000
    enable_performance_headers = True

    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to add performance monitoring."""
        start_time = time.time()
        response = super().dispatch(request, *args, **kwargs)

        if request.headers.get("HX-Request") and self.enable_performance_headers:
            execution_time = (time.time() - start_time) * 1000

            # Add performance headers
            response["HX-Performance"] = f"{execution_time:.2f}ms"
            response["HX-Request-ID"] = getattr(request, "request_id", "unknown")

            # Log slow requests
            if execution_time > self.performance_threshold_ms:
                self.log_slow_request(request, execution_time)

        return response

    def log_slow_request(self, request, execution_time: float):
        """Log slow HTMX requests for monitoring."""
        import logging

        logger = logging.getLogger("htmx.performance")

        logger.warning(
            "Slow HTMX request detected",
            extra={
                "execution_time_ms": execution_time,
                "view_name": self.__class__.__name__,
                "url": request.path,
                "method": request.method,
                "user_id": getattr(request.user, "id", None),
                "threshold_ms": self.performance_threshold_ms,
            },
        )


class HTMXRetryMixin:
    """Mixin for automatic retry with exponential backoff."""

    max_retries = 3
    initial_delay = 1000  # milliseconds
    retry_backoff = 2.0
    retryable_status_codes = [500, 502, 503, 504]

    def get_retry_headers(self, attempt: int = 1, error_code: Optional[int] = None) -> Dict[str, str]:
        """Generate retry headers with exponential backoff."""
        if attempt > self.max_retries:
            return {"HX-Trigger": "max-retries-exceeded"}

        if error_code and error_code not in self.retryable_status_codes:
            return {}

        delay = int(self.initial_delay * (self.retry_backoff ** (attempt - 1)))

        return {
            "HX-Trigger-After-Settle": f"retry-request delay:{delay}ms",
            "HX-Retry-Attempt": str(attempt),
            "HX-Retry-Delay": str(delay),
            "HX-Max-Retries": str(self.max_retries),
        }

    def should_retry(self, response, attempt: int) -> bool:
        """Determine if request should be retried."""
        return attempt <= self.max_retries and response.status_code in self.retryable_status_codes


# Utility functions for template use
def htmx_partial_url(url: str, **params) -> str:
    """Generate URL with HTMX parameters."""
    if params:
        query_string = "&".join(f"{k}={v}" for k, v in params.items())
        return f"{url}?{query_string}"
    return url


def htmx_trigger_event(event_name: str, data: Optional[Dict] = None) -> str:
    """Generate HX-Trigger header value."""
    if data:
        return json.dumps({event_name: data})
    return event_name


def htmx_swap_strategy(strategy: str, timing: Optional[str] = None) -> str:
    """Generate HX-Swap header value."""
    if timing:
        return f"{strategy} {timing}"
    return strategy


# Common HTMX response utilities
def htmx_redirect(url: str, replace_url: bool = False) -> HttpResponse:
    """Create HTMX redirect response."""
    response = HttpResponse("")
    header_name = "HX-Replace-Url" if replace_url else "HX-Redirect"
    response[header_name] = url
    return response


def htmx_refresh() -> HttpResponse:
    """Create HTMX page refresh response."""
    response = HttpResponse("")
    response["HX-Refresh"] = "true"
    return response


def htmx_stop_polling() -> HttpResponse:
    """Create response to stop polling."""
    response = HttpResponse("")
    response["HX-Trigger"] = "stop-polling"
    return response


# Feature flag integration
class HTMXFeatureFlagMixin:
    """Mixin for feature flag support in HTMX responses."""

    def get_feature_flags(self) -> Dict[str, bool]:
        """Get current feature flags for user/organization."""
        # Import here to avoid circular dependency
        try:
            from apps.core.feature_flags.utils import get_user_flags

            return get_user_flags(self.request.user)
        except ImportError:
            return {}

    def add_feature_flag_headers(self, response: HttpResponse) -> HttpResponse:
        """Add feature flag data to response headers."""
        flags = self.get_feature_flags()
        if flags:
            response["HX-Feature-Flags"] = json.dumps(flags)
        return response

    def is_feature_enabled(self, flag_name: str) -> bool:
        """Check if specific feature flag is enabled."""
        flags = self.get_feature_flags()
        return flags.get(flag_name, False)
