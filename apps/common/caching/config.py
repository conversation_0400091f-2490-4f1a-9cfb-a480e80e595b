"""Redis Cache Configuration for CLEAR Analytics Platform

This module provides centralized cache configuration management for the CLEAR platform,
supporting Redis 7+ with django-redis backend. Features include:

- Centralized cache type definitions and timeouts
- Per-user cache key management
- Cache tag support for granular invalidation
- TTL configuration based on metric volatility
- Cache hit/miss monitoring
- Performance optimization settings
"""

import logging
from typing import Any, Dict, List, Optional

from django.core.cache import cache, caches

logger = logging.getLogger(__name__)


class CacheConfig:
    """Centralized cache configuration management for CLEAR platform"""

    # Cache type definitions with optimized TTL based on data volatility
    CACHE_TYPES = {
        # Dashboard metrics - 5 minutes for active users, 1 hour for historical
        "DASHBOARD": {
            "timeout": 300,  # 5 minutes for real-time metrics
            "key_prefix": "dashboard:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["dashboard", "user_metrics"],
            "compress": True,
            "description": "Dashboard statistics and metrics",
        },
        # Project data - 30 minutes (changes less frequently)
        "PROJECT_DATA": {
            "timeout": 1800,  # 30 minutes
            "key_prefix": "project:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["projects", "user_data"],
            "compress": True,
            "description": "Project information and statistics",
        },
        # Analytics data - 1 hour (historical data)
        "ANALYTICS": {
            "timeout": 3600,  # 1 hour
            "key_prefix": "analytics:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["analytics", "reports"],
            "compress": True,
            "description": "Analytics and reporting data",
        },
        # HTMX fragments - 5 minutes (frequent updates)
        "HTMX_FRAGMENTS": {
            "timeout": 300,  # 5 minutes
            "key_prefix": "htmx:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["htmx", "fragments"],
            "compress": False,  # Small fragments, compression overhead not worth it
            "description": "HTMX partial templates and components",
        },
        # Chart data - 15 minutes (balance between freshness and performance)
        "CHART_DATA": {
            "timeout": 900,  # 15 minutes
            "key_prefix": "chart:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["charts", "visualization"],
            "compress": True,
            "description": "Chart.js data and configurations",
        },
        # Session data - 30 minutes (user-specific session info)
        "SESSION_DATA": {
            "timeout": 1800,  # 30 minutes
            "key_prefix": "session:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["session", "user_state"],
            "compress": False,
            "description": "User session and state data",
        },
        # Search results - 1 hour (expensive operations)
        "SEARCH_RESULTS": {
            "timeout": 3600,  # 1 hour
            "key_prefix": "search:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["search", "results"],
            "compress": True,
            "description": "Search results and filters",
        },
        # Static content - 24 hours (rarely changes)
        "STATIC_CONTENT": {
            "timeout": 86400,  # 24 hours
            "key_prefix": "static:",
            "backend": "default",
            "vary_on_user": False,
            "tags": ["static", "navigation"],
            "compress": True,
            "description": "Navigation, menus, and static content",
        },
        # Quick actions - 5 minutes (user-specific actions)
        "QUICK_ACTIONS": {
            "timeout": 300,  # 5 minutes
            "key_prefix": "actions:",
            "backend": "default",
            "vary_on_user": True,
            "tags": ["actions", "user_interface"],
            "compress": False,
            "description": "Quick action buttons and menus",
        },
    }

    # Performance monitoring settings
    performance_settings = {
        "MONITORING_ENABLED": True,
        "TARGET_HIT_RATE": 75,  # 75% target hit rate
        "ALERT_HIT_RATE": 50,  # Alert if hit rate falls below 50%
        "RESPONSE_TIME_TARGET": 100,  # 100ms target response time
        "LOG_SLOW_QUERIES": True,
        "SLOW_QUERY_THRESHOLD": 200,  # Log queries taking more than 200ms
    }

    # Cache warming settings
    warming_settings = {
        "ENABLED": True,
        "MAX_WORKERS": 5,
        "BATCH_SIZE": 50,
        "CRITICAL_ENDPOINTS": [
            "dashboard_stats",
            "user_projects",
            "navigation",
            "notifications",
        ],
    }

    # Auto invalidation settings
    invalidation_settings = {
        "AUTO_INVALIDATE": True,
        "PATTERNS": {
            "project_update": ["PROJECT_DATA", "DASHBOARD"],
            "user_activity": ["SESSION_DATA", "DASHBOARD"],
            "analytics_update": ["ANALYTICS", "CHART_DATA"],
            "content_update": ["STATIC_CONTENT", "HTMX_FRAGMENTS"],
        },
    }

    @classmethod
    def get_timeout(cls, cache_type: str) -> int:
        """Get cache timeout for a specific cache type"""
        config = cls.CACHE_TYPES.get(cache_type, {})
        return config.get("timeout", 300)  # Default 5 minutes

    @classmethod
    def get_key_prefix(cls, cache_type: str) -> str:
        """Get cache key prefix for a specific cache type"""
        config = cls.CACHE_TYPES.get(cache_type, {})
        return config.get("key_prefix", "cache:")

    @classmethod
    def get_cache_backend(cls, cache_type: str) -> str:
        """Get cache backend for a specific cache type"""
        config = cls.CACHE_TYPES.get(cache_type, {})
        return config.get("backend", "default")

    @classmethod
    def should_vary_on_user(cls, cache_type: str) -> bool:
        """Check if cache should vary by user"""
        config = cls.CACHE_TYPES.get(cache_type, {})
        return config.get("vary_on_user", True)

    @classmethod
    def get_tags(cls, cache_type: str) -> List[str]:
        """Get cache tags for invalidation"""
        config = cls.CACHE_TYPES.get(cache_type, {})
        return config.get("tags", [])

    @classmethod
    def should_compress(cls, cache_type: str) -> bool:
        """Check if cache should be compressed"""
        config = cls.CACHE_TYPES.get(cache_type, {})
        return config.get("compress", False)

    @classmethod
    def get_vary_headers(cls, cache_type: str) -> List[str]:
        """Get HTTP headers to vary cache on"""
        if cls.should_vary_on_user(cache_type):
            return ["Authorization", "Cookie"]
        return []

    @classmethod
    def get_cache_instance(cls, cache_type: str):
        """Get cache instance for a specific cache type"""
        backend = cls.get_cache_backend(cache_type)
        return caches[backend]

    @classmethod
    def build_cache_key(
        cls,
        cache_type: str,
        identifier: str,
        user_id: Optional[int] = None,
        organization_id: Optional[int] = None,
    ) -> str:
        """Build a standardized cache key"""
        prefix = cls.get_key_prefix(cache_type)

        key_parts = [prefix.rstrip(":"), identifier]

        if cls.should_vary_on_user(cache_type) and user_id:
            key_parts.append(f"user:{user_id}")

        if organization_id:
            key_parts.append(f"org:{organization_id}")

        return ":".join(key_parts)

    @classmethod
    def validate_cache_type(cls, cache_type: str) -> bool:
        """Validate if cache type exists"""
        return cache_type in cls.CACHE_TYPES

    @classmethod
    def get_all_cache_types(cls) -> List[str]:
        """Get all available cache types"""
        return list(cls.CACHE_TYPES.keys())

    @classmethod
    def is_warming_enabled(cls) -> bool:
        """Check if cache warming is enabled"""
        return cls.warming_settings.get("ENABLED", False)

    @classmethod
    def is_monitoring_enabled(cls) -> bool:
        """Check if cache monitoring is enabled"""
        return cls.performance_settings.get("MONITORING_ENABLED", False)

    @classmethod
    def get_cache_info(cls, cache_type: str) -> Dict[str, Any]:
        """Get comprehensive cache information for a type"""
        if not cls.validate_cache_type(cache_type):
            return {}

        config = cls.CACHE_TYPES[cache_type].copy()
        config.update(
            {
                "cache_instance": cls.get_cache_instance(cache_type),
                "vary_headers": cls.get_vary_headers(cache_type),
                "is_compressed": cls.should_compress(cache_type),
                "user_specific": cls.should_vary_on_user(cache_type),
            }
        )

        return config

    @classmethod
    def get_target_hit_rate(cls) -> float:
        """Get target hit rate as decimal (0.75 for 75%)"""
        return cls.performance_settings.get("TARGET_HIT_RATE", 75) / 100.0

    @classmethod
    def get_alert_hit_rate(cls) -> float:
        """Get alert hit rate threshold as decimal (0.50 for 50%)"""
        return cls.performance_settings.get("ALERT_HIT_RATE", 50) / 100.0


class CacheKeyBuilder:
    """Advanced cache key building with organization and user context"""

    @staticmethod
    def dashboard_stats(user_id: int, organization_id: int) -> str:
        """Build cache key for dashboard statistics"""
        return CacheConfig.build_cache_key("DASHBOARD", "stats", user_id, organization_id)

    @staticmethod
    def user_projects(user_id: int, organization_id: int) -> str:
        """Build cache key for user projects"""
        return CacheConfig.build_cache_key("PROJECT_DATA", "user_projects", user_id, organization_id)

    @staticmethod
    def user_tasks(user_id: int, organization_id: int) -> str:
        """Build cache key for user tasks"""
        return CacheConfig.build_cache_key("HTMX_FRAGMENTS", "user_tasks", user_id, organization_id)

    @staticmethod
    def user_time(user_id: int, organization_id: int) -> str:
        """Build cache key for user time tracking"""
        return CacheConfig.build_cache_key("HTMX_FRAGMENTS", "user_time", user_id, organization_id)

    @staticmethod
    def team_chat(organization_id: int) -> str:
        """Build cache key for team chat (organization-wide)"""
        return CacheConfig.build_cache_key("HTMX_FRAGMENTS", "team_chat", organization_id=organization_id)

    @staticmethod
    def chart_data(chart_type: str, user_id: int, organization_id: int, time_range: str = "week") -> str:
        """Build cache key for chart data"""
        identifier = f"{chart_type}:{time_range}"
        return CacheConfig.build_cache_key("CHART_DATA", identifier, user_id, organization_id)

    @staticmethod
    def quick_actions(user_id: int, context: str = "dashboard") -> str:
        """Build cache key for quick actions"""
        identifier = f"actions:{context}"
        return CacheConfig.build_cache_key("QUICK_ACTIONS", identifier, user_id)

    @staticmethod
    def search_results(query_hash: str, user_id: int, organization_id: int, filters: str = "") -> str:
        """Build cache key for search results"""
        identifier = f"search:{query_hash}"
        if filters:
            identifier += f":{filters}"
        return CacheConfig.build_cache_key("SEARCH_RESULTS", identifier, user_id, organization_id)


class CacheMetrics:
    """Cache performance monitoring and metrics collection"""

    @staticmethod
    def log_cache_hit(cache_type: str, key: str, response_time: float = None):
        """Log cache hit for monitoring"""
        if not CacheConfig.is_monitoring_enabled():
            return

        logger.info(
            f"Cache HIT: {cache_type} - {key}",
            extra={
                "cache_type": cache_type,
                "cache_key": key,
                "response_time": response_time,
                "event": "cache_hit",
            },
        )

    @staticmethod
    def log_cache_miss(cache_type: str, key: str, response_time: float = None):
        """Log cache miss for monitoring"""
        if not CacheConfig.is_monitoring_enabled():
            return

        logger.info(
            f"Cache MISS: {cache_type} - {key}",
            extra={
                "cache_type": cache_type,
                "cache_key": key,
                "response_time": response_time,
                "event": "cache_miss",
            },
        )

    @staticmethod
    def log_cache_set(cache_type: str, key: str, size: int = None):
        """Log cache set operation"""
        if not CacheConfig.is_monitoring_enabled():
            return

        logger.info(
            f"Cache SET: {cache_type} - {key}",
            extra={
                "cache_type": cache_type,
                "cache_key": key,
                "cache_size": size,
                "event": "cache_set",
            },
        )

    @staticmethod
    def log_slow_query(cache_type: str, key: str, response_time: float):
        """Log slow cache query"""
        threshold = CacheConfig.performance_settings.get("SLOW_QUERY_THRESHOLD", 200)

        if response_time > threshold:
            logger.warning(
                f"Slow cache query: {cache_type} - {key} ({response_time}ms)",
                extra={
                    "cache_type": cache_type,
                    "cache_key": key,
                    "response_time": response_time,
                    "event": "slow_query",
                },
            )


class CacheInvalidator:
    """Smart cache invalidation using tags and patterns"""

    @staticmethod
    def invalidate_by_tags(tags: List[str]):
        """Invalidate cache entries by tags"""
        # Note: django-redis supports cache tagging
        # This is a simplified version - full implementation would use django-cache-tagging
        for tag in tags:
            cache_instance = CacheConfig.get_cache_instance("default")
            if hasattr(cache_instance, "delete_many"):
                # Get all keys with this tag and delete them
                # Implementation depends on Redis version and django-redis setup
                logger.info(f"Invalidating cache entries with tag: {tag}")

    @staticmethod
    def invalidate_user_cache(user_id: int, organization_id: int):
        """Invalidate all user-specific cache entries"""
        patterns = [
            CacheKeyBuilder.dashboard_stats(user_id, organization_id),
            CacheKeyBuilder.user_projects(user_id, organization_id),
            CacheKeyBuilder.user_tasks(user_id, organization_id),
            CacheKeyBuilder.user_time(user_id, organization_id),
        ]

        for pattern in patterns:
            cache.delete(pattern)

        logger.info(f"Invalidated user cache for user {user_id}")

    @staticmethod
    def invalidate_organization_cache(organization_id: int):
        """Invalidate organization-wide cache entries"""
        # Team chat and other organization-wide data
        team_chat_key = CacheKeyBuilder.team_chat(organization_id)
        cache.delete(team_chat_key)

        logger.info(f"Invalidated organization cache for org {organization_id}")

    @staticmethod
    def invalidate_by_pattern(pattern: str):
        """Invalidate cache entries matching a pattern"""
        cache_patterns = CacheConfig.invalidation_settings.get("PATTERNS", {})

        if pattern in cache_patterns:
            cache_types = cache_patterns[pattern]
            for cache_type in cache_types:
                # This would require Redis pattern-based deletion
                # For now, we'll log the intention
                logger.info(f"Invalidating {cache_type} cache for pattern: {pattern}")
