"""
Django management command to test comment system migration to unified markdown editor.
"""

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Test comment system migration to unified markdown editor"

    def handle(self, *args, **options):
        """Test the comment system migration."""
        self.stdout.write("🚀 Testing Comment System Migration to Unified Markdown Editor\n")

        tests = [
            self.test_comment_forms,
            self.test_task_comment_forms,
            self.test_markdown_widget_configuration,
            self.test_template_integration,
            self.test_form_rendering,
        ]

        passed = 0
        total = len(tests)

        for test in tests:
            if test():
                passed += 1
            self.stdout.write()  # Add spacing between tests

        self.stdout.write("=" * 60)
        self.stdout.write(f"📊 Test Results: {passed}/{total} tests passed")

        if passed == total:
            self.stdout.write(self.style.SUCCESS("🎉 Comment system migration successful!"))
            self.stdout.write("\n✨ Next steps:")
            self.stdout.write("   1. Update any remaining template files")
            self.stdout.write("   2. Test the forms in browser")
            self.stdout.write("   3. Verify HTMX integration works")
            self.stdout.write("   4. Check autosave functionality")
        else:
            self.stdout.write(self.style.WARNING("⚠️  Some tests failed. Please review the output above."))

    def test_comment_forms(self):
        """Test that comment forms are using our markdown editor."""
        self.stdout.write("🧪 Testing Comment Form Migration...")

        try:
            # Test universal comment form
            from apps.comments.forms import (
                CommentEditForm,
                CommentForm,
                CommentReplyForm,
            )
            from apps.common.editor.widgets import MinimalMarkdownWidget

            self.stdout.write("✅ Successfully imported comment forms and markdown widget")

            # Test CommentForm widget
            form = CommentForm()
            text_widget = form.fields["text"].widget

            if isinstance(text_widget, MinimalMarkdownWidget):
                self.stdout.write("✅ CommentForm is using MinimalMarkdownWidget")
                self.stdout.write(f"   Widget attrs: {text_widget.attrs}")
            else:
                self.stdout.write(f"❌ CommentForm is using {type(text_widget)} instead of MinimalMarkdownWidget")
                return False

            # Test CommentReplyForm widget
            reply_form = CommentReplyForm()
            reply_widget = reply_form.fields["text"].widget

            if isinstance(reply_widget, MinimalMarkdownWidget):
                self.stdout.write("✅ CommentReplyForm is using MinimalMarkdownWidget")
                self.stdout.write(f"   Widget attrs: {reply_widget.attrs}")
            else:
                self.stdout.write(f"❌ CommentReplyForm is using {type(reply_widget)} instead of MinimalMarkdownWidget")
                return False

            # Test CommentEditForm widget
            edit_form = CommentEditForm()
            edit_widget = edit_form.fields["text"].widget

            if isinstance(edit_widget, MinimalMarkdownWidget):
                self.stdout.write("✅ CommentEditForm is using MinimalMarkdownWidget")
                self.stdout.write(f"   Widget attrs: {edit_widget.attrs}")
            else:
                self.stdout.write(f"❌ CommentEditForm is using {type(edit_widget)} instead of MinimalMarkdownWidget")
                return False

            return True

        except Exception as e:
            self.stdout.write(f"❌ Error testing comment forms: {e}")
            return False

    def test_task_comment_forms(self):
        """Test that task comment forms are using our markdown editor."""
        self.stdout.write("\n🧪 Testing Task Comment Form Migration...")

        try:
            from apps.common.editor.widgets import MinimalMarkdownWidget
            from apps.projects.forms.comment_forms import (
                TaskCommentEditForm,
                TaskCommentForm,
                TaskCommentReplyForm,
            )

            self.stdout.write("✅ Successfully imported task comment forms")

            # Test TaskCommentForm widget
            form = TaskCommentForm()
            content_widget = form.fields["content"].widget

            if isinstance(content_widget, MinimalMarkdownWidget):
                self.stdout.write("✅ TaskCommentForm is using MinimalMarkdownWidget")
                self.stdout.write(f"   Widget attrs: {content_widget.attrs}")
            else:
                self.stdout.write(
                    f"❌ TaskCommentForm is using {type(content_widget)} instead of MinimalMarkdownWidget"
                )
                return False

            # Test TaskCommentReplyForm widget
            reply_form = TaskCommentReplyForm()
            reply_widget = reply_form.fields["content"].widget

            if isinstance(reply_widget, MinimalMarkdownWidget):
                self.stdout.write("✅ TaskCommentReplyForm is using MinimalMarkdownWidget")
                self.stdout.write(f"   Widget attrs: {reply_widget.attrs}")
            else:
                self.stdout.write(
                    f"❌ TaskCommentReplyForm is using {type(reply_widget)} instead of MinimalMarkdownWidget"
                )
                return False

            # Test TaskCommentEditForm widget
            edit_form = TaskCommentEditForm()
            edit_widget = edit_form.fields["content"].widget

            if isinstance(edit_widget, MinimalMarkdownWidget):
                self.stdout.write("✅ TaskCommentEditForm is using MinimalMarkdownWidget")
                self.stdout.write(f"   Widget attrs: {edit_widget.attrs}")
            else:
                self.stdout.write(
                    f"❌ TaskCommentEditForm is using {type(edit_widget)} instead of MinimalMarkdownWidget"
                )
                return False

            return True

        except Exception as e:
            self.stdout.write(f"❌ Error testing task comment forms: {e}")
            return False

    def test_markdown_widget_configuration(self):
        """Test that markdown widgets are properly configured for comments."""
        self.stdout.write("\n🧪 Testing Markdown Widget Configuration...")

        try:
            from apps.common.editor.configurations import MinimalConfig
            from apps.common.editor.widgets import MinimalMarkdownWidget

            # Test widget initialization
            widget = MinimalMarkdownWidget(
                attrs={
                    "data-content-type": "comment",
                    "data-context": "comment",
                }
            )

            self.stdout.write("✅ MinimalMarkdownWidget can be initialized")
            self.stdout.write(f"   Widget class: {widget.__class__.__name__}")

            # Test configuration
            config = MinimalConfig()
            self.stdout.write("✅ MinimalConfig can be initialized")
            self.stdout.write(f"   Mode: {config.mode}")
            self.stdout.write(f"   Features enabled: {len([k for k, v in config.to_dict().items() if v])}")

            # Test that widget works with forms
            from django import forms

            class TestForm(forms.Form):
                text = forms.CharField(widget=widget)

            TestForm()
            self.stdout.write("✅ MinimalMarkdownWidget works in Django forms")

            return True

        except Exception as e:
            self.stdout.write(f"❌ Error testing widget configuration: {e}")
            return False

    def test_template_integration(self):
        """Test that templates can render our markdown editor."""
        self.stdout.write("\n🧪 Testing Template Integration...")

        try:
            from django.template import Context, Template
            from django.template.loader import get_template

            # Test that our editor templates exist
            template_paths = [
                "common/editor/minimal_widget.html",
                "common/editor/widget.html",
            ]

            for template_path in template_paths:
                try:
                    template = get_template(template_path)
                    self.stdout.write(f"✅ Template exists: {template_path}")
                except Exception as e:
                    self.stdout.write(f"❌ Template missing: {template_path} - {e}")
                    return False

            # Test basic template rendering
            template_content = """
            {% load static %}
            <div class="test">{{ widget.media.css }}{{ widget.media.js }}</div>
            """

            template = Template(template_content)

            from apps.common.editor.widgets import MinimalMarkdownWidget

            widget = MinimalMarkdownWidget()

            context = Context({"widget": widget})
            rendered = template.render(context)

            self.stdout.write("✅ Template rendering works")
            self.stdout.write(f"   Rendered length: {len(rendered)} characters")

            return True

        except Exception as e:
            self.stdout.write(f"❌ Error testing template integration: {e}")
            return False

    def test_form_rendering(self):
        """Test that forms render properly with markdown widgets."""
        self.stdout.write("\n🧪 Testing Form Rendering...")

        try:
            from apps.comments.forms import CommentForm

            # Create a test form
            form = CommentForm()

            # Test form rendering
            str(form["text"])
            widget_type = type(form.fields["text"].widget)

            from apps.common.editor.widgets import MinimalMarkdownWidget

            if isinstance(form.fields["text"].widget, MinimalMarkdownWidget):
                self.stdout.write("✅ Comment form renders with markdown editor")
                self.stdout.write(f"   Widget type: {widget_type.__name__}")
            else:
                self.stdout.write("❌ Comment form does not render markdown editor")
                self.stdout.write(f"   Widget type: {widget_type.__name__}")
                return False

            return True

        except Exception as e:
            self.stdout.write(f"❌ Error testing form rendering: {e}")
            return False
