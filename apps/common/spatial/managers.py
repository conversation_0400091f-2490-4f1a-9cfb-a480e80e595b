"""Spatial managers for PostGIS operations.

This module provides optimized managers for performing spatial queries using PostGIS.
Includes specialized managers for caching and querying spatial data, with a focus
on performance and reliability.

Key Features:
- Optimized spatial queries using PostGIS
- Query caching for spatial operations
- Custom filtering and distance calculations
- Common spatial operation patterns
"""

from __future__ import annotations

import hashlib
import json
import logging
from typing import TYPE_CHECKING, Any

from django.contrib.gis.db import models
from django.contrib.gis.db.models import Q
from django.contrib.gis.measure import D
from django.core.cache import cache

if TYPE_CHECKING:
    from django.contrib.gis.db.models import Model
    from django.contrib.gis.geos import GEOSGeometry, MultiPolygon, Point, Polygon
    from django.db.models import QuerySet

logger = logging.getLogger(__name__)


class OptimizedSpatialManager(models.Manager):
    """Optimized spatial manager for better performance.

    Provides optimized spatial query methods with PostGIS indexes and
    query optimization. Uses Django's spatial features while maintaining
    high performance.

    Note: Only usable with models that have a spatial field.
    """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """Initialize the spatial manager.

        Args:
            *args: Positional arguments to pass to parent
            **kwargs: Keyword arguments with optional configs:
                - spatial_field: Name of the geometry field (default: 'geometry')
                - srid: SRID for coordinate system (default: 4326)
        """
        super().__init__(*args, **kwargs)
        self.spatial_field = kwargs.get("spatial_field", "geometry")
        self.srid = kwargs.get("srid", 4326)

    def within_distance(
        self,
        point: Point,
        distance: float | D,
        field_name: str | None = None,
        include_self: bool = False,
    ) -> QuerySet[Model]:
        """Find objects within distance of a point.

        Args:
            point: The reference point
            distance: Distance radius (float = meters, or Distance object)
            field_name: Optional spatial field name
            include_self: Whether to include the reference point

        Returns:
            QuerySet: Objects within the specified distance
        """
        if not isinstance(distance, D):
            distance = D(m=float(distance))

        spatial_field = field_name or self.spatial_field
        queryset = self.get_queryset().filter(
            **{f"{spatial_field}__dwithin": (point, distance)},
        )

        if not include_self and point is not None:
            queryset = queryset.exclude(**{spatial_field: point})

        return queryset.annotate_distance(point, field_name=spatial_field)

    def intersecting(
        self,
        geometry: GEOSGeometry,
        field_name: str | None = None,
        include_touches: bool = True,
    ) -> QuerySet[Model]:
        """Find objects intersecting with geometry.

        Args:
            geometry: The geometry to test intersection with
            field_name: Optional spatial field name
            include_touches: Whether to include objects that only touch

        Returns:
            QuerySet: Objects that intersect with the geometry
        """
        spatial_field = field_name or self.spatial_field
        q_objects = Q(**{f"{spatial_field}__intersects": geometry})

        if include_touches:
            q_objects |= Q(**{f"{spatial_field}__touches": geometry})

        return (
            self.get_queryset()
            .filter(q_objects)
            .annotate_intersection_area(
                geometry,
                field_name=spatial_field,
            )
        )

    def nearby(
        self,
        point: Point,
        radius_km: float = 1.0,
        field_name: str | None = None,
        limit: int | None = None,
        order_by_distance: bool = True,
    ) -> QuerySet[Model]:
        """Find objects near a point within radius.

        Args:
            point: Reference point
            radius_km: Search radius in kilometers
            field_name: Optional spatial field name
            limit: Optional limit on results
            order_by_distance: Whether to order by distance

        Returns:
            QuerySet: Objects within the radius
        """
        spatial_field = field_name or self.spatial_field
        radius_m = radius_km * 1000

        qs = self.within_distance(point, D(m=radius_m), field_name=spatial_field)

        if order_by_distance:
            qs = qs.order_by("distance")

        if limit:
            qs = qs[:limit]

        return qs


class CachedSpatialManager(OptimizedSpatialManager):
    """Spatial manager with caching capabilities.

    Extends OptimizedSpatialManager to add caching for expensive spatial
    queries. Uses Django's cache framework with spatial-specific optimizations.
    """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        """Initialize the cached spatial manager.

        Args:
            *args: Positional arguments to pass to parent
            **kwargs: Keyword arguments with optional configs:
                - cache_timeout: Cache timeout in seconds (default: 300)
                - cache_prefix: Prefix for cache keys (default: 'spatial')
        """
        super().__init__(*args, **kwargs)
        self.cache_timeout = kwargs.get("cache_timeout", 300)  # 5 minutes
        self.cache_prefix = kwargs.get("cache_prefix", "spatial")

    def get_cache_key(self, query_type: str, **params: Any) -> str:
        """Generate cache key for spatial query.

        Args:
            query_type: Type of spatial query
            **params: Query parameters to include in key

        Returns:
            str: Cache key
        """
        # import hashlib (moved to top level)
        # import json (moved to top level)

        # Sort params for consistent keys
        param_str = json.dumps(params, sort_keys=True)
        param_hash = hashlib.md5(param_str.encode(), usedforsecurity=False).hexdigest()

        return f"{self.cache_prefix}:{query_type}:{param_hash}"

    def get_cached_query(self, cache_key: str) -> QuerySet[Model] | None:
        """Get cached query result.

        Args:
            cache_key: Cache key to retrieve

        Returns:
            QuerySet or None: Cached queryset if found
        """
        try:
            cached_ids = cache.get(cache_key)
            if cached_ids is not None:
                return self.get_queryset().filter(id__in=cached_ids)
        except (ConnectionError, TimeoutError, AttributeError, KeyError) as e:
            logger.warning(f"Error retrieving cached query: {e}")

        return None

    def set_cached_query(self, cache_key: str, queryset: QuerySet[Model]) -> None:
        """Cache query result.

        Args:
            cache_key: Cache key to store
            queryset: Queryset to cache
        """
        try:
            # Cache only IDs to minimize memory usage
            cached_ids = list(queryset.values_list("id", flat=True))
            cache.set(cache_key, cached_ids, self.cache_timeout)
        except (ConnectionError, TimeoutError, AttributeError, KeyError) as e:
            logger.warning(f"Error caching query: {e}")

    def cached_within_distance(self, point: Point, distance: float | D, **kwargs: Any) -> QuerySet[Model]:
        """Cached version of within_distance query.

        Args:
            point: Reference point
            distance: Distance radius
            **kwargs: Additional parameters for within_distance

        Returns:
            QuerySet: Objects within distance
        """
        cache_key = self.get_cache_key(
            "within_distance",
            point=point.ewkt,
            distance=str(distance),
            **kwargs,
        )

        cached_result = self.get_cached_query(cache_key)
        if cached_result is not None:
            return cached_result

        result = super().within_distance(point, distance, **kwargs)
        self.set_cached_query(cache_key, result)
        return result


class SpatialQueryManager(models.Manager):
    """Advanced spatial query manager for complex PostGIS operations.

    Provides high-level methods for common spatial queries with
    built-in PostGIS optimizations and type safety.
    """

    def in_bounds(self, bounds: Polygon | MultiPolygon, field_name: str = "location") -> QuerySet[Model]:
        """Find objects within bounding geometry.

        Args:
            bounds: Bounding geometry
            field_name: Name of spatial field

        Returns:
            QuerySet: Objects within bounds
        """
        return self.get_queryset().filter(**{f"{field_name}__within": bounds})

    def within_polygon(
        self,
        polygon: Polygon,
        field_name: str = "location",
        include_boundary: bool = True,
    ) -> QuerySet[Model]:
        """Find objects within a polygon.

        Args:
            polygon: Area polygon
            field_name: Name of spatial field
            include_boundary: Whether to include objects on boundary

        Returns:
            QuerySet: Objects within polygon
        """
        qs = self.get_queryset()

        if include_boundary:
            return qs.filter(
                Q(**{f"{field_name}__within": polygon}) | Q(**{f"{field_name}__touches": polygon}),
            )

        return qs.filter(**{f"{field_name}__within": polygon})

    def nearest_to(
        self,
        point: Point,
        limit: int | None = None,
        max_distance: float | None = None,
        field_name: str = "location",
    ) -> QuerySet[Model]:
        """Find nearest objects to a point.

        Args:
            point: Reference point
            limit: Maximum number of results
            max_distance: Maximum distance in meters
            field_name: Name of spatial field

        Returns:
            QuerySet: Objects ordered by distance
        """
        qs = self.get_queryset().annotate_distance(point, field_name)

        if max_distance is not None:
            qs = qs.filter(distance__lte=max_distance)

        qs = qs.order_by("distance")

        if limit:
            qs = qs[:limit]

        return qs

    def cluster_points(
        self,
        tolerance: float = 100.0,
        field_name: str = "location",
        min_points: int = 2,
    ) -> QuerySet[Model]:
        """Cluster nearby points using PostGIS.

        Args:
            tolerance: Clustering tolerance in meters
            field_name: Name of spatial field
            min_points: Minimum points per cluster

        Returns:
            QuerySet: Objects with cluster information
        """
        from django.contrib.gis.db.models.functions import ClusterDBSCAN

        return (
            self.get_queryset()
            .annotate(
                cluster_id=ClusterDBSCAN(
                    field_name,
                    eps=tolerance,
                    min_samples=min_points,
                ),
            )
            .exclude(cluster_id__isnull=True)
            .order_by("cluster_id")
        )

    def distance_matrix(
        self,
        points: list[Point],
        field_name: str = "location",
        max_distance: float | None = None,
    ) -> list[dict[str, Any]]:
        """Calculate distance matrix between points.

        Args:
            points: List of points
            field_name: Name of spatial field
            max_distance: Maximum distance to include

        Returns:
            list: Matrix of distances between points
        """
        from django.contrib.gis.db.models.functions import Distance

        matrix = []
        for i, point1 in enumerate(points):
            distances = []
            for j, point2 in enumerate(points):
                if i == j:
                    distances.append(0.0)
                    continue

                distance = (
                    self.get_queryset()
                    .filter(**{field_name: point1})
                    .annotate(distance=Distance(field_name, point2))
                    .values_list("distance", flat=True)
                    .first()
                )

                if distance is not None and (max_distance is None or distance <= max_distance):
                    distances.append(float(distance))
                else:
                    distances.append(None)

            matrix.append(
                {
                    "point": point1,
                    "distances": distances,
                },
            )

        return matrix
