"""
Chart Widget for CLEAR Platform.

Displays data visualizations using Chart.js with support for
line charts, bar charts, pie charts, and other chart types.
Enhanced with ChartDataSerializer for robust data conversion.
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional

from django.http import HttpRequest
from django.utils import timezone

from apps.common.utils.chart_serializers import (
    ChartDataSerializer,
)

from .base import AbstractWidget


class ChartWidget(AbstractWidget):
    """
    Widget for displaying data visualizations.

    Supports: line charts, bar charts, pie charts, doughnut charts,
    area charts with configurable datasets, colors, and options.
    """

    widget_type = "chart"
    widget_name = "Chart Widget"
    widget_description = "Display data visualizations using Chart.js"

    config_schema = {
        **AbstractWidget.config_schema,
        "properties": {
            **AbstractWidget.config_schema["properties"],
            "chart_type": {
                "type": "string",
                "default": "line",
            },  # line, bar, pie, doughnut, area
            "data_source": {"type": "string", "required": True},
            "time_period": {"type": "string", "default": "30d"},  # 7d, 30d, 90d, 1y
            "aggregation": {
                "type": "string",
                "default": "day",
            },  # hour, day, week, month
            "height": {"type": "integer", "default": 300},
            "show_legend": {"type": "boolean", "default": True},
            "show_grid": {"type": "boolean", "default": True},
            "show_tooltips": {"type": "boolean", "default": True},
            "animate": {"type": "boolean", "default": True},
            "color_scheme": {"type": "string", "default": "default"},
            "datasets": {"type": "array", "default": []},
            "x_axis_label": {"type": "string", "default": ""},
            "y_axis_label": {"type": "string", "default": ""},
            "responsive": {"type": "boolean", "default": True},
            "maintain_aspect_ratio": {"type": "boolean", "default": False},
        },
    }

    # Predefined color schemes
    COLOR_SCHEMES = {
        "default": [
            "#007bff",
            "#28a745",
            "#ffc107",
            "#dc3545",
            "#6f42c1",
            "#20c997",
            "#fd7e14",
        ],
        "blue": ["#0d6efd", "#6ea8fe", "#9ec5fe", "#cfe2ff", "#e7f1ff"],
        "green": ["#198754", "#25d366", "#6edaa7", "#a5e6ca", "#d1f2e0"],
        "warm": ["#fd7e14", "#ffc107", "#dc3545", "#e83e8c", "#6f42c1"],
        "cool": ["#0dcaf0", "#20c997", "#198754", "#6f42c1", "#0d6efd"],
        "monochrome": ["#212529", "#495057", "#6c757d", "#adb5bd", "#dee2e6"],
    }

    def get_data(self, request: HttpRequest) -> Dict[str, Any]:
        """
        Get chart data for visualization using enhanced ChartDataSerializer.

        Args:
            request: HTTP request object

        Returns:
            Dictionary containing chart configuration and data
        """
        try:
            # Initialize serializer with color scheme
            color_scheme = self.get_config("color_scheme", "default")
            serializer = ChartDataSerializer(color_scheme=color_scheme)

            # Get data source configuration
            data_source = self.get_config("data_source")
            chart_type = self.get_config("chart_type", "line")

            # Use serializer to get chart configuration
            chart_config = self._get_serialized_chart_data(serializer, request)

            if not chart_config:
                return {
                    "error": "No data available",
                    "has_data": False,
                    "chart_config": None,
                }

            return {
                "chart_config": chart_config,
                "chart_id": f"chart-{self.widget_id}",
                "height": self.get_config("height", 300),
                "has_data": bool(chart_config and chart_config.get("data", {}).get("datasets")),
                "timestamp": timezone.now(),
                "data_source": data_source,
                "chart_type": chart_type,
            }

        except Exception as e:
            return {
                "error": str(e),
                "has_data": False,
                "chart_config": None,
            }

    def _get_serialized_chart_data(
        self, serializer: ChartDataSerializer, request: HttpRequest
    ) -> Optional[Dict[str, Any]]:
        """
        Get chart data using ChartDataSerializer.

        Args:
            serializer: ChartDataSerializer instance
            request: HTTP request object

        Returns:
            Chart.js configuration dictionary or None
        """
        data_source = self.get_config("data_source")
        chart_type = self.get_config("chart_type", "line")

        # Handle configured datasets
        configured_datasets = self.get_config("datasets", [])
        if configured_datasets:
            labels = self.get_config("labels", [])
            return serializer.serialize_custom_data(
                labels=labels,
                datasets=configured_datasets,
                chart_type=chart_type,
                **self._get_chart_options(),
            )

        # Handle predefined data sources
        if data_source == "user_registrations":
            return self._get_user_registration_data(serializer, request)
        elif data_source == "project_activity":
            return self._get_project_activity_data(serializer, request)
        elif data_source == "system_metrics":
            return self._get_system_metrics_data(serializer, request)
        elif data_source == "usage_statistics":
            return self._get_usage_statistics_data(serializer, request)
        else:
            # Try to call a custom method
            method_name = f"get_{data_source}_data"
            if hasattr(self, method_name):
                return getattr(self, method_name)(serializer, request)

        return None

    def _get_user_registration_data(self, serializer: ChartDataSerializer, request: HttpRequest) -> Dict[str, Any]:
        """Get user registration chart data using serializer."""
        from django.contrib.auth import get_user_model

        User = get_user_model()

        time_period = self.get_config("time_period", "30d")
        days = int(time_period.rstrip("d")) if time_period.endswith("d") else 30

        return serializer.serialize_time_series(
            queryset=User.objects.all(),
            date_field="date_joined",
            aggregation="count",
            time_unit="day",
            time_range=days,
            label="New Users",
            chart_type=self.get_config("chart_type", "line"),
            **self._get_chart_options(),
        )

    def _get_project_activity_data(self, serializer: ChartDataSerializer, request: HttpRequest) -> Dict[str, Any]:
        """Get project activity chart data using serializer."""
        try:
            from apps.projects.models import Project

            time_period = self.get_config("time_period", "30d")
            days = int(time_period.rstrip("d")) if time_period.endswith("d") else 30

            # Create multiple datasets for projects created and completed
            datasets = [
                {
                    "queryset": Project.objects.all(),
                    "date_field": ("created_at" if hasattr(Project, "created_at") else "created"),
                    "label": "Projects Created",
                    "aggregation": "count",
                    "time_unit": "day",
                    "time_range": days,
                }
            ]

            # If there's a status field, add completed projects
            if hasattr(Project, "status"):
                completed_qs = Project.objects.filter(status__in=["completed", "finished", "done"])
                datasets.append(
                    {
                        "queryset": completed_qs,
                        "date_field": ("updated_at" if hasattr(Project, "updated_at") else "created"),
                        "label": "Projects Completed",
                        "aggregation": "count",
                        "time_unit": "day",
                        "time_range": days,
                    }
                )

            return serializer.serialize_multiple_series(
                datasets=datasets,
                chart_type=self.get_config("chart_type", "line"),
                **self._get_chart_options(),
            )

        except ImportError:
            # Fallback to mock data
            return self._get_mock_project_data(serializer, request)

    def _get_system_metrics_data(self, serializer: ChartDataSerializer, request: HttpRequest) -> Dict[str, Any]:
        """Get system metrics chart data (mock data for demo)."""
        import random

        time_period = self.get_config("time_period", "30d")
        days = int(time_period.rstrip("d")) if time_period.endswith("d") else 30

        # Generate mock time series labels
        labels = []
        for i in range(days):
            dt = timezone.now().date() - timedelta(days=days - i - 1)
            labels.append(dt.strftime("%m/%d"))

        # Generate mock datasets
        datasets = [
            {
                "label": "CPU Usage (%)",
                "data": [random.uniform(20, 80) for _ in labels],
            },
            {
                "label": "Memory Usage (%)",
                "data": [random.uniform(30, 90) for _ in labels],
            },
            {
                "label": "Disk Usage (%)",
                "data": [random.uniform(10, 60) for _ in labels],
            },
        ]

        return serializer.serialize_custom_data(
            labels=labels,
            datasets=datasets,
            chart_type=self.get_config("chart_type", "line"),
            **self._get_chart_options(),
        )

    def _get_usage_statistics_data(self, serializer: ChartDataSerializer, request: HttpRequest) -> Dict[str, Any]:
        """Get usage statistics chart data."""
        import random

        time_period = self.get_config("time_period", "30d")
        days = int(time_period.rstrip("d")) if time_period.endswith("d") else 30

        # Generate mock time series labels
        labels = []
        for i in range(days):
            dt = timezone.now().date() - timedelta(days=days - i - 1)
            labels.append(dt.strftime("%m/%d"))

        # Generate mock active sessions data
        datasets = [
            {
                "label": "Active Sessions",
                "data": [random.randint(10, 100) for _ in labels],
            }
        ]

        return serializer.serialize_custom_data(
            labels=labels,
            datasets=datasets,
            chart_type=self.get_config("chart_type", "area"),
            **self._get_chart_options(),
        )

    def _get_mock_project_data(self, serializer: ChartDataSerializer, request: HttpRequest) -> Dict[str, Any]:
        """Generate mock project data when Project model is not available."""
        import random

        time_period = self.get_config("time_period", "30d")
        days = int(time_period.rstrip("d")) if time_period.endswith("d") else 30

        # Generate mock time series labels
        labels = []
        for i in range(days):
            dt = timezone.now().date() - timedelta(days=days - i - 1)
            labels.append(dt.strftime("%m/%d"))

        # Generate mock datasets
        datasets = [
            {
                "label": "Projects Created",
                "data": [random.randint(0, 5) for _ in labels],
            },
            {
                "label": "Projects Completed",
                "data": [random.randint(0, 3) for _ in labels],
            },
        ]

        return serializer.serialize_custom_data(
            labels=labels,
            datasets=datasets,
            chart_type=self.get_config("chart_type", "line"),
            **self._get_chart_options(),
        )

    def _get_chart_options(self) -> Dict[str, Any]:
        """Get chart options from widget configuration."""
        return {
            "responsive": self.get_config("responsive", True),
            "maintainAspectRatio": self.get_config("maintain_aspect_ratio", False),
            "show_legend": self.get_config("show_legend", True),
            "show_grid": self.get_config("show_grid", True),
            "show_tooltips": self.get_config("show_tooltips", True),
            "animate": self.get_config("animate", True),
            "x_axis_label": self.get_config("x_axis_label", ""),
            "y_axis_label": self.get_config("y_axis_label", ""),
            "begin_at_zero": self.get_config("begin_at_zero", True),
            "animation_duration": self.get_config("animation_duration", 1000),
        }

    # Legacy support methods (deprecated - use ChartDataSerializer instead)
    def _get_chart_datasets(self, request: HttpRequest) -> List[Dict[str, Any]]:
        """
        Legacy method for getting chart datasets.
        Kept for backward compatibility. Use ChartDataSerializer instead.
        """
        # This method is deprecated but kept for compatibility
        return []

    def _get_chart_labels(self, request: HttpRequest) -> List[str]:
        """Legacy method - use ChartDataSerializer instead."""
        return []

    def get_template_name(self) -> str:
        """Get template name for chart widget."""
        return "dashboard/widgets/chart_widget.html"
