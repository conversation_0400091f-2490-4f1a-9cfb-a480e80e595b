{% load i18n %}

{% comment %}
Notification Dropdown - Quick access notification panel
Designed to be embedded in the main navigation bar
{% endcomment %}

<div class="notification-dropdown" id="notification-dropdown">
  <button class="btn btn-link notification-bell"
          type="button"
          data-bs-toggle="dropdown"
          aria-expanded="false"
          hx-get="{% url 'activity:notification_dropdown' %}"
          hx-target="#notification-dropdown-content"
          hx-trigger="click">
    <i class="fas fa-bell"></i>

    {% if unread_count > 0 %}<span class="notification-count badge bg-danger">{{ unread_count }}</span>{% endif %}

  </button>
  <div class="dropdown-menu dropdown-menu-end notification-dropdown-menu"
       style="width: 380px">
    <div class="dropdown-header d-flex justify-content-between align-items-center">
      <h6 class="mb-0">{% trans "Notifications" %}</h6>

      {% if unread_count > 0 %}
        <button class="btn btn-sm btn-link text-primary"
                hx-post="{% url 'activity:mark_all_notifications_read' %}"
                hx-target="#notification-dropdown-content"
                hx-swap="innerHTML">{% trans "Mark all read" %}</button>
      {% endif %}

    </div>
    <div id="notification-dropdown-content">{% include "activity/notifications/dropdown_content.html" %}</div>
  </div>
</div>
<style>
.notification-dropdown .notification-bell {
    position: relative;
    color: #6c757d;
    font-size: 1.2rem;
    padding: 0.5rem;
    border: none;
    background: none;
}

.notification-dropdown .notification-bell:hover {
    color: #007bff;
}

.notification-count {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 0.75rem;
    min-width: 1.2rem;
    height: 1.2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-dropdown-menu {
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
}

.notification-dropdown-menu .dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: #f8f9fa;
}

.notification-item-mini {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.notification-item-mini:last-child {
    border-bottom: none;
}

.notification-item-mini:hover {
    background: rgba(0, 123, 255, 0.05);
}

.notification-item-mini.unread {
    background: rgba(0, 123, 255, 0.03);
    border-left: 3px solid #007bff;
}

.notification-mini-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.notification-mini-content {
    flex: 1;
    min-width: 0;
}

.notification-mini-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #212529;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-mini-message {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-mini-time {
    font-size: 0.7rem;
    color: #adb5bd;
    margin-top: 0.25rem;
}

.notification-dropdown-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: #f8f9fa;
    text-align: center;
}

.notification-dropdown-footer a {
    font-size: 0.875rem;
    color: #007bff;
    text-decoration: none;
}

.notification-dropdown-footer a:hover {
    text-decoration: underline;
}

.notification-empty-state {
    padding: 2rem 1rem;
    text-align: center;
    color: #6c757d;
}

.notification-empty-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
</style>
