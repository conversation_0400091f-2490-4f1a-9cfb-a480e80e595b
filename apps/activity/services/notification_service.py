"""
Notification delivery service for multi-channel notification delivery.

This service handles creating, dispatching, and tracking notifications
across different delivery channels (in-app, email, browser push, etc.).
"""

import json
import logging
import re
from abc import ABC, abstractmethod
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, List, Optional, Tuple

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.mail import send_mail, send_mass_mail
from django.db import models
from django.db.models import QuerySet
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import strip_tags

from apps.activity.models import (
    Activity,
    DigestEmail,
    Notification,
    NotificationDeliveryLog,
    NotificationPreference,
    PushSubscription,
)

User = get_user_model()
logger = logging.getLogger(__name__)


class SMSProviderInterface(ABC):
    """Abstract interface for SMS providers."""

    @abstractmethod
    def send_sms(
        self, to_number: str, message: str, metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Send an SMS message.

        Args:
            to_number: Phone number to send to (E.164 format)
            message: Message content
            metadata: Optional metadata for the SMS

        Returns:
            Tuple of (success: bool, response_data: dict)
        """
        pass

    @abstractmethod
    def validate_phone_number(self, phone_number: str) -> Tuple[bool, Optional[str]]:
        """
        Validate and normalize a phone number.

        Args:
            phone_number: Phone number to validate

        Returns:
            Tuple of (is_valid: bool, normalized_number: Optional[str])
        """
        pass

    @abstractmethod
    def get_delivery_status(self, message_id: str) -> Dict[str, Any]:
        """
        Get delivery status for a sent message.

        Args:
            message_id: Provider-specific message ID

        Returns:
            Dict with status information
        """
        pass


class MockSMSProvider(SMSProviderInterface):
    """Mock SMS provider for development and testing."""

    def __init__(self):
        """Initialize mock provider."""
        self.sent_messages = []
        self.delivery_statuses = {}

    def send_sms(
        self, to_number: str, message: str, metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """Send SMS (mock implementation)."""
        import uuid

        message_id = str(uuid.uuid4())

        # Simulate different scenarios based on phone number
        if to_number.endswith("0000"):
            # Simulate failure
            return False, {
                "error": "Invalid destination number",
                "error_code": "INVALID_NUMBER",
                "provider": "mock",
            }

        if to_number.endswith("9999"):
            # Simulate rate limit
            return False, {
                "error": "Rate limit exceeded",
                "error_code": "RATE_LIMIT",
                "provider": "mock",
                "retry_after": 60,
            }

        # Simulate success
        self.sent_messages.append(
            {
                "message_id": message_id,
                "to": to_number,
                "message": message,
                "metadata": metadata,
                "timestamp": timezone.now().isoformat(),
            }
        )

        self.delivery_statuses[message_id] = "delivered"

        return True, {
            "message_id": message_id,
            "provider": "mock",
            "status": "sent",
            "segments": len(message) // 160 + 1,
            "cost": 0.01 * (len(message) // 160 + 1),
        }

    def validate_phone_number(self, phone_number: str) -> Tuple[bool, Optional[str]]:
        """Validate phone number (mock implementation)."""
        # Remove all non-digit characters
        cleaned = re.sub(r"\D", "", phone_number)

        # Basic validation
        if len(cleaned) < 10 or len(cleaned) > 15:
            return False, None

        # Add country code if missing (assuming US)
        if len(cleaned) == 10:
            cleaned = f"+1{cleaned}"
        elif not cleaned.startswith("+"):
            cleaned = f"+{cleaned}"

        return True, cleaned

    def get_delivery_status(self, message_id: str) -> Dict[str, Any]:
        """Get delivery status (mock implementation)."""
        status = self.delivery_statuses.get(message_id, "unknown")
        return {
            "message_id": message_id,
            "status": status,
            "provider": "mock",
            "timestamp": timezone.now().isoformat(),
        }


class TwilioSMSProvider(SMSProviderInterface):
    """Twilio SMS provider implementation."""

    def __init__(self, account_sid: str, auth_token: str, from_number: str):
        """
        Initialize Twilio provider.

        Args:
            account_sid: Twilio account SID
            auth_token: Twilio auth token
            from_number: Twilio phone number to send from
        """
        self.account_sid = account_sid
        self.auth_token = auth_token
        self.from_number = from_number
        self._client = None

    @property
    def client(self):
        """Lazy load Twilio client."""
        if self._client is None:
            try:
                from twilio.rest import Client

                self._client = Client(self.account_sid, self.auth_token)
            except ImportError:
                logger.error("Twilio SDK not installed. Install with: pip install twilio")
                raise
        return self._client

    def send_sms(
        self, to_number: str, message: str, metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """Send SMS via Twilio."""
        try:
            # Send message
            response = self.client.messages.create(
                body=message,
                from_=self.from_number,
                to=to_number,
                status_callback=metadata.get("status_callback") if metadata else None,
            )

            return True, {
                "message_id": response.sid,
                "provider": "twilio",
                "status": response.status,
                "segments": response.num_segments,
                "price": response.price,
                "price_unit": response.price_unit,
            }

        except Exception as e:
            logger.error(f"Twilio SMS send failed: {e}")

            error_data = {
                "error": str(e),
                "provider": "twilio",
            }

            # Extract error code if available
            if hasattr(e, "code"):
                error_data["error_code"] = e.code

            return False, error_data

    def validate_phone_number(self, phone_number: str) -> Tuple[bool, Optional[str]]:
        """Validate phone number via Twilio."""
        try:
            # Use Twilio's lookup API
            lookup = self.client.lookups.v1.phone_numbers(phone_number).fetch()
            return True, lookup.phone_number
        except Exception as e:
            logger.debug(f"Phone validation failed: {e}")
            return False, None

    def get_delivery_status(self, message_id: str) -> Dict[str, Any]:
        """Get delivery status via Twilio."""
        try:
            message = self.client.messages(message_id).fetch()
            return {
                "message_id": message_id,
                "status": message.status,
                "provider": "twilio",
                "error_code": message.error_code,
                "error_message": message.error_message,
                "timestamp": message.date_updated.isoformat() if message.date_updated else None,
            }
        except Exception as e:
            logger.error(f"Failed to get delivery status: {e}")
            return {
                "message_id": message_id,
                "status": "unknown",
                "provider": "twilio",
                "error": str(e),
            }


class AWSSNSSMSProvider(SMSProviderInterface):
    """AWS SNS SMS provider implementation."""

    def __init__(self, region_name: str, access_key_id: str, secret_access_key: str):
        """
        Initialize AWS SNS provider.

        Args:
            region_name: AWS region
            access_key_id: AWS access key ID
            secret_access_key: AWS secret access key
        """
        self.region_name = region_name
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self._client = None

    @property
    def client(self):
        """Lazy load AWS SNS client."""
        if self._client is None:
            try:
                import boto3

                self._client = boto3.client(
                    "sns",
                    region_name=self.region_name,
                    aws_access_key_id=self.access_key_id,
                    aws_secret_access_key=self.secret_access_key,
                )
            except ImportError:
                logger.error("Boto3 not installed. Install with: pip install boto3")
                raise
        return self._client

    def send_sms(
        self, to_number: str, message: str, metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """Send SMS via AWS SNS."""
        try:
            # Set SMS attributes
            sms_attributes = {
                "AWS.SNS.SMS.SMSType": {
                    "DataType": "String",
                    "StringValue": metadata.get("sms_type", "Transactional") if metadata else "Transactional",
                }
            }

            # Send message
            response = self.client.publish(
                PhoneNumber=to_number,
                Message=message,
                MessageAttributes=sms_attributes,
            )

            return True, {
                "message_id": response["MessageId"],
                "provider": "aws_sns",
                "status": "sent",
                "response_metadata": response.get("ResponseMetadata", {}),
            }

        except Exception as e:
            logger.error(f"AWS SNS SMS send failed: {e}")

            error_data = {
                "error": str(e),
                "provider": "aws_sns",
            }

            # Extract error code if available
            if hasattr(e, "response"):
                error_data["error_code"] = e.response.get("Error", {}).get("Code")
                error_data["error_message"] = e.response.get("Error", {}).get("Message")

            return False, error_data

    def validate_phone_number(self, phone_number: str) -> Tuple[bool, Optional[str]]:
        """Validate phone number for AWS SNS."""
        # AWS SNS requires E.164 format
        cleaned = re.sub(r"\D", "", phone_number)

        # Basic validation
        if len(cleaned) < 10 or len(cleaned) > 15:
            return False, None

        # Ensure E.164 format
        if not cleaned.startswith("+"):
            # Assume US if no country code
            if len(cleaned) == 10:
                cleaned = f"+1{cleaned}"
            else:
                cleaned = f"+{cleaned}"

        # Validate E.164 format
        if re.match(r"^\+[1-9]\d{1,14}$", cleaned):
            return True, cleaned

        return False, None

    def get_delivery_status(self, message_id: str) -> Dict[str, Any]:
        """Get delivery status via AWS SNS."""
        # AWS SNS doesn't provide direct message status lookup
        # Would need to implement via CloudWatch logs or delivery status topics
        return {
            "message_id": message_id,
            "status": "unknown",
            "provider": "aws_sns",
            "note": "Direct status lookup not available. Check CloudWatch logs.",
        }


class SMSDeliveryService:
    """Service for managing SMS delivery with retry logic and provider abstraction."""

    def __init__(self, provider: Optional[SMSProviderInterface] = None):
        """
        Initialize SMS delivery service.

        Args:
            provider: SMS provider implementation (defaults to configured provider)
        """
        self.provider = provider or self._get_configured_provider()
        self.max_retries = getattr(settings, "SMS_MAX_RETRIES", 3)
        self.retry_delays = [5, 30, 120]  # Seconds between retries

    def _get_configured_provider(self) -> SMSProviderInterface:
        """Get provider based on Django settings."""
        provider_name = getattr(settings, "SMS_PROVIDER", "mock")

        if provider_name == "twilio":
            return TwilioSMSProvider(
                account_sid=settings.TWILIO_ACCOUNT_SID,
                auth_token=settings.TWILIO_AUTH_TOKEN,
                from_number=settings.TWILIO_FROM_NUMBER,
            )
        elif provider_name == "aws_sns":
            return AWSSNSSMSProvider(
                region_name=settings.AWS_REGION,
                access_key_id=settings.AWS_ACCESS_KEY_ID,
                secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            )
        else:
            # Default to mock provider
            return MockSMSProvider()

    def send_notification_sms(self, notification: Notification) -> Tuple[bool, Dict[str, Any]]:
        """
        Send SMS for a notification with retry logic.

        Args:
            notification: Notification to send

        Returns:
            Tuple of (success: bool, response_data: dict)
        """
        # Get recipient's phone number
        phone_number = self._get_recipient_phone(notification.recipient)
        if not phone_number:
            return False, {
                "error": "No phone number available for recipient",
                "error_code": "NO_PHONE_NUMBER",
            }

        # Validate phone number
        is_valid, normalized_number = self.provider.validate_phone_number(phone_number)
        if not is_valid:
            return False, {
                "error": "Invalid phone number format",
                "error_code": "INVALID_PHONE_FORMAT",
                "phone_number": phone_number,
            }

        # Prepare SMS message
        message = self._prepare_sms_message(notification)

        # Add metadata
        metadata = {
            "notification_id": str(notification.id),
            "notification_type": notification.notification_type,
            "organization_id": str(notification.organization.id) if notification.organization else None,
        }

        # Send with retry logic
        attempt = 0
        last_error = None

        while attempt <= self.max_retries:
            success, response = self.provider.send_sms(normalized_number, message, metadata)

            if success:
                # Log successful delivery
                logger.info(f"SMS sent successfully for notification {notification.id} to {normalized_number}")
                return True, response

            # Check if error is retryable
            if not self._is_retryable_error(response):
                logger.warning(f"Non-retryable SMS error for notification {notification.id}: {response}")
                return False, response

            # Increment retry counter
            notification.retry_count = attempt
            notification.save(update_fields=["retry_count"])

            last_error = response
            attempt += 1

            # Wait before retry (if not last attempt)
            if attempt <= self.max_retries:
                delay = self.retry_delays[min(attempt - 1, len(self.retry_delays) - 1)]
                logger.info(
                    f"Retrying SMS for notification {notification.id} in {delay} seconds "
                    f"(attempt {attempt}/{self.max_retries})"
                )
                import time

                time.sleep(delay)

        # All retries exhausted
        logger.error(f"SMS delivery failed after {self.max_retries} retries for notification {notification.id}")
        return False, last_error or {"error": "Max retries exceeded"}

    def _get_recipient_phone(self, user: User) -> Optional[str]:
        """Get phone number for user."""
        # Check user model for phone_number field
        if hasattr(user, "phone_number") and user.phone_number:
            return user.phone_number

        # Check profile if exists
        if hasattr(user, "profile") and hasattr(user.profile, "phone"):
            return user.profile.phone

        return None

    def _prepare_sms_message(self, notification: Notification) -> str:
        """Prepare SMS message content."""
        # SMS messages should be concise
        max_length = 160  # Single SMS segment

        # Start with title
        message = notification.title

        # Add shortened message if space allows
        if notification.message:
            # Calculate remaining space (with separator)
            remaining = max_length - len(message) - 3  # " - " separator

            if remaining > 20:  # Only add if meaningful content fits
                truncated_msg = notification.message[:remaining]
                if len(notification.message) > remaining:
                    truncated_msg = truncated_msg[:-3] + "..."
                message = f"{message} - {truncated_msg}"

        # Add link if provided and space allows
        if notification.data and "url" in notification.data:
            url = notification.data["url"]
            if len(message) + len(url) + 1 < max_length:
                message = f"{message}\n{url}"

        return message

    def _is_retryable_error(self, response: Dict[str, Any]) -> bool:
        """Check if error is retryable."""
        error_code = response.get("error_code", "")

        # Non-retryable errors
        non_retryable = [
            "INVALID_NUMBER",
            "BLACKLISTED",
            "UNSUBSCRIBED",
            "INVALID_CREDENTIALS",
        ]

        if error_code in non_retryable:
            return False

        # Rate limit errors are retryable
        if error_code == "RATE_LIMIT":
            return True

        # Network/timeout errors are retryable
        error_message = response.get("error", "").lower()
        retryable_keywords = ["timeout", "network", "connection", "temporary"]

        return any(keyword in error_message for keyword in retryable_keywords)


class NotificationService:
    """Service for creating and delivering notifications across multiple channels."""

    def __init__(self):
        """Initialize notification service."""
        self.batch_size = getattr(settings, "NOTIFICATION_BATCH_SIZE", 50)
        self.max_retries = getattr(settings, "NOTIFICATION_MAX_RETRIES", 3)

    def create_notification(
        self,
        recipient: User,
        notification_type: str,
        title: str,
        message: str,
        data: Dict[str, Any] = None,
        related_activity: Activity = None,
        expires_in_hours: int = 24 * 7,  # 1 week default
        organization: Any = None,
    ) -> List[Notification]:
        """
        Create notifications for all enabled delivery channels for a user.

        Args:
            recipient: User to receive the notification
            notification_type: Type of notification (from NotificationType choices)
            title: Notification title
            message: Notification message
            data: Additional data (URLs, metadata, etc.)
            related_activity: Activity that triggered this notification
            expires_in_hours: Hours until notification expires
            organization: Organization context

        Returns:
            List of created Notification instances
        """
        if not organization and related_activity:
            organization = related_activity.organization
        elif not organization and hasattr(recipient, "profile"):
            organization = getattr(recipient.profile, "organization", None)

        if not organization:
            logger.warning(f"No organization found for notification to {recipient}")
            return []

        # Get user's preferences for this notification type
        preferences = NotificationPreference.objects.filter(
            user=recipient,
            notification_type=notification_type,
            is_enabled=True,
            organization=organization,
        )

        # If no preferences exist, create default in-app notification
        if not preferences.exists():
            preferences = [self._create_default_preference(recipient, notification_type, organization)]

        notifications = []
        expires_at = timezone.now() + timedelta(hours=expires_in_hours)

        for preference in preferences:
            # Check quiet hours
            if self._is_quiet_hours(preference):
                logger.debug(f"Skipping notification to {recipient} due to quiet hours")
                continue

            # Check Do Not Disturb status
            from apps.activity.services.dnd_service import DNDService

            should_deliver, dnd_reason = DNDService.should_deliver_notification(
                user=recipient, notification_type=notification_type
            )

            if not should_deliver:
                logger.debug(f"Skipping notification to {recipient} due to DND: {dnd_reason}")
                continue

            notification = Notification.objects.create(
                recipient=recipient,
                notification_type=notification_type,
                delivery_channel=preference.delivery_channel,
                title=title,
                message=message,
                data=data or {},
                expires_at=expires_at,
                related_activity=related_activity,
                organization=organization,
            )
            notifications.append(notification)

        # Dispatch notifications immediately for immediate delivery
        immediate_notifications = [n for n in notifications if self._should_deliver_immediately(n)]

        if immediate_notifications:
            self.dispatch_notifications(immediate_notifications)

        return notifications

    def dispatch_notifications(self, notifications: List[Notification]) -> Dict[str, int]:
        """
        Dispatch a list of notifications through their respective channels.

        Args:
            notifications: List of notifications to dispatch

        Returns:
            Dict with delivery statistics
        """
        results = {"sent": 0, "failed": 0, "skipped": 0}

        # Group notifications by delivery channel for batch processing
        by_channel = {}
        for notification in notifications:
            channel = notification.delivery_channel
            if channel not in by_channel:
                by_channel[channel] = []
            by_channel[channel].append(notification)

        # Dispatch each channel
        for channel, channel_notifications in by_channel.items():
            try:
                if channel == NotificationPreference.DeliveryChannel.IN_APP:
                    results["sent"] += self._deliver_in_app(channel_notifications)
                elif channel == NotificationPreference.DeliveryChannel.EMAIL:
                    results["sent"] += self._deliver_email(channel_notifications)
                elif channel == NotificationPreference.DeliveryChannel.EMAIL_DIGEST:
                    results["skipped"] += len(channel_notifications)  # Handled by digest task
                elif channel == NotificationPreference.DeliveryChannel.BROWSER_PUSH:
                    results["sent"] += self._deliver_browser_push(channel_notifications)
                elif channel == NotificationPreference.DeliveryChannel.SMS:
                    results["sent"] += self._deliver_sms(channel_notifications)
                else:
                    logger.warning(f"Unknown delivery channel: {channel}")
                    results["skipped"] += len(channel_notifications)
            except Exception as e:
                logger.error(f"Error delivering {channel} notifications: {e}")
                for notification in channel_notifications:
                    self._log_delivery_failure(notification, str(e))
                results["failed"] += len(channel_notifications)

        return results

    def _deliver_in_app(self, notifications: List[Notification]) -> int:
        """Deliver in-app notifications (just mark as sent, SSE handles display)."""
        delivered_count = 0

        for notification in notifications:
            try:
                notification.mark_as_sent()
                notification.mark_as_delivered()

                # Broadcast via SSE
                self._broadcast_notification_websocket(notification)

                # Track delivery analytics
                self._track_delivery_analytics(notification)

                self._log_delivery_success(notification, "In-app notification sent")
                delivered_count += 1

            except Exception as e:
                logger.error(f"Failed to deliver in-app notification {notification.id}: {e}")
                notification.mark_as_failed(str(e))
                self._log_delivery_failure(notification, str(e))

        return delivered_count

    def _deliver_email(self, notifications: List[Notification]) -> int:
        """Deliver email notifications."""
        delivered_count = 0

        # Batch email sending for efficiency
        email_messages = []

        for notification in notifications:
            try:
                # Render email content
                html_content = render_to_string(
                    "activity/email/notification.html",
                    {
                        "notification": notification,
                        "recipient": notification.recipient,
                        "site_url": getattr(settings, "SITE_URL", "http://localhost:8000"),
                    },
                )
                text_content = strip_tags(html_content)

                email_messages.append(
                    (
                        notification.title,
                        text_content,
                        settings.DEFAULT_FROM_EMAIL,
                        [notification.recipient.email],
                    )
                )

                notification.mark_as_sent()

            except Exception as e:
                logger.error(f"Failed to prepare email for notification {notification.id}: {e}")
                notification.mark_as_failed(str(e))
                self._log_delivery_failure(notification, str(e))
                continue

        # Send batch emails
        if email_messages:
            try:
                send_mass_mail(email_messages, fail_silently=False)

                # Mark all as delivered
                for _i, notification in enumerate(notifications[: len(email_messages)]):
                    notification.mark_as_delivered()

                    # Track delivery analytics
                    self._track_delivery_analytics(notification)

                    self._log_delivery_success(notification, "Email sent successfully")
                    delivered_count += 1

            except Exception as e:
                logger.error(f"Failed to send batch emails: {e}")
                for notification in notifications[: len(email_messages)]:
                    notification.mark_as_failed(str(e))
                    self._log_delivery_failure(notification, str(e))

        return delivered_count

    def _deliver_browser_push(self, notifications: List[Notification]) -> int:
        """Deliver browser push notifications using Web Push API."""
        delivered_count = 0

        # Check if pywebpush is available
        try:
            from pywebpush import WebPushException, webpush
        except ImportError:
            logger.error("pywebpush library is not installed. Install it with: pip install pywebpush")
            # Mark all notifications as failed due to missing dependency
            for notification in notifications:
                notification.mark_as_failed("pywebpush library not installed")
                self._log_delivery_failure(notification, "pywebpush library not installed")
            return 0

        # Get VAPID keys from settings
        vapid_private_key = getattr(settings, "WEBPUSH_VAPID_PRIVATE_KEY", None)
        vapid_public_key = getattr(settings, "WEBPUSH_VAPID_PUBLIC_KEY", None)
        vapid_email = getattr(settings, "WEBPUSH_VAPID_EMAIL", None)

        if not all([vapid_private_key, vapid_public_key, vapid_email]):
            logger.error(
                "Web Push VAPID keys not configured. Set WEBPUSH_VAPID_PRIVATE_KEY, "
                "WEBPUSH_VAPID_PUBLIC_KEY, and WEBPUSH_VAPID_EMAIL in settings."
            )
            # Mark all notifications as failed due to missing configuration
            for notification in notifications:
                notification.mark_as_failed("Web Push not configured")
                self._log_delivery_failure(notification, "Web Push not configured")
            return 0

        # Process each notification
        for notification in notifications:
            try:
                # Get active push subscriptions for the user
                subscriptions = PushSubscription.objects.filter(
                    user=notification.recipient,
                    status=PushSubscription.SubscriptionStatus.ACTIVE,
                    organization=notification.organization,
                )

                # Filter by notification type if subscriptions have preferences
                subscriptions = [
                    sub for sub in subscriptions if sub.supports_notification_type(notification.notification_type)
                ]

                if not subscriptions:
                    logger.warning(f"No active push subscriptions found for user {notification.recipient.id}")
                    notification.mark_as_failed("No active push subscriptions")
                    self._log_delivery_failure(notification, "No active push subscriptions found")
                    continue

                # Prepare notification payload
                payload = self._prepare_push_payload(notification)

                # Send to all user's subscriptions
                successful_sends = 0
                failed_subscriptions = []

                for subscription in subscriptions:
                    try:
                        # Send the push notification
                        response = webpush(
                            subscription_info=subscription.subscription_data,
                            data=json.dumps(payload),
                            vapid_private_key=vapid_private_key,
                            vapid_claims={
                                "sub": f"mailto:{vapid_email}",
                                "aud": self._get_push_audience(subscription.endpoint),
                            },
                            ttl=86400,  # 24 hours
                            content_encoding="aes128gcm",
                        )

                        if response.status_code in [200, 201, 204]:
                            # Success
                            subscription.mark_as_used()
                            successful_sends += 1
                            logger.debug(f"Push notification sent to subscription {subscription.id}")
                        else:
                            # Non-fatal error
                            logger.warning(
                                f"Push notification failed with status {response.status_code}: {response.text}"
                            )
                            subscription.mark_as_failed()
                            failed_subscriptions.append(subscription)

                    except WebPushException as e:
                        # Handle specific Web Push errors
                        error_message = str(e)
                        logger.error(f"WebPushException for subscription {subscription.id}: {error_message}")

                        # Check if subscription is invalid/expired
                        if e.response and e.response.status_code in [404, 410]:
                            # Subscription is no longer valid
                            subscription.mark_as_expired()
                            logger.info(f"Marked subscription {subscription.id} as expired")
                        else:
                            subscription.mark_as_failed()
                            failed_subscriptions.append(subscription)

                    except Exception as e:
                        # Generic error
                        logger.error(f"Unexpected error sending push to subscription {subscription.id}: {e}")
                        subscription.mark_as_failed()
                        failed_subscriptions.append(subscription)

                # Update notification status based on results
                if successful_sends > 0:
                    notification.mark_as_sent()
                    notification.mark_as_delivered()

                    # Track delivery analytics
                    self._track_delivery_analytics(notification)

                    self._log_delivery_success(
                        notification, f"Browser push sent to {successful_sends}/{len(subscriptions)} devices"
                    )
                    delivered_count += 1
                else:
                    # All sends failed
                    notification.mark_as_failed(f"Failed to send to all {len(subscriptions)} subscriptions")
                    self._log_delivery_failure(
                        notification, f"Failed to send to all {len(subscriptions)} subscriptions"
                    )

            except Exception as e:
                logger.error(f"Failed to deliver browser push notification {notification.id}: {e}")
                notification.mark_as_failed(str(e))
                self._log_delivery_failure(notification, str(e))

        return delivered_count

    def _prepare_push_payload(self, notification: Notification) -> dict:
        """Prepare the payload for a push notification."""
        # Base payload structure following Web Push protocol
        payload = {
            "title": notification.title,
            "body": notification.message,
            "tag": f"notification-{notification.id}",
            "timestamp": int(notification.created_at.timestamp() * 1000),
            "requireInteraction": notification.notification_type
            in [
                NotificationPreference.NotificationType.CRITICAL_ALERT,
                NotificationPreference.NotificationType.SYSTEM_ALERT,
            ],
            "data": {
                "notificationId": str(notification.id),
                "type": notification.notification_type,
                "url": notification.data.get("url", "/notifications/"),
            },
        }

        # Add icon if configured
        notification_icon = getattr(settings, "WEBPUSH_NOTIFICATION_ICON", None)
        if notification_icon:
            payload["icon"] = notification_icon

        # Add badge if configured
        notification_badge = getattr(settings, "WEBPUSH_NOTIFICATION_BADGE", None)
        if notification_badge:
            payload["badge"] = notification_badge

        # Add actions based on notification type
        actions = self._get_notification_actions(notification)
        if actions:
            payload["actions"] = actions

        # Add additional data from notification
        if notification.data:
            payload["data"].update(notification.data)

        return payload

    def _get_notification_actions(self, notification: Notification) -> List[dict]:
        """Get appropriate actions for a notification type."""
        actions = []

        # Define actions based on notification type
        if notification.notification_type == NotificationPreference.NotificationType.COMMENT:
            actions = [
                {"action": "reply", "title": "Reply", "icon": "/static/icons/reply.png"},
                {"action": "view", "title": "View", "icon": "/static/icons/view.png"},
            ]
        elif notification.notification_type == NotificationPreference.NotificationType.MENTION:
            actions = [
                {"action": "view", "title": "View", "icon": "/static/icons/view.png"},
                {"action": "dismiss", "title": "Dismiss", "icon": "/static/icons/dismiss.png"},
            ]
        elif notification.notification_type in [
            NotificationPreference.NotificationType.PROJECT_UPDATE,
            NotificationPreference.NotificationType.DOCUMENT_CHANGE,
        ]:
            actions = [
                {"action": "view", "title": "View Changes", "icon": "/static/icons/view.png"},
                {"action": "later", "title": "Later", "icon": "/static/icons/later.png"},
            ]

        return actions

    def _get_push_audience(self, endpoint: str) -> str:
        """Extract the audience (origin) from a push endpoint URL."""
        from urllib.parse import urlparse

        parsed = urlparse(endpoint)
        return f"{parsed.scheme}://{parsed.netloc}"

    def _deliver_sms(self, notifications: List[Notification]) -> int:
        """Deliver SMS notifications."""
        delivered_count = 0

        # Initialize SMS delivery service
        sms_service = SMSDeliveryService()

        for notification in notifications:
            try:
                # Send SMS using the delivery service
                success, response_data = sms_service.send_notification_sms(notification)

                if success:
                    notification.mark_as_sent()
                    notification.mark_as_delivered()

                    # Store delivery details in notification data
                    if not notification.data:
                        notification.data = {}
                    notification.data["sms_delivery"] = {
                        "provider": response_data.get("provider"),
                        "message_id": response_data.get("message_id"),
                        "segments": response_data.get("segments", 1),
                        "cost": response_data.get("cost"),
                        "delivered_at": timezone.now().isoformat(),
                    }
                    notification.save(update_fields=["data"])

                    self._log_delivery_success(notification, f"SMS sent via {response_data.get('provider', 'unknown')}")
                    delivered_count += 1
                else:
                    # Handle failure
                    error_message = response_data.get("error", "Unknown SMS delivery error")
                    notification.mark_as_failed(error_message)
                    self._log_delivery_failure(notification, error_message)

                    # If phone number issue, disable SMS for user
                    if response_data.get("error_code") in ["NO_PHONE_NUMBER", "INVALID_PHONE_FORMAT"]:
                        self._disable_sms_preference(notification.recipient, notification.organization)

            except Exception as e:
                logger.error(f"Failed to deliver SMS notification {notification.id}: {e}")
                notification.mark_as_failed(str(e))
                self._log_delivery_failure(notification, str(e))

        return delivered_count

    def _disable_sms_preference(self, user: User, organization: Any) -> None:
        """Disable SMS preferences for user when phone number issues occur."""
        try:
            NotificationPreference.objects.filter(
                user=user,
                organization=organization,
                delivery_channel=NotificationPreference.DeliveryChannel.SMS,
            ).update(is_enabled=False)

            logger.info(f"Disabled SMS preferences for user {user.id} due to phone number issues")
        except Exception as e:
            logger.error(f"Failed to disable SMS preferences for user {user.id}: {e}")

    def _broadcast_notification_websocket(self, notification: Notification) -> None:
        """Broadcast notification via SSE."""
        try:
            from apps.activity.signals import (
                broadcast_notification_count_update,
                broadcast_notification_update,
            )

            broadcast_notification_update(notification)
            broadcast_notification_count_update(notification.recipient)
        except Exception as e:
            logger.error(f"Failed to broadcast notification via SSE: {e}")


    def cleanup_expired_notifications(self) -> int:
        """Remove expired notifications."""
        expired = Notification.objects.filter(expires_at__lte=timezone.now())
        count = expired.count()
        expired.delete()
        logger.info(f"Cleaned up {count} expired notifications")
        return count

    def retry_failed_notifications(self) -> Dict[str, int]:
        """Retry failed notifications that haven't exceeded max retries."""
        results = {"retried": 0, "skipped": 0, "failed": 0}

        failed_notifications = Notification.objects.filter(
            status=Notification.NotificationStatus.FAILED,
            retry_count__lt=self.max_retries,
            expires_at__gt=timezone.now(),
        )

        for notification in failed_notifications:
            try:
                # Reset status for retry
                notification.status = Notification.NotificationStatus.PENDING
                notification.save(update_fields=["status"])

                # Dispatch single notification
                dispatch_results = self.dispatch_notifications([notification])

                if dispatch_results["sent"] > 0:
                    results["retried"] += 1
                else:
                    results["failed"] += 1

            except Exception as e:
                logger.error(f"Error retrying notification {notification.id}: {e}")
                results["failed"] += 1

        return results

    def _should_deliver_immediately(self, notification: Notification) -> bool:
        """Check if notification should be delivered immediately."""
        immediate_channels = [
            NotificationPreference.DeliveryChannel.IN_APP,
            NotificationPreference.DeliveryChannel.EMAIL,
            NotificationPreference.DeliveryChannel.BROWSER_PUSH,
            NotificationPreference.DeliveryChannel.SMS,
        ]
        return notification.delivery_channel in immediate_channels

    def _is_quiet_hours(self, preference: NotificationPreference) -> bool:
        """Check if current time is within user's quiet hours."""
        if not preference.quiet_hours_start or not preference.quiet_hours_end:
            return False

        now = timezone.now().time()
        start = preference.quiet_hours_start
        end = preference.quiet_hours_end

        if start <= end:
            # Same day quiet hours
            return start <= now <= end
        else:
            # Overnight quiet hours
            return now >= start or now <= end

    def _create_default_preference(
        self, user: User, notification_type: str, organization: Any
    ) -> NotificationPreference:
        """Create default notification preference for user."""
        preference = NotificationPreference.objects.create(
            user=user,
            organization=organization,
            notification_type=notification_type,
            delivery_channel=NotificationPreference.DeliveryChannel.IN_APP,
            is_enabled=True,
        )
        logger.info(f"Created default notification preference for {user}")
        return preference

    def _log_delivery_success(self, notification: Notification, message: str) -> None:
        """Log successful delivery."""
        NotificationDeliveryLog.objects.create(
            notification=notification,
            delivery_channel=notification.delivery_channel,
            status=Notification.NotificationStatus.DELIVERED,
            response_data={"message": message},
            attempt_number=notification.retry_count + 1,
        )

    def _log_delivery_failure(self, notification: Notification, error_message: str) -> None:
        """Log delivery failure."""
        NotificationDeliveryLog.objects.create(
            notification=notification,
            delivery_channel=notification.delivery_channel,
            status=Notification.NotificationStatus.FAILED,
            error_message=error_message,
            attempt_number=notification.retry_count + 1,
        )

    def get_user_notification_stats(self, user: User) -> Dict[str, Any]:
        """Get notification statistics for a user."""
        notifications = Notification.objects.filter(recipient=user)

        return {
            "total": notifications.count(),
            "unread": notifications.filter(is_read=False).count(),
            "pending": notifications.filter(status=Notification.NotificationStatus.PENDING).count(),
            "failed": notifications.filter(status=Notification.NotificationStatus.FAILED).count(),
            "by_type": dict(
                notifications.values("notification_type")
                .annotate(count=models.Count("id"))
                .values_list("notification_type", "count")
            ),
            "by_channel": dict(
                notifications.values("delivery_channel")
                .annotate(count=models.Count("id"))
                .values_list("delivery_channel", "count")
            ),
        }

    def create_activity_notification(
        self,
        activity: Activity,
        users: List[User] = None,
        notification_type: str = None,
    ) -> List[Notification]:
        """
        Create notifications for an activity.

        Args:
            activity: Activity to create notifications for
            users: List of users to notify (if None, determines automatically)
            notification_type: Override notification type

        Returns:
            List of created notifications
        """
        if not users:
            users = self._get_activity_notification_recipients(activity)

        if not notification_type:
            notification_type = self._determine_notification_type_from_activity(activity)

        all_notifications = []

        for user in users:
            notifications = self.create_notification(
                recipient=user,
                notification_type=notification_type,
                title=f"New {activity.get_activity_type_display()}",
                message=activity.description,
                data={
                    "activity_id": activity.id,
                    "activity_type": activity.activity_type,
                    "url": f"/activity/{activity.id}/",
                },
                related_activity=activity,
                organization=activity.organization,
            )
            all_notifications.extend(notifications)

        return all_notifications

    def _get_activity_notification_recipients(self, activity: Activity) -> List[User]:
        """Determine which users should be notified about an activity."""
        # Basic implementation - notify all org members
        # Could be enhanced with more sophisticated rules
        if hasattr(activity.organization, "members"):
            return list(activity.organization.members.all())
        return []

    def _determine_notification_type_from_activity(self, activity: Activity) -> str:
        """Map activity type to notification type."""
        mapping = {
            Activity.ActivityType.PROJECT_UPDATE: NotificationPreference.NotificationType.PROJECT_UPDATE,
            Activity.ActivityType.DOCUMENT_CHANGE: NotificationPreference.NotificationType.DOCUMENT_CHANGE,
            Activity.ActivityType.COMMENT: NotificationPreference.NotificationType.COMMENT,
            Activity.ActivityType.SYSTEM_EVENT: NotificationPreference.NotificationType.SYSTEM_ALERT,
        }
        return mapping.get(
            activity.activity_type,
            NotificationPreference.NotificationType.ACTIVITY_UPDATE,
        )

    def _track_delivery_analytics(self, notification: Notification) -> None:
        """Track delivery analytics for notification."""
        try:
            from apps.activity.models import NotificationAnalytics

            NotificationAnalytics.record_delivery(
                notification=notification,
                metadata={
                    "service_tracked": True,
                    "delivery_timestamp": timezone.now().isoformat(),
                    "delivery_method": "notification_service",
                },
            )
            logger.debug(f"Tracked delivery analytics for notification {notification.id}")

        except Exception as e:
            logger.error(f"Failed to track delivery analytics: {e}")

    def track_notification_open(self, notification: Notification, request=None) -> None:
        """Track when a notification is opened/read."""
        try:
            from apps.activity.models import NotificationAnalytics

            # Check if we already tracked this open event
            existing_open = NotificationAnalytics.objects.filter(
                notification=notification,
                metric_type=NotificationAnalytics.MetricType.OPEN,
            ).exists()

            if not existing_open:
                metadata = {
                    "service_tracked": True,
                    "open_timestamp": timezone.now().isoformat(),
                }

                # Add request metadata if available
                if request:
                    metadata.update(
                        {
                            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                            "ip_address": self._get_client_ip(request),
                            "referer": request.META.get("HTTP_REFERER", ""),
                        }
                    )

                NotificationAnalytics.record_open(notification=notification, metadata=metadata)
                logger.debug(f"Tracked open analytics for notification {notification.id}")

        except Exception as e:
            logger.error(f"Failed to track open analytics: {e}")

    def track_notification_click(self, notification: Notification, click_url: str, request=None) -> None:
        """Track when a notification link is clicked."""
        try:
            from apps.activity.models import NotificationAnalytics

            metadata = {
                "service_tracked": True,
                "click_timestamp": timezone.now().isoformat(),
            }

            # Add request metadata if available
            if request:
                metadata.update(
                    {
                        "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                        "ip_address": self._get_client_ip(request),
                        "referer": request.META.get("HTTP_REFERER", ""),
                    }
                )

            NotificationAnalytics.record_click(notification=notification, click_url=click_url, metadata=metadata)
            logger.debug(f"Tracked click analytics for notification {notification.id}: {click_url}")

        except Exception as e:
            logger.error(f"Failed to track click analytics: {e}")

    def _get_client_ip(self, request) -> str:
        """Extract client IP address from request."""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0]
        else:
            ip = request.META.get("REMOTE_ADDR")
        return ip or "unknown"

    def process_digest_notifications(self, frequency: str) -> Dict[str, Any]:
        """
        Process and send email digest notifications for a given frequency.

        Args:
            frequency: Digest frequency ('hourly', 'daily', 'weekly')

        Returns:
            Dict with processing results
        """

        now = timezone.now()

        # Calculate period boundaries based on frequency
        if frequency == "hourly":
            period_end = now.replace(minute=0, second=0, microsecond=0)
            period_start = period_end - timedelta(hours=1)
        elif frequency == "daily":
            period_end = now.replace(hour=0, minute=0, second=0, microsecond=0)
            period_start = period_end - timedelta(days=1)
        elif frequency == "weekly":
            # Weekly digest on Monday
            days_since_monday = now.weekday()
            period_end = now.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=days_since_monday)
            period_start = period_end - timedelta(days=7)
        else:
            logger.error(f"Invalid digest frequency: {frequency}")
            return {"error": f"Invalid frequency: {frequency}"}

        # Get users who have digest preferences enabled for this frequency
        digest_preferences = NotificationPreference.objects.filter(
            delivery_channel=NotificationPreference.DeliveryChannel.EMAIL_DIGEST,
            digest_frequency=frequency,
            is_enabled=True,
        ).select_related("user")

        results = {
            "frequency": frequency,
            "period_start": period_start.isoformat(),
            "period_end": period_end.isoformat(),
            "digests_created": 0,
            "digests_sent": 0,
            "users_processed": 0,
            "errors": [],
        }

        for preference in digest_preferences:
            user = preference.user

            try:
                # Check if digest already exists for this period
                existing_digest = DigestEmail.objects.filter(
                    recipient=user,
                    frequency=frequency,
                    period_start=period_start,
                    organization=(user.organization if hasattr(user, "organization") else None),
                ).first()

                if existing_digest:
                    if existing_digest.status == DigestEmail.DigestStatus.SENT:
                        continue  # Already sent
                    elif existing_digest.status == DigestEmail.DigestStatus.FAILED and not existing_digest.can_retry:
                        continue  # Failed and can't retry
                else:
                    # Create new digest
                    existing_digest = DigestEmail.objects.create(
                        recipient=user,
                        frequency=frequency,
                        period_start=period_start,
                        period_end=period_end,
                        organization=(user.organization if hasattr(user, "organization") else None),
                    )
                    results["digests_created"] += 1

                # Generate and send digest
                if self._generate_and_send_digest(existing_digest, preference):
                    results["digests_sent"] += 1

                results["users_processed"] += 1

            except Exception as e:
                error_msg = f"Error processing digest for user {user.id}: {e}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                continue

        return results

    def _generate_and_send_digest(self, digest: "DigestEmail", preference: NotificationPreference) -> bool:
        """
        Generate digest content and send email.

        Args:
            digest: DigestEmail instance
            preference: User's notification preference

        Returns:
            True if successfully sent, False otherwise
        """

        try:
            digest.mark_as_generating()

            # Get notifications for digest period
            notifications = self._get_digest_notifications(digest, preference)

            if not notifications.exists():
                # No notifications to include, mark as sent but don't send email
                digest.mark_as_sent()
                return True

            # Generate digest content
            html_content, text_content = self._render_digest_content(digest, notifications)

            # Mark digest as ready
            digest.mark_as_ready(html_content, text_content, notifications.count())

            # Add notifications to digest
            digest.notifications.set(notifications)

            # Send email
            self._send_digest_email(digest)

            return True

        except Exception as e:
            logger.error(f"Error generating/sending digest {digest.id}: {e}")
            digest.mark_as_failed(str(e))
            return False

    def _get_digest_notifications(self, digest: "DigestEmail", preference: NotificationPreference) -> "QuerySet":
        """
        Get notifications to include in digest based on user preferences and digest period.

        Args:
            digest: DigestEmail instance
            preference: User's notification preference

        Returns:
            QuerySet of notifications to include
        """
        # Get all notification types this user wants in digests
        user_digest_preferences = NotificationPreference.objects.filter(
            user=digest.recipient,
            delivery_channel=NotificationPreference.DeliveryChannel.EMAIL_DIGEST,
            digest_frequency=digest.frequency,
            is_enabled=True,
        ).values_list("notification_type", flat=True)

        # Get notifications in the digest period that haven't been sent via digest yet
        notifications = (
            Notification.objects.filter(
                recipient=digest.recipient,
                notification_type__in=user_digest_preferences,
                delivery_channel=NotificationPreference.DeliveryChannel.EMAIL_DIGEST,
                created__gte=digest.period_start,
                created__lt=digest.period_end,
                status=Notification.NotificationStatus.PENDING,
            )
            .exclude(digest_emails__status=DigestEmail.DigestStatus.SENT)
            .order_by("-created")
        )

        return notifications

    def _render_digest_content(self, digest: "DigestEmail", notifications: "QuerySet") -> tuple[str, str]:
        """
        Render HTML and text content for digest email.

        Args:
            digest: DigestEmail instance
            notifications: QuerySet of notifications to include

        Returns:
            Tuple of (html_content, text_content)
        """
        # Group notifications by type for better organization
        notifications_by_type = {}
        for notification in notifications:
            type_display = notification.get_notification_type_display()
            if type_display not in notifications_by_type:
                notifications_by_type[type_display] = []
            notifications_by_type[type_display].append(notification)

        context = {
            "digest": digest,
            "recipient": digest.recipient,
            "notifications": notifications,
            "notifications_by_type": notifications_by_type,
            "notification_count": notifications.count(),
            "site_url": getattr(settings, "SITE_URL", "http://localhost:8000"),
            "unsubscribe_url": (
                f"{getattr(settings, 'SITE_URL', 'http://localhost:8000')}"
                f"/notifications/unsubscribe/{digest.recipient.id}/"
            ),
        }

        # Render HTML content
        html_content = render_to_string("activity/email/digest.html", context)

        # Render text content
        text_content = render_to_string("activity/email/digest.txt", context)

        return html_content, text_content

    def _send_digest_email(self, digest: "DigestEmail") -> None:
        """
        Send the digest email.

        Args:
            digest: DigestEmail instance with generated content
        """
        try:
            subject = (
                f"{digest.get_frequency_display()} Notification Digest - {digest.period_start.strftime('%B %d, %Y')}"
            )

            send_mail(
                subject=subject,
                message=digest.content_text,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[digest.recipient.email],
                html_message=digest.content_html,
                fail_silently=False,
            )

            # Mark notifications as sent
            digest.notifications.update(status=Notification.NotificationStatus.SENT, sent_at=timezone.now())

            digest.mark_as_sent()

            logger.info(f"Sent digest email {digest.id} to {digest.recipient.email}")

        except Exception as e:
            logger.error(f"Failed to send digest email {digest.id}: {e}")
            digest.mark_as_failed(str(e))
            raise

    def register_push_subscription(
        self,
        user: User,
        subscription_info: dict,
        organization: Any,
        device_name: Optional[str] = None,
        user_agent: Optional[str] = None,
    ) -> PushSubscription:
        """
        Register a new push subscription for a user.

        Args:
            user: User to register subscription for
            subscription_info: Subscription info from browser (endpoint, keys)
            organization: Organization context
            device_name: Optional human-readable device name
            user_agent: Optional browser user agent string

        Returns:
            Created or updated PushSubscription instance
        """
        try:
            # Extract subscription data
            endpoint = subscription_info.get("endpoint")
            keys = subscription_info.get("keys", {})
            p256dh = keys.get("p256dh")
            auth = keys.get("auth")

            if not all([endpoint, p256dh, auth]):
                raise ValueError("Missing required subscription fields")

            # Check if subscription already exists
            existing_subscription = PushSubscription.objects.filter(
                user=user,
                endpoint=endpoint,
            ).first()

            if existing_subscription:
                # Update existing subscription
                existing_subscription.p256dh_key = p256dh
                existing_subscription.auth_key = auth
                existing_subscription.status = PushSubscription.SubscriptionStatus.ACTIVE
                existing_subscription.failure_count = 0
                existing_subscription.last_failure = None

                if device_name:
                    existing_subscription.device_name = device_name
                if user_agent:
                    existing_subscription.user_agent = user_agent

                existing_subscription.save()
                logger.info(f"Updated push subscription {existing_subscription.id} for user {user.id}")
                return existing_subscription

            # Create new subscription
            subscription = PushSubscription.objects.create(
                user=user,
                organization=organization,
                endpoint=endpoint,
                p256dh_key=p256dh,
                auth_key=auth,
                device_name=device_name or "Unknown Device",
                user_agent=user_agent or "",
                status=PushSubscription.SubscriptionStatus.ACTIVE,
            )

            logger.info(f"Created new push subscription {subscription.id} for user {user.id}")
            return subscription

        except Exception as e:
            logger.error(f"Failed to register push subscription for user {user.id}: {e}")
            raise

    def unregister_push_subscription(self, user: User, endpoint: str) -> bool:
        """
        Unregister a push subscription for a user.

        Args:
            user: User who owns the subscription
            endpoint: Push endpoint URL to unregister

        Returns:
            True if subscription was found and revoked, False otherwise
        """
        try:
            subscription = PushSubscription.objects.filter(
                user=user,
                endpoint=endpoint,
            ).first()

            if subscription:
                subscription.mark_as_revoked()
                logger.info(f"Revoked push subscription {subscription.id} for user {user.id}")
                return True

            logger.warning(f"Push subscription not found for user {user.id} with endpoint {endpoint}")
            return False

        except Exception as e:
            logger.error(f"Failed to unregister push subscription: {e}")
            return False

    def get_user_push_subscriptions(self, user: User, active_only: bool = True) -> List[PushSubscription]:
        """
        Get all push subscriptions for a user.

        Args:
            user: User to get subscriptions for
            active_only: If True, only return active subscriptions

        Returns:
            List of PushSubscription instances
        """
        queryset = PushSubscription.objects.filter(user=user)

        if active_only:
            queryset = queryset.filter(status=PushSubscription.SubscriptionStatus.ACTIVE)

        return list(queryset.order_by("-created_at"))

    def cleanup_expired_push_subscriptions(self) -> int:
        """
        Clean up expired push subscriptions.

        Returns:
            Number of subscriptions cleaned up
        """
        now = timezone.now()

        # Mark expired subscriptions
        expired_count = PushSubscription.objects.filter(
            expires_at__lte=now,
            status=PushSubscription.SubscriptionStatus.ACTIVE,
        ).update(status=PushSubscription.SubscriptionStatus.EXPIRED)

        # Delete very old expired/revoked subscriptions (older than 30 days)
        cutoff_date = now - timedelta(days=30)
        deleted_count = PushSubscription.objects.filter(
            status__in=[
                PushSubscription.SubscriptionStatus.EXPIRED,
                PushSubscription.SubscriptionStatus.REVOKED,
            ],
            created_at__lt=cutoff_date,
        ).delete()[0]

        logger.info(f"Cleaned up push subscriptions: {expired_count} expired, {deleted_count} deleted")

        return expired_count + deleted_count

    def test_push_subscription(self, subscription: PushSubscription, test_message: Optional[str] = None) -> bool:
        """
        Test a push subscription by sending a test notification.

        Args:
            subscription: PushSubscription to test
            test_message: Optional custom test message

        Returns:
            True if test was successful, False otherwise
        """
        try:
            from pywebpush import WebPushException, webpush
        except ImportError:
            logger.error("pywebpush library is not installed")
            return False

        # Get VAPID configuration
        vapid_private_key = getattr(settings, "WEBPUSH_VAPID_PRIVATE_KEY", None)
        vapid_email = getattr(settings, "WEBPUSH_VAPID_EMAIL", None)

        if not all([vapid_private_key, vapid_email]):
            logger.error("Web Push VAPID keys not configured")
            return False

        # Prepare test payload
        payload = {
            "title": "Test Notification",
            "body": test_message or "This is a test notification from CLEAR",
            "tag": "test-notification",
            "icon": getattr(settings, "WEBPUSH_NOTIFICATION_ICON", None),
            "data": {
                "test": True,
                "timestamp": int(timezone.now().timestamp() * 1000),
            },
        }

        try:
            response = webpush(
                subscription_info=subscription.subscription_data,
                data=json.dumps(payload),
                vapid_private_key=vapid_private_key,
                vapid_claims={
                    "sub": f"mailto:{vapid_email}",
                    "aud": self._get_push_audience(subscription.endpoint),
                },
                ttl=3600,  # 1 hour
                content_encoding="aes128gcm",
            )

            if response.status_code in [200, 201, 204]:
                subscription.mark_as_used()
                logger.info(f"Test notification sent successfully to subscription {subscription.id}")
                return True
            else:
                logger.warning(f"Test notification failed with status {response.status_code}")
                return False

        except WebPushException as e:
            logger.error(f"WebPushException during test: {e}")
            if e.response and e.response.status_code in [404, 410]:
                subscription.mark_as_expired()
            return False
        except Exception as e:
            logger.error(f"Unexpected error during push test: {e}")
            return False
