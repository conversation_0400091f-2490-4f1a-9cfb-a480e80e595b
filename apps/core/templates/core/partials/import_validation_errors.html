<!-- Import Validation Errors Template -->
<div class="alert alert-warning border-warning" role="alert">
  <div class="d-flex align-items-center mb-3">
    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
    <div>
      <h6 class="mb-1">Validation Errors Found</h6>
      <p class="mb-0">
        {{ total_errors }} error{{ total_errors|pluralize }} found in the uploaded file. Please fix the issues and try again.
      </p>
    </div>
  </div>
  <div class="validation-errors">

    {% for error_info in errors_by_row %}
      <div class="border rounded p-2 mb-2 bg-light">
        <div class="fw-bold text-danger">
          <i class="fas fa-times-circle me-1"></i>
          Row {{ error_info.row }}:
        </div>
        <ul class="mb-0 mt-1 ps-3">

          {% for error in error_info.errors %}<li class="text-muted">{{ error }}</li>{% endfor %}

        </ul>
      </div>
    {% endfor %}

  </div>
  <div class="mt-3">
    <button type="button"
            class="btn btn-outline-warning btn-sm"
            onclick="location.reload()">
      <i class="fas fa-upload me-1"></i>Upload Corrected File
    </button>
  </div>
</div>
