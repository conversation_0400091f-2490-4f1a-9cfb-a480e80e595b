{% load i18n %}

<div class="card border-success">
  <div class="card-header bg-success text-white">
    <h5 class="card-title mb-0">
      <i class="fas fa-check-circle me-2"></i>
      {% trans "Resource Created Successfully" %}
    </h5>
  </div>
  <div class="card-body">
    <div class="alert alert-success d-flex align-items-center" role="alert">
      <i class="fas fa-check-circle fa-2x me-3"></i>
      <div>
        <h6 class="alert-heading mb-2">{% trans "Success!" %}</h6>
        <p class="mb-1">
          <strong>{{ resource_type }}</strong> "{{ resource_name }}" {% trans "has been created successfully." %}
        </p>
        <p class="mb-0 small text-muted">{% trans "The resource is now available in the system." %}</p>
      </div>
    </div>
    <div class="d-flex justify-content-between align-items-center mt-3">
      <div class="text-muted small">
        <i class="fas fa-clock me-1"></i>
        {% trans "Created at" %} {{ now|date:"H:i" }}
      </div>
      <div class="d-flex gap-2">
        <a href="{% url 'core:create' %}" class="btn btn-outline-primary">
          <i class="fas fa-plus me-2"></i>
          {% trans "Create Another" %}
        </a>
        <a href="{{ redirect_url }}" class="btn btn-primary">
          <i class="fas fa-arrow-right me-2"></i>
          {% trans "Back to Dashboard" %}
        </a>
      </div>
    </div>
  </div>
</div>
<script>
// Auto-redirect after 3 seconds
setTimeout(function() {
    window.location.href = "{{ redirect_url }}";
}, 3000);

// Show success notification if available
if (typeof bootstrap !== 'undefined') {
    const toastHtml = `
        <div class="toast align-items-center text-bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ resource_type }} "{{ resource_name }}" {% trans "created successfully" %}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Add toast to container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Show the toast
    const toastElement = toastContainer.lastElementChild;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
}
</script>
