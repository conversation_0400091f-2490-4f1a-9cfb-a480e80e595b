<div class="recent-activity-container"
        {% if refresh_url %}
     hx-get="{{ refresh_url }}"
     hx-trigger="every {{ refresh_interval|default:60 }}s"
     hx-target="this"
     hx-swap="outerHTML"
        {% endif %}>

  <div class="card shadow-sm">
    <div class="card-header bg-white border-bottom-0 pb-0">
      <div class="d-flex align-items-center justify-content-between">
        <h5 class="card-title mb-0">
          <i class="bi bi-clock-history me-2 text-primary" aria-hidden="true"></i>
          {{ title|default:'Recent Activity' }}
        </h5>
        {% if activity_url %}
          <a href="{{ activity_url }}"
             class="btn btn-outline-primary btn-sm"
                  {% if activity_htmx %}
             hx-get="{{ activity_url }}"
             hx-target="#main-content"
                  {% endif %}>
            <i class="bi bi-arrow-right" aria-hidden="true"></i>
            View All
          </a>
        {% endif %}
      </div>
    </div>

    <div class="card-body pt-3">
      {% if activities %}
        <div class="activity-list" id="activity-list">
          {% for activity in activities %}
            <div
                    class="activity-item {% if not forloop.last %}border-bottom{% endif %} pb-3 {% if not forloop.first %}pt-3{% endif %}">
              <div class="d-flex align-items-start">
                <!-- Activity Icon -->
                <div class="activity-icon me-3 flex-shrink-0">
                  {% if activity.type == 'project_created' %}
                    <div class="bg-success text-white rounded-circle p-1">
                      <i class="bi bi-plus-circle" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'project_updated' %}
                    <div class="bg-info text-white rounded-circle p-1">
                      <i class="bi bi-pencil" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'conflict_detected' %}
                    <div class="bg-danger text-white rounded-circle p-1">
                      <i class="bi bi-exclamation-triangle" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'conflict_resolved' %}
                    <div class="bg-success text-white rounded-circle p-1">
                      <i class="bi bi-check-circle" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'comment_added' %}
                    <div class="bg-primary text-white rounded-circle p-1">
                      <i class="bi bi-chat" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'file_uploaded' %}
                    <div class="bg-warning text-white rounded-circle p-1">
                      <i class="bi bi-file-earmark" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'task_completed' %}
                    <div class="bg-success text-white rounded-circle p-1">
                      <i class="bi bi-check-square" aria-hidden="true"></i>
                    </div>
                  {% elif activity.type == 'user_assigned' %}
                    <div class="bg-info text-white rounded-circle p-1">
                      <i class="bi bi-person-plus" aria-hidden="true"></i>
                    </div>
                  {% else %}
                    <div class="bg-secondary text-white rounded-circle p-1">
                      <i class="bi bi-circle" aria-hidden="true"></i>
                    </div>
                  {% endif %}
                </div>

                <!-- Activity Content -->
                <div class="activity-content flex-grow-1 {% if compact %}small{% endif %}">
                  <div class="activity-description">
                    {% if activity.user %}
                      <strong>{{ activity.user.get_full_name|default:activity.user.username }}</strong>
                    {% endif %}

                    {% if activity.type == 'project_created' %}
                      created a new project
                    {% elif activity.type == 'project_updated' %}
                      updated project details
                    {% elif activity.type == 'conflict_detected' %}
                      detected a conflict
                    {% elif activity.type == 'conflict_resolved' %}
                      resolved a conflict
                    {% elif activity.type == 'comment_added' %}
                      added a comment
                    {% elif activity.type == 'file_uploaded' %}
                      uploaded a file
                    {% elif activity.type == 'task_completed' %}
                      completed a task
                    {% elif activity.type == 'user_assigned' %}
                      was assigned to a project
                    {% else %}
                      performed an action
                    {% endif %}

                    {% if activity.target_object %}
                      {% if activity.target_url %}
                        <a href="{{ activity.target_url }}"
                           class="text-decoration-none fw-medium"
                                {% if activity.target_htmx %}
                           hx-get="{{ activity.target_url }}"
                           hx-target="#main-content"
                                {% endif %}>
                          {{ activity.target_object }}
                        </a>
                      {% else %}
                        <span class="fw-medium">{{ activity.target_object }}</span>
                      {% endif %}
                    {% endif %}
                  </div>

                  {% if activity.description and not compact %}
                    <div class="activity-details text-muted small mt-1">
                      {{ activity.description|truncatewords:20 }}
                    </div>
                  {% endif %}

                  <div class="activity-meta text-muted small mt-1">
                    <time datetime="{{ activity.created_at|date:'c' }}"
                          title="{{ activity.created_at|date:'F j, Y g:i A' }}">
                      {% if activity.created_at|timesince == "0 minutes" %}
                        Just now
                      {% else %}
                        {{ activity.created_at|timesince }} ago
                      {% endif %}
                    </time>

                    {% if activity.project and not compact %}
                      <span class="mx-1">•</span>
                      {% if activity.project_url %}
                        <a href="{{ activity.project_url }}"
                           class="text-muted text-decoration-none"
                                {% if activity.project_htmx %}
                           hx-get="{{ activity.project_url }}"
                           hx-target="#main-content"
                                {% endif %}>
                          {{ activity.project.name }}
                        </a>
                      {% else %}
                        {{ activity.project.name }}
                      {% endif %}
                    {% endif %}
                  </div>
                </div>

                <!-- Activity Actions -->
                {% if activity.actions and not compact %}
                  <div class="activity-actions flex-shrink-0 ms-2">
                    <div class="dropdown">
                      <button class="btn btn-link btn-sm text-muted"
                              type="button"
                              data-bs-toggle="dropdown"
                              aria-expanded="false">
                        <i class="bi bi-three-dots" aria-hidden="true"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end">
                        {% for action in activity.actions %}
                          <li>
                            <a class="dropdown-item"
                               href="{{ action.url }}"
                                    {% if action.htmx %}
                               hx-{{ action.method|default:'get' }}="{{ action.url }}"
                               hx-target="{{ action.target|default:'#main-content' }}"
                                      {% if action.confirm %}
                               hx-confirm="{{ action.confirm }}"
                                      {% endif %}
                                    {% endif %}>
                              {% if action.icon %}
                                <i class="bi bi-{{ action.icon }} me-2" aria-hidden="true"></i>
                              {% endif %}
                              {{ action.label }}
                            </a>
                          </li>
                        {% endfor %}
                      </ul>
                    </div>
                  </div>
                {% endif %}
              </div>
            </div>
          {% empty %}
            <div class="text-center text-muted py-3">
              <i class="bi bi-clock-history display-6 text-muted mb-3" aria-hidden="true"></i>
              <p>No recent activity to display.</p>
            </div>
          {% endfor %}
        </div>

        <!-- Load More Button -->
        {% if show_load_more|default:True and activities|length >= max_items|default:10 %}
          <div class="text-center mt-3 pt-3 border-top">
            <button type="button"
                    class="btn btn-outline-primary"
                    {% if activity_url %}
                    hx-get="{{ activity_url }}"
                    hx-target="#activity-list"
                    hx-swap="beforeend"
                    hx-vals="{&quot;offset&quot;: {{ activities|length }}}"
                    {% endif %}>
              <i class="bi bi-arrow-down-circle me-2" aria-hidden="true"></i>
              Load More Activities
            </button>
          </div>
        {% endif %}

      {% else %}
        <div class="text-center text-muted py-3">
          <i class="bi bi-clock-history display-6 text-muted mb-3" aria-hidden="true"></i>
          <p>No recent activity to display.</p>
          {% if activity_url %}
            <a href="{{ activity_url }}"
               class="btn btn-primary"
                    {% if activity_htmx %}
               hx-get="{{ activity_url }}"
               hx-target="#main-content"
                    {% endif %}>
              View Activity History
            </a>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
</div>

<!-- Custom styles for recent activity -->
<style>
  .activity-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .activity-icon .rounded-circle {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
  }

  .activity-item:hover {
    background-color: var(--bs-light);
    border-radius: 0.375rem;
    margin: 0 -0.5rem;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .activity-content a:hover {
    text-decoration: underline !important;
  }

  @media (max-width: 768px) {
    .activity-icon {
      width: 2rem;
      height: 2rem;
    }

    .activity-icon .rounded-circle {
      font-size: 0.75rem;
    }

    .activity-actions {
      display: none;
    }
  }
</style>
