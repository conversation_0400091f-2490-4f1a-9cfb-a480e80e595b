<!-- Mobile Communication History Component -->
<div id="mobile-communications-list">

  {% if communications %}
    <div class="space-y-3">

      {% for comm in communications %}
        <div class="bg-gray-50  rounded-3 p-2 border-l-4 
          {% if comm.type =="call' %}border-blue-400 {% elif comm.type == 'email' %}border-green-400 {% elif comm.type == 'meeting' %}border-purple-400 {% else %}border-gray-400{% endif %}
           ">
          <div class="d-flex align-items-start justify-content-between">
            <div class="d-flex align-items-start space-x-2 flex-1  min-w-0">
              <div class="w-8  h-8  rounded-3 d-flex align-items-center justify-content-center mt-0.5 
                {% if comm.type =="call' %}bg-blue-100 {% elif comm.type == 'email' %}bg-green-100 {% elif comm.type == 'meeting' %}bg-purple-100 {% else %}bg-gray-100{% endif %}
                 ">
                <i data-lucide=" 
                  {% if comm.type == 'call' %}phone {% elif comm.type == 'email' %}mail {% elif comm.type == 'meeting' %}calendar {% else %}message-circle{% endif %}
                   " class="h-4  w-4 
                  {% if comm.type =="call' %}text-blue-600 {% elif comm.type == 'email' %}text-green-600 {% elif comm.type == 'meeting' %}text-purple-600 {% else %}text-gray-600{% endif %}
                 "></i>
              </div>
              <div class="flex-1  min-w-0">
                <div class="d-flex align-items-center justify-content-between mb-1">
                  <h4 class="small font-medium text-gray-900 ">
                    {{ comm.type|title }}

                    {% if comm.outcome %}<span class="text-xs  text-secondary">- {{ comm.outcome|escape }}</span>{% endif %}

                  </h4>
                  <span class="text-xs  text-muted">{{ comm.timestamp|timesince }} ago</span>
                </div>

                {% if comm.summary %}<p class="small text-gray-700  mb-2">{{ comm.summary|truncatechars:80 }}</p>{% endif %}

                {% if comm.details %}<p class="text-xs  text-secondary">{{ comm.details|truncatechars:100 }}</p>{% endif %}

                {% if comm.duration %}
                  <div class="d-flex align-items-center mt-1">
                    <i class="bi bi-clock h-3 me-1 text-gray-400 w-3"></i>
                    <span class="text-xs  text-muted">{{ comm.duration|escape }} minutes</span>
                  </div>
                {% endif %}

                <div class="d-flex align-items-center justify-content-between mt-2">
                  <span class="text-xs  text-muted">by {{ comm.user.get_full_name|default:comm.user.username }}</span>
                  <button onclick="editMobileCommunication('{{ comm.id|escape }}')"
                          class="text-xs  text-blue-600  hover:text-blue-800"
                          type="button"
                          aria-label="Edit">Edit</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% empty %}
        <p>No items available.</p>
      {% endfor %}

    </div>
    <!-- Load More Button (if there are more communications) -->

    {% if has_more_communications %}
      <div class="text-center mt-3">
        <button onclick="loadMoreMobileCommunications()"
                class="small text-blue-600  hover:text-blue-800  font-medium"
                type="button"
                aria-label="on">
          Load More Communicati">
          Load More Communications
        </button>
      </div>
    {% endif %}

  {% else %}
    <div class="text-center py-6 ">
      <i class="bi bi-chat h-8 mb-2 mx-auto text-gray-400 w-8"></i>
      <p class="small text-muted mb-2">No communications logged</p>
      <p class="text-xs  text-gray-400 ">Tap "Log Call" to record your first communication</p>
    </div>
  {% endif %}

  <script>
    function editMobileCommunication(commId) {
      // Load mobile edit form via HTMX
      htmx.ajax('GET', `/htmx/communications/${commId}/mobile-edit/`, {
        target: '#mobile-communication-edit-content',
        swap: 'innerHTML'
      }).then(() => {
        // Show mobile edit modal
        const modal = document.getElementById('mobile-communication-edit-modal');
        const backdrop = document.getElementById('mobile-backdrop');
        modal.classList.remove('d-none');
        backdrop.classList.remove('d-none');
        showMobileToast('Edit communication details');
      });
    }

    function loadMoreMobileCommunications() {
      const container = document.getElementById('mobile-communications-list');
      const currentCount = container.children.length;

      htmx.ajax('GET', `/htmx/communications/mobile-list/?offset=${currentCount}`, {
        target: '#mobile-communications-list',
        swap: 'beforeend'
      }).then(() => {
        showMobileToast('Loaded more communications');
        // Re-initialize lucide icons
        if (typeof lucide !== 'undefined') {
          lucide.createIcons();
        }
      }).catch(() => {
        showMobileToast('No more communications to load');
      });
    }

    // Re-initialize lucide icons
    if (typeof lucide !== 'undefined') {
      lucide.createIcons();
    }
  </script>
</div>
<!-- Mobile Communication Edit Modal -->
<div id="mobile-communication-edit-modal"
     class="d-none position-fixed inset-x-0 bottom-0 z-50 bg-white rounded-t-xl shadow-2xl border-t border-gray-200">
  <div class="p-3">
    <div class="w-12 h-1 bg-gray-300 rounded-circle mx-auto mb-3"></div>
    <div class="d-flex align-items-center justify-content-between mb-4">
      <h3 class="fs-5 fw-semibold">Edit Communication</h3>
      <button onclick="closeMobileCommunicationModal()"
              class="hover:text-gray-6 hover:text-gray-600 p-1 text-gray-400"
              type="button"
              aria-label="odal()">
        <i class="bi bi-x h-5 w-5"></i>
      </button>
    </div>
    <div id="mobile-communication-edit-content">
      <!-- Dynamic content will be loaded here via HTMX -->
    </div>
  </div>
</div>
<script>
  function closeMobileCommunicationModal() {
    document.getElementById('mobile-communication-edit-modal').classList.add('d-none');
    document.getElementById('mobile-backdrop').classList.add('d-none');
  }

  function saveMobileCommunicationEdit(commId) {
    const form = document.getElementById('mobile-communication-edit-form');
    const formData = new FormData(form);

    htmx.ajax('PUT', `/htmx/communications/${commId}/mobile-edit/`, {
      values: Object.fromEntries(formData),
      headers: {
        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
      },
      target: '#mobile-communications-list',
      swap: 'outerHTML'
    }).then(() => {
      closeMobileCommunicationModal();
      showMobileToast('Communication updated');
    }).catch(() => {
      showMobileToast('Error updating communication');
    });
  }
</script>
