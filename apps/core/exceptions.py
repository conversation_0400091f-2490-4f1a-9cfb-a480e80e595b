"""
CLEAR Project Exception Hierarchy

This module provides a standardized exception hierarchy for consistent error handling
across all apps in the CLEAR project. All custom exceptions should inherit from
ClearBaseException to ensure consistent error handling and logging.

Usage:
    from apps.core.exceptions import ClearBaseException, ValidationError

    try:
        # Some operation
        pass
    except SomeError as e:
        raise ValidationError("Invalid data provided") from e
"""

from __future__ import annotations

import logging
from typing import Any, ClassVar, Dict, List, Optional, Type, Union
from uuid import uuid4

from django.core.exceptions import ValidationError as DjangoValidationError
from django.http import Http404
from django.utils import timezone

from apps.core.types import JSONDict, PrimaryKey

logger = logging.getLogger(__name__)

# Error severity levels
class ErrorSeverity:
    """Error severity levels for categorization and handling."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

# Error categories for better organization
class ErrorCategory:
    """Error categories for systematic error handling."""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    SYSTEM = "system"
    SPATIAL = "spatial"
    WORKFLOW = "workflow"


class ClearBaseException(Exception):
    """
    Base exception class for all CLEAR project exceptions.

    This class provides:
    - Consistent error structure across the application
    - Automatic error logging with context
    - Error tracking with unique IDs
    - Support for error details and metadata
    - Severity and category classification
    - User-friendly message handling
    """

    # Class-level defaults that can be overridden by subclasses
    default_severity: ClassVar[str] = ErrorSeverity.MEDIUM
    default_category: ClassVar[str] = ErrorCategory.SYSTEM
    default_user_message: ClassVar[str] = "An error occurred. Please try again or contact support."

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[JSONDict] = None,
        user_message: Optional[str] = None,
        severity: Optional[str] = None,
        category: Optional[str] = None,
        log_level: int = logging.ERROR,
        should_log: bool = True
    ) -> None:
        """
        Initialize the exception.

        Args:
            message: Technical error message for developers
            error_code: Unique error code for categorization
            details: Additional error context and metadata
            user_message: User-friendly error message
            severity: Error severity level (low, medium, high, critical)
            category: Error category for systematic handling
            log_level: Logging level for this exception
            should_log: Whether to automatically log this exception
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.user_message = user_message or self.default_user_message
        self.severity = severity or self.default_severity
        self.category = category or self.default_category
        self.error_id = str(uuid4())[:8]  # Shorter ID for user display
        self.timestamp = timezone.now()
        self.log_level = log_level
        self.should_log = should_log

        # Log the exception if requested
        if self.should_log:
            self._log_exception()

    def _log_exception(self) -> None:
        """Log the exception with comprehensive context."""
        log_data = {
            'error_id': self.error_id,
            'error_code': self.error_code,
            'message': self.message,
            'severity': self.severity,
            'category': self.category,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
        }

        logger.log(
            self.log_level,
            f"CLEAR Exception [{self.error_code}]: {self.message}",
            extra={'error_context': log_data}
        )

    def to_dict(self) -> JSONDict:
        """Convert exception to dictionary for serialization."""
        return {
            'error_id': self.error_id,
            'error_code': self.error_code,
            'message': self.message,
            'user_message': self.user_message,
            'severity': self.severity,
            'category': self.category,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
        }

    def to_json_response(self) -> JSONDict:
        """Convert exception to JSON response format for APIs."""
        return {
            'error': True,
            'error_id': self.error_id,
            'error_code': self.error_code,
            'message': self.user_message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
        }

    def is_critical(self) -> bool:
        """Check if this is a critical error."""
        return self.severity == ErrorSeverity.CRITICAL

    def is_user_error(self) -> bool:
        """Check if this is a user-caused error (validation, business rule)."""
        return self.category in [ErrorCategory.VALIDATION, ErrorCategory.BUSINESS_LOGIC]

    def add_context(self, key: str, value: Any) -> None:
        """Add additional context to the error details."""
        self.details[key] = value


# Business Logic Exceptions
class ValidationError(ClearBaseException):
    """Raised when data validation fails."""

    default_severity = ErrorSeverity.MEDIUM
    default_category = ErrorCategory.VALIDATION
    default_user_message = "The provided data is invalid. Please check your input and try again."

    def __init__(self, message: str = "Validation failed", **kwargs: Any) -> None:
        super().__init__(
            message,
            error_code="VALIDATION_ERROR",
            severity=self.default_severity,
            category=self.default_category,
            user_message=kwargs.pop('user_message', self.default_user_message),
            **kwargs
        )


class BusinessRuleError(ClearBaseException):
    """Raised when business rules are violated."""

    default_severity = ErrorSeverity.HIGH
    default_category = ErrorCategory.BUSINESS_LOGIC
    default_user_message = "This action violates business rules. Please contact support if you believe this is an error."

    def __init__(self, message: str = "Business rule violation", **kwargs: Any) -> None:
        super().__init__(
            message,
            error_code="BUSINESS_RULE_ERROR",
            severity=self.default_severity,
            category=self.default_category,
            user_message=kwargs.pop('user_message', self.default_user_message),
            **kwargs
        )


class ConfigurationError(ClearBaseException):
    """Raised when system configuration is invalid."""

    default_severity = ErrorSeverity.CRITICAL
    default_category = ErrorCategory.SYSTEM
    default_user_message = "A system configuration error occurred. Please contact support."

    def __init__(self, message: str = "Configuration error", **kwargs: Any) -> None:
        super().__init__(
            message,
            error_code="CONFIGURATION_ERROR",
            severity=self.default_severity,
            category=self.default_category,
            user_message=kwargs.pop('user_message', self.default_user_message),
            **kwargs
        )


# Authentication and Authorization Exceptions
class AuthenticationError(ClearBaseException):
    """Raised when authentication fails."""

    def __init__(self, message: str = "Authentication failed", **kwargs) -> None:
        super().__init__(
            message,
            error_code="AUTHENTICATION_ERROR",
            user_message="Please check your credentials and try again.",
            **kwargs
        )


class AuthorizationError(ClearBaseException):
    """Raised when user lacks required permissions."""

    def __init__(self, message: str = "Access denied", **kwargs) -> None:
        super().__init__(
            message,
            error_code="AUTHORIZATION_ERROR",
            user_message="You don't have permission to perform this action.",
            **kwargs
        )


class OrganizationAccessError(AuthorizationError):
    """Raised when user tries to access resources outside their organization."""

    def __init__(self, message: str = "Organization access denied", **kwargs) -> None:
        super().__init__(
            message,
            error_code="ORGANIZATION_ACCESS_ERROR",
            user_message="You can only access resources within your organization.",
            **kwargs
        )


# Resource Exceptions
class ResourceNotFoundError(ClearBaseException):
    """Raised when a requested resource cannot be found."""

    def __init__(self, message: str = "Resource not found", **kwargs) -> None:
        super().__init__(
            message,
            error_code="RESOURCE_NOT_FOUND",
            user_message="The requested resource could not be found.",
            log_level=logging.WARNING,
            **kwargs
        )


class ResourceConflictError(ClearBaseException):
    """Raised when a resource conflict occurs."""

    def __init__(self, message: str = "Resource conflict", **kwargs) -> None:
        super().__init__(
            message,
            error_code="RESOURCE_CONFLICT",
            user_message="The operation conflicts with the current state of the resource.",
            **kwargs
        )


class ResourceExhaustedError(ClearBaseException):
    """Raised when system resources are exhausted."""

    def __init__(self, message: str = "Resource exhausted", **kwargs) -> None:
        super().__init__(
            message,
            error_code="RESOURCE_EXHAUSTED",
            user_message="System resources are temporarily unavailable. Please try again later.",
            **kwargs
        )


# Data Processing Exceptions
class DataProcessingError(ClearBaseException):
    """Raised when data processing fails."""

    def __init__(self, message: str = "Data processing failed", **kwargs) -> None:
        super().__init__(message, error_code="DATA_PROCESSING_ERROR", **kwargs)


class ImportError(DataProcessingError):
    """Raised when data import fails."""

    def __init__(self, message: str = "Data import failed", **kwargs) -> None:
        super().__init__(message, error_code="IMPORT_ERROR", **kwargs)


class ExportError(DataProcessingError):
    """Raised when data export fails."""

    def __init__(self, message: str = "Data export failed", **kwargs) -> None:
        super().__init__(message, error_code="EXPORT_ERROR", **kwargs)


# Spatial/GIS Exceptions
class SpatialError(ClearBaseException):
    """Raised when spatial operations fail."""

    def __init__(self, message: str = "Spatial operation failed", **kwargs) -> None:
        super().__init__(message, error_code="SPATIAL_ERROR", **kwargs)


class CoordinateSystemError(SpatialError):
    """Raised when coordinate system operations fail."""

    def __init__(self, message: str = "Coordinate system error", **kwargs) -> None:
        super().__init__(message, error_code="COORDINATE_SYSTEM_ERROR", **kwargs)


class GeometryError(SpatialError):
    """Raised when geometry operations fail."""

    def __init__(self, message: str = "Geometry operation failed", **kwargs) -> None:
        super().__init__(message, error_code="GEOMETRY_ERROR", **kwargs)


# Integration Exceptions
class ExternalServiceError(ClearBaseException):
    """Raised when external service integration fails."""

    def __init__(self, message: str = "External service error", **kwargs) -> None:
        super().__init__(
            message,
            error_code="EXTERNAL_SERVICE_ERROR",
            user_message="An external service is temporarily unavailable. Please try again later.",
            **kwargs
        )


class APIError(ExternalServiceError):
    """Raised when API calls fail."""

    def __init__(self, message: str = "API error", **kwargs) -> None:
        super().__init__(message, error_code="API_ERROR", **kwargs)


class DatabaseError(ClearBaseException):
    """Raised when database operations fail."""

    def __init__(self, message: str = "Database error", **kwargs) -> None:
        super().__init__(
            message,
            error_code="DATABASE_ERROR",
            user_message="A database error occurred. Please try again later.",
            **kwargs
        )


# Utility Functions
def convert_django_exception(exc: Exception) -> ClearBaseException:
    """
    Convert Django exceptions to CLEAR exceptions.

    Args:
        exc: Django exception to convert

    Returns:
        Appropriate CLEAR exception
    """
    if isinstance(exc, DjangoValidationError):
        return ValidationError(str(exc), details={'original_error': str(exc)})
    elif isinstance(exc, Http404):
        return ResourceNotFoundError(str(exc), details={'original_error': str(exc)})
    else:
        return ClearBaseException(
            f"Converted from {exc.__class__.__name__}: {exc}",
            details={'original_error': str(exc), 'original_type': exc.__class__.__name__}
        )


def handle_exception_chain(exc: Exception) -> ClearBaseException:
    """
    Handle exception chains and convert to CLEAR exceptions.

    Args:
        exc: Exception to handle

    Returns:
        CLEAR exception with chain context
    """
    if isinstance(exc, ClearBaseException):
        return exc

    # Build exception chain context
    chain_details = []
    current = exc
    while current:
        chain_details.append({
            'type': current.__class__.__name__,
            'message': str(current),
        })
        current = current.__cause__ or current.__context__

    return ClearBaseException(
        f"Exception chain: {exc.__class__.__name__}: {exc}",
        details={'exception_chain': chain_details}
    )
