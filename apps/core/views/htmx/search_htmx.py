"""
Search HTMX Views

This module contains all search-related HTMX views that provide dynamic search functionality
across various entities including notebooks, stakeholders, templates, projects, and global search.
"""

import logging

from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.shortcuts import render
from django.views.decorators.http import require_http_methods

from apps.common.mixins.htmx_form_mixins import HTMXSearchMixin
from apps.core.decorators.access_control import (
    requires_spatial_access,
    requires_stakeholder_access,
)
from apps.messaging.models import ChatMessage
from apps.notes.models import Note
from apps.projects.models import Project, ProjectTemplate, Stakeholder

logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["GET"])
def notebook_search(request):
    """Search notebook entries (HTMX endpoint)"""
    query = request.GET.get("q", "")
    user = request.user

    notes = Note.objects.filter(
        Q(author=user) | Q(shared_with=user),
        Q(title__icontains=query) | Q(content__icontains=query),
    ).order_by("-updated_at")[:10]

    return render(
        request,
        "partials/notebook_search_results.html",
        {"notes": notes, "query": query},
    )


@login_required
@require_http_methods(["GET"])
@requires_stakeholder_access("view")
def stakeholder_search(request):
    """Search stakeholders (HTMX endpoint)"""
    query = request.GET.get("q", "")

    stakeholders = Stakeholder.objects.filter(
        Q(full_name__icontains=query) | Q(contact_company__icontains=query) | Q(email__icontains=query),
    ).order_by("contact_company", "full_name")[:10]

    return render(
        request,
        "shared/partials/stakeholder_search_results.html",
        {"stakeholders": stakeholders, "query": query},
    )


@login_required
@require_http_methods(["GET"])
def template_search(request):
    """Search project templates (HTMX endpoint)"""
    query = request.GET.get("q", "")

    templates = ProjectTemplate.objects.filter(
        Q(name__icontains=query) | Q(description__icontains=query),
        is_active=True,
    ).order_by("name")[:10]

    return render(
        request,
        "partials/template_search_results.html",
        {"templates": templates, "query": query},
    )


@login_required
@require_http_methods(["GET"])
def projects_search(request):
    """Search projects accessible to the user (HTMX endpoint)"""
    query = request.GET.get("q", "")
    user = request.user

    # Search projects user has access to
    projects = Project.objects.filter(
        Q(manager_id=user.id) | Q(coordinator_id=str(user.id)),
        Q(name__icontains=query) | Q(description__icontains=query),
    ).order_by("name")[:10]

    return render(
        request,
        "projects/partials/project_search_results.html",
        {"projects": projects, "query": query},
    )


@login_required
@require_http_methods(["GET"])
def message_search_htmx(request):
    """Search messages (HTMX endpoint)"""
    query = request.GET.get("q", "")

    if not query.strip():
        return render(
            request,
            "messaging/partials/message_search_results.html",
            {"messages": [], "query": query},
        )

    # Search messages user has access to
    messages = ChatMessage.objects.filter(
        Q(content__icontains=query),
        # Add proper permission filtering based on your message model
    ).order_by("-created_at")[:20]

    return render(
        request,
        "messaging/partials/message_search_results.html",
        {"messages": messages, "query": query},
    )


@login_required
@require_http_methods(["GET"])
@requires_spatial_access("view")
def spatial_search_htmx(request, project_id):
    """Search spatial features within a project (HTMX endpoint)"""
    query = request.GET.get("q", "")

    try:
        project = Project.objects.get(id=project_id)

        # Check user has access to project
        if not (project.manager_id == request.user.id or project.coordinator_id == str(request.user.id)):
            return render(
                request,
                "shared/error/403.html",
                {"error_message": "Access denied to project"},
                status=403,
            )

        # Spatial search implementation would go here
        # This is a placeholder for actual spatial search logic
        spatial_features = []

        return render(
            request,
            "infrastructure/partials/spatial_search_results.html",
            {
                "features": spatial_features,
                "query": query,
                "project": project,
            },
        )
    except Project.DoesNotExist:
        return render(
            request,
            "shared/error/404.html",
            {"error_message": "Project not found"},
            status=404,
        )
    except Exception as e:
        logger.exception(f"Error in spatial search: {e!s}")
        return render(
            request,
            "shared/error/500.html",
            {"error_message": "Error performing spatial search"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def project_conversations_search_htmx(request, project_id):
    """Search conversations within a project (HTMX endpoint)"""
    query = request.GET.get("q", "")

    try:
        project = Project.objects.get(id=project_id)

        # Check user has access to project
        if not (project.manager_id == request.user.id or project.coordinator_id == str(request.user.id)):
            return render(
                request,
                "shared/error/403.html",
                {"error_message": "Access denied to project"},
                status=403,
            )

        # Search conversations/messages related to the project
        conversations = []  # Placeholder for actual conversation search

        return render(
            request,
            "messaging/partials/project_conversations_search.html",
            {
                "conversations": conversations,
                "query": query,
                "project": project,
            },
        )
    except Project.DoesNotExist:
        return render(
            request,
            "shared/error/404.html",
            {"error_message": "Project not found"},
            status=404,
        )
    except Exception as e:
        logger.exception(f"Error in project conversations search: {e!s}")
        return render(
            request,
            "shared/error/500.html",
            {"error_message": "Error searching conversations"},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def global_search_htmx(request):
    """Global search across all searchable entities (HTMX endpoint)"""
    query = request.GET.get("q", "")
    search_type = request.GET.get("type", "all")  # all, projects, notes, stakeholders, etc.
    user = request.user

    if not query.strip():
        return render(
            request,
            "core/partials/global_search_results.html",
            {"results": {}, "query": query, "search_type": search_type},
        )

    results = {}

    try:
        if search_type in ["all", "projects"]:
            results["projects"] = Project.objects.filter(
                Q(manager_id=user.id) | Q(coordinator_id=str(user.id)),
                Q(name__icontains=query) | Q(description__icontains=query),
            ).order_by("name")[:5]

        if search_type in ["all", "notes"]:
            results["notes"] = Note.objects.filter(
                Q(author=user) | Q(shared_with=user),
                Q(title__icontains=query) | Q(content__icontains=query),
            ).order_by("-updated_at")[:5]

        if search_type in ["all", "stakeholders"]:
            try:
                results["stakeholders"] = Stakeholder.objects.filter(
                    Q(full_name__icontains=query) | Q(contact_company__icontains=query) | Q(email__icontains=query),
                ).order_by("contact_company", "full_name")[:5]
            except Exception:
                results["stakeholders"] = []

        if search_type in ["all", "templates"]:
            results["templates"] = ProjectTemplate.objects.filter(
                Q(name__icontains=query) | Q(description__icontains=query),
                is_active=True,
            ).order_by("name")[:5]

        if search_type in ["all", "messages"]:
            results["messages"] = ChatMessage.objects.filter(
                Q(content__icontains=query),
                # Add proper permission filtering
            ).order_by("-created_at")[:5]

        return render(
            request,
            "core/partials/global_search_results.html",
            {
                "results": results,
                "query": query,
                "search_type": search_type,
                "total_results": sum(len(v) for v in results.values() if hasattr(v, "__len__")),
            },
        )
    except Exception as e:
        logger.exception(f"Error in global search: {e!s}")
        return render(
            request,
            "shared/error/500.html",
            {"error_message": "Error performing global search"},
            status=500,
        )


class SearchHTMXMixin(HTMXSearchMixin):
    """Enhanced search mixin with CLEAR-specific functionality"""

    def filter_by_user_access(self, queryset, user):
        """Filter queryset by user access permissions"""
        # Override in specific search views as needed
        return queryset

    def get_search_context(self, results, query, **kwargs):
        """Get enhanced search context"""
        context = {
            "results": results,
            "query": query,
            "has_results": bool(results),
            "result_count": len(results) if hasattr(results, "__len__") else 0,
            **kwargs,
        }
        return context
