"""
Search performance optimization service.

Provides advanced caching, query optimization, and performance monitoring
to ensure sub-500ms search response times under load.
"""

import hashlib
import logging
from datetime import timedelta
from typing import Any, Dict, List, Optional

from django.core.cache import cache
from django.db import connection
from django.db.models import QuerySet
from django.utils import timezone

logger = logging.getLogger(__name__)


def get_optimized_search_service(user=None, organization=None):
    """
    Get a fully optimized search service with all performance enhancements.

    Args:
        user: User context for search
        organization: Organization context for search

    Returns:
        Optimized search service instance
    """
    from apps.core.services.search_cache_enhanced import get_enhanced_search_cache
    from apps.core.services.search_connection_pool import get_search_connection_pool
    from apps.core.services.search_failover_service import get_search_failover_service

    # Initialize all performance components
    connection_pool = get_search_connection_pool()
    enhanced_cache = get_enhanced_search_cache(user, organization)
    failover_service = get_search_failover_service(user, organization)

    # Warm up connection pool if not already done
    connection_pool.warm_pool()

    return {
        "connection_pool": connection_pool,
        "cache_manager": enhanced_cache,
        "failover_service": failover_service,
        "performance_monitor": SearchPerformanceMonitor(),
    }


class SearchCacheManager:
    """
    Advanced caching manager for search operations.

    Implements intelligent cache invalidation, multi-level caching,
    and cache warming strategies.
    """

    # Cache timeout configurations (in seconds)
    INSTANT_SEARCH_TIMEOUT = 300  # 5 minutes
    FACET_CACHE_TIMEOUT = 900  # 15 minutes
    SUGGESTION_CACHE_TIMEOUT = 3600  # 1 hour
    RESULT_CACHE_TIMEOUT = 1800  # 30 minutes

    # Cache key prefixes
    INSTANT_SEARCH_PREFIX = "instant_search"
    FACET_PREFIX = "search_facets"
    SUGGESTION_PREFIX = "search_suggestions"
    RESULT_PREFIX = "search_results"
    PERFORMANCE_PREFIX = "search_performance"

    def __init__(self, user=None, organization=None):
        """Initialize cache manager with user context."""
        self.user = user
        self.organization = organization

    def get_instant_search_cache_key(self, query: str, filters: Dict, limit: int) -> str:
        """Generate cache key for instant search results."""
        # Create hash from query parameters
        key_data = {
            "query": query.lower().strip(),
            "filters": sorted(filters.items()) if filters else [],
            "limit": limit,
            "user_id": self.user.pk if self.user else None,
            "org_id": self.organization.pk if self.organization else None,
        }

        key_hash = hashlib.md5(str(key_data).encode(), usedforsecurity=False).hexdigest()
        return f"{self.INSTANT_SEARCH_PREFIX}:{key_hash}"

    def get_facet_cache_key(self, entity_types: List[str], base_query: str = "") -> str:
        """Generate cache key for facet data."""
        key_data = {
            "entity_types": sorted(entity_types) if entity_types else [],
            "base_query": base_query.lower().strip(),
            "org_id": self.organization.pk if self.organization else None,
        }

        key_hash = hashlib.md5(str(key_data).encode(), usedforsecurity=False).hexdigest()
        return f"{self.FACET_PREFIX}:{key_hash}"

    def get_suggestion_cache_key(self, partial_query: str, limit: int) -> str:
        """Generate cache key for search suggestions."""
        key_data = {
            "partial_query": partial_query.lower().strip(),
            "limit": limit,
            "org_id": self.organization.pk if self.organization else None,
        }

        key_hash = hashlib.md5(str(key_data).encode(), usedforsecurity=False).hexdigest()
        return f"{self.SUGGESTION_PREFIX}:{key_hash}"

    def cache_instant_search(self, key: str, response: Any) -> None:
        """Cache instant search response with timeout."""
        cache.set(key, response, self.INSTANT_SEARCH_TIMEOUT)

        # Track cache operations for monitoring
        self._track_cache_operation("instant_search", "set")

    def get_instant_search(self, key: str) -> Any:
        """Get cached instant search response."""
        result = cache.get(key)

        # Track cache hit/miss
        if result:
            self._track_cache_operation("instant_search", "hit")
        else:
            self._track_cache_operation("instant_search", "miss")

        return result

    def cache_facets(self, key: str, facets: Dict) -> None:
        """Cache facet data with longer timeout."""
        cache.set(key, facets, self.FACET_CACHE_TIMEOUT)
        self._track_cache_operation("facets", "set")

    def get_facets(self, key: str) -> Optional[Dict]:
        """Get cached facet data."""
        result = cache.get(key)

        if result:
            self._track_cache_operation("facets", "hit")
        else:
            self._track_cache_operation("facets", "miss")

        return result

    def cache_suggestions(self, key: str, suggestions: List) -> None:
        """Cache search suggestions."""
        cache.set(key, suggestions, self.SUGGESTION_CACHE_TIMEOUT)
        self._track_cache_operation("suggestions", "set")

    def get_suggestions(self, key: str) -> Optional[List]:
        """Get cached suggestions."""
        result = cache.get(key)

        if result:
            self._track_cache_operation("suggestions", "hit")
        else:
            self._track_cache_operation("suggestions", "miss")

        return result

    def invalidate_search_cache(self, pattern: str = None) -> int:
        """
        Invalidate search cache entries.

        Args:
            pattern: Cache key pattern to match (optional)

        Returns:
            Number of cache entries invalidated
        """
        try:
            if hasattr(cache, "delete_pattern"):
                # Redis cache backend with pattern support
                if pattern:
                    return cache.delete_pattern(pattern)
                else:
                    # Invalidate all search-related cache
                    count = 0
                    for prefix in [
                        self.INSTANT_SEARCH_PREFIX,
                        self.FACET_PREFIX,
                        self.SUGGESTION_PREFIX,
                        self.RESULT_PREFIX,
                    ]:
                        count += cache.delete_pattern(f"{prefix}:*")
                    return count
            else:
                # Fallback for cache backends without pattern support
                logger.warning("Cache backend doesn't support pattern deletion")
                return 0

        except Exception as e:
            logger.error(f"Error invalidating search cache: {e}")
            return 0

    def warm_popular_searches(self) -> int:
        """
        Warm cache with popular search queries.

        Returns:
            Number of cache entries warmed
        """
        try:
            from apps.core.services.search_analytics_service import (
                get_search_analytics_service,
            )

            analytics_service = get_search_analytics_service(self.user, self.organization)
            analytics_data = analytics_service.get_search_analytics(days=7)

            # Get top queries to warm
            top_queries = analytics_data.get("top_queries", [])[:10]

            # Import services here to avoid circular imports
            from apps.core.services.instant_search_service import (
                get_instant_search_service,
            )

            search_service = get_instant_search_service(self.user, self.organization)

            warmed_count = 0
            for query_data in top_queries:
                query = query_data.get("query", "")
                if len(query) >= 2:
                    try:
                        # Perform search to warm cache
                        search_service.instant_search(query, limit=10)
                        warmed_count += 1
                    except Exception as e:
                        logger.warning(f"Error warming cache for query '{query}': {e}")

            logger.info(f"Warmed cache for {warmed_count} popular queries")
            return warmed_count

        except Exception as e:
            logger.error(f"Error warming search cache: {e}")
            return 0

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        try:
            # Get cache stats from the last hour
            stats_key = f"{self.PERFORMANCE_PREFIX}:cache_stats"
            stats = cache.get(
                stats_key,
                {
                    "instant_search": {"hits": 0, "misses": 0, "sets": 0},
                    "facets": {"hits": 0, "misses": 0, "sets": 0},
                    "suggestions": {"hits": 0, "misses": 0, "sets": 0},
                },
            )

            # Calculate hit rates
            for _cache_type, type_stats in stats.items():
                total_requests = type_stats["hits"] + type_stats["misses"]
                if total_requests > 0:
                    type_stats["hit_rate"] = (type_stats["hits"] / total_requests) * 100
                else:
                    type_stats["hit_rate"] = 0

            return stats

        except Exception as e:
            logger.error(f"Error getting cache statistics: {e}")
            return {}

    def _track_cache_operation(self, cache_type: str, operation: str) -> None:
        """Track cache operations for monitoring."""
        try:
            stats_key = f"{self.PERFORMANCE_PREFIX}:cache_stats"
            stats = cache.get(
                stats_key,
                {
                    "instant_search": {"hits": 0, "misses": 0, "sets": 0},
                    "facets": {"hits": 0, "misses": 0, "sets": 0},
                    "suggestions": {"hits": 0, "misses": 0, "sets": 0},
                },
            )

            if cache_type in stats and operation in stats[cache_type]:
                stats[cache_type][operation] += 1
                cache.set(stats_key, stats, 3600)  # Store for 1 hour

        except Exception as e:
            logger.warning(f"Error tracking cache operation: {e}")


class SearchQueryOptimizer:
    """
    Database query optimizer for search operations.

    Provides query analysis, index recommendations, and
    performance optimization strategies.
    """

    def __init__(self):
        """Initialize query optimizer."""
        self.connection = connection

    def optimize_search_query(self, queryset: QuerySet) -> QuerySet:
        """
        Optimize a search queryset for better performance.

        Args:
            queryset: Base search queryset

        Returns:
            Optimized queryset
        """
        # Apply standard optimizations
        optimized = queryset.select_related("creator", "organization")

        # Use prefetch for many-to-many relationships
        optimized = optimized.prefetch_related("tags")

        # Add only() to limit fields if not needed
        optimized = optimized.only(
            "id",
            "entity_type",
            "entity_id",
            "title",
            "content",
            "excerpt",
            "search_rank",
            "entity_created_at",
            "entity_updated_at",
            "creator__username",
            "creator__first_name",
            "creator__last_name",
            "organization__name",
        )

        return optimized

    def get_query_plan(self, queryset: QuerySet) -> Dict[str, Any]:
        """
        Get the execution plan for a search query.

        Args:
            queryset: Search queryset to analyze

        Returns:
            Query execution plan and statistics
        """
        try:
            with self.connection.cursor() as cursor:
                # Get the SQL query
                sql, params = queryset.query.sql_with_params()

                # Get query plan
                cursor.execute(f"EXPLAIN ANALYZE {sql}", params)
                plan_rows = cursor.fetchall()

                # Parse execution plan
                plan_text = "\n".join([row[0] for row in plan_rows])

                # Extract key metrics
                metrics = self._parse_plan_metrics(plan_text)

                return {"sql": sql, "plan": plan_text, "metrics": metrics}

        except Exception as e:
            logger.error(f"Error getting query plan: {e}")
            return {"error": str(e)}

    def analyze_search_performance(self) -> Dict[str, Any]:
        """Analyze overall search performance and provide recommendations."""
        try:
            with self.connection.cursor() as cursor:
                # Check for missing indexes
                cursor.execute(
                    """
                    SELECT schemaname, tablename, attname
                    FROM pg_stats
                    WHERE schemaname = 'public'
                    AND tablename LIKE '%searchindex%'
                    AND n_distinct > 100
                    ORDER BY n_distinct DESC
                """
                )

                potential_indexes = cursor.fetchall()

                # Check current index usage
                cursor.execute(
                    """
                    SELECT indexname, idx_scan, idx_tup_read, idx_tup_fetch
                    FROM pg_stat_user_indexes
                    WHERE schemaname = 'public'
                    AND relname LIKE '%searchindex%'
                    ORDER BY idx_scan DESC
                """
                )

                index_stats = cursor.fetchall()

                # Get table size and statistics
                cursor.execute(
                    """
                    SELECT
                        pg_size_pretty(pg_total_relation_size('core_searchindex')) as table_size,
                        (SELECT count(*) FROM core_searchindex) as row_count
                """
                )

                table_stats = cursor.fetchone()

                return {
                    "table_size": table_stats[0] if table_stats else "Unknown",
                    "row_count": table_stats[1] if table_stats else 0,
                    "index_usage": [
                        {
                            "name": row[0],
                            "scans": row[1],
                            "tuples_read": row[2],
                            "tuples_fetched": row[3],
                        }
                        for row in index_stats
                    ],
                    "potential_indexes": [{"table": row[1], "column": row[2]} for row in potential_indexes[:5]],
                }

        except Exception as e:
            logger.error(f"Error analyzing search performance: {e}")
            return {"error": str(e)}

    def _parse_plan_metrics(self, plan_text: str) -> Dict[str, Any]:
        """Parse execution plan to extract key metrics."""
        metrics = {"execution_time": 0, "planning_time": 0, "rows": 0, "cost": 0}

        try:
            for line in plan_text.split("\n"):
                if "Execution Time:" in line:
                    time_str = line.split(":")[1].strip().replace(" ms", "")
                    metrics["execution_time"] = float(time_str)
                elif "Planning Time:" in line:
                    time_str = line.split(":")[1].strip().replace(" ms", "")
                    metrics["planning_time"] = float(time_str)
                elif "rows=" in line:
                    # Extract rows from plan node
                    parts = line.split("rows=")[1].split(" ")[0]
                    metrics["rows"] = int(parts)
                elif "cost=" in line:
                    # Extract cost from plan node
                    cost_part = line.split("cost=")[1].split(" ")[0]
                    if ".." in cost_part:
                        metrics["cost"] = float(cost_part.split("..")[1])

        except (ValueError, IndexError) as e:
            logger.warning(f"Error parsing plan metrics: {e}")

        return metrics


class SearchPerformanceMonitor:
    """
    Performance monitoring for search operations.

    Tracks response times, query counts, and performance degradation.
    """

    def __init__(self):
        """Initialize performance monitor."""
        self.cache_manager = SearchCacheManager()

    def track_search_performance(
        self, operation: str, duration_ms: float, query: str = "", result_count: int = 0
    ) -> None:
        """
        Track performance metrics for search operations.

        Args:
            operation: Type of search operation
            duration_ms: Operation duration in milliseconds
            query: Search query (optional)
            result_count: Number of results returned
        """
        try:
            # Store performance data with timestamp
            perf_data = {
                "timestamp": timezone.now().isoformat(),
                "operation": operation,
                "duration_ms": duration_ms,
                "query_length": len(query) if query else 0,
                "result_count": result_count,
                "is_slow": duration_ms > 500,  # Mark as slow if > 500ms
            }

            # Store in cache for real-time monitoring
            perf_key = f"search_perf:{timezone.now().strftime('%Y%m%d%H%M%S')}"
            cache.set(perf_key, perf_data, 3600)  # Keep for 1 hour

            # Log slow queries
            if duration_ms > 500:
                logger.warning(
                    f"Slow search query: {operation} took {duration_ms}ms "
                    f"for query '{query[:50]}...' returning {result_count} results"
                )

        except Exception as e:
            logger.error(f"Error tracking search performance: {e}")

    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get performance summary for the specified time period.

        Args:
            hours: Number of hours to analyze

        Returns:
            Performance summary with key metrics
        """
        try:
            # Get performance data from cache
            end_time = timezone.now()
            end_time - timedelta(hours=hours)

            # This would typically query a time-series database
            # For now, we'll provide estimated metrics
            summary = {
                "period_hours": hours,
                "total_searches": 0,
                "avg_response_time": 0,
                "slow_queries": 0,
                "cache_hit_rate": 0,
                "peak_response_time": 0,
                "recommendations": [],
            }

            # Get cache statistics
            cache_stats = self.cache_manager.get_cache_statistics()
            if cache_stats:
                instant_stats = cache_stats.get("instant_search", {})
                summary["cache_hit_rate"] = instant_stats.get("hit_rate", 0)

            # Add performance recommendations
            if summary["cache_hit_rate"] < 60:
                summary["recommendations"].append("Consider increasing cache timeout or warming popular queries")

            if summary["avg_response_time"] > 300:
                summary["recommendations"].append("Query optimization needed - check database indexes")

            return summary

        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {"error": str(e)}


def get_search_cache_manager(user=None, organization=None) -> SearchCacheManager:
    """Factory function to get search cache manager instance."""
    return SearchCacheManager(user=user, organization=organization)


def get_search_query_optimizer() -> SearchQueryOptimizer:
    """Factory function to get search query optimizer instance."""
    return SearchQueryOptimizer()


def get_search_performance_monitor() -> SearchPerformanceMonitor:
    """Factory function to get search performance monitor instance."""
    return SearchPerformanceMonitor()
