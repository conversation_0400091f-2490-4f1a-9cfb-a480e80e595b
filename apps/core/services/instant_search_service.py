"""
Instant search service for real-time search-as-you-type functionality.

Provides optimized search with debouncing, caching, and progressive
enhancement for HTMX instant search interfaces.
"""

import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple

from django.contrib.postgres.search import SearchQuery, SearchRank, TrigramSimilarity
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db.models import Q, QuerySet

from apps.core.models import SearchIndex
from apps.core.services.faceted_search_service import FacetFilters, FacetManager
from apps.core.services.query_parser import parse_search_query, validate_search_query

logger = logging.getLogger(__name__)


@dataclass
class InstantSearchResult:
    """Lightweight search result for instant search."""

    entity_id: str
    entity_type: str
    title: str
    snippet: str
    url: str
    icon: str
    score: float
    highlights: List[str] = None

    def __post_init__(self):
        if self.highlights is None:
            self.highlights = []


@dataclass
class InstantSearchResponse:
    """Response object for instant search."""

    results: List[InstantSearchResult]
    total_count: int
    query: str
    has_more: bool
    search_time_ms: float
    suggestions: List[str] = None
    cached: bool = False

    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []


class InstantSearchService:
    """
    Service for instant search functionality.

    Optimized for real-time search-as-you-type with:
    - Aggressive caching for performance
    - Lightweight result objects
    - Debounced search queries
    - Progressive result loading
    """

    def __init__(self, user=None, organization=None):
        """
        Initialize the instant search service.

        Args:
            user: User context for permissions and personalization
            organization: Organization context for multi-tenant isolation
        """
        self.user = user
        self.organization = organization
        self.cache_timeout = 300  # 5 minutes
        self.instant_cache_timeout = 60  # 1 minute for instant results
        self.min_query_length = 2
        self.max_instant_results = 10

        # Initialize performance optimizations
        self._init_performance_services()

    def instant_search(
        self, query: str, filters: Optional[FacetFilters] = None, limit: int = 10
    ) -> InstantSearchResponse:
        """
        Perform instant search with optimized performance.

        Args:
            query: Search query string
            filters: Applied facet filters
            limit: Maximum number of results to return

        Returns:
            InstantSearchResponse with lightweight results
        """
        start_time = time.time()

        # Validate minimum query length
        if len(query.strip()) < self.min_query_length:
            return InstantSearchResponse(
                results=[],
                total_count=0,
                query=query,
                has_more=False,
                search_time_ms=0,
                suggestions=[],
            )

        # Check cache first for instant results using performance-optimized cache
        filters_dict = self._convert_filters_to_dict(filters) if filters else {}
        cache_key = self.cache_manager.get_instant_search_cache_key(query, filters_dict, limit)
        cached_response = self.cache_manager.get_instant_search(cache_key)
        if cached_response:
            cached_response.cached = True
            # Track cache hit performance
            self.performance_monitor.track_search_performance(
                "instant_search_cached", 0, query, len(cached_response.results)
            )
            return cached_response

        try:
            # Validate query syntax
            is_valid, error_message = validate_search_query(query)
            if not is_valid:
                return InstantSearchResponse(
                    results=[],
                    total_count=0,
                    query=query,
                    has_more=False,
                    search_time_ms=(time.time() - start_time) * 1000,
                    suggestions=self._get_query_suggestions(query),
                )

            # Build optimized queryset for instant search
            queryset = self._build_instant_queryset(query, filters)

            # Get total count efficiently
            total_count = min(queryset.count(), 1000)  # Cap at 1000 for performance

            # Get limited results for instant display
            instant_results = queryset[:limit]

            # Convert to lightweight result objects
            results = self._convert_to_instant_results(instant_results, query)

            # Generate suggestions if few results
            suggestions = []
            if len(results) < 3:
                suggestions = self._get_query_suggestions(query)

            search_time_ms = (time.time() - start_time) * 1000

            response = InstantSearchResponse(
                results=results,
                total_count=total_count,
                query=query,
                has_more=total_count > limit,
                search_time_ms=search_time_ms,
                suggestions=suggestions,
            )

            # Record analytics event
            self._record_search_analytics(query, total_count, search_time_ms, filters)

            # Cache instant results using performance-optimized cache
            self.cache_manager.cache_instant_search(cache_key, response)

            # Track search performance
            self.performance_monitor.track_search_performance("instant_search", search_time_ms, query, total_count)

            return response

        except Exception as e:
            logger.error(f"Instant search error: {e}")
            return InstantSearchResponse(
                results=[],
                total_count=0,
                query=query,
                has_more=False,
                search_time_ms=(time.time() - start_time) * 1000,
                suggestions=[],
            )

    def progressive_search(
        self,
        query: str,
        page: int = 1,
        per_page: int = 20,
        filters: Optional[FacetFilters] = None,
    ) -> Tuple[List[InstantSearchResult], bool]:
        """
        Get progressive search results for infinite scroll.

        Args:
            query: Search query string
            page: Page number (1-based)
            per_page: Results per page
            filters: Applied facet filters

        Returns:
            Tuple of (results, has_more)
        """
        try:
            # Build queryset
            queryset = self._build_instant_queryset(query, filters)

            # Apply pagination
            paginator = Paginator(queryset, per_page)
            page_obj = paginator.get_page(page)

            # Convert to instant results
            results = self._convert_to_instant_results(page_obj.object_list, query)

            return results, page_obj.has_next()

        except Exception as e:
            logger.error(f"Progressive search error: {e}")
            return [], False

    def get_search_suggestions(self, partial_query: str, limit: int = 5) -> List[str]:
        """
        Get search suggestions for autocomplete.

        Args:
            partial_query: Partial search query
            limit: Maximum number of suggestions

        Returns:
            List of suggested queries
        """
        if len(partial_query.strip()) < 2:
            return []

        # Check cache first
        cache_key = f"instant_suggestions_{hash(partial_query)}_{limit}"
        cached_suggestions = cache.get(cache_key)
        if cached_suggestions:
            return cached_suggestions

        suggestions = []

        try:
            # Get suggestions from search index content using trigram similarity
            base_queryset = self._get_base_queryset()

            # Search in titles for similar terms
            title_matches = (
                base_queryset.annotate(similarity=TrigramSimilarity("title", partial_query))
                .filter(similarity__gt=0.2)
                .order_by("-similarity")[: limit * 2]
            )

            # Extract unique terms
            seen_suggestions = set()
            for match in title_matches:
                if match.title and len(suggestions) < limit:
                    title_words = match.title.lower().split()
                    for word in title_words:
                        if (
                            word.startswith(partial_query.lower())
                            and word not in seen_suggestions
                            and len(word) > len(partial_query)
                        ):
                            suggestions.append(word)
                            seen_suggestions.add(word)
                            if len(suggestions) >= limit:
                                break

            # Cache suggestions for 5 minutes
            cache.set(cache_key, suggestions, 300)

        except Exception as e:
            logger.warning(f"Error getting suggestions: {e}")

        return suggestions[:limit]

    def _build_instant_queryset(self, query: str, filters: Optional[FacetFilters] = None) -> QuerySet:
        """Build optimized queryset for instant search."""
        # Start with base queryset
        queryset = self._get_base_queryset()

        # Apply facet filters if provided
        if filters:
            facet_manager = FacetManager(self.user, self.organization)
            queryset = facet_manager.apply_facet_filters(queryset, filters)

        # Apply text search with PostgreSQL full-text search
        if query.strip():
            # Try parsing the query for complex searches
            try:
                q_object, metadata = parse_search_query(query)
                queryset = queryset.filter(q_object)

                # Add full-text search ranking if no complex operators
                if not metadata.get("operators"):
                    search_query = SearchQuery(query, config="english_unaccent")
                    queryset = queryset.filter(search_vector=search_query)
                    queryset = queryset.annotate(rank=SearchRank("search_vector", search_query)).order_by(
                        "-rank", "-entity_updated_at"
                    )
                else:
                    # Fall back to date ordering for complex queries
                    queryset = queryset.order_by("-entity_updated_at")

            except Exception:
                # Fall back to simple text search
                search_terms = query.split()
                search_q = Q()

                for term in search_terms:
                    term_q = Q(title__icontains=term) | Q(content__icontains=term) | Q(excerpt__icontains=term)
                    search_q &= term_q

                queryset = queryset.filter(search_q).order_by("-entity_updated_at")

        # Apply query optimizations using performance service
        if hasattr(self, "query_optimizer") and self.query_optimizer:
            queryset = self.query_optimizer.optimize_search_query(queryset)
        else:
            # Fallback optimizations
            queryset = queryset.select_related("creator", "organization")
            queryset = queryset.prefetch_related("tags")
            queryset = queryset.only(
                "id",
                "entity_type",
                "entity_id",
                "title",
                "content",
                "excerpt",
                "search_rank",
                "entity_created_at",
                "entity_updated_at",
                "creator__username",
                "creator__first_name",
                "creator__last_name",
                "organization__name",
            )

        # Only active entries
        queryset = queryset.filter(is_active=True)

        return queryset

    def _get_base_queryset(self) -> QuerySet:
        """Get base queryset with user permissions applied."""
        queryset = SearchIndex.objects.all()

        # Apply organization filtering for multi-tenancy
        if self.organization:
            queryset = queryset.filter(organization=self.organization)

        return queryset

    def _convert_to_instant_results(self, search_indexes: List[SearchIndex], query: str) -> List[InstantSearchResult]:
        """Convert SearchIndex objects to lightweight InstantSearchResult objects."""
        results = []

        for index in search_indexes:
            # Calculate score
            score = getattr(index, "rank", index.search_rank or 0.0)
            if score is None:
                score = 0.0

            # Generate snippet
            snippet = self._generate_instant_snippet(index, query)

            # Generate highlights
            highlights = self._generate_instant_highlights(index, query)

            # Determine URL based on entity type
            url = self._generate_entity_url(index)

            # Determine icon based on entity type
            icon = self._get_entity_icon(index.entity_type)

            # Apply highlighting to title
            highlighted_title = self._add_highlighting(index.title or "Untitled", query)

            result = InstantSearchResult(
                entity_id=str(index.entity_id),
                entity_type=index.entity_type,
                title=highlighted_title,
                snippet=snippet,
                url=url,
                icon=icon,
                score=float(score),
                highlights=highlights,
            )

            results.append(result)

        return results

    def _generate_instant_snippet(self, index: SearchIndex, query: str, max_length: int = 150) -> str:
        """Generate a short snippet for instant results with highlighting."""
        if index.excerpt:
            snippet = index.excerpt[:max_length] + ("..." if len(index.excerpt) > max_length else "")
            return self._add_highlighting(snippet, query)

        content = index.content or ""
        if not content:
            return ""

        # Find query term in content for relevant snippet
        query_words = query.lower().split()
        content_lower = content.lower()

        best_position = 0
        for word in query_words:
            pos = content_lower.find(word)
            if pos != -1:
                best_position = pos
                break

        # Extract snippet around the found position
        start = max(0, best_position - max_length // 3)
        end = min(len(content), start + max_length)

        snippet = content[start:end].strip()

        # Add ellipsis if truncated
        if start > 0:
            snippet = "..." + snippet
        if end < len(content):
            snippet = snippet + "..."

        return self._add_highlighting(snippet, query)

    def _generate_instant_highlights(self, index: SearchIndex, query: str) -> List[str]:
        """Generate highlights for instant search results."""
        highlights = []
        query_words = [word.strip().lower() for word in query.split() if len(word.strip()) > 2]

        # Check title and content for matches
        for field in [index.title, index.content]:
            if field:
                field_lower = field.lower()
                for word in query_words:
                    if word in field_lower and word not in highlights:
                        highlights.append(word)

        return highlights[:3]  # Limit to 3 highlights for instant results

    def _generate_entity_url(self, index: SearchIndex) -> str:
        """Generate URL for entity based on type."""
        entity_type = index.entity_type
        entity_id = index.entity_id

        url_mapping = {
            "project": f"/projects/{entity_id}/",
            "task": f"/projects/tasks/{entity_id}/",
            "document": f"/documents/{entity_id}/",
            "utility": f"/infrastructure/utilities/{entity_id}/",
            "user": f"/users/{entity_id}/",
        }

        return url_mapping.get(entity_type, "#")

    def _get_entity_icon(self, entity_type: str) -> str:
        """Get icon class for entity type."""
        icon_mapping = {
            "project": "fas fa-project-diagram",
            "task": "fas fa-tasks",
            "document": "fas fa-file-alt",
            "utility": "fas fa-tools",
            "user": "fas fa-user",
            "message": "fas fa-envelope",
            "knowledge": "fas fa-book",
        }

        return icon_mapping.get(entity_type, "fas fa-file")

    def _get_query_suggestions(self, query: str) -> List[str]:
        """Get query suggestions for improving search results."""
        suggestions = []

        try:
            # Get similar queries from cache or database
            similar_terms = self.get_search_suggestions(query, limit=3)
            suggestions.extend(similar_terms)

            # Add some common search improvements
            if len(query.split()) == 1:
                # Suggest using quotes for exact match
                suggestions.append(f'"{query}"')

            # Suggest field-specific searches
            if "project" not in query.lower():
                suggestions.append(f"title:{query}")

        except Exception as e:
            logger.warning(f"Error generating query suggestions: {e}")

        return suggestions[:5]

    def _generate_instant_cache_key(self, query: str, filters: Optional[FacetFilters], limit: int) -> str:
        """Generate cache key for instant search results."""
        filter_hash = hash(str(filters)) if filters else 0
        cache_key = f"instant_search_{hash(query)}_{filter_hash}_{limit}"

        # Add user/org context
        if self.user:
            cache_key += f"_user_{self.user.pk}"
        if self.organization:
            cache_key += f"_org_{self.organization.pk}"

        return cache_key

    def _add_highlighting(self, text: str, query: str) -> str:
        """Add HTML highlighting to text based on query terms."""
        if not text or not query or len(query.strip()) < 2:
            return text

        import re

        # Split query into individual words
        query_words = [word.strip() for word in query.split() if len(word.strip()) >= 2]

        highlighted_text = text
        for word in query_words:
            # Escape special regex characters
            escaped_word = re.escape(word)
            # Create case-insensitive pattern
            pattern = re.compile(f"({escaped_word})", re.IGNORECASE)
            # Replace with highlighted version
            highlighted_text = pattern.sub(r"<mark>\1</mark>", highlighted_text)

        return highlighted_text

    def _record_search_analytics(
        self,
        query: str,
        total_count: int,
        search_time_ms: float,
        filters: Optional[FacetFilters] = None,
    ):
        """Record search analytics event."""
        try:
            # Import here to avoid circular imports
            from apps.core.services.search_analytics_service import (
                get_search_analytics_service,
            )

            analytics_service = get_search_analytics_service(self.user, self.organization)

            # Extract entity types from filters
            entity_types = []
            if filters and filters.entity_types:
                entity_types = filters.entity_types

            # Convert filters to dict
            filters_dict = {}
            if filters:
                if filters.statuses:
                    filters_dict["statuses"] = filters.statuses
                if filters.priorities:
                    filters_dict["priorities"] = filters.priorities
                if filters.categories:
                    filters_dict["categories"] = filters.categories
                if filters.creators:
                    filters_dict["creators"] = filters.creators
                if filters.organizations:
                    filters_dict["organizations"] = filters.organizations
                if filters.tags:
                    filters_dict["tags"] = filters.tags
                if filters.date_from:
                    filters_dict["date_from"] = filters.date_from.isoformat()
                if filters.date_to:
                    filters_dict["date_to"] = filters.date_to.isoformat()

            # Record the search event
            analytics_service.record_search_event(
                query=query,
                results_count=total_count,
                search_duration_ms=int(search_time_ms),
                entity_types=entity_types,
                filters_applied=filters_dict,
                search_type="instant",
            )

        except Exception as e:
            logger.warning(f"Error recording search analytics: {e}")

    def _init_performance_services(self):
        """Initialize performance optimization services."""
        try:
            from apps.core.services.search_performance_service import (
                get_search_cache_manager,
                get_search_performance_monitor,
                get_search_query_optimizer,
            )

            self.cache_manager = get_search_cache_manager(self.user, self.organization)
            self.query_optimizer = get_search_query_optimizer()
            self.performance_monitor = get_search_performance_monitor()

        except ImportError as e:
            logger.warning(f"Performance services not available: {e}")
            # Fallback to basic implementations
            self.cache_manager = None
            self.query_optimizer = None
            self.performance_monitor = None

    def _convert_filters_to_dict(self, filters: Optional[FacetFilters]) -> Dict[str, Any]:
        """Convert FacetFilters to dictionary for caching."""
        if not filters:
            return {}

        filters_dict = {}
        if filters.entity_types:
            filters_dict["entity_types"] = filters.entity_types
        if filters.statuses:
            filters_dict["statuses"] = filters.statuses
        if filters.priorities:
            filters_dict["priorities"] = filters.priorities
        if filters.categories:
            filters_dict["categories"] = filters.categories
        if filters.creators:
            filters_dict["creators"] = filters.creators
        if filters.organizations:
            filters_dict["organizations"] = filters.organizations
        if filters.tags:
            filters_dict["tags"] = filters.tags
        if filters.date_from:
            filters_dict["date_from"] = filters.date_from.isoformat()
        if filters.date_to:
            filters_dict["date_to"] = filters.date_to.isoformat()

        return filters_dict

    def clear_instant_cache(self):
        """Clear instant search cache."""
        # In production, consider using cache tags or versioning
        logger.info("Instant search cache clearing not implemented - consider using cache versioning")


def get_instant_search_service(user=None, organization=None) -> InstantSearchService:
    """Factory function to get instant search service instance."""
    return InstantSearchService(user=user, organization=organization)
