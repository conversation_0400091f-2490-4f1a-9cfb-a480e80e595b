"""Edge cases and stress testing for spatial operations.

This module tests boundary conditions, error handling, and stress scenarios
for the spatial testing suite.
"""

import uuid
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.gis.geos import (
    GEOSException,
    LineString,
    Point,
)
from django.core.exceptions import ValidationError
from django.db import IntegrityError, transaction
from django.test import TestCase, TransactionTestCase

from apps.infrastructure.models import SpatialUtility, UtilityPoint, UtilityStatus

from .fixtures import SpatialTestFixtures

User = get_user_model()


class GeometryEdgeCasesTestCase(TestCase):
    """Test edge cases in geometry handling."""

    def setUp(self):
        """Set up test data for edge case testing."""
        self.organization = SpatialTestFixtures.create_test_organization("Edge Case Org")
        self.user = SpatialTestFixtures.create_test_user("edgeuser")
        self.project = SpatialTestFixtures.create_test_project(self.organization, self.user)

    def test_empty_geometry_handling(self):
        """Test handling of empty and null geometries."""
        # Test with null geometry
        utility_null = SpatialUtility(
            organization=self.organization,
            project=self.project,
            utility_type="electric",
            owner="Test Electric Co",
            material="aluminum",
            size=Decimal("4.0"),
            geometry_2d=None,
        )

        # Should allow null geometry
        try:
            utility_null.save()
            self.assertIsNone(utility_null.geometry_2d)
        except Exception as e:
            # If null not allowed, should get clear error
            self.assertIsInstance(e, (ValidationError, IntegrityError))

    def test_minimal_valid_linestring(self):
        """Test minimal valid LineString (2 points)."""
        minimal_geometry = LineString([(-86.160, 39.770), (-86.159, 39.771)], srid=4326)

        utility = SpatialUtility.objects.create(
            organization=self.organization,
            project=self.project,
            utility_type="electric",
            owner="Minimal Electric Co",
            material="aluminum",
            size=Decimal("4.0"),
            geometry_2d=minimal_geometry,
        )

        self.assertEqual(len(list(utility.geometry_2d.coords)), 2)

    def test_extremely_short_linestring(self):
        """Test extremely short LineString segments."""
        # Create geometry with very short segment (~1 millimeter)
        very_short_geometry = LineString([(-86.160000000, 39.770000000), (-86.159999999, 39.770000001)], srid=4326)

        utility = SpatialUtility(
            organization=self.organization,
            project=self.project,
            utility_type="gas",  # Gas has stricter validation
            owner="Gas Co",
            material="steel",
            size=Decimal("6.0"),
            geometry_2d=very_short_geometry,
        )

        # Should fail validation for gas utilities
        with self.assertRaises(ValidationError):
            utility.clean()

    def test_extremely_long_linestring(self):
        """Test extremely long LineString segments."""
        # Create geometry spanning multiple states (~500km)
        very_long_geometry = LineString(
            [(-86.160, 39.770), (-82.000, 42.000)],  # Indianapolis  # Detroit area
            srid=4326,
        )

        utility = SpatialUtility(
            organization=self.organization,
            project=self.project,
            utility_type="electric",  # Electric has max segment limits
            owner="Long Distance Electric",
            material="aluminum",
            size=Decimal("4.0"),
            geometry_2d=very_long_geometry,
        )

        # Should fail validation for electric utilities
        with self.assertRaises(ValidationError):
            utility.clean()

    def test_high_precision_coordinates(self):
        """Test handling of high-precision coordinates."""
        # Use coordinates with many decimal places
        high_precision_geometry = LineString(
            [
                (-86.158123456789012345, 39.768987654321098765),
                (-86.157234567890123456, 39.769876543210987654),
            ],
            srid=4326,
        )

        utility = SpatialUtility.objects.create(
            organization=self.organization,
            project=self.project,
            utility_type="telecom",
            owner="Precision Telecom Co",
            material="fiber",
            size=Decimal("1.0"),
            geometry_2d=high_precision_geometry,
        )

        # Verify precision is preserved (at least 6 decimal places)
        coords = list(utility.geometry_2d.coords)
        self.assertAlmostEqual(coords[0][0], -86.158123456789012345, places=6)
        self.assertAlmostEqual(coords[0][1], 39.768987654321098765, places=6)

    def test_coordinate_boundary_values(self):
        """Test coordinates at the boundaries of valid ranges."""
        # Test coordinates at valid extremes
        boundary_cases = [
            # Near poles
            (0.0, 89.9),
            (0.0, -89.9),
            # Near dateline
            (179.9, 0.0),
            (-179.9, 0.0),
            # Prime meridian/equator
            (0.0, 0.0),
        ]

        for i, (lon, lat) in enumerate(boundary_cases):
            try:
                geometry = LineString([(lon, lat), (lon + 0.1, lat + 0.1)], srid=4326)

                utility = SpatialUtility.objects.create(
                    organization=self.organization,
                    project=self.project,
                    utility_type="telecom",
                    owner=f"Boundary Test Co {i}",
                    material="fiber",
                    size=Decimal("1.0"),
                    geometry_2d=geometry,
                )

                self.assertIsNotNone(utility.geometry_2d)

            except (ValidationError, GEOSException) as e:
                # Some extreme coordinates may fail validation
                print(f"Boundary case ({lon}, {lat}) failed: {e}")

    def test_invalid_coordinate_values(self):
        """Test handling of invalid coordinate values."""
        invalid_cases = [
            # Invalid longitude
            (-200.0, 39.770),
            (200.0, 39.770),
            # Invalid latitude
            (-86.160, 100.0),
            (-86.160, -100.0),
        ]

        for lon, lat in invalid_cases:
            try:
                geometry = LineString([(lon, lat), (lon + 0.1, lat + 0.1)], srid=4326)

                utility = SpatialUtility(
                    organization=self.organization,
                    project=self.project,
                    utility_type="electric",
                    owner="Invalid Coords Co",
                    material="aluminum",
                    size=Decimal("4.0"),
                    geometry_2d=geometry,
                )

                # Should fail validation
                with self.assertRaises(ValidationError):
                    utility.clean()

            except GEOSException:
                # GEOS may prevent creation of invalid geometry
                pass

    def test_self_intersecting_linestring(self):
        """Test handling of self-intersecting LineString."""
        # Create figure-8 pattern
        self_intersecting = LineString(
            [
                (-86.160, 39.770),
                (-86.155, 39.775),
                (-86.155, 39.765),
                (-86.160, 39.770),  # Back to start
                (-86.165, 39.775),
                (-86.165, 39.765),
                (-86.160, 39.770),  # Complete figure-8
            ],
            srid=4326,
        )

        utility = SpatialUtility(
            organization=self.organization,
            project=self.project,
            utility_type="water",
            owner="Self-Intersecting Water Co",
            material="pvc",
            size=Decimal("8.0"),
            geometry_2d=self_intersecting,
        )

        # Should fail validation
        with self.assertRaises(ValidationError):
            utility.clean()

    def test_degenerate_geometries(self):
        """Test handling of degenerate geometries."""
        # LineString with all points identical
        try:
            degenerate_line = LineString([(-86.160, 39.770), (-86.160, 39.770), (-86.160, 39.770)], srid=4326)

            utility = SpatialUtility(
                organization=self.organization,
                project=self.project,
                utility_type="electric",
                owner="Degenerate Electric Co",
                material="aluminum",
                size=Decimal("4.0"),
                geometry_2d=degenerate_line,
            )

            # Should fail validation (zero-length segments)
            with self.assertRaises(ValidationError):
                utility.clean()

        except GEOSException:
            # GEOS may prevent creation of degenerate geometry
            pass

    def test_very_complex_linestring(self):
        """Test handling of very complex LineString (many points)."""
        # Create LineString with many points
        coords = []
        for i in range(10000):  # 10k points
            x = -86.200 + (i * 0.00001)
            y = 39.700 + (i * 0.00001)
            coords.append((x, y))

        try:
            complex_geometry = LineString(coords, srid=4326)

            utility = SpatialUtility.objects.create(
                organization=self.organization,
                project=self.project,
                utility_type="telecom",
                owner="Complex Telecom Co",
                material="fiber",
                size=Decimal("2.0"),
                geometry_2d=complex_geometry,
            )

            # Should handle complex geometry
            self.assertEqual(len(list(utility.geometry_2d.coords)), 10000)

        except Exception as e:
            # May fail due to memory or complexity limits
            print(f"Complex geometry test failed: {e}")

    def test_nan_and_infinity_coordinates(self):
        """Test handling of NaN and infinity in coordinates."""

        invalid_coord_cases = [
            (float("nan"), 39.770),
            (-86.160, float("nan")),
            (float("inf"), 39.770),
            (-86.160, float("inf")),
            (float("-inf"), 39.770),
        ]

        for lon, lat in invalid_coord_cases:
            try:
                # GEOS should prevent creation of invalid coordinates
                geometry = LineString([(lon, lat), (-86.160, 39.770)], srid=4326)

                # If creation succeeds, validation should catch it
                utility = SpatialUtility(
                    organization=self.organization,
                    project=self.project,
                    utility_type="electric",
                    owner="Invalid Float Co",
                    material="aluminum",
                    size=Decimal("4.0"),
                    geometry_2d=geometry,
                )

                with self.assertRaises(ValidationError):
                    utility.clean()

            except (GEOSException, ValueError, OverflowError):
                # Expected - GEOS/Python should prevent invalid coordinates
                pass


class ConcurrencyStressTestCase(TransactionTestCase):
    """Test concurrency and stress scenarios."""

    def setUp(self):
        """Set up test data for concurrency testing."""
        self.organization = SpatialTestFixtures.create_test_organization("Concurrency Org")
        self.user = SpatialTestFixtures.create_test_user("concurrentuser")
        self.project = SpatialTestFixtures.create_test_project(self.organization, self.user)

    def test_concurrent_geometry_updates(self):
        """Test concurrent updates to the same utility geometry."""
        # Create initial utility
        utility = SpatialUtility.objects.create(
            organization=self.organization,
            project=self.project,
            utility_type="water",
            owner="Concurrent Water Co",
            material="pvc",
            size=Decimal("8.0"),
            geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
        )

        import threading
        import time

        results = []

        def update_geometry(thread_id, new_coords):
            try:
                # Get fresh instance
                util = SpatialUtility.objects.get(id=utility.id)

                # Simulate some processing time
                time.sleep(0.1)

                # Update geometry
                util.geometry_2d = LineString(new_coords, srid=4326)
                util.save()

                results.append(f"Thread {thread_id} succeeded")

            except Exception as e:
                results.append(f"Thread {thread_id} failed: {e}")

        # Start concurrent updates
        threads = []
        for i in range(5):
            coords = [(-86.160 - i * 0.01, 39.770), (-86.155 - i * 0.01, 39.770)]
            thread = threading.Thread(target=update_geometry, args=(i, coords))
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # All updates should succeed (last one wins)
        self.assertEqual(len(results), 5)
        for result in results:
            print(result)

    def test_bulk_insertion_stress(self):
        """Test stress scenarios for bulk insertion."""
        import time

        # Create large batch of utilities
        batch_size = 5000  # Reduced for CI environments
        utilities_data = []

        start_time = time.time()

        for i in range(batch_size):
            x = -86.300 + (i * 0.0001)
            y = 39.600 + (i * 0.0001)

            utility = SpatialUtility(
                organization=self.organization,
                project=self.project,
                utility_type="electric" if i % 2 == 0 else "water",
                owner=f"Bulk Test Co {i}",
                material="aluminum" if i % 2 == 0 else "pvc",
                size=Decimal(str(4.0 + (i % 8))),
                geometry_2d=LineString([(x, y), (x + 0.001, y + 0.001)], srid=4326),
            )
            utilities_data.append(utility)

        # Bulk create
        created_utilities = SpatialUtility.objects.bulk_create(utilities_data)

        end_time = time.time()
        insertion_time = end_time - start_time

        # Verify all were created
        self.assertEqual(len(created_utilities), batch_size)
        self.assertEqual(SpatialUtility.objects.count(), batch_size)

        print(f"Bulk inserted {batch_size} utilities in {insertion_time:.2f} seconds")
        print(f"Rate: {batch_size / insertion_time:.0f} utilities/second")

        # Should handle large batch efficiently
        self.assertLess(insertion_time, 30.0)  # 30 seconds max

    def test_memory_pressure_scenarios(self):
        """Test behavior under memory pressure."""
        import gc

        # Create many utilities to consume memory
        utilities = []
        batch_size = 1000

        try:
            for batch in range(10):  # 10k total utilities
                batch_utilities = []

                for i in range(batch_size):
                    x = -86.200 + (batch * 0.1) + (i * 0.0001)
                    y = 39.700 + (batch * 0.1) + (i * 0.0001)

                    utility = SpatialUtility(
                        organization=self.organization,
                        project=self.project,
                        utility_type="electric",
                        owner=f"Memory Test Co {batch}-{i}",
                        material="aluminum",
                        size=Decimal("4.0"),
                        geometry_2d=LineString([(x, y), (x + 0.001, y + 0.001)], srid=4326),
                    )
                    batch_utilities.append(utility)

                # Create batch
                created_batch = SpatialUtility.objects.bulk_create(batch_utilities)
                utilities.extend(created_batch)

                # Force garbage collection
                gc.collect()

                print(f"Created batch {batch + 1}, total utilities: {len(utilities)}")

            # Verify final count
            total_count = SpatialUtility.objects.count()
            self.assertEqual(total_count, 10000)

        except Exception as e:
            print(f"Memory pressure test failed at {len(utilities)} utilities: {e}")
            # Test should handle memory constraints gracefully

    def test_transaction_rollback_scenarios(self):
        """Test transaction rollback with spatial data."""
        initial_count = SpatialUtility.objects.count()

        try:
            with transaction.atomic():
                # Create some utilities
                for i in range(100):
                    x = -86.200 + (i * 0.001)
                    y = 39.700 + (i * 0.001)

                    SpatialUtility.objects.create(
                        organization=self.organization,
                        project=self.project,
                        utility_type="electric",
                        owner=f"Rollback Test Co {i}",
                        material="aluminum",
                        size=Decimal("4.0"),
                        geometry_2d=LineString([(x, y), (x + 0.001, y + 0.001)], srid=4326),
                    )

                # Force rollback with invalid operation
                raise Exception("Forced rollback")

        except Exception:
            # Expected rollback
            pass

        # Count should be unchanged
        final_count = SpatialUtility.objects.count()
        self.assertEqual(final_count, initial_count)

    def test_database_connection_exhaustion(self):
        """Test behavior with many database connections."""
        import threading
        import time

        connection_results = []

        def database_worker(worker_id):
            try:
                # Perform database operations
                for _i in range(10):
                    SpatialUtility.objects.count()
                    time.sleep(0.01)  # Small delay

                connection_results.append(f"Worker {worker_id} completed")

            except Exception as e:
                connection_results.append(f"Worker {worker_id} failed: {e}")

        # Start many concurrent workers
        threads = []
        for i in range(20):  # 20 concurrent connections
            thread = threading.Thread(target=database_worker, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Most workers should succeed
        success_count = sum(1 for result in connection_results if "completed" in result)
        self.assertGreaterEqual(success_count, 15)  # At least 75% success

        print(f"Database connection test: {success_count}/20 workers succeeded")


class ErrorHandlingTestCase(TestCase):
    """Test error handling and recovery scenarios."""

    def setUp(self):
        """Set up test data for error handling tests."""
        self.organization = SpatialTestFixtures.create_test_organization("Error Test Org")
        self.user = SpatialTestFixtures.create_test_user("erroruser")
        self.project = SpatialTestFixtures.create_test_project(self.organization, self.user)

    def test_invalid_srid_handling(self):
        """Test handling of invalid SRID values."""
        try:
            # Try to create geometry with invalid SRID
            invalid_geometry = LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=99999)  # Invalid SRID

            utility = SpatialUtility(
                organization=self.organization,
                project=self.project,
                utility_type="electric",
                owner="Invalid SRID Co",
                material="aluminum",
                size=Decimal("4.0"),
                geometry_2d=invalid_geometry,
            )

            # Should handle invalid SRID gracefully
            with self.assertRaises((ValidationError, GEOSException)):
                utility.clean()

        except GEOSException:
            # GEOS may prevent creation with invalid SRID
            pass

    def test_corrupted_geometry_data(self):
        """Test handling of corrupted geometry data."""
        # Create utility with valid geometry first
        utility = SpatialUtility.objects.create(
            organization=self.organization,
            project=self.project,
            utility_type="water",
            owner="Test Water Co",
            material="pvc",
            size=Decimal("8.0"),
            geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
        )

        # Simulate corrupted geometry by direct database manipulation
        from django.db import connection

        try:
            with connection.cursor() as cursor:
                # Try to insert invalid WKB data (this may fail at DB level)
                cursor.execute(
                    """
                    UPDATE infrastructure_spatialutility
                    SET geometry_2d = 'invalid_wkb_data'::geometry
                    WHERE id = %s
                """,
                    [utility.id],
                )

        except Exception as e:
            # Database should prevent corruption
            print(f"Database prevented geometry corruption: {e}")

    def test_missing_required_fields(self):
        """Test handling of missing required fields."""
        # Try to create utility without required fields
        with self.assertRaises((ValidationError, IntegrityError)):
            SpatialUtility.objects.create(
                # Missing organization
                project=self.project,
                utility_type="electric",
                owner="Test Electric Co",
                material="aluminum",
                size=Decimal("4.0"),
                geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
            )

    def test_constraint_violation_handling(self):
        """Test handling of database constraint violations."""
        # Create utility
        SpatialUtility.objects.create(
            organization=self.organization,
            project=self.project,
            utility_type="water",
            owner="Constraint Test Co",
            material="pvc",
            size=Decimal("8.0"),
            geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
        )

        # Try to create utility point with invalid foreign key
        with self.assertRaises(IntegrityError):
            UtilityPoint.objects.create(
                spatial_utility_id=uuid.uuid4(),  # Non-existent utility
                feature_type="valve",
                location=Point(-86.158, 39.770, srid=4326),
                elevation=Decimal("250.0"),
            )

    def test_circular_dependency_prevention(self):
        """Test prevention of circular dependencies in status tracking."""
        utility = SpatialUtility.objects.create(
            organization=self.organization,
            project=self.project,
            utility_type="gas",
            owner="Gas Co",
            material="steel",
            size=Decimal("6.0"),
            geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
        )

        # Create status entries
        status1 = UtilityStatus.objects.create(
            spatial_utility=utility,
            status="proposed",
            status_date=timezone.now(),
            changed_by=self.user,
            notes="Initial status",
        )

        # Should allow multiple status entries for the same utility
        status2 = UtilityStatus.objects.create(
            spatial_utility=utility,
            status="approved",
            status_date=timezone.now(),
            changed_by=self.user,
            notes="Approved status",
        )

        self.assertNotEqual(status1.id, status2.id)
        self.assertEqual(status1.spatial_utility, status2.spatial_utility)

    def test_large_field_values(self):
        """Test handling of extremely large field values."""
        from django.core.exceptions import ValidationError

        # Test very large size value
        try:
            utility = SpatialUtility(
                organization=self.organization,
                project=self.project,
                utility_type="electric",
                owner="Large Size Co",
                material="aluminum",
                size=Decimal("999999.99"),  # Very large size
                geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
            )

            # May fail validation depending on constraints
            utility.clean()
            utility.save()

        except (ValidationError, ValueError) as e:
            # Expected for unreasonable values
            print(f"Large size value rejected: {e}")

        # Test very long string fields
        try:
            utility = SpatialUtility(
                organization=self.organization,
                project=self.project,
                utility_type="electric",
                owner="X" * 1000,  # Very long owner name
                material="aluminum",
                size=Decimal("4.0"),
                geometry_2d=LineString([(-86.160, 39.770), (-86.155, 39.770)], srid=4326),
            )

            utility.save()

        except Exception as e:
            # May fail due to field length constraints
            print(f"Long string field rejected: {e}")
