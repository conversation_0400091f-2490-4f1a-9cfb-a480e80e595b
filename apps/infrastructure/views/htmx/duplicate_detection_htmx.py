"""
HTMX Views for Duplicate Detection Operations

Provides comprehensive HTMX endpoints for duplicate detection configuration,
execution, preview, and resolution with real-time progress updates.
"""

import logging

from django.contrib import messages
from django.utils import timezone
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods, require_POST

from apps.common.mixins import HTMXResponseMixin
from apps.infrastructure.models_file_import import (
    DuplicateDetectionConfig,
    DuplicateDetectionResult,
    Import,
)
from apps.infrastructure.services.duplicate_detection_service import (
    DuplicateDetectionService,
)

logger = logging.getLogger(__name__)


@login_required
@require_http_methods(["GET"])
def duplicate_detection_config(request, import_id):
    """
    Display duplicate detection configuration interface.

    Shows available configurations and allows creation of new ones.
    """
    try:
        import_record = get_object_or_404(Import, import_id=import_id, created_by=request.user)

        # Get available configurations
        configs = DuplicateDetectionConfig.objects.filter(created_by=request.user).order_by(
            "-times_used", "-updated_at"
        )[:10]

        # Get default configuration
        default_config = {
            "matching_rules": {
                "name": DuplicateDetectionService.FUZZY_MATCH,
                "identifier": DuplicateDetectionService.EXACT_MATCH,
                "code": DuplicateDetectionService.EXACT_MATCH,
                "description": DuplicateDetectionService.SIMILAR_MATCH,
            },
            "similarity_threshold": 0.8,
            "default_merge_strategy": DuplicateDetectionService.SKIP_DUPLICATE,
        }

        context = {
            "import_record": import_record,
            "available_configs": configs,
            "default_config": default_config,
            "matching_rule_choices": DuplicateDetectionService.MATCHING_RULES,
            "merge_strategy_choices": DuplicateDetectionService.MERGE_STRATEGIES,
        }

        return render(request, "infrastructure/partials/duplicate_detection_config.html", context)

    except Exception as e:
        logger.error(f"Error displaying duplicate detection config: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Failed to load configuration: {e}"},
        )


@login_required
@require_POST
@csrf_protect
def detect_duplicates(request, import_id):
    """
    Perform duplicate detection on import data.

    Uses configuration from form to detect duplicates in source data.
    """
    try:
        import_record = get_object_or_404(Import, import_id=import_id, created_by=request.user)

        # Get configuration from form
        config_id = request.POST.get("config_id")
        if config_id:
            # Use existing configuration
            detection_config = get_object_or_404(DuplicateDetectionConfig, config_id=config_id, created_by=request.user)
            matching_rules = detection_config.matching_rules
            similarity_threshold = detection_config.similarity_threshold
            detection_config.increment_usage()
        else:
            # Use custom configuration from form
            matching_rules = {}
            for key, value in request.POST.items():
                if key.startswith("rule_"):
                    field_name = key[5:]  # Remove 'rule_' prefix
                    matching_rules[field_name] = value

            similarity_threshold = float(request.POST.get("similarity_threshold", 0.8))
            detection_config = None

        # Get sample data for detection (in real implementation, this would come from actual import data)
        # For now, using placeholder data
        sample_data = [
            {
                "_record_id": "1",
                "name": "John Smith",
                "identifier": "JS001",
                "code": "ABC123",
            },
            {
                "_record_id": "2",
                "name": "Jon Smith",
                "identifier": "JS001",
                "code": "ABC124",
            },
            {
                "_record_id": "3",
                "name": "Jane Doe",
                "identifier": "JD001",
                "code": "DEF456",
            },
            {
                "_record_id": "4",
                "name": "John Smyth",
                "identifier": "JS002",
                "code": "ABC125",
            },
        ]

        # Perform duplicate detection
        service = DuplicateDetectionService(request.user)
        detection_results = service.detect_duplicates(import_record, sample_data, matching_rules, similarity_threshold)

        if "error" in detection_results:
            return render(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {"error": detection_results["error"], "import_record": import_record},
            )

        # Save detection results
        result_record = DuplicateDetectionResult.objects.create(
            import_record=import_record,
            detection_config=detection_config,
            created_by=request.user,
            total_records=detection_results["total_records"],
            duplicates_found=detection_results["duplicates_found"],
            unique_records=detection_results["unique_records"],
            duplicate_groups_count=len(detection_results["duplicate_groups"]),
            duplicate_groups=detection_results["duplicate_groups"],
            processing_stats=detection_results["processing_stats"],
            matching_rules_used=detection_results["matching_rules_used"],
        )

        context = {
            "import_record": import_record,
            "detection_results": detection_results,
            "result_record": result_record,
            "merge_strategy_choices": DuplicateDetectionService.MERGE_STRATEGIES,
        }

        return render(request, "infrastructure/partials/duplicate_detection_results.html", context)

    except Exception as e:
        logger.error(f"Error detecting duplicates: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Duplicate detection failed: {e}"},
        )


@login_required
@require_http_methods(["GET"])
def duplicate_preview(request, result_id):
    """
    Display detailed preview of detected duplicates.

    Shows duplicate groups with similarity scores and suggested actions.
    """
    try:
        result_record = get_object_or_404(DuplicateDetectionResult, result_id=result_id, created_by=request.user)

        # Get pagination parameters
        page = int(request.GET.get("page", 1))
        per_page = int(request.GET.get("per_page", 10))

        # Get filter parameters
        min_similarity = float(request.GET.get("min_similarity", 0.0))
        group_size_filter = request.GET.get("group_size")

        # Filter duplicate groups
        duplicate_groups = result_record.duplicate_groups
        filtered_groups = []

        for group in duplicate_groups:
            # Apply similarity filter
            avg_similarity = group.get("confidence_score", 0)
            if avg_similarity < min_similarity:
                continue

            # Apply group size filter
            if group_size_filter:
                if group_size_filter == "pairs" and group["duplicate_count"] != 2:
                    continue
                elif group_size_filter == "large" and group["duplicate_count"] <= 2:
                    continue

            filtered_groups.append(group)

        # Paginate
        paginator = Paginator(filtered_groups, per_page)
        page_obj = paginator.get_page(page)

        context = {
            "result_record": result_record,
            "page_obj": page_obj,
            "duplicate_groups": page_obj.object_list,
            "current_filters": {
                "min_similarity": min_similarity,
                "group_size": group_size_filter,
            },
            "merge_strategy_choices": DuplicateDetectionService.MERGE_STRATEGIES,
        }

        return render(request, "infrastructure/partials/duplicate_preview.html", context)

    except Exception as e:
        logger.error(f"Error displaying duplicate preview: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Failed to load preview: {e}"},
        )


@login_required
@require_POST
@csrf_protect
def resolve_duplicates(request, result_id):
    """
    Resolve detected duplicates using specified strategies.

    Supports both bulk resolution and individual group resolution.
    """
    try:
        result_record = get_object_or_404(DuplicateDetectionResult, result_id=result_id, created_by=request.user)

        if result_record.status == "resolved":
            return render(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {
                    "error": "Duplicates have already been resolved",
                    "result_record": result_record,
                },
            )

        # Get resolution strategy
        default_strategy = request.POST.get("default_strategy", "skip")

        # Get custom resolutions for specific groups
        custom_resolutions = {}
        for key, value in request.POST.items():
            if key.startswith("group_strategy_"):
                group_id = key[15:]  # Remove 'group_strategy_' prefix
                custom_resolutions[group_id] = value

        # Perform resolution
        service = DuplicateDetectionService(request.user)
        resolution_results = service.resolve_duplicates(
            result_record.import_record,
            result_record.duplicate_groups,
            default_strategy,
            custom_resolutions,
        )

        if "error" in resolution_results:
            return render(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {"error": resolution_results["error"], "result_record": result_record},
            )

        # Update result record
        result_record.mark_resolved(request.user, resolution_results)

        # Add success message
        messages.success(
            request,
            f"Duplicates resolved successfully. {resolution_results['resolved_groups']} groups processed.",
        )

        context = {
            "result_record": result_record,
            "resolution_results": resolution_results,
        }

        return render(
            request,
            "infrastructure/partials/duplicate_resolution_results.html",
            context,
        )

    except Exception as e:
        logger.error(f"Error resolving duplicates: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Resolution failed: {e}"},
        )


@login_required
@require_POST
@csrf_protect
def save_detection_config(request):
    """
    Save duplicate detection configuration for reuse.

    Creates a new configuration based on form data.
    """
    try:
        # Get configuration data from form
        name = request.POST.get("config_name", "").strip()
        description = request.POST.get("config_description", "").strip()

        if not name:
            return render(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {"error": "Configuration name is required"},
            )

        # Build matching rules
        matching_rules = {}
        for key, value in request.POST.items():
            if key.startswith("rule_"):
                field_name = key[5:]  # Remove 'rule_' prefix
                matching_rules[field_name] = value

        # Create configuration
        config = DuplicateDetectionConfig.objects.create(
            name=name,
            description=description,
            created_by=request.user,
            organization=getattr(request.user, "organization", None),
            matching_rules=matching_rules,
            similarity_threshold=float(request.POST.get("similarity_threshold", 0.8)),
            default_merge_strategy=request.POST.get("default_merge_strategy", "skip"),
            ignore_case=request.POST.get("ignore_case") == "on",
            trim_whitespace=request.POST.get("trim_whitespace") == "on",
            normalize_text=request.POST.get("normalize_text") == "on",
        )

        messages.success(request, f"Configuration '{name}' saved successfully.")

        context = {
            "config": config,
            "saved": True,
        }

        return render(request, "infrastructure/partials/duplicate_config_saved.html", context)

    except Exception as e:
        logger.error(f"Error saving detection config: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Failed to save configuration: {e}"},
        )


@login_required
@require_http_methods(["GET"])
def detection_history(request, import_id):
    """
    Display duplicate detection history for an import.

    Shows all previous detection operations with results.
    """
    try:
        import_record = get_object_or_404(Import, import_id=import_id, created_by=request.user)

        # Get detection history
        results = DuplicateDetectionResult.objects.filter(import_record=import_record).order_by("-created_at")

        context = {
            "import_record": import_record,
            "detection_results": results,
        }

        return render(request, "infrastructure/partials/duplicate_detection_history.html", context)

    except Exception as e:
        logger.error(f"Error getting detection history: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Failed to load history: {e}"},
        )


@login_required
@require_http_methods(["GET"])
def detection_progress(request, result_id):
    """
    Get duplicate detection progress for real-time updates.

    Returns JSON with current progress information.
    """
    try:
        result_record = get_object_or_404(DuplicateDetectionResult, result_id=result_id, created_by=request.user)

        progress = {
            "status": result_record.status,
            "total_records": result_record.total_records,
            "duplicates_found": result_record.duplicates_found,
            "unique_records": result_record.unique_records,
            "duplicate_groups_count": result_record.duplicate_groups_count,
            "resolved_groups": result_record.resolved_groups,
            "processing_time_ms": result_record.processing_stats.get("matching_time_ms", 0),
            "last_updated": result_record.created_at.isoformat(),
        }

        return JsonResponse(progress)

    except Exception as e:
        logger.error(f"Error getting detection progress: {e}")
        return JsonResponse({"error": str(e)}, status=500)


@method_decorator(login_required, name="dispatch")
class DuplicateDetectionModalView(HTMXResponseMixin, View):
    """
    View for displaying duplicate detection modal with all functionality.

    Combines configuration, detection, and resolution in a single modal interface.
    """

    def get(self, request, import_id):
        """Display duplicate detection modal."""
        try:
            import_record = get_object_or_404(Import, import_id=import_id, created_by=request.user)

            # Get available configurations
            configs = DuplicateDetectionConfig.objects.filter(created_by=request.user).order_by(
                "-times_used", "-updated_at"
            )[:5]

            # Get recent detection results
            recent_results = DuplicateDetectionResult.objects.filter(import_record=import_record).order_by(
                "-created_at"
            )[:3]

            context = {
                "import_record": import_record,
                "available_configs": configs,
                "recent_results": recent_results,
                "matching_rule_choices": DuplicateDetectionService.MATCHING_RULES,
                "merge_strategy_choices": DuplicateDetectionService.MERGE_STRATEGIES,
            }

            return self.render_htmx_response(request, "infrastructure/modals/duplicate_detection_modal.html", context)

        except Exception as e:
            logger.error(f"Error displaying duplicate detection modal: {e}")
            return self.render_htmx_response(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {"error": f"Failed to load duplicate detection interface: {e}"},
            )


@login_required
@require_POST
@csrf_protect
def bulk_resolve_groups(request, result_id):
    """
    Bulk resolve selected duplicate groups.

    Allows resolution of multiple groups with different strategies.
    """
    try:
        result_record = get_object_or_404(DuplicateDetectionResult, result_id=result_id, created_by=request.user)

        # Get selected groups
        selected_groups = request.POST.getlist("selected_groups")
        if not selected_groups:
            return render(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {"error": "No groups selected for resolution"},
            )

        # Build custom resolutions
        custom_resolutions = {}
        for group_id in selected_groups:
            strategy_key = f"strategy_{group_id}"
            strategy = request.POST.get(strategy_key, "skip")
            custom_resolutions[group_id] = strategy

        # Filter groups to only selected ones
        selected_group_data = [
            group for group in result_record.duplicate_groups if group["group_id"] in selected_groups
        ]

        # Perform resolution
        service = DuplicateDetectionService(request.user)
        resolution_results = service.resolve_duplicates(
            result_record.import_record,
            selected_group_data,
            "manual_review",  # Default strategy for unspecified
            custom_resolutions,
        )

        if "error" in resolution_results:
            return render(
                request,
                "infrastructure/partials/duplicate_detection_error.html",
                {"error": resolution_results["error"], "result_record": result_record},
            )

        # Update result record partially
        result_record.resolved_groups += resolution_results["resolved_groups"]
        if result_record.resolved_groups >= result_record.duplicate_groups_count:
            result_record.status = "resolved"
            result_record.resolved_at = timezone.now()
            result_record.resolved_by = request.user
        else:
            result_record.status = "partially_resolved"

        result_record.save(update_fields=["resolved_groups", "status", "resolved_at", "resolved_by"])

        messages.success(
            request,
            f"Bulk resolution completed. {resolution_results['resolved_groups']} groups processed.",
        )

        context = {
            "result_record": result_record,
            "resolution_results": resolution_results,
            "bulk_operation": True,
        }

        return render(
            request,
            "infrastructure/partials/duplicate_resolution_results.html",
            context,
        )

    except Exception as e:
        logger.error(f"Error in bulk resolution: {e}")
        return render(
            request,
            "infrastructure/partials/duplicate_detection_error.html",
            {"error": f"Bulk resolution failed: {e}"},
        )
