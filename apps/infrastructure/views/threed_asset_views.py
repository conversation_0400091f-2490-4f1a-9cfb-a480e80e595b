"""3D Asset Management Views for CLEAR Platform

Enterprise-grade views for managing 3D assets, libraries, and processing workflows.
Provides comprehensive asset lifecycle management with real-time status updates.
"""

from __future__ import annotations

import json
from datetime import timedelta
import logging
from typing import TYPE_CHECKING

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Avg, Count, Q, Sum
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
from django.views.generic import DetailView, ListView

if TYPE_CHECKING:
    from typing import Any

    from django.db.models import QuerySet
    from django.http import HttpRequest, HttpResponse

    from apps.infrastructure.models_3d_assets import (
        AssetPerformanceProfile,
        AssetProcessingJob,
        AssetUsageTracking,
        ProjectAssetMapping,
        ThreeDAsset,
        ThreeDAssetLibrary,
    )

from apps.common.security.file_upload_security import (
    file_upload_security_headers,
    secure_file_upload,
)
from apps.common.services.asset_processing import (
    asset_processor,
    get_processing_recommendations,
)
from apps.infrastructure.models_3d_assets import (
    AssetPerformanceProfile,
    AssetProcessingJob,
    AssetUsageTracking,
    ProjectAssetMapping,
    ThreeDAsset,
    ThreeDAssetLibrary,
)

logger = logging.getLogger(__name__)


# =============================================================================
# ASSET LIBRARY VIEWS
# =============================================================================


@method_decorator(login_required, name="dispatch")
class AssetLibraryListView(ListView):
    """Display list of 3D asset libraries with filtering and statistics.

    Provides paginated list view of 3D asset libraries with advanced filtering
    capabilities and statistical information for asset management.
    """

    model = ThreeDAssetLibrary
    template_name: str = "admin/3d_assets/asset_library_list.html"
    context_object_name: str = "libraries"
    paginate_by: int = 20

    def get_queryset(self) -> QuerySet[ThreeDAssetLibrary]:
        """Get filtered queryset of asset libraries based on user permissions and filters.

        Returns
        -------
            QuerySet of ThreeDAssetLibrary objects filtered by access permissions
            and user-specified filters (type, access level, search).

        """
        from apps.common.utils.permissions import filter_projects_by_access
        from apps.projects.models import Project

        queryset = ThreeDAssetLibrary.objects.select_related("project", "created_by")

        # Get accessible projects for the user
        accessible_projects = filter_projects_by_access(self.request.user, Project.objects.all())
        project_ids = list(accessible_projects.values_list("id", flat=True))

        # Filter by user's accessible projects if not admin
        if not self.request.user.is_superuser:
            if project_ids:
                queryset = queryset.filter(
                    Q(project_id__in=project_ids)
                    | Q(access_level="public")
                    | Q(shared_with_users=self.request.user)
                    | Q(created_by=self.request.user),
                ).distinct()
            else:
                # No project access - only show public or user's own libraries
                queryset = queryset.filter(Q(access_level="public") | Q(created_by=self.request.user)).distinct()

        # Apply filters
        library_type = self.request.GET.get("type")
        if library_type:
            queryset = queryset.filter(library_type=library_type)

        access_level = self.request.GET.get("access")
        if access_level:
            queryset = queryset.filter(access_level=access_level)

        search = self.request.GET.get("search")
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search) | Q(tags__icontains=search),
            )

        return queryset.order_by("-updated_at")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add statistical data to the template context.

        Args:
        ----
            **kwargs: Additional keyword arguments from parent class.

        Returns:
        -------
            Context dictionary with library statistics including total counts,
            asset counts, storage size, and download statistics.

        """
        context = super().get_context_data(**kwargs)

        # Calculate statistics
        libraries = self.get_queryset()
        context.update(
            {
                "total_libraries": libraries.count(),
                "total_assets": libraries.aggregate(total=Sum("total_assets"))["total"] or 0,
                "total_size_gb": (libraries.aggregate(total=Sum("total_size_bytes"))["total"] or 0) / (1024**3),
                "download_count": libraries.aggregate(total=Sum("download_count"))["total"] or 0,
            },
        )

        return context


@method_decorator(login_required, name="dispatch")
class AssetLibraryDetailView(DetailView):
    """Display detailed view of a single asset library with its assets.

    Provides comprehensive detail view including library information,
    filtered asset listings, pagination, and statistical analysis.
    """

    model = ThreeDAssetLibrary
    template_name: str = "admin/3d_assets/asset_library_detail.html"
    context_object_name: str = "library"

    def get_queryset(self) -> QuerySet[ThreeDAssetLibrary]:
        """Get queryset of libraries filtered by user access permissions.

        Returns
        -------
            QuerySet of ThreeDAssetLibrary objects accessible to the current user.

        """
        queryset = ThreeDAssetLibrary.objects.select_related("organization", "created_by")

        # Filter by access permissions
        if not self.request.user.is_superuser:
            queryset = queryset.filter(
                Q(organization=self.request.user.organization)
                | Q(access_level="public")
                | Q(shared_with_users=self.request.user),
            ).distinct()

        return queryset

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add asset listings and statistics to template context.

        Args:
        ----
            **kwargs: Additional keyword arguments from parent class.

        Returns:
        -------
            Context dictionary with paginated assets, filtering options,
            and statistical analysis of the library's contents.

        """
        context = super().get_context_data(**kwargs)
        library = self.object

        # Get library assets with filtering
        assets = library.assets.filter(status="approved")

        asset_type = self.request.GET.get("asset_type")
        if asset_type:
            assets = assets.filter(asset_type=asset_type)

        complexity = self.request.GET.get("complexity")
        if complexity:
            assets = assets.filter(complexity_level=complexity)

        search = self.request.GET.get("search")
        if search:
            assets = assets.filter(
                Q(name__icontains=search) | Q(description__icontains=search) | Q(tags__icontains=search),
            )

        # Paginate assets
        paginator = Paginator(assets.order_by("-updated_at"), 24)
        page_number = self.request.GET.get("page")
        assets_page = paginator.get_page(page_number)

        # Calculate statistics
        context.update(
            {
                "assets": assets_page,
                "asset_stats": {
                    "total_count": assets.count(),
                    "avg_quality": assets.aggregate(avg=Avg("quality_score"))["avg"] or 0,
                    "total_downloads": assets.aggregate(total=Sum("download_count"))["total"] or 0,
                    "complexity_distribution": assets.values("complexity_level").annotate(count=Count("id")),
                },
            },
        )

        return context


@require_http_methods(["POST"])
@login_required
def create_asset_library(request: HttpRequest) -> HttpResponse:
    """Create a new asset library.

    Args:
    ----
        request: HTTP request object containing POST data for library creation.

    Returns:
    -------
        HTTP redirect response to library detail page on success,
        or back to library list with error message on failure.

    """
    try:
        # Validate input
        name = request.POST.get("name", "").strip()
        if not name:
            messages.error(request, "Library name is required.")
            return redirect("admin:3d_assets:library_list")

        # Create library
        library = ThreeDAssetLibrary.objects.create(
            name=name,
            description=request.POST.get("description", ""),
            library_type=request.POST.get("library_type", "custom"),
            access_level=request.POST.get("access_level", "organization"),
            organization=request.user.organization,
            created_by=request.user,
            tags=(request.POST.get("tags", "").split(",") if request.POST.get("tags") else []),
        )

        messages.success(request, f'Asset library "{name}" created successfully.')
        return redirect("admin:3d_assets:library_detail", pk=library.id)

    except Exception:
        logger.exception("Failed to create asset library")
        messages.error(request, "Failed to create asset library.")
        return redirect("admin:3d_assets:library_list")


@require_http_methods(["GET"])
@login_required
def library_analytics(request: HttpRequest, library_id: str) -> HttpResponse:
    """Return analytics data for a specific library.

    Args:
    ----
        request: HTTP request object.
        library_id: UUID string identifying the asset library.

    Returns:
    -------
        Rendered HTML response with analytics dashboard or JSON error response.

    """
    try:
        library = get_object_or_404(ThreeDAssetLibrary, id=library_id)

        # Calculate analytics
        assets = library.assets.all()
        usage_data = AssetUsageTracking.objects.filter(asset__library=library)

        analytics: dict[str, Any] = {
            "asset_count": assets.count(),
            "total_downloads": assets.aggregate(total=Sum("download_count"))["total"] or 0,
            "average_quality": assets.aggregate(avg=Avg("quality_score"))["avg"] or 0,
            "usage_trends": list(
                usage_data.extra(select={"day": "date(started_at)"})
                .values("day")
                .annotate(count=Count("id"))
                .order_by("day"),
            ),
            "asset_type_distribution": list(assets.values("asset_type").annotate(count=Count("id")).order_by("-count")),
            "complexity_distribution": list(
                assets.values("complexity_level").annotate(count=Count("id")).order_by("complexity_level"),
            ),
            "top_assets": list(
                assets.order_by("-download_count")[:10].values("id", "name", "download_count", "quality_score"),
            ),
        }

        return render(
            request,
            "admin/3d_assets/library_analytics.html",
            {"library": library, "analytics": analytics},
        )

    except Exception:
        logger.exception("Failed to get library analytics")
        return JsonResponse({"error": "Failed to load analytics"}, status=500)


# =============================================================================
# ASSET VIEWS
# =============================================================================


@method_decorator(login_required, name="dispatch")
class AssetDetailView(DetailView):
    """Display detailed view of a single 3D asset with comprehensive information.

    Provides complete asset information including processing status, usage analytics,
    performance profiles, and project mappings with optimization recommendations.
    """

    model = ThreeDAsset
    template_name: str = "admin/3d_assets/asset_detail.html"
    context_object_name: str = "asset"

    def get_queryset(self) -> QuerySet[ThreeDAsset]:
        """Get optimized queryset with related data for asset detail view.

        Returns
        -------
            QuerySet of ThreeDAsset objects with prefetched related data
            for optimal performance in detail view rendering.

        """
        return ThreeDAsset.objects.select_related("library", "created_by", "reviewed_by").prefetch_related(
            "lod_variants",
            "textures",
            "processing_jobs",
            "usage_records",
        )

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add comprehensive asset data to template context.

        Args:
        ----
            **kwargs: Additional keyword arguments from parent class.

        Returns:
        -------
            Context dictionary with processing recommendations, usage analytics,
            performance profiles, and project mapping information.

        """
        context = super().get_context_data(**kwargs)
        asset = self.object

        # Get processing recommendations
        context["recommendations"] = get_processing_recommendations(asset)

        # Get recent usage analytics
        recent_usage = AssetUsageTracking.objects.filter(asset=asset).order_by("-started_at")[:10]

        context.update(
            {
                "recent_usage": recent_usage,
                "performance_profiles": asset.performance_profiles.all()[:5],
                "project_mappings": asset.project_mappings.all()[:10],
            },
        )

        return context


@require_http_methods(["POST"])
@login_required
@secure_file_upload(
    max_size=200 * 1024 * 1024,  # 200MB for 3D assets
    allowed_extensions=[".gltf", ".glb", ".obj", ".fbx", ".dae", ".3ds", ".blend"],
    max_uploads=3,  # 3 uploads per hour for 3D assets
    time_window=3600,
)
def upload_asset(request: HttpRequest) -> JsonResponse:
    """Handle 3D asset upload with processing queue.

    Args:
    ----
        request: HTTP request containing asset upload data including name, library_id,
                asset_file, and optional processing parameters.

    Returns:
    -------
        JSON response with success status, asset_id on success,
        or error message on failure.

    """
    try:
        # Validate required fields
        name = request.POST.get("name", "").strip()
        library_id = request.POST.get("library_id")

        if not name or not library_id:
            return JsonResponse({"success": False, "error": "Name and library are required"})

        # Get library
        library = get_object_or_404(ThreeDAssetLibrary, id=library_id)

        # Check permissions
        if not request.user.is_superuser and library.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Permission denied"})

        # Handle file upload
        asset_file = request.FILES.get("asset_file")
        if not asset_file:
            return JsonResponse({"success": False, "error": "Asset file is required"})

        # Validate file format
        allowed_extensions = ["gltf", "glb", "obj", "fbx", "dae"]
        file_extension = asset_file.name.split(".")[-1].lower()
        if file_extension not in allowed_extensions:
            return JsonResponse(
                {
                    "success": False,
                    "error": f"Unsupported file format. Allowed: {', '.join(allowed_extensions)}",
                },
            )

        # Create asset
        asset = ThreeDAsset.objects.create(
            name=name,
            description=request.POST.get("description", ""),
            asset_type=request.POST.get("asset_type", "custom"),
            category=request.POST.get("category", ""),
            library=library,
            original_file=asset_file,
            file_format=file_extension,
            file_size_bytes=asset_file.size,
            created_by=request.user,
            tags=(request.POST.get("tags", "").split(",") if request.POST.get("tags") else []),
            utility_types=(
                request.POST.get("utility_types", "").split(",") if request.POST.get("utility_types") else []
            ),
        )

        # Queue processing job
        processing_options = {
            "generate_lods": request.POST.get("generate_lods") == "true",
            "optimize_textures": request.POST.get("optimize_textures") == "true",
            "validate_model": True,
            "generate_thumbnail": True,
            "calculate_quality_metrics": True,
        }

        AssetProcessingJob.objects.create(
            asset=asset,
            job_type="optimization",
            parameters=processing_options,
            created_by=request.user,
        )

        return JsonResponse(
            {
                "success": True,
                "asset_id": str(asset.id),
                "message": "Asset uploaded successfully. Processing will begin shortly.",
            },
        )

    except Exception:
        logger.exception("Asset upload failed")
        return JsonResponse({"success": False, "error": "Upload failed. Please try again."})


@require_http_methods(["POST"])
@login_required
@file_upload_security_headers
def process_asset(request: HttpRequest, asset_id: str) -> JsonResponse:
    """Start asset processing with specified options.

    Args:
    ----
        request: HTTP request containing processing options in JSON body.
        asset_id: UUID string identifying the asset to process.

    Returns:
    -------
        JSON response with processing results on success,
        or error message on failure.

    """
    try:
        asset = get_object_or_404(ThreeDAsset, id=asset_id)

        # Check permissions
        if not request.user.is_superuser and asset.library.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Permission denied"})

        # Parse processing options
        options = json.loads(request.body) if request.body else {}

        # Start processing
        result = asset_processor.process_asset_sync(str(asset.id), options)

        return JsonResponse(
            {
                "success": True,
                "message": "Asset processing completed",
                "results": result,
            },
        )

    except Exception as e:
        logger.exception("Asset processing failed")
        return JsonResponse({"success": False, "error": str(e)})


@require_http_methods(["POST"])
@login_required
def generate_asset_lods(request: HttpRequest, asset_id: str) -> JsonResponse:
    """Generate Level of Detail variants for an asset.

    Args:
    ----
        request: HTTP request object.
        asset_id: UUID string identifying the asset for LOD generation.

    Returns:
    -------
        JSON response with job_id and status message on success,
        or error message on failure.

    """
    try:
        asset = get_object_or_404(ThreeDAsset, id=asset_id)

        # Check permissions
        if not request.user.is_superuser and asset.library.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Permission denied"})

        # Create LOD generation job
        job = AssetProcessingJob.objects.create(
            asset=asset,
            job_type="lod_generation",
            parameters={"lod_levels": [1, 2, 3, 4]},
            created_by=request.user,
        )

        return JsonResponse(
            {
                "success": True,
                "job_id": str(job.id),
                "message": "LOD generation started",
            },
        )

    except Exception as e:
        logger.exception("LOD generation failed")
        return JsonResponse({"success": False, "error": str(e)})


@require_http_methods(["POST"])
@login_required
def duplicate_asset(request: HttpRequest, asset_id: str) -> JsonResponse:
    """Create a duplicate of an existing asset.

    Args:
    ----
        request: HTTP request object.
        asset_id: UUID string identifying the asset to duplicate.

    Returns:
    -------
        JSON response with new_asset_id and success message on success,
        or error message on failure.

    """
    try:
        original_asset = get_object_or_404(ThreeDAsset, id=asset_id)

        # Check permissions
        if not request.user.is_superuser and original_asset.library.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Permission denied"})

        # Create duplicate
        duplicate = ThreeDAsset.objects.create(
            name=f"{original_asset.name} (Copy)",
            description=original_asset.description,
            asset_type=original_asset.asset_type,
            category=original_asset.category,
            library=original_asset.library,
            file_format=original_asset.file_format,
            tags=original_asset.tags.copy(),
            utility_types=original_asset.utility_types.copy(),
            created_by=request.user,
        )

        # Copy file
        if original_asset.original_file:
            duplicate.original_file.save(
                original_asset.original_file.name,
                original_asset.original_file,
                save=True,
            )

        return JsonResponse(
            {
                "success": True,
                "new_asset_id": str(duplicate.id),
                "message": "Asset duplicated successfully",
            },
        )

    except Exception as e:
        logger.exception("Asset duplication failed")
        return JsonResponse({"success": False, "error": str(e)})


@require_http_methods(["DELETE"])
@login_required
def delete_asset(request: HttpRequest, asset_id: str) -> JsonResponse:
    """Delete an asset and all associated files.

    Args:
    ----
        request: HTTP request object.
        asset_id: UUID string identifying the asset to delete.

    Returns:
    -------
        JSON response with success message on completion,
        or error message on failure.

    """
    try:
        asset = get_object_or_404(ThreeDAsset, id=asset_id)

        # Check permissions
        if not request.user.is_superuser and asset.library.organization != request.user.organization:
            return JsonResponse({"success": False, "error": "Permission denied"})

        # Delete associated files
        if asset.original_file:
            asset.original_file.delete()
        if asset.optimized_file:
            asset.optimized_file.delete()
        if asset.thumbnail:
            asset.thumbnail.delete()

        # Delete LOD files
        for lod in asset.lod_variants.all():
            if lod.file:
                lod.file.delete()

        # Delete texture files
        for texture in asset.textures.all():
            if texture.original_file:
                texture.original_file.delete()
            if texture.optimized_file:
                texture.optimized_file.delete()

        # Delete the asset
        asset.delete()

        return JsonResponse({"success": True, "message": "Asset deleted successfully"})

    except Exception as e:
        logger.exception("Asset deletion failed")
        return JsonResponse({"success": False, "error": str(e)})


@require_http_methods(["GET"])
@login_required
def asset_status(request: HttpRequest, asset_id: str) -> JsonResponse:
    """Get real-time status of an asset including processing jobs.

    Args:
    ----
        request: HTTP request object.
        asset_id: UUID string identifying the asset.

    Returns:
    -------
        JSON response with asset status, processing jobs, and metrics,
        or error message on failure.

    """
    try:
        asset = get_object_or_404(ThreeDAsset, id=asset_id)

        # Get processing jobs
        jobs = asset.processing_jobs.order_by("-created_at")[:10]

        status_data = {
            "asset_id": str(asset.id),
            "status": asset.status,
            "quality_score": asset.quality_score,
            "processing_jobs": [
                {
                    "id": str(job.id),
                    "type": job.job_type,
                    "status": job.status,
                    "status_display": job.get_status_display(),
                    "progress": job.progress_percentage,
                    "created_at": job.created_at.isoformat(),
                    "error": job.error_message,
                }
                for job in jobs
            ],
            "lod_count": asset.lod_variants.count(),
            "texture_count": asset.textures.count(),
        }

        return JsonResponse(status_data)

    except Exception:
        logger.exception("Failed to get asset status")
        return JsonResponse({"error": "Failed to get status"}, status=500)


# =============================================================================
# PROJECT INTEGRATION VIEWS
# =============================================================================


@require_http_methods(["POST"])
@login_required
@file_upload_security_headers
def map_asset_to_project(request: HttpRequest) -> JsonResponse:
    """Map a 3D asset to a specific location in a project.

    Args:
    ----
        request: HTTP request containing JSON data with asset_id, project_id,
                location geometry, mapping_type, and optional transformation parameters.

    Returns:
    -------
        JSON response with mapping_id and success message on completion,
        or error message on failure.

    """
    try:
        data = json.loads(request.body)

        # Validate required fields
        required_fields = ["asset_id", "project_id", "location", "mapping_type"]
        for field in required_fields:
            if field not in data:
                return JsonResponse({"success": False, "error": f"Missing required field: {field}"})

        # Get objects
        asset = get_object_or_404(ThreeDAsset, id=data["asset_id"])
        project = get_object_or_404("apps.CLEAR.Project", id=data["project_id"])

        # Check permissions
        if not request.user.is_superuser:
            # Check project access
            if not project.team_members.filter(id=request.user.id).exists():
                return JsonResponse({"success": False, "error": "Permission denied"})

        # Create mapping
        mapping = ProjectAssetMapping.objects.create(
            project=project,
            asset=asset,
            mapping_type=data["mapping_type"],
            location=data["location"],  # Point geometry
            elevation=data.get("elevation", 0.0),
            rotation=data.get("rotation", {"x": 0, "y": 0, "z": 0}),
            scale=data.get("scale", {"x": 1, "y": 1, "z": 1}),
            utility=data.get("utility_id") and get_object_or_404("infrastructure.Utility", id=data["utility_id"]),
            created_by=request.user,
            metadata=data.get("metadata", {}),
        )

        # Track usage
        AssetUsageTracking.objects.create(
            asset=asset,
            project=project,
            user=request.user,
            context="project_visualization",
            location=data["location"],
        )

        # Increment asset usage counter
        asset.increment_usage()

        return JsonResponse(
            {
                "success": True,
                "mapping_id": str(mapping.id),
                "message": "Asset mapped successfully",
            },
        )

    except Exception as e:
        logger.exception("Asset mapping failed")
        return JsonResponse({"success": False, "error": str(e)})


@require_http_methods(["GET"])
@login_required
def get_project_assets(request: HttpRequest, project_id: str) -> JsonResponse:
    """Get all assets mapped to a specific project.

    Args:
    ----
        request: HTTP request object.
        project_id: UUID string identifying the project.

    Returns:
    -------
        JSON response with array of mapped assets and their properties,
        or error message on failure.

    """
    try:
        project = get_object_or_404("apps.CLEAR.Project", id=project_id)

        # Check permissions
        if not request.user.is_superuser and not project.team_members.filter(id=request.user.id).exists():
            return JsonResponse({"success": False, "error": "Permission denied"})

        # Get asset mappings
        mappings = (
            ProjectAssetMapping.objects.filter(project=project)
            .select_related("asset", "asset__library")
            .order_by("-created_at")
        )

        assets_data = []
        for mapping in mappings:
            asset = mapping.asset

            # Get LOD variants
            lod_variants = [
                {
                    "level": lod.lod_level,
                    "url": lod.file.url,
                    "distance": lod.min_distance,
                    "triangles": lod.triangle_count,
                    "size_mb": lod.file_size_bytes / (1024 * 1024),
                }
                for lod in asset.lod_variants.all()
            ]

            assets_data.append(
                {
                    "mapping_id": str(mapping.id),
                    "asset_id": str(asset.id),
                    "name": asset.name,
                    "type": asset.asset_type,
                    "url": (asset.optimized_file.url if asset.optimized_file else asset.original_file.url),
                    "thumbnail": asset.thumbnail.url if asset.thumbnail else None,
                    "transform": mapping.get_transform_matrix(),
                    "mapping_type": mapping.mapping_type,
                    "visibility": mapping.visibility,
                    "opacity": mapping.opacity,
                    "tint_color": mapping.tint_color,
                    "lod_variants": lod_variants,
                    "metadata": mapping.metadata,
                    "display_properties": mapping.display_properties,
                },
            )

        return JsonResponse({"success": True, "assets": assets_data, "count": len(assets_data)})

    except Exception as e:
        logger.exception("Failed to get project assets")
        return JsonResponse({"success": False, "error": str(e)})


# =============================================================================
# ANALYTICS AND REPORTING VIEWS
# =============================================================================


@require_http_methods(["GET"])
@login_required
def asset_analytics_dashboard(request: HttpRequest) -> HttpResponse:
    """Comprehensive analytics dashboard for 3D asset management.

    Args:
    ----
        request: HTTP request object.

    Returns:
    -------
        Rendered HTML response with analytics dashboard template,
        including statistics, trends, and performance metrics.

    """
    try:
        # Calculate overall statistics
        total_assets = ThreeDAsset.objects.count()
        total_libraries = ThreeDAssetLibrary.objects.count()
        total_downloads = ThreeDAsset.objects.aggregate(total=Sum("download_count"))["total"] or 0

        # Usage trends (last 30 days)
        # from datetime import timedelta (moved to top level)

        from django.utils import timezone

        thirty_days_ago = timezone.now() - timedelta(days=30)
        usage_trend = (
            AssetUsageTracking.objects.filter(started_at__gte=thirty_days_ago)
            .extra(select={"day": "date(started_at)"})
            .values("day")
            .annotate(count=Count("id"))
            .order_by("day")
        )

        # Top performing assets
        top_assets = ThreeDAsset.objects.order_by("-download_count")[:10]

        # Quality distribution
        quality_distribution = (
            ThreeDAsset.objects.exclude(quality_score__isnull=True)
            .extra(
                select={
                    "quality_range": """
                            CASE
                                WHEN quality_score >= 90 THEN 'Excellent (90-100)'
                                WHEN quality_score >= 80 THEN 'Good (80-89)'
                                WHEN quality_score >= 70 THEN 'Fair (70-79)'
                                WHEN quality_score >= 60 THEN 'Poor (60-69)'
                                ELSE 'Very Poor (0-59)'
                            END
                        """,
                },
            )
            .values("quality_range")
            .annotate(count=Count("id"))
        )

        # Asset type distribution
        type_distribution = ThreeDAsset.objects.values("asset_type").annotate(count=Count("id")).order_by("-count")

        # Processing job statistics
        job_stats = AssetProcessingJob.objects.values("status").annotate(count=Count("id"))

        context = {
            "total_assets": total_assets,
            "total_libraries": total_libraries,
            "total_downloads": total_downloads,
            "usage_trend": list(usage_trend),
            "top_assets": top_assets,
            "quality_distribution": list(quality_distribution),
            "type_distribution": list(type_distribution),
            "job_stats": list(job_stats),
        }

        return render(request, "admin/3d_assets/analytics_dashboard.html", context)

    except Exception:
        logger.exception("Analytics dashboard failed")
        return render(
            request,
            "admin/3d_assets/analytics_dashboard.html",
            {"error": "Failed to load analytics data"},
        )


@require_http_methods(["GET"])
@login_required
def performance_report(request: HttpRequest) -> HttpResponse:
    """Generate performance report for 3D assets.

    Args:
    ----
        request: HTTP request object.

    Returns:
    -------
        Rendered HTML response with performance report template,
        including device-specific performance metrics and analysis.

    """
    try:
        # Get performance metrics
        profiles = AssetPerformanceProfile.objects.select_related("asset")

        # Group by device category
        performance_by_device = {}
        for profile in profiles:
            device = profile.device_category
            if device not in performance_by_device:
                performance_by_device[device] = {"count": 0, "avg_fps": 0, "assets": []}

            performance_by_device[device]["count"] += 1
            performance_by_device[device]["avg_fps"] += profile.average_fps
            performance_by_device[device]["assets"].append(
                {
                    "name": profile.asset.name,
                    "fps": profile.average_fps,
                    "grade": profile.get_performance_grade(),
                    "vr_ready": profile.is_vr_ready(),
                },
            )

        # Calculate averages
        for device_data in performance_by_device.values():
            if device_data["count"] > 0:
                device_data["avg_fps"] /= device_data["count"]

        context = {
            "performance_by_device": performance_by_device,
            "total_profiles": profiles.count(),
        }

        return render(request, "admin/3d_assets/performance_report.html", context)

    except Exception:
        logger.exception("Performance report failed")
        return JsonResponse({"error": "Failed to generate report"}, status=500)
