"""Validate URLs Management Command.

Django management command to validate URL patterns against design standards.
Part of the URI standardization initiative from Phase 7 analysis.

Features:
- Check naming conventions (kebab-case vs snake_case)
- Validate identifier patterns (UUID vs integer usage)
- Analyze URL depth and complexity
- Validate HTMX pattern compliance
- Check parameter naming conventions
- Generate detailed compliance reports

Usage:
    python manage.py validate_urls [options]

Examples
--------
    python manage.py validate_urls --app=infrastructure
    python manage.py validate_urls --report --fix
    python manage.py validate_urls --max-depth=3 --fix

"""

from __future__ import annotations

import re
from collections import defaultdict
from typing import TYPE_CHECKING, Any

from django.core.management.base import BaseCommand, CommandError
from django.urls import URLPattern, URLResolver, get_resolver

if TYPE_CHECKING:
    from argparse import ArgumentParser


class Command(BaseCommand):
    """Management command for validating URL patterns against URI design standards."""

    help = "Validate URL patterns against URI design standards"

    def add_arguments(self, parser: ArgumentParser) -> None:
        """Add command line arguments.

        Args:
        ----
            parser: Argument parser instance

        """
        parser.add_argument(
            "--app",
            type=str,
            help="Check specific app only",
        )
        parser.add_argument(
            "--fix",
            action="store_true",
            help="Generate suggested fixes (read-only)",
        )
        parser.add_argument(
            "--report",
            action="store_true",
            help="Generate detailed compliance report",
        )
        parser.add_argument(
            "--max-depth",
            type=int,
            default=4,
            help="Maximum allowed URL depth (default: 4)",
        )

    def handle(self, *args: Any, **options: dict[str, Any]) -> None:
        """Execute the URL validation command.

        Args:
        ----
            *args: Positional arguments
            **options: Command options

        Raises:
        ------
            CommandError: If URL analysis fails

        """
        self.app_filter: str | None = options.get("app")
        self.show_fixes: bool = options.get("fix", False)
        self.generate_report: bool = options.get("report", False)
        self.max_depth: int = options.get("max_depth", 4)

        self.violations: dict[str, list[dict[str, Any]]] = defaultdict(list)
        self.pattern_stats: dict[str, int] = {
            "total_patterns": 0,
            "uuid_patterns": 0,
            "int_patterns": 0,
            "str_patterns": 0,
            "htmx_patterns": 0,
            "api_patterns": 0,
            "compliant_patterns": 0,
        }

        try:
            resolver = get_resolver()
            self._analyze_resolver(resolver, "")
            self._display_results()

            if self.generate_report:
                self._generate_report()

        except Exception as e:
            raise CommandError(f"Error analyzing URLs: {e}") from e

    def _analyze_resolver(self, resolver: Any, prefix: str = "") -> None:
        """Recursively analyze URL patterns.

        Args:
        ----
            resolver: Django URL resolver instance
            prefix: URL prefix for nested patterns

        """
        for pattern in resolver.url_patterns:
            if isinstance(pattern, URLResolver):
                # Recursively analyze included URL patterns
                new_prefix = prefix + str(pattern.pattern)
                self._analyze_resolver(pattern, new_prefix)
            elif isinstance(pattern, URLPattern):
                # Analyze individual URL pattern
                full_pattern = prefix + str(pattern.pattern)
                self._analyze_pattern(pattern, full_pattern)

    def _analyze_pattern(self, pattern: URLPattern, full_pattern: str) -> None:
        """Analyze a single URL pattern for compliance.

        Args:
        ----
            pattern: URL pattern to analyze
            full_pattern: Full pattern string including prefix

        """
        self.pattern_stats["total_patterns"] += 1

        # Skip if filtering by app
        if self.app_filter and not self._pattern_belongs_to_app(pattern, self.app_filter):
            return

        pattern_str = str(pattern.pattern)
        pattern_name = getattr(pattern, "name", "unnamed")

        violations = []

        # Check naming conventions
        violations.extend(self._check_naming_conventions(pattern_str, pattern_name))

        # Check identifier patterns
        violations.extend(self._check_identifier_patterns(pattern_str, pattern_name))

        # Check URL depth
        violations.extend(self._check_url_depth(pattern_str, pattern_name))

        # Check HTMX patterns
        violations.extend(self._check_htmx_patterns(pattern_str, pattern_name))

        # Check parameter naming
        violations.extend(self._check_parameter_naming(pattern_str, pattern_name))

        # Track statistics
        self._update_stats(pattern_str)

        if not violations:
            self.pattern_stats["compliant_patterns"] += 1
        else:
            for violation in violations:
                self.violations[violation["category"]].append(
                    {
                        "pattern": pattern_str,
                        "name": pattern_name,
                        "full_pattern": full_pattern,
                        "message": violation["message"],
                        "suggestion": violation.get("suggestion", ""),
                        "severity": violation.get("severity", "warning"),
                    },
                )

    def _check_naming_conventions(self, pattern_str: str, pattern_name: str) -> list[dict[str, Any]]:
        """Check URL naming convention compliance.

        Args:
        ----
            pattern_str: URL pattern string to check
            pattern_name: Name of the URL pattern

        Returns:
        -------
            List of violation dictionaries with category, message, suggestion, and severity

        """
        violations = []

        # Check for underscores in URL paths (should use hyphens)
        if "_" in pattern_str and not pattern_str.startswith("htmx/"):
            violations.append(
                {
                    "category": "naming_convention",
                    "message": "Use kebab-case (hyphens) instead of underscores in URL path",
                    "suggestion": f"Replace underscores with hyphens: {pattern_str.replace('_', '-')}",
                    "severity": "warning",
                },
            )

        # Check for inconsistent pluralization
        if re.search(r"/[a-z]+/$", pattern_str):
            # Simple check for common singular words that should be plural
            singular_words = ["project", "user", "task", "document", "message"]
            for word in singular_words:
                if f"/{word}/" in pattern_str and f"/{word}s/" not in pattern_str:
                    violations.append(
                        {
                            "category": "naming_convention",
                            "message": "Use plural form for collection endpoints",
                            "suggestion": f"Use /{word}s/ instead of /{word}/",
                            "severity": "info",
                        },
                    )

        # Check HTMX endpoint naming
        if "htmx/" in pattern_str and pattern_name and not pattern_name.endswith("_htmx"):
            violations.append(
                {
                    "category": "naming_convention",
                    "message": 'HTMX endpoint names should end with "_htmx"',
                    "suggestion": f"Rename to {pattern_name}_htmx",
                    "severity": "warning",
                },
            )

        return violations

    def _check_identifier_patterns(self, pattern_str: str, pattern_name: str) -> list[dict[str, Any]]:
        """Check identifier pattern compliance.

        Args:
        ----
            pattern_str: URL pattern string to check
            pattern_name: Name of the URL pattern

        Returns:
        -------
            List of violation dictionaries with category, message, suggestion, and severity

        """
        violations = []

        # Check for integer IDs in new patterns (should use UUIDs)
        int_patterns = re.findall(r"<int:([^>]+)>", pattern_str)
        if int_patterns and "legacy" not in pattern_name.lower():
            for param in int_patterns:
                violations.append(
                    {
                        "category": "identifier_pattern",
                        "message": f"Use UUID instead of integer for identifier: {param}",
                        "suggestion": f"Replace <int:{param}> with <uuid:{param}>",
                        "severity": "error",
                    },
                )

        # Check for generic parameter names
        generic_params = re.findall(r"<(?:uuid|int|str):([^>]+)>", pattern_str)
        for param in generic_params:
            if param in ["id", "pk"]:
                violations.append(
                    {
                        "category": "identifier_pattern",
                        "message": f'Use descriptive parameter names instead of generic "{param}"',
                        "suggestion": 'Use resource-specific name like "user_id" or "project_uuid"',
                        "severity": "warning",
                    },
                )

        return violations

    def _check_url_depth(self, pattern_str: str, pattern_name: str) -> list[dict[str, Any]]:
        """Check URL depth compliance.

        Args:
        ----
            pattern_str: URL pattern string to check
            pattern_name: Name of the URL pattern

        Returns:
        -------
            List of violation dictionaries with category, message, suggestion, and severity

        """
        violations = []

        # Count path segments (excluding parameters)
        path_without_params = re.sub(r"<[^>]+>", "PARAM", pattern_str)
        segments = [s for s in path_without_params.split("/") if s and s != "PARAM"]
        depth = len(segments)

        if depth > self.max_depth:
            violations.append(
                {
                    "category": "url_depth",
                    "message": f"URL too deep ({depth} levels, max {self.max_depth})",
                    "suggestion": "Consider flattening hierarchy or using query parameters",
                    "severity": "warning",
                },
            )

        return violations

    def _check_htmx_patterns(self, pattern_str: str, pattern_name: str) -> list[dict[str, Any]]:
        """Check HTMX-specific pattern compliance.

        Args:
        ----
            pattern_str: URL pattern string to check
            pattern_name: Name of the URL pattern

        Returns:
        -------
            List of violation dictionaries with category, message, suggestion, and severity

        """
        violations = []

        if "htmx/" in pattern_str and not pattern_str.startswith("htmx/"):
            violations.append(
                {
                    "category": "htmx_pattern",
                    "message": 'HTMX endpoints should start with "htmx/" prefix',
                    "suggestion": "Move HTMX endpoints to htmx/ namespace",
                    "severity": "error",
                },
            )

        return violations

    def _check_parameter_naming(self, pattern_str: str, pattern_name: str) -> list[dict[str, Any]]:
        """Check URL parameter naming conventions.

        Args:
        ----
            pattern_str: URL pattern string to check
            pattern_name: Name of the URL pattern

        Returns:
        -------
            List of violation dictionaries with category, message, suggestion, and severity

        """
        violations = []

        # Extract all parameters
        params = re.findall(r"<([^:]+):([^>]+)>", pattern_str)

        for _param_type, param_name in params:
            # Check parameter naming convention (should be snake_case)
            if "-" in param_name:
                violations.append(
                    {
                        "category": "parameter_naming",
                        "message": f"Use snake_case for parameter names: {param_name}",
                        "suggestion": f"Replace with {param_name.replace('-', '_')}",
                        "severity": "info",
                    },
                )

            # Check for descriptive names
            if len(param_name) < 3:
                violations.append(
                    {
                        "category": "parameter_naming",
                        "message": f"Parameter name too short: {param_name}",
                        "suggestion": "Use descriptive parameter names",
                        "severity": "info",
                    },
                )

        return violations

    def _update_stats(self, pattern_str: str) -> None:
        """Update pattern statistics.

        Args:
        ----
            pattern_str: URL pattern string to analyze for statistics

        """
        if "uuid:" in pattern_str:
            self.pattern_stats["uuid_patterns"] += 1
        if "int:" in pattern_str:
            self.pattern_stats["int_patterns"] += 1
        if "str:" in pattern_str:
            self.pattern_stats["str_patterns"] += 1
        if "htmx/" in pattern_str:
            self.pattern_stats["htmx_patterns"] += 1
        if "api/" in pattern_str:
            self.pattern_stats["api_patterns"] += 1

    def _pattern_belongs_to_app(self, pattern: URLPattern, app_name: str) -> bool:
        """Check if pattern belongs to specified app.

        Args:
        ----
            pattern: URL pattern to check
            app_name: Name of the app to filter by

        Returns:
        -------
            True if pattern belongs to the specified app, False otherwise

        """
        # Simple check based on pattern callback module
        if hasattr(pattern, "callback") and pattern.callback:
            module_name = pattern.callback.__module__
            return app_name in module_name
        return False

    def _display_results(self) -> None:
        """Display validation results to stdout.

        Displays comprehensive statistics and violations found during URL analysis.
        """
        total_violations = sum(len(violations) for violations in self.violations.values())

        # Display statistics
        self.stdout.write(self.style.HTTP_INFO("\n=== URL Pattern Analysis ==="))
        self.stdout.write(f"Total patterns analyzed: {self.pattern_stats['total_patterns']}")
        self.stdout.write(f"Compliant patterns: {self.pattern_stats['compliant_patterns']}")
        self.stdout.write(f"Total violations: {total_violations}")

        if self.pattern_stats["total_patterns"] > 0:
            compliance_rate = (self.pattern_stats["compliant_patterns"] / self.pattern_stats["total_patterns"]) * 100
            self.stdout.write(f"Compliance rate: {compliance_rate:.1f}%")

        # Display pattern type breakdown
        self.stdout.write("\n=== Pattern Type Breakdown ===")
        self.stdout.write(f"UUID patterns: {self.pattern_stats['uuid_patterns']}")
        self.stdout.write(f"Integer patterns: {self.pattern_stats['int_patterns']}")
        self.stdout.write(f"String patterns: {self.pattern_stats['str_patterns']}")
        self.stdout.write(f"HTMX patterns: {self.pattern_stats['htmx_patterns']}")
        self.stdout.write(f"API patterns: {self.pattern_stats['api_patterns']}")

        # Display violations by category
        if total_violations > 0:
            self.stdout.write(self.style.ERROR(f"\n=== Found {total_violations} violations ==="))

            for category, violations in self.violations.items():
                if violations:
                    self.stdout.write(
                        f"\n{category.replace('_', ' ').title()} ({len(violations)} issues):",
                    )

                    for violation in violations:
                        severity_style = (
                            self.style.ERROR
                            if violation["severity"] == "error"
                            else (self.style.WARNING if violation["severity"] == "warning" else self.style.HTTP_INFO)
                        )

                        self.stdout.write(
                            f"  {severity_style(violation['severity'].upper())}: {violation['pattern']}",
                        )
                        self.stdout.write(f"    {violation['message']}")

                        if self.show_fixes and violation.get("suggestion"):
                            self.stdout.write(f"    Suggestion: {violation['suggestion']}")

                        self.stdout.write("")
        else:
            self.stdout.write(self.style.SUCCESS("\n✓ All URL patterns follow design standards!"))

    def _generate_report(self) -> None:
        """Generate detailed compliance report.

        Creates a markdown file with comprehensive URL pattern analysis results,
        including statistics, violations by category, and suggested fixes.
        """
        report_file = "url_compliance_report.md"

        with open(report_file, "w") as f:
            f.write("# URL Pattern Compliance Report\n\n")
            f.write("Generated by: python manage.py validate_urls --report\n\n")

            # Statistics section
            f.write("## Summary\n\n")
            f.write(f"- Total patterns analyzed: {self.pattern_stats['total_patterns']}\n")
            f.write(f"- Compliant patterns: {self.pattern_stats['compliant_patterns']}\n")
            f.write(f"- Total violations: {sum(len(v) for v in self.violations.values())}\n")

            if self.pattern_stats["total_patterns"] > 0:
                compliance_rate = (
                    self.pattern_stats["compliant_patterns"] / self.pattern_stats["total_patterns"]
                ) * 100
                f.write(f"- Compliance rate: {compliance_rate:.1f}%\n")

            # Pattern breakdown
            f.write("\n## Pattern Type Breakdown\n\n")
            f.write(f"- UUID patterns: {self.pattern_stats['uuid_patterns']}\n")
            f.write(f"- Integer patterns: {self.pattern_stats['int_patterns']}\n")
            f.write(f"- String patterns: {self.pattern_stats['str_patterns']}\n")
            f.write(f"- HTMX patterns: {self.pattern_stats['htmx_patterns']}\n")
            f.write(f"- API patterns: {self.pattern_stats['api_patterns']}\n")

            # Violations section
            f.write("\n## Violations by Category\n\n")
            for category, violations in self.violations.items():
                if violations:
                    f.write(f"### {category.replace('_', ' ').title()}\n\n")
                    for violation in violations:
                        f.write(f"**{violation['severity'].upper()}**: `{violation['pattern']}`\n")
                        f.write(f"- {violation['message']}\n")
                        if violation.get("suggestion"):
                            f.write(f"- Suggestion: {violation['suggestion']}\n")
                        f.write("\n")

        self.stdout.write(f"\nDetailed report generated: {report_file}")
