"""
Field Mapping Models for Infrastructure App

This module provides models for mapping fields between data sources
and target models during import operations.
"""

import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _

from apps.common.models import TimestampedModel
from apps.infrastructure.models_import_template import ImportTemplate


class FieldMapping(TimestampedModel):
    """
    Maps fields from source data to target model fields.

    Defines how data from import files should be mapped to specific
    fields in the target Django model, including validation and
    transformation rules.
    """

    FIELD_TYPES = [
        ("text", _("Text")),
        ("number", _("Number")),
        ("decimal", _("Decimal")),
        ("boolean", _("Boolean")),
        ("date", _("Date")),
        ("datetime", _("Date/Time")),
        ("email", _("Email")),
        ("url", _("URL")),
        ("json", _("JSON")),
        ("geometry", _("Geometry")),
    ]

    mapping_id = models.UUIDField(_("mapping ID"), default=uuid.uuid4, editable=False, unique=True)

    template = models.ForeignKey(
        ImportTemplate,
        on_delete=models.CASCADE,
        related_name="template_field_mappings",
        verbose_name=_("import template"),
    )

    source_field = models.CharField(_("source field"), max_length=255, help_text=_("Field name in the source data"))

    target_field = models.CharField(_("target field"), max_length=255, help_text=_("Field name in the target model"))

    field_type = models.CharField(_("field type"), max_length=20, choices=FIELD_TYPES, default="text")

    is_required = models.BooleanField(
        _("is required"), default=False, help_text=_("Whether this field is required for import")
    )

    default_value = models.TextField(
        _("default value"), blank=True, help_text=_("Default value if source field is empty")
    )

    validation_rules = models.JSONField(
        _("validation rules"), default=dict, blank=True, help_text=_("JSON object containing validation rules")
    )

    transformation_rules = models.JSONField(
        _("transformation rules"), default=dict, blank=True, help_text=_("JSON object containing transformation rules")
    )

    class Meta:
        app_label = "infrastructure"
        verbose_name = _("Field Mapping")
        verbose_name_plural = _("Field Mappings")
        ordering = ["template", "source_field"]
        unique_together = ["template", "source_field"]
        indexes = [
            models.Index(fields=["template", "source_field"]),
            models.Index(fields=["target_field"]),
        ]

    def __str__(self):
        return f"{self.source_field} → {self.target_field}"

    def clean(self):
        """Validate field mapping configuration."""
        super().clean()

        # Validate validation rules is a dict
        if self.validation_rules and not isinstance(self.validation_rules, dict):
            from django.core.exceptions import ValidationError

            raise ValidationError({"validation_rules": _("Validation rules must be a valid JSON object.")})

        # Validate transformation rules is a dict
        if self.transformation_rules and not isinstance(self.transformation_rules, dict):
            from django.core.exceptions import ValidationError

            raise ValidationError({"transformation_rules": _("Transformation rules must be a valid JSON object.")})


class DataTransformation(TimestampedModel):
    """
    Defines data transformation operations for import processing.

    Provides configurable transformation logic that can be applied
    to data during import operations, supporting complex data processing
    and field manipulation.
    """

    TRANSFORMATION_TYPES = [
        ("format", _("Format Transformation")),
        ("calculate", _("Calculated Field")),
        ("lookup", _("Lookup Transformation")),
        ("combine", _("Combine Fields")),
        ("split", _("Split Field")),
        ("conditional", _("Conditional Logic")),
        ("custom", _("Custom Function")),
    ]

    transformation_id = models.UUIDField(_("transformation ID"), default=uuid.uuid4, editable=False, unique=True)

    template = models.ForeignKey(
        ImportTemplate,
        on_delete=models.CASCADE,
        related_name="template_data_transformations",
        verbose_name=_("import template"),
    )

    name = models.CharField(
        _("transformation name"), max_length=255, help_text=_("Descriptive name for this transformation")
    )

    description = models.TextField(
        _("description"), blank=True, help_text=_("Optional description of what this transformation does")
    )

    transformation_type = models.CharField(_("transformation type"), max_length=20, choices=TRANSFORMATION_TYPES)

    source_fields = models.JSONField(
        _("source fields"), default=list, help_text=_("List of source field names used in this transformation")
    )

    target_field = models.CharField(
        _("target field"), max_length=255, help_text=_("Target field name for the transformation result")
    )

    transformation_logic = models.JSONField(
        _("transformation logic"),
        default=dict,
        help_text=_("JSON object containing the transformation logic and parameters"),
    )

    is_active = models.BooleanField(_("is active"), default=True, help_text=_("Whether this transformation is active"))

    execution_order = models.PositiveIntegerField(
        _("execution order"),
        default=0,
        help_text=_("Order in which transformations are executed (lower numbers first)"),
    )

    class Meta:
        app_label = "infrastructure"
        verbose_name = _("Data Transformation")
        verbose_name_plural = _("Data Transformations")
        ordering = ["template", "execution_order", "name"]
        indexes = [
            models.Index(fields=["template", "execution_order"]),
            models.Index(fields=["transformation_type"]),
            models.Index(fields=["is_active"]),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_transformation_type_display()})"

    def clean(self):
        """Validate transformation configuration."""
        super().clean()

        # Validate source_fields is a list
        if not isinstance(self.source_fields, list):
            from django.core.exceptions import ValidationError

            raise ValidationError({"source_fields": _("Source fields must be a list.")})

        # Validate transformation_logic is a dict
        if not isinstance(self.transformation_logic, dict):
            from django.core.exceptions import ValidationError

            raise ValidationError({"transformation_logic": _("Transformation logic must be a valid JSON object.")})
