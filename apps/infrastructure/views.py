"""GIS and Mapping Views for CLEAR Application.

Provides comprehensive infrastructure management views including:
- Utility management and visualization
- Spatial conflict detection and resolution
- Interactive mapping interfaces (2D and 3D)
- GIS professional tools and dashboards
- Collaborative spatial editing features
"""

import json
import logging
from decimal import Decimal
from typing import Any, ClassVar

from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, QuerySet
from django.forms import ModelForm
from django.http import HttpRequest, HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, DetailView, ListView, TemplateView

from apps.common.mixins import RoleRequiredMixin
from apps.infrastructure.models import Conflict, Utility
from apps.projects.models import Project

logger = logging.getLogger(__name__)


class OpenLayersDemoView(LoginRequiredMixin, TemplateView):
    """OpenLayers integration demo view.

    Demonstrates OpenLayers 9.1.0 integration with responsive design
    and Bootstrap styling.
    """

    template_name = "infrastructure/openlayers_demo.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["title"] = "OpenLayers Integration Demo"
        return context


class ConflictsListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """List all conflicts across projects.

    Displays conflicts with filtering and organization isolation.
    """

    model = Conflict
    template_name = "infrastructure/conflicts_list.html"
    context_object_name = "conflicts"
    paginate_by = 25
    required_roles = ["stakeholder"]

    def get_queryset(self):
        """Get conflicts for user's organization."""
        return (
            Conflict.objects.filter(project__organization=self.request.user.organization)
            .select_related("project")
            .order_by("-created_at")
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Conflicts"
        return context


class UtilityListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """List utilities for a specific project.

    Displays all utilities associated with a project, ordered by type and name.
    Provides project context for breadcrumb navigation and filtering.

    Args:
    ----
        request: HTTP request object
        project_id: Project identifier from URL parameters

    Returns:
    -------
        Rendered utility list template with project context

    """

    required_roles = ["stakeholder"]

    model = Utility
    template_name: str = "infrastructure/utilities/list.html"
    context_object_name: str = "utilities"
    project: Project

    def get_queryset(self) -> QuerySet[Utility]:
        """Get utilities for the specified project, ordered by type and name.

        Returns
        -------
            QuerySet of utilities filtered by project and ordered by type/name

        """
        self.project = get_object_or_404(Project, pk=self.kwargs["project_id"])
        return self.project.utilities.all().order_by("type", "name")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add project context to template.

        Args:
        ----
            **kwargs: Additional keyword arguments

        Returns:
        -------
            Template context dictionary with project data

        """
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        return context


class UtilityDetailView(LoginRequiredMixin, RoleRequiredMixin, DetailView):
    """Detailed utility view with spatial data.

    Displays comprehensive information about a single utility including
    contact details, spatial geometry, and associated conflicts.

    Args:
    ----
        request: HTTP request object
        pk: Utility primary key from URL parameters

    Returns:
    -------
        Rendered utility detail template with utility data

    """

    required_roles = ["stakeholder"]

    model = Utility
    template_name: str = "infrastructure/utilities/detail.html"
    context_object_name: str = "utility"


class UtilityCreateView(LoginRequiredMixin, RoleRequiredMixin, CreateView):
    """Create new utility for a project.

    Handles creation of utility records with automatic project association.
    Includes form validation and success URL generation.

    Args:
    ----
        request: HTTP request object
        project_id: Project identifier from URL parameters

    Returns:
    -------
        Rendered utility creation form or redirect to utility list

    """

    required_roles = ["utility-coordinator"]

    model = Utility
    template_name: str = "infrastructure/utilities/create.html"
    fields: ClassVar[list[str]] = [
        "name",
        "type",
        "contact_name",
        "contact_email",
        "contact_phone",
        "notes",
    ]

    def form_valid(self, form: ModelForm) -> HttpResponse:
        """Associate utility with project before saving.

        Args:
        ----
            form: Validated utility creation form

        Returns:
        -------
            HTTP response redirecting to success URL

        """
        project: Project = get_object_or_404(Project, pk=self.kwargs["project_id"])
        form.instance.project = project
        return super().form_valid(form)

    def get_success_url(self) -> str:
        """Return URL to utility list after successful creation.

        Returns
        -------
            URL string for utility list view

        """
        return reverse_lazy("CLEAR:utility_list", kwargs={"project_id": self.kwargs["project_id"]})


class ConflictListView(LoginRequiredMixin, RoleRequiredMixin, ListView):
    """List conflicts for a project.

    Displays all spatial conflicts detected within a project, ordered by
    creation date with most recent first. Includes related utility data.

    Args:
    ----
        request: HTTP request object
        project_id: Project identifier from URL parameters

    Returns:
    -------
        Rendered conflict list template with project context

    """

    required_roles = ["stakeholder"]

    model = Conflict
    template_name: str = "infrastructure/conflicts/list.html"
    context_object_name: str = "conflicts"
    project: Project

    def get_queryset(self) -> QuerySet[Conflict]:
        """Get conflicts for the project with related utility data.

        Returns
        -------
            QuerySet of conflicts with optimized utility relationships

        """
        self.project = get_object_or_404(Project, pk=self.kwargs["project_id"])
        return self.project.conflicts.select_related("utility", "utility2").order_by("-created_at")

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add project context to template.

        Args:
        ----
            **kwargs: Additional keyword arguments

        Returns:
        -------
            Template context dictionary with project data

        """
        context = super().get_context_data(**kwargs)
        context["project"] = self.project
        return context


class ConflictDetailView(LoginRequiredMixin, RoleRequiredMixin, DetailView):
    """Detailed conflict view with resolution tracking.

    Displays comprehensive information about a spatial conflict including
    involved utilities, spatial geometry, and resolution status/history.

    Args:
    ----
        request: HTTP request object
        pk: Conflict primary key from URL parameters

    Returns:
    -------
        Rendered conflict detail template with conflict data

    """

    required_roles = ["stakeholder"]

    model = Conflict
    template_name: str = "infrastructure/conflicts/detail.html"
    context_object_name: str = "conflict"


class ConflictDetectionView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Run automated conflict detection for a project.

    Provides interface for running spatial conflict detection algorithms
    on project utilities to identify potential infrastructure conflicts.

    Args:
    ----
        request: HTTP request object
        project_id: Project identifier from URL parameters

    Returns:
    -------
        Rendered conflict detection interface template

    """

    required_roles = ["stakeholder"]

    template_name: str = "infrastructure/conflicts/detection.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add project context for conflict detection.

        Args:
        ----
            **kwargs: Additional keyword arguments

        Returns:
        -------
            Template context dictionary with project data

        """
        context = super().get_context_data(**kwargs)
        context["project"] = get_object_or_404(Project, pk=self.kwargs["project_id"])
        return context


class ProjectMapView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Two-dimensional mapping interface for project.

    Provides interactive 2D map view for visualizing project utilities,
    infrastructure, and spatial relationships within the project boundary.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/mapping/map.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add project context for 2D mapping interface."""
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, pk=self.kwargs["project_id"])
        context["project"] = project
        return context


class ProjectThreeDView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Three-dimensional visualization interface for project.

    Provides immersive 3D visualization of project infrastructure including
    utilities, terrain, and spatial relationships. Calculates project center
    from geometry or defaults to Indiana center coordinates.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/mapping/3d.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add project context and calculate 3D view center coordinates."""
        context = super().get_context_data(**kwargs)
        project = get_object_or_404(Project, pk=self.kwargs["project_id"])
        context["project"] = project
        context["page_title"] = f"3D View - {project.name}"

        # Calculate project center for three-dimensional coordinate system
        if hasattr(project, "geometry") and project.geometry:
            centroid = project.geometry.centroid
            context["project_center"] = {"lat": centroid.y, "lng": centroid.x}
        else:
            # Default center for Indiana
            context["project_center"] = {"lat": 39.7684, "lng": -86.1581}

        return context


# ============================================================================
# MISSING GIS/MEETINGS VIEWS - ADDED TO UNBLOCK SERVER STARTUP
# ============================================================================


class MeetingsView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Meetings and collaboration view.

    Provides interface for managing project meetings and collaborative
    sessions related to infrastructure planning and coordination.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/meetings.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add meetings context (currently empty placeholder)."""
        context = super().get_context_data(**kwargs)
        context["meetings"] = []
        return context


# Implemented class-based views for URL resolution


class GISProfessionalView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """GIS Professional dashboard view.

    Specialized dashboard for GIS professionals with advanced spatial
    analysis tools, data management interfaces, and technical utilities.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/gis_professional.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add GIS professional dashboard context."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = "GIS Professional Dashboard"
        return context


class MappingView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """General mapping interface.

    Provides general-purpose mapping functionality for infrastructure
    visualization and spatial data interaction across the platform.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/mapping.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add general mapping interface context."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Mapping Interface"
        return context


class UtilityNetworkView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Utility network management view.

    Provides comprehensive interface for managing utility networks including
    topology analysis, network optimization, and connectivity management.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/utility_network.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add utility network management context."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Utility Network Management"
        return context


class SpatialCollaborationView(LoginRequiredMixin, RoleRequiredMixin, TemplateView):
    """Spatial collaboration interface.

    Enables real-time collaborative editing and review of spatial data
    with conflict resolution and team coordination features.
    """

    required_roles = ["stakeholder"]

    template_name = "infrastructure/spatial_collaboration.html"

    def get_context_data(self, **kwargs: Any) -> dict[str, Any]:
        """Add spatial collaboration context."""
        context = super().get_context_data(**kwargs)
        context["page_title"] = "Spatial Collaboration"
        return context


@login_required
def mapping_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX partial for mapping interface components."""
    return render(request, "infrastructure/partials/mapping.html", {})


@login_required
def utilities_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX partial for utilities display components."""
    return render(request, "infrastructure/partials/utilities.html", {})


@login_required
def project_map_view(request: HttpRequest, project_id: int | str) -> HttpResponse:
    """Function-based view for project map display.

    Args:
    ----
        request: HTTP request object
        project_id: Project identifier for map context

    Returns:
    -------
        Rendered project map template

    """
    context: dict[str, Any] = {"project_id": project_id}
    return render(request, "infrastructure/project_map.html", context)


@login_required
def spatial_analysis_panel_htmx(request: HttpRequest, project_id: int | str) -> HttpResponse:
    """HTMX partial for spatial analysis panel interface.

    Args:
    ----
        request: HTTP request object
        project_id: Project identifier for analysis context

    Returns:
    -------
        Rendered spatial analysis panel partial

    """
    context: dict[str, Any] = {"project_id": project_id}
    return render(
        request,
        "infrastructure/partials/spatial_analysis.html",
        context,
    )


@login_required
def map_layers_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX partial for map layers control interface.

    Args:
    ----
        request: HTTP request object

    Returns:
    -------
        Rendered map layers control partial

    """
    context: dict[str, Any] = {}
    return render(request, "infrastructure/partials/map_layers.html", context)


@login_required
def utility_conflicts_htmx(request: HttpRequest) -> HttpResponse:
    """HTMX partial for utility conflicts display.

    Args:
    ----
        request: HTTP request object

    Returns:
    -------
        Rendered utility conflicts partial

    """
    context: dict[str, Any] = {}
    return render(request, "infrastructure/partials/utility_conflicts.html", context)


# Import symbol views if available
try:
    from .symbol_views import *
except ImportError:

    @login_required
    def symbol_search_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX partial for symbol search functionality."""
        try:
            from apps.infrastructure.symbol_models import SymbolCategory, UtilitySymbol

            # Get search parameters
            query = request.GET.get("q", "").strip()
            category_id = request.GET.get("category")
            utility_type = request.GET.get("utility_type")
            symbol_type = request.GET.get("symbol_type")

            # Build queryset
            symbols = UtilitySymbol.objects.filter(is_active=True, status="published")

            # Apply filters
            if query:
                symbols = symbols.filter(
                    Q(name__icontains=query)
                    | Q(description__icontains=query)
                    | Q(symbol_code__icontains=query)
                    | Q(keywords__icontains=query)
                )

            if category_id:
                symbols = symbols.filter(category_id=category_id)

            if utility_type:
                symbols = symbols.filter(utility_type=utility_type)

            if symbol_type:
                symbols = symbols.filter(symbol_type=symbol_type)

            # Limit results and order by usage
            symbols = symbols.order_by("-usage_count", "name")[:20]

            # Get categories for filtering
            categories = SymbolCategory.objects.filter(is_active=True).order_by("name")

            context = {
                "symbols": symbols,
                "categories": categories,
                "query": query,
                "selected_category": category_id,
                "selected_utility_type": utility_type,
                "selected_symbol_type": symbol_type,
            }

            return render(
                request,
                "infrastructure/symbol_palette/symbol_search_results.html",
                context,
            )

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/symbol_search_results.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def symbol_categories_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX partial for symbol category display."""
        try:
            from apps.infrastructure.symbol_models import SymbolCategory

            # Get parent category if specified
            parent_id = request.GET.get("parent")

            if parent_id:
                try:
                    parent = SymbolCategory.objects.get(id=parent_id, is_active=True)
                    categories = parent.subcategories.filter(is_active=True).order_by("sort_order", "name")
                except SymbolCategory.DoesNotExist:
                    categories = SymbolCategory.objects.filter(parent=None, is_active=True).order_by(
                        "sort_order", "name"
                    )
            else:
                categories = SymbolCategory.objects.filter(parent=None, is_active=True).order_by("sort_order", "name")

            context = {
                "categories": categories,
                "parent_id": parent_id,
            }

            return render(request, "infrastructure/symbol_palette/symbol_categories.html", context)

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/symbol_categories.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def symbol_detail_htmx(request: HttpRequest, symbol_id: str) -> HttpResponse:
        """HTMX partial for symbol detail view."""
        try:
            from apps.infrastructure.symbol_models import SymbolPlacement, UtilitySymbol

            try:
                symbol = UtilitySymbol.objects.get(id=symbol_id, is_active=True)

                # Get usage statistics
                total_placements = SymbolPlacement.objects.filter(symbol=symbol).count()
                recent_placements = SymbolPlacement.objects.filter(symbol=symbol).order_by("-placed_at")[:5]

                # Get related symbols
                related_symbols = UtilitySymbol.objects.filter(
                    category=symbol.category, is_active=True, status="published"
                ).exclude(id=symbol.id)[:5]

                context = {
                    "symbol": symbol,
                    "total_placements": total_placements,
                    "recent_placements": recent_placements,
                    "related_symbols": related_symbols,
                }

                return render(request, "infrastructure/symbol_palette/symbol_detail.html", context)

            except UtilitySymbol.DoesNotExist:
                return render(
                    request,
                    "infrastructure/symbol_palette/symbol_detail.html",
                    {"error": "Symbol not found"},
                )

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/symbol_detail.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def symbol_categories_tree_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX partial for symbol categories tree structure."""
        try:
            from apps.infrastructure.symbol_models import SymbolCategory

            # Get root categories with their subcategories
            root_categories = (
                SymbolCategory.objects.filter(parent=None, is_active=True)
                .prefetch_related("subcategories")
                .order_by("sort_order", "name")
            )

            context = {
                "root_categories": root_categories,
            }

            return render(
                request,
                "infrastructure/symbol_palette/symbol_categories_tree.html",
                context,
            )

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/symbol_categories_tree.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def symbol_filter_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX partial for symbol filtering interface."""
        try:
            from apps.infrastructure.symbol_models import (
                APWAStandards,
                SymbolCategory,
                UtilitySymbol,
            )

            # Get filter options
            categories = SymbolCategory.objects.filter(is_active=True).order_by("name")
            utility_types = [(k, v["description"]) for k, v in APWAStandards.UTILITY_COLORS.items()]
            symbol_types = UtilitySymbol.SYMBOL_TYPES

            context = {
                "categories": categories,
                "utility_types": utility_types,
                "symbol_types": symbol_types,
            }

            return render(request, "infrastructure/symbol_palette/symbol_filter.html", context)

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/symbol_filter.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def popular_symbols_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX partial for popular symbols display."""
        try:
            from apps.infrastructure.symbol_models import UtilitySymbol

            # Get popular symbols based on usage count
            popular_symbols = UtilitySymbol.objects.filter(
                is_active=True, status="published", usage_count__gt=0
            ).order_by("-usage_count")[:10]

            context = {
                "popular_symbols": popular_symbols,
            }

            return render(request, "infrastructure/symbol_palette/popular_symbols.html", context)

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/popular_symbols.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def recent_symbols_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX partial for recent symbols display."""
        try:
            from apps.infrastructure.symbol_models import UtilitySymbol

            # Get recently created symbols
            recent_symbols = UtilitySymbol.objects.filter(is_active=True, status="published").order_by("-created_at")[
                :10
            ]

            context = {
                "recent_symbols": recent_symbols,
            }

            return render(request, "infrastructure/symbol_palette/recent_symbols.html", context)

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/recent_symbols.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    def place_symbol_htmx(request: HttpRequest) -> JsonResponse:
        """HTMX endpoint for symbol placement."""
        if request.method != "POST":
            return JsonResponse({"success": False, "message": "POST method required"})

        try:
            from django.contrib.gis.geos import Point

            from apps.infrastructure.symbol_models import SymbolPlacement, UtilitySymbol

            # Get placement data
            symbol_id = request.POST.get("symbol_id")
            project_id = request.POST.get("project_id")
            latitude = request.POST.get("latitude")
            longitude = request.POST.get("longitude")
            scale = request.POST.get("scale", "1.0")
            rotation = request.POST.get("rotation", "0.0")
            label = request.POST.get("label", "")

            # Validate required fields
            if not all([symbol_id, project_id, latitude, longitude]):
                return JsonResponse({"success": False, "message": "Missing required fields"})

            # Get symbol and project
            try:
                symbol = UtilitySymbol.objects.get(id=symbol_id, is_active=True)
                project = Project.objects.get(id=project_id)
            except (UtilitySymbol.DoesNotExist, Project.DoesNotExist):
                return JsonResponse({"success": False, "message": "Symbol or project not found"})

            # Validate and convert coordinates
            try:
                lat_float = float(latitude)
                lon_float = float(longitude)
                scale_decimal = Decimal(scale)
                rotation_decimal = Decimal(rotation)
            except (ValueError, TypeError):
                return JsonResponse({"success": False, "message": "Invalid numeric values"})

            # Create placement
            placement = SymbolPlacement.objects.create(
                symbol=symbol,
                project=project,
                geometry=Point(lon_float, lat_float),
                scale=scale_decimal,
                rotation=rotation_decimal,
                label=label,
                placed_by=request.user,
            )

            # Update symbol usage count
            symbol.increment_usage()

            return JsonResponse(
                {
                    "success": True,
                    "placement_id": str(placement.id),
                    "message": "Symbol placed successfully",
                }
            )

        except ImportError:
            return JsonResponse({"success": False, "message": "Symbol models not available"})
        except Exception as e:
            return JsonResponse({"success": False, "message": f"Error placing symbol: {e!s}"})

    @login_required
    def update_symbol_placement_htmx(request: HttpRequest, placement_id: str) -> JsonResponse:
        """HTMX endpoint for updating symbol placement."""
        if request.method != "POST":
            return JsonResponse({"success": False, "message": "POST method required"})

        try:
            from django.contrib.gis.geos import Point

            from apps.infrastructure.symbol_models import SymbolPlacement

            try:
                placement = SymbolPlacement.objects.get(id=placement_id, placed_by=request.user)
            except SymbolPlacement.DoesNotExist:
                return JsonResponse(
                    {
                        "success": False,
                        "message": "Placement not found or access denied",
                    }
                )

            # Update placement data
            if "latitude" in request.POST and "longitude" in request.POST:
                try:
                    latitude = float(request.POST["latitude"])
                    longitude = float(request.POST["longitude"])
                    placement.geometry = Point(longitude, latitude)
                except (ValueError, TypeError):
                    return JsonResponse({"success": False, "message": "Invalid coordinates"})

            if "scale" in request.POST:
                try:
                    placement.scale = Decimal(request.POST["scale"])
                except (ValueError, TypeError):
                    return JsonResponse({"success": False, "message": "Invalid scale value"})

            if "rotation" in request.POST:
                try:
                    placement.rotation = Decimal(request.POST["rotation"])
                except (ValueError, TypeError):
                    return JsonResponse({"success": False, "message": "Invalid rotation value"})

            if "label" in request.POST:
                placement.label = request.POST["label"]

            if "custom_color" in request.POST:
                placement.custom_color = request.POST["custom_color"]

            placement.save()

            return JsonResponse({"success": True, "message": "Symbol placement updated successfully"})

        except ImportError:
            return JsonResponse({"success": False, "message": "Symbol models not available"})
        except Exception as e:
            return JsonResponse({"success": False, "message": f"Error updating placement: {e!s}"})

    @login_required
    def delete_symbol_placement_htmx(request: HttpRequest, placement_id: str) -> JsonResponse:
        """HTMX endpoint for deleting symbol placement."""
        if request.method != "POST":
            return JsonResponse({"success": False, "message": "POST method required"})

        try:
            from apps.infrastructure.symbol_models import SymbolPlacement

            try:
                placement = SymbolPlacement.objects.get(id=placement_id, placed_by=request.user)
                placement.delete()

                return JsonResponse(
                    {
                        "success": True,
                        "message": "Symbol placement deleted successfully",
                    }
                )

            except SymbolPlacement.DoesNotExist:
                return JsonResponse(
                    {
                        "success": False,
                        "message": "Placement not found or access denied",
                    }
                )

        except ImportError:
            return JsonResponse({"success": False, "message": "Symbol models not available"})
        except Exception as e:
            return JsonResponse({"success": False, "message": f"Error deleting placement: {e!s}"})

    @login_required
    def project_symbol_placements_htmx(request: HttpRequest, project_id: str) -> HttpResponse:
        """HTMX partial for project symbol placements."""
        try:
            from apps.infrastructure.symbol_models import SymbolPlacement

            try:
                project = Project.objects.get(id=project_id)

                # Get symbol placements for this project
                placements = (
                    SymbolPlacement.objects.filter(project=project)
                    .select_related("symbol", "placed_by")
                    .order_by("-placed_at")
                )

                context = {
                    "project": project,
                    "placements": placements,
                }

                return render(
                    request,
                    "infrastructure/symbol_palette/project_symbols.html",
                    context,
                )

            except Project.DoesNotExist:
                return render(
                    request,
                    "infrastructure/symbol_palette/project_symbols.html",
                    {"error": "Project not found"},
                )

        except ImportError:
            return render(
                request,
                "infrastructure/symbol_palette/project_symbols.html",
                {"message": "Symbol models not available"},
            )

    @login_required
    @require_http_methods(["GET", "POST"])
    def symbol_quick_add_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX endpoint for quick symbol addition."""
        if request.method == "GET":
            # Show quick add form
            try:
                from apps.infrastructure.symbol_models import (
                    APWAStandards,
                    SymbolCategory,
                )

                categories = SymbolCategory.objects.filter(is_active=True).order_by("name")
                utility_types = [(k, v["description"]) for k, v in APWAStandards.UTILITY_COLORS.items()]

                context = {
                    "categories": categories,
                    "utility_types": utility_types,
                }

                return render(
                    request,
                    "infrastructure/symbol_palette/quick_add_form.html",
                    context,
                )

            except ImportError:
                return render(
                    request,
                    "infrastructure/symbol_palette/quick_add_form.html",
                    {"message": "Symbol models not available"},
                )

        elif request.method == "POST":
            # Process quick add
            try:
                from apps.infrastructure.symbol_models import (
                    APWAStandards,
                    SymbolCategory,
                    UtilitySymbol,
                )

                # Get form data
                name = request.POST.get("name", "").strip()
                description = request.POST.get("description", "").strip()
                category_id = request.POST.get("category_id")
                utility_type = request.POST.get("utility_type")
                symbol_type = request.POST.get("symbol_type")
                svg_content = request.POST.get("svg_content", "").strip()

                # Validate required fields
                if not all([name, category_id, utility_type, symbol_type, svg_content]):
                    return JsonResponse({"success": False, "message": "All fields are required"})

                # Get category
                try:
                    category = SymbolCategory.objects.get(id=category_id, is_active=True)
                except SymbolCategory.DoesNotExist:
                    return JsonResponse({"success": False, "message": "Category not found"})

                # Generate symbol code
                symbol_code = f"{utility_type.upper()}-{symbol_type.upper()}-{len(name)}"

                # Get APWA color
                apwa_color = APWAStandards.get_utility_color(utility_type)["color"]

                # Create symbol
                symbol = UtilitySymbol.objects.create(
                    name=name,
                    symbol_code=symbol_code,
                    description=description,
                    symbol_type=symbol_type,
                    category=category,
                    svg_content=svg_content,
                    svg_viewbox="0 0 100 100",  # Default viewbox
                    base_width=Decimal("100.00"),
                    base_height=Decimal("100.00"),
                    utility_type=utility_type,
                    apwa_color=apwa_color,
                    created_by=request.user,
                    status="draft",  # Start as draft
                )

                return JsonResponse(
                    {
                        "success": True,
                        "symbol_id": str(symbol.id),
                        "message": f"Symbol '{name}' created successfully",
                    }
                )

            except ImportError:
                return JsonResponse({"success": False, "message": "Symbol models not available"})
            except Exception as e:
                return JsonResponse({"success": False, "message": f"Error creating symbol: {e!s}"})

    @login_required
    @require_http_methods(["GET", "POST"])
    def import_builtin_library_htmx(request: HttpRequest) -> HttpResponse:
        """HTMX endpoint for importing builtin symbol library."""
        if request.method == "GET":
            # Show import options
            try:
                from apps.infrastructure.symbol_models import SymbolLibrary

                # Get available builtin libraries
                builtin_libraries = SymbolLibrary.objects.filter(library_type="standard", is_published=True).order_by(
                    "name"
                )

                context = {
                    "builtin_libraries": builtin_libraries,
                }

                return render(
                    request,
                    "infrastructure/symbol_palette/import_library_form.html",
                    context,
                )

            except ImportError:
                return render(
                    request,
                    "infrastructure/symbol_palette/import_library_form.html",
                    {"message": "Symbol models not available"},
                )

        elif request.method == "POST":
            # Process import
            try:
                from apps.infrastructure.symbol_models import (
                    SymbolLibrary,
                    SymbolLibraryItem,
                )

                library_id = request.POST.get("library_id")
                if not library_id:
                    return JsonResponse({"success": False, "message": "Library ID required"})

                try:
                    library = SymbolLibrary.objects.get(id=library_id, is_published=True)

                    # Get symbols from the library
                    library_items = SymbolLibraryItem.objects.filter(library=library).select_related("symbol")

                    imported_count = 0
                    for item in library_items:
                        # Create a copy of the symbol for the user's organization
                        symbol = item.symbol
                        # Here you would implement the actual import logic
                        # For now, just increment the usage count
                        symbol.increment_usage()
                        imported_count += 1

                    return JsonResponse(
                        {
                            "success": True,
                            "imported_count": imported_count,
                            "message": f"Imported {imported_count} symbols from {library.name}",
                        }
                    )

                except SymbolLibrary.DoesNotExist:
                    return JsonResponse({"success": False, "message": "Library not found"})

            except ImportError:
                return JsonResponse({"success": False, "message": "Symbol models not available"})
            except Exception as e:
                return JsonResponse({"success": False, "message": f"Error importing library: {e!s}"})


# API Views for GeoJSON data
@login_required
@require_http_methods(["GET"])
def api_utilities_geojson(request: HttpRequest) -> JsonResponse:
    """Return utilities data in GeoJSON format.

    Returns utility infrastructure as GeoJSON features for map display.
    Supports filtering by project ID.
    """
    try:
        # Base queryset
        utilities = Utility.objects.filter(project__organization=request.user.organization).select_related(
            "project", "created_by"
        )

        # Apply project filter if provided
        project_id = request.GET.get("project")
        if project_id:
            utilities = utilities.filter(project_id=project_id)

        # Build GeoJSON features
        features = []
        for utility in utilities:
            # Create a point feature for each utility (as geometry is not directly on model)
            # In a real implementation, you'd get geometry from related spatial data
            feature = {
                "type": "Feature",
                "properties": {
                    "id": str(utility.id),
                    "name": utility.name,
                    "type": utility.type,
                    "status": utility.status,
                    "owner": getattr(utility, "owner", None),
                    "operator": getattr(utility, "operator", None),
                    "condition": getattr(utility, "condition", None),
                    "install_date": (utility.initiation_date.isoformat() if utility.initiation_date else None),
                    "project_name": utility.project.name,
                    "project_id": str(utility.project.id),
                    "description": utility.notes,
                    "contact_name": utility.contact_name,
                    "contact_email": utility.contact_email,
                    "contact_phone": utility.contact_phone,
                },
                "geometry": {
                    # Default to a point near Indianapolis for demo
                    # In production, this would come from actual spatial data
                    "type": "Point",
                    "coordinates": [
                        -86.1581 + (hash(str(utility.id)) % 100) * 0.001,
                        39.7684 + (hash(utility.name) % 100) * 0.001,
                    ],
                },
            }
            features.append(feature)

        geojson_data = {"type": "FeatureCollection", "features": features}

        return JsonResponse(geojson_data)
    except Exception as e:
        logger.error(f"Error generating utilities GeoJSON: {e}")
        return JsonResponse({"error": "Failed to generate GeoJSON"}, status=500)


@login_required
@require_http_methods(["GET"])
def api_conflicts_geojson(request: HttpRequest) -> JsonResponse:
    """Return conflicts data in GeoJSON format.

    Returns infrastructure conflicts as GeoJSON features for map display.
    """
    try:
        # Get conflicts for user's organization
        conflicts = Conflict.objects.filter(project__organization=request.user.organization).select_related(
            "project", "resolved_by"
        )

        # Build GeoJSON features
        features = []
        for conflict in conflicts:
            # Create a point feature for each conflict
            feature = {
                "type": "Feature",
                "properties": {
                    "id": str(conflict.id),
                    "type": "conflict",  # Important for popup system
                    "conflict_type": conflict.conflict_type,
                    "severity": getattr(conflict, "severity", "medium"),
                    "status": getattr(conflict, "status", "active"),
                    "description": getattr(conflict, "description", ""),
                    "project_name": conflict.project.name,
                    "project_id": str(conflict.project.id),
                    "created_at": (conflict.created_at.isoformat() if conflict.created_at else None),
                    "resolved_by": (conflict.resolved_by.get_full_name() if conflict.resolved_by else None),
                    "resolved_at": getattr(conflict, "resolved_at", None),
                },
                "geometry": {
                    # Generate a point location for the conflict
                    # In production, this would be based on actual conflict location
                    "type": "Point",
                    "coordinates": [
                        -86.1581 + (hash(str(conflict.id)) % 80) * 0.0015,
                        39.7684 + (hash(conflict.conflict_type) % 80) * 0.0015,
                    ],
                },
            }
            features.append(feature)

        geojson_data = {"type": "FeatureCollection", "features": features}

        return JsonResponse(geojson_data)
    except Exception as e:
        logger.error(f"Error generating conflicts GeoJSON: {e}")
        return JsonResponse({"error": "Failed to generate GeoJSON"}, status=500)


# ============================================================================
# 3D VISUALIZATION HTMX DATA ENDPOINTS
# ============================================================================


@login_required
@require_http_methods(["GET"])
def htmx_utilities_3d_data(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for 3D utilities data delivery.

    Returns utilities data formatted for Three.js 3D visualization with
    depth information, geometry data, and material properties.
    """
    try:
        from apps.projects.models import Project

        project_id = request.GET.get("project_id")
        if not project_id:
            return JsonResponse({"error": "Project ID required"}, status=400)

        project = get_object_or_404(Project, pk=project_id)

        # Check project access
        if not project.user_has_access(request.user):
            return JsonResponse({"error": "Access denied"}, status=403)

        # Get utilities for the project with 3D data
        utilities = UtilityLineData.objects.filter(project=project).select_related("utility")

        utilities_data = []
        for utility in utilities:
            # Convert utility data for 3D visualization
            utility_data = {
                "id": str(utility.id),
                "utility_type": (utility.utility.utility_type if utility.utility else "other"),
                "owner": utility.utility.owner if utility.utility else "Unknown",
                "material": getattr(utility, "material", "Unknown"),
                "diameter_inches": getattr(utility, "diameter_inches", 6),
                "depth_feet": getattr(utility, "depth_feet", 5),
                "condition": getattr(utility, "condition", "good"),
                "installation_year": getattr(utility, "installation_year", None),
                "geometry_3d": (
                    {
                        "type": "LineString",
                        "coordinates": [
                            # Convert line_data to 3D coordinates if available
                            [
                                -86.1581 + (hash(str(utility.id)) % 100) * 0.001,
                                39.7684 + (hash(str(utility.id)) % 100) * 0.001,
                                -(getattr(utility, "depth_feet", 5)),
                            ]
                        ],
                    }
                    if utility.line_data
                    else None
                ),
            }

            # Add real geometry if available
            if hasattr(utility, "line_data") and utility.line_data:
                try:
                    # Convert LineString geometry to 3D coordinates
                    if hasattr(utility.line_data, "coords"):
                        coords_3d = []
                        for coord in utility.line_data.coords:
                            # Add depth as Z coordinate (negative for underground)
                            coords_3d.append(
                                [
                                    coord[0],
                                    coord[1],
                                    -(getattr(utility, "depth_feet", 5)),
                                ]
                            )
                        utility_data["geometry_3d"] = {
                            "type": "LineString",
                            "coordinates": coords_3d,
                        }
                except (AttributeError, TypeError):
                    pass  # Keep default geometry

            utilities_data.append(utility_data)

        # Get utility type statistics
        utility_types = {}
        for utility in utilities_data:
            util_type = utility["utility_type"]
            if util_type not in utility_types:
                utility_types[util_type] = {
                    "id": util_type,
                    "name": util_type.replace("_", " ").title(),
                    "count": 0,
                    "color": _get_utility_color(util_type),
                }
            utility_types[util_type]["count"] += 1

        return JsonResponse(
            {
                "utilities": utilities_data,
                "utilityTypes": list(utility_types.values()),
                "projectCenter": {
                    "lat": project.geometry.centroid.y if project.geometry else 39.7684,
                    "lng": (project.geometry.centroid.x if project.geometry else -86.1581),
                },
            }
        )

    except Exception as e:
        logger.error(f"Error generating 3D utilities data: {e}")
        return JsonResponse({"error": "Failed to generate 3D data"}, status=500)


@login_required
@require_http_methods(["GET"])
def htmx_conflicts_3d_data(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for 3D conflicts data delivery."""
    try:
        from apps.projects.models import Project

        project_id = request.GET.get("project_id")
        if not project_id:
            return JsonResponse({"error": "Project ID required"}, status=400)

        project = get_object_or_404(Project, pk=project_id)

        # Check project access
        if not project.user_has_access(request.user):
            return JsonResponse({"error": "Access denied"}, status=403)

        # Get conflicts for the project
        conflicts = Conflict.objects.filter(project=project)

        conflicts_data = []
        for conflict in conflicts:
            conflict_data = {
                "id": conflict.id,
                "conflict_type": conflict.conflict_type,
                "severity": getattr(conflict, "severity", "medium"),
                "risk_score": getattr(conflict, "risk_score", 5),
                "distance_feet": getattr(conflict, "distance_feet", 3),
                "depth_feet": getattr(conflict, "depth_feet", 5),
                "description": conflict.description,
                "resolution_status": getattr(conflict, "resolution_status", "pending"),
                "conflict_3d_geometry": {
                    "type": "Point",
                    "coordinates": [
                        -86.1581 + (hash(str(conflict.id)) % 80) * 0.0015,
                        39.7684 + (hash(conflict.conflict_type) % 80) * 0.0015,
                        -(getattr(conflict, "depth_feet", 5)),
                    ],
                },
            }
            conflicts_data.append(conflict_data)

        return JsonResponse({"conflicts": conflicts_data})

    except Exception as e:
        logger.error(f"Error generating 3D conflicts data: {e}")
        return JsonResponse({"error": "Failed to generate 3D conflicts data"}, status=500)


@login_required
@require_http_methods(["GET"])
def htmx_3d_depth_filter(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for 3D depth filtering."""
    try:
        project_id = request.GET.get("project_id")
        min_depth = float(request.GET.get("min_depth", 0))
        max_depth = float(request.GET.get("max_depth", 20))

        if not project_id:
            return JsonResponse({"error": "Project ID required"}, status=400)

        # Filter utilities and conflicts by depth
        utilities_response = htmx_utilities_3d_data(request)
        utilities_data = json.loads(utilities_response.content)

        # Filter by depth range
        filtered_utilities = [
            utility
            for utility in utilities_data.get("utilities", [])
            if min_depth <= utility.get("depth_feet", 5) <= max_depth
        ]

        conflicts_response = htmx_conflicts_3d_data(request)
        conflicts_data = json.loads(conflicts_response.content)

        filtered_conflicts = [
            conflict
            for conflict in conflicts_data.get("conflicts", [])
            if min_depth <= conflict.get("depth_feet", 5) <= max_depth
        ]

        return JsonResponse(
            {
                "utilities": filtered_utilities,
                "conflicts": filtered_conflicts,
                "depthRange": {"min": min_depth, "max": max_depth},
            }
        )

    except Exception as e:
        logger.error(f"Error filtering 3D depth data: {e}")
        return JsonResponse({"error": "Failed to filter depth data"}, status=500)


@login_required
@require_http_methods(["POST"])
def htmx_3d_selection_sync(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for syncing 3D object selection state with server."""
    try:
        import json

        data = json.loads(request.body)
        selected_utilities = data.get("selectedUtilities", [])
        selected_conflicts = data.get("selectedConflicts", [])

        # Store selection state in session for persistence
        request.session["3d_selected_utilities"] = selected_utilities
        request.session["3d_selected_conflicts"] = selected_conflicts

        # Return selection details for UI updates
        return JsonResponse(
            {
                "status": "success",
                "selectedCount": len(selected_utilities) + len(selected_conflicts),
                "selectedUtilities": selected_utilities,
                "selectedConflicts": selected_conflicts,
            }
        )

    except Exception as e:
        logger.error(f"Error syncing 3D selection: {e}")
        return JsonResponse({"error": "Failed to sync selection"}, status=500)


@login_required
@require_http_methods(["POST"])
def htmx_3d_export_image(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for exporting 3D visualization as image."""
    try:
        import base64
        import json

        from django.core.files.base import ContentFile
        from django.core.files.storage import default_storage

        data = json.loads(request.body)
        image_data = data.get("imageData")  # Base64 encoded image
        project_id = data.get("projectId")
        export_format = data.get("format", "png")

        if not image_data or not project_id:
            return JsonResponse({"error": "Image data and project ID required"}, status=400)

        # Remove data URL prefix
        if image_data.startswith("data:image/"):
            image_data = image_data.split(",")[1]

        # Decode base64 image
        image_binary = base64.b64decode(image_data)

        # Generate filename
        timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
        filename = f"3d_export_project_{project_id}_{timestamp}.{export_format}"

        # Save to storage
        file_path = default_storage.save(f"exports/3d/{filename}", ContentFile(image_binary))

        return JsonResponse(
            {
                "status": "success",
                "filename": filename,
                "downloadUrl": default_storage.url(file_path),
                "message": "3D visualization exported successfully",
            }
        )

    except Exception as e:
        logger.error(f"Error exporting 3D image: {e}")
        return JsonResponse({"error": "Failed to export image"}, status=500)


def _get_utility_color(utility_type: str) -> str:
    """Get color code for utility type visualization."""
    color_map = {
        "water": "#2196F3",  # Blue
        "sewer": "#795548",  # Brown
        "gas": "#FFEB3B",  # Yellow
        "electric": "#FF5722",  # Red/Orange
        "telecom": "#9C27B0",  # Purple
        "cable": "#000000",  # Black
        "other": "#9E9E9E",  # Grey
    }
    return color_map.get(utility_type.lower(), color_map["other"])


# HTMX Views for Conflict Layer Infrastructure
@login_required
@require_http_methods(["GET"])
def htmx_conflicts_map_data(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for conflict map data loading."""
    try:
        # Get conflicts for user's organization
        conflicts = Conflict.objects.filter(project__organization=request.user.organization).select_related(
            "project", "resolved_by"
        )

        # Apply filters from request
        conflict_type = request.GET.get("type")
        request.GET.get("risk_level")
        status = request.GET.get("status")

        if conflict_type and conflict_type != "all":
            conflicts = conflicts.filter(conflict_type=conflict_type)

        if status and status != "all":
            conflicts = conflicts.filter(status=status)

        # Build GeoJSON features
        features = []
        for conflict in conflicts:
            # Calculate risk score with more realistic distribution for testing styling
            base_risk = getattr(conflict, "risk_score", None)
            if base_risk is None:
                # Generate realistic risk scores based on conflict type and ID for testing
                conflict_id_num = int(str(conflict.id)[-1]) if str(conflict.id) else 5
                if conflict.conflict_type == "clearance":
                    risk_score = 70 + (conflict_id_num * 2)  # 70-88 range
                elif conflict.conflict_type == "crossing":
                    risk_score = 60 + (conflict_id_num * 3)  # 60-87 range
                elif conflict.conflict_type == "parallel":
                    risk_score = 30 + (conflict_id_num * 4)  # 30-66 range
                else:
                    risk_score = 40 + (conflict_id_num * 2)  # 40-58 range

                # Add some variation for critical risks
                if conflict_id_num >= 8:
                    risk_score = min(95, risk_score + 10)  # Create some critical risks
            else:
                risk_score = base_risk

            feature = {
                "type": "Feature",
                "properties": {
                    "id": str(conflict.id),
                    "conflict_type": conflict.conflict_type,
                    "risk_score": risk_score,
                    "severity": getattr(conflict, "severity", "medium"),
                    "status": getattr(conflict, "status", "active"),
                    "description": getattr(conflict, "description", ""),
                    "project_name": conflict.project.name,
                    "project_id": str(conflict.project.id),
                    "created_at": (conflict.created_at.isoformat() if conflict.created_at else None),
                    "resolved_by": (conflict.resolved_by.get_full_name() if conflict.resolved_by else None),
                },
                "geometry": {
                    # Generate conflict location based on ID hash
                    "type": "Point",
                    "coordinates": [
                        -86.1581 + (hash(str(conflict.id)) % 80) * 0.0015,
                        39.7684 + (hash(conflict.conflict_type) % 80) * 0.0015,
                    ],
                },
            }
            features.append(feature)

        geojson_data = {"type": "FeatureCollection", "features": features}

        return JsonResponse(geojson_data)
    except Exception as e:
        logger.error(f"Error generating conflict map data: {e}")
        return JsonResponse({"error": "Failed to generate conflict data"}, status=500)


@login_required
@require_http_methods(["GET"])
def htmx_conflicts_filtered(request: HttpRequest) -> HttpResponse:
    """Enhanced HTMX endpoint for comprehensive conflict filtering."""
    try:
        # Get filter parameters with multi-value support
        conflict_types = request.GET.getlist("type")
        risk_levels = request.GET.getlist("risk_level")
        status = request.GET.get("status", "all")
        project_id = request.GET.get("project", "all")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        risk_score_min = request.GET.get("risk_score_min")
        risk_score_max = request.GET.get("risk_score_max")
        utility_search = request.GET.get("utility_search", "").strip()
        preset = request.GET.get("preset")

        # Get base conflicts for user's organization
        conflicts = Conflict.objects.filter(project__organization=request.user.organization).select_related(
            "project", "resolved_by", "utility", "utility2"
        )

        # Handle presets for quick filtering
        if preset:
            if preset == "high_risk":
                conflicts = conflicts.filter(risk_score__gte=80)
                risk_levels = ["high"]
            elif preset == "active":
                conflicts = conflicts.filter(status="active")
                status = "active"
        else:
            # Apply individual filters

            # Filter by conflict types (multiple selection)
            if conflict_types and "all" not in conflict_types:
                conflicts = conflicts.filter(conflict_type__in=conflict_types)

            # Filter by risk levels (multiple selection)
            if risk_levels and "all" not in risk_levels:
                risk_queries = []
                if "high" in risk_levels:
                    risk_queries.append(Q(risk_score__gte=80))
                if "medium" in risk_levels:
                    risk_queries.append(Q(risk_score__gte=40, risk_score__lt=80))
                if "low" in risk_levels:
                    risk_queries.append(Q(risk_score__lt=40))

                if risk_queries:
                    import operator
                    from functools import reduce

                    conflicts = conflicts.filter(reduce(operator.or_, risk_queries))

            # Filter by status
            if status and status != "all":
                conflicts = conflicts.filter(status=status)

            # Filter by project
            if project_id and project_id != "all":
                try:
                    conflicts = conflicts.filter(project_id=int(project_id))
                except (ValueError, TypeError):
                    pass

            # Filter by date range
            if date_from:
                try:
                    from datetime import datetime

                    date_from_parsed = datetime.strptime(date_from, "%Y-%m-%d").date()
                    conflicts = conflicts.filter(created_at__date__gte=date_from_parsed)
                except ValueError:
                    pass

            if date_to:
                try:
                    from datetime import datetime

                    date_to_parsed = datetime.strptime(date_to, "%Y-%m-%d").date()
                    conflicts = conflicts.filter(created_at__date__lte=date_to_parsed)
                except ValueError:
                    pass

            # Filter by risk score range
            if risk_score_min:
                try:
                    conflicts = conflicts.filter(risk_score__gte=int(risk_score_min))
                except (ValueError, TypeError):
                    pass

            if risk_score_max:
                try:
                    conflicts = conflicts.filter(risk_score__lte=int(risk_score_max))
                except (ValueError, TypeError):
                    pass

            # Filter by utility search
            if utility_search:
                conflicts = conflicts.filter(
                    Q(utility__name__icontains=utility_search)
                    | Q(utility2__name__icontains=utility_search)
                    | Q(description__icontains=utility_search)
                )

        # Order conflicts by risk score and creation date
        conflicts = conflicts.order_by("-risk_score", "-created_at")

        # Get available projects for filter dropdown
        available_projects = Project.objects.filter(organization=request.user.organization).order_by("name")

        # Prepare context with filter state
        context = {
            "conflicts": conflicts,
            "filter_types": conflict_types,
            "filter_risk_levels": risk_levels,
            "filter_status": status,
            "filter_project": project_id,
            "filter_date_from": date_from,
            "filter_date_to": date_to,
            "filter_risk_score_min": risk_score_min,
            "filter_risk_score_max": risk_score_max,
            "filter_utility_search": utility_search,
            "available_projects": available_projects,
            "filter_count": sum(
                [
                    len(conflict_types) if conflict_types else 0,
                    len(risk_levels) if risk_levels else 0,
                    1 if status != "all" else 0,
                    1 if project_id != "all" else 0,
                    1 if date_from else 0,
                    1 if date_to else 0,
                    1 if risk_score_min else 0,
                    1 if risk_score_max else 0,
                    1 if utility_search else 0,
                ]
            ),
            "total_conflicts": conflicts.count(),
        }

        # Add HX-Trigger header for client-side updates
        response = render(request, "infrastructure/partials/conflict_results.html", context)
        response["HX-Trigger"] = "conflictFiltersApplied"

        return response

    except Exception as e:
        logger.error(f"Error filtering conflicts: {e}")
        return HttpResponse(
            '<div class="alert alert-danger">'
            '<i class="bi bi-exclamation-triangle me-2"></i>'
            "Error filtering conflicts. Please try again."
            "</div>"
        )


@login_required
@require_http_methods(["GET"])
def htmx_conflict_filter_form(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for rendering conflict filter form with current state."""
    try:
        # Get current filter parameters
        conflict_types = request.GET.getlist("type")
        risk_levels = request.GET.getlist("risk_level")
        status = request.GET.get("status", "all")
        project_id = request.GET.get("project", "all")
        date_from = request.GET.get("date_from")
        date_to = request.GET.get("date_to")
        risk_score_min = request.GET.get("risk_score_min")
        risk_score_max = request.GET.get("risk_score_max")
        utility_search = request.GET.get("utility_search", "")

        # Get available projects for filter dropdown
        available_projects = Project.objects.filter(organization=request.user.organization).order_by("name")

        context = {
            "filter_types": conflict_types,
            "filter_risk_levels": risk_levels,
            "filter_status": status,
            "filter_project": project_id,
            "filter_date_from": date_from,
            "filter_date_to": date_to,
            "filter_risk_score_min": risk_score_min,
            "filter_risk_score_max": risk_score_max,
            "filter_utility_search": utility_search,
            "available_projects": available_projects,
        }

        return render(request, "infrastructure/partials/conflict_filter_form.html", context)

    except Exception as e:
        logger.error(f"Error rendering conflict filter form: {e}")
        return HttpResponse(
            '<div class="alert alert-warning">'
            '<i class="bi bi-exclamation-triangle me-2"></i>'
            "Filter form temporarily unavailable."
            "</div>"
        )


@login_required
@require_http_methods(["GET"])
def htmx_conflicts_updates(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for real-time conflict updates with server-side change detection."""
    try:
        # Get last update timestamp from request headers (used for change detection)
        last_update = request.headers.get("X-Last-Update")
        project_id = request.GET.get("project_id")

        # Get conflicts for user's organization
        conflicts = Conflict.objects.filter(project__organization=request.user.organization).select_related(
            "project", "utility", "utility2"
        )

        # Filter by project if provided
        if project_id and project_id != "all":
            conflicts = conflicts.filter(project_id=project_id)

        # Server-side change detection using modified timestamp
        if last_update:
            try:
                from datetime import datetime

                last_update_dt = datetime.fromisoformat(last_update.replace("Z", "+00:00"))
                # Only return conflicts modified since last update
                new_conflicts = conflicts.filter(updated_at__gt=last_update_dt)
                if not new_conflicts.exists():
                    # No changes detected - return minimal response
                    response = HttpResponse("")
                    response["X-HTMX-No-Update"] = "true"
                    return response
                conflicts = new_conflicts
            except (ValueError, TypeError):
                # Invalid timestamp - return all conflicts
                pass

        # Limit results for performance (most recent conflicts first)
        conflicts = conflicts.order_by("-created_at", "-updated_at")[:50]

        # Get current timestamp for next polling cycle
        current_time = timezone.now().isoformat()

        # Count active conflicts by severity for summary
        conflict_summary = {
            "total": conflicts.count(),
            "high_risk": conflicts.filter(risk_score__gte=80).count(),
            "medium_risk": conflicts.filter(risk_score__range=(40, 79)).count(),
            "low_risk": conflicts.filter(risk_score__lt=40).count(),
            "unresolved": conflicts.filter(resolved_at__isnull=True).count(),
        }

        context = {
            "conflicts": conflicts,
            "conflict_summary": conflict_summary,
            "last_update": current_time,
            "has_changes": True,
            "update_count": conflicts.count(),
        }

        response = render(request, "infrastructure/partials/conflict_updates.html", context)

        # Add headers for HTMX polling optimization
        response["X-Current-Time"] = current_time
        response["X-Update-Count"] = str(conflicts.count())
        response["Cache-Control"] = "no-cache, no-store, must-revalidate"

        return response

    except Exception as e:
        logger.error(f"Error in conflict updates endpoint: {e}")
        # Return error state partial
        context = {
            "error": True,
            "error_message": "Unable to load conflict updates",
            "last_update": timezone.now().isoformat(),
        }
        return render(request, "infrastructure/partials/conflict_updates.html", context)


@login_required
@require_http_methods(["GET"])
def htmx_conflicts_print_preview(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for server-rendered conflict print preview."""
    try:
        project_id = request.GET.get("project_id")

        # Get conflicts for user's organization
        conflicts = Conflict.objects.filter(project__organization=request.user.organization).select_related(
            "project", "utility", "utility2", "resolved_by"
        )

        # Filter by project if provided
        if project_id and project_id != "all":
            conflicts = conflicts.filter(project_id=project_id)
            project = get_object_or_404(
                Project.objects.filter(organization=request.user.organization),
                id=project_id,
            )
        else:
            project = None

        # Order conflicts by severity and risk score
        conflicts = conflicts.order_by("-risk_score", "-created_at")

        # Generate conflict statistics
        conflict_stats = {
            "total": conflicts.count(),
            "high_risk": conflicts.filter(risk_score__gte=80).count(),
            "medium_risk": conflicts.filter(risk_score__range=(40, 79)).count(),
            "low_risk": conflicts.filter(risk_score__lt=40).count(),
            "unresolved": conflicts.filter(resolved_at__isnull=True).count(),
            "resolved": conflicts.filter(resolved_at__isnull=False).count(),
        }

        # Group conflicts by type for legend
        conflicts.values("conflict_type").distinct()

        # Generate print legend data
        legend_data = {
            "risk_levels": [
                {
                    "name": "Critical Risk",
                    "color": "#6f42c1",
                    "range": "90-100",
                    "description": "Immediate action required",
                },
                {
                    "name": "High Risk",
                    "color": "#dc3545",
                    "range": "80-89",
                    "description": "Priority resolution needed",
                },
                {
                    "name": "Medium-High Risk",
                    "color": "#fd7e14",
                    "range": "60-79",
                    "description": "Significant concern",
                },
                {
                    "name": "Medium Risk",
                    "color": "#ffc107",
                    "range": "40-59",
                    "description": "Moderate concern",
                },
                {
                    "name": "Low Risk",
                    "color": "#28a745",
                    "range": "0-39",
                    "description": "Minor concern",
                },
            ],
            "conflict_types": [
                {
                    "type": "clearance",
                    "symbol": "C",
                    "color": "#ffcccc",
                    "description": "Clearance violation between utilities",
                },
                {
                    "type": "crossing",
                    "symbol": "X",
                    "color": "#ccffcc",
                    "description": "Utility crossing conflict",
                },
                {
                    "type": "proximity",
                    "symbol": "P",
                    "color": "#ccccff",
                    "description": "Proximity distance violation",
                },
                {
                    "type": "depth",
                    "symbol": "D",
                    "color": "#ffffcc",
                    "description": "Depth separation conflict",
                },
            ],
        }

        # Generate current timestamp for print metadata

        current_time = timezone.now()

        context = {
            "conflicts": conflicts[:50],  # Limit for print performance
            "conflict_stats": conflict_stats,
            "legend_data": legend_data,
            "project": project,
            "print_date": current_time.strftime("%B %d, %Y at %I:%M %p"),
            "print_timestamp": current_time.isoformat(),
            "organization": request.user.organization,
            "generated_by": request.user.get_full_name() or request.user.username,
            "total_conflicts": conflicts.count(),
            "is_preview": True,
        }

        return render(request, "infrastructure/partials/conflict_print_preview.html", context)

    except Exception as e:
        logger.error(f"Error generating conflict print preview: {e}")
        context = {
            "error": True,
            "error_message": "Unable to generate print preview",
            "print_date": timezone.now().strftime("%B %d, %Y at %I:%M %p"),
        }
        return render(request, "infrastructure/partials/conflict_print_preview.html", context)


@login_required
@require_http_methods(["GET"])
def htmx_conflict_detail_panel(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Enhanced HTMX endpoint for conflict detail panel with comprehensive data loading."""
    try:
        # Enhanced query with all related data for complete panel
        conflict = get_object_or_404(
            Conflict.objects.select_related(
                "project",
                "project__organization",
                "resolved_by",
                "reviewed_by",
                "utility",
                "utility2",
            ).prefetch_related("affected_utilities", "affected_utilities__owner"),
            id=conflict_id,
            project__organization=request.user.organization,
        )

        # Get affected utilities with optimized query
        affected_utilities = None
        if hasattr(conflict, "affected_utilities"):
            affected_utilities = conflict.affected_utilities.select_related("owner").all()
        # Fallback to direct utilities if affected_utilities doesn't exist
        elif conflict.utility or conflict.utility2:
            affected_utilities = []
            if conflict.utility:
                affected_utilities.append(conflict.utility)
            if conflict.utility2:
                affected_utilities.append(conflict.utility2)

        # Check if request is from mobile device for responsive rendering
        is_mobile = "Mobile" in request.META.get("HTTP_USER_AGENT", "")

        template_name = "infrastructure/partials/conflict_detail_panel.html"

        context = {
            "conflict": conflict,
            "affected_utilities": affected_utilities,
            "is_mobile": is_mobile,
            "user_can_resolve": request.user.has_perm("infrastructure.change_conflict"),
            "user_can_report": request.user.has_perm("infrastructure.add_conflictresolution"),
        }

        return render(request, template_name, context)

    except Conflict.DoesNotExist:
        logger.warning(f"Conflict {conflict_id} not found for user {request.user.id}")
        return render(
            request,
            "infrastructure/partials/conflict_detail_panel.html",
            {"error": "Conflict not found or access denied"},
            status=404,
        )

    except Exception as e:
        logger.error(f"Error loading conflict detail {conflict_id}: {str(e)}")
        return render(
            request,
            "infrastructure/partials/conflict_detail_panel.html",
            {"error": "Failed to load conflict details. Please try again."},
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def htmx_conflict_tooltip(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """HTMX endpoint for server-side conflict tooltips."""
    try:
        conflict = get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

        return render(
            request,
            "infrastructure/partials/conflict_tooltip.html",
            {
                "conflict": conflict,
            },
        )
    except Exception as e:
        logger.error(f"Error loading conflict tooltip: {e}")
        return HttpResponse('<div class="tooltip-error">Error loading tooltip</div>')


# HTMX Views
@login_required
@require_http_methods(["GET", "POST"])
def htmx_filter_utilities(request: HttpRequest) -> HttpResponse:
    """Filter utilities via HTMX."""
    # Stub implementation
    utilities = Utility.objects.none()  # Empty queryset for now
    return render(request, "infrastructure/partials/utility_list.html", {"utilities": utilities})


@login_required
@require_http_methods(["GET", "POST"])
def htmx_filter_by_project(request: HttpRequest) -> HttpResponse:
    """Filter by project via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/filtered_results.html", {})


@login_required
@require_http_methods(["POST"])
def htmx_run_conflict_detection(request: HttpRequest) -> HttpResponse:
    """Run conflict detection via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/conflict_results.html", {"conflicts": []})


@login_required
@require_http_methods(["GET"])
def htmx_conflict_detail(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Show conflict detail via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/conflict_detail.html", {})


@login_required
@require_http_methods(["POST"])
def htmx_resolve_conflict(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Resolve conflict via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/conflict_resolved.html", {})


@login_required
@require_http_methods(["POST"])
def htmx_spatial_import(request: HttpRequest) -> HttpResponse:
    """Import spatial data via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/import_results.html", {})


@login_required
@require_http_methods(["POST"])
def htmx_cache_refresh(request: HttpRequest) -> HttpResponse:
    """Refresh cache via HTMX."""
    # Stub implementation
    return HttpResponse('<div class="alert alert-success">Cache refreshed</div>')


@login_required
@require_http_methods(["GET"])
def htmx_system_diagnostics(request: HttpRequest) -> HttpResponse:
    """Show system diagnostics via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/diagnostics.html", {})


@login_required
@require_http_methods(["GET"])
def htmx_system_status(request: HttpRequest) -> HttpResponse:
    """Show system status via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/system_status.html", {})


@login_required
@require_http_methods(["GET"])
def htmx_proximity_analysis_form(request: HttpRequest) -> HttpResponse:
    """Show proximity analysis form via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/proximity_form.html", {})


@login_required
@require_http_methods(["GET"])
def htmx_buffer_analysis_form(request: HttpRequest) -> HttpResponse:
    """Show buffer analysis form via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/buffer_form.html", {})


@login_required
@require_http_methods(["GET"])
def htmx_spatial_import_form(request: HttpRequest) -> HttpResponse:
    """Show spatial import form via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/import_form.html", {})


@login_required
@require_http_methods(["POST"])
def htmx_detect_conflicts(request: HttpRequest) -> HttpResponse:
    """Detect conflicts via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/detected_conflicts.html", {"conflicts": []})


@login_required
@require_http_methods(["POST"])
def htmx_flow_analysis(request: HttpRequest) -> HttpResponse:
    """Run flow analysis via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/flow_analysis.html", {})


@login_required
@require_http_methods(["POST"])
def htmx_pressure_analysis(request: HttpRequest) -> HttpResponse:
    """Run pressure analysis via HTMX."""
    # Stub implementation
    return render(request, "infrastructure/partials/pressure_analysis.html", {})


@login_required
@require_http_methods(["GET"])
def htmx_utility_popup(request: HttpRequest, utility_id: int) -> HttpResponse:
    """Get utility popup content via HTMX.

    Retrieves utility details and returns a partial HTML popup
    for seamless map integration.
    """
    utility = get_object_or_404(
        Utility.objects.select_related("project", "created_by", "updated_by"),
        id=utility_id,
        project__organization=request.user.organization,
    )

    return render(
        request,
        "infrastructure/spatial/partials/utility_popup.html",
        {
            "utility": utility,
        },
    )


@login_required
@require_http_methods(["GET"])
def htmx_conflict_popup(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Get conflict popup content via HTMX.

    Retrieves conflict details and returns a partial HTML popup
    with resolution options and affected utilities.
    """
    conflict = get_object_or_404(
        Conflict.objects.select_related("project", "resolved_by").prefetch_related("affected_utilities"),
        id=conflict_id,
        project__organization=request.user.organization,
    )

    return render(
        request,
        "infrastructure/spatial/partials/conflict_popup.html",
        {
            "conflict": conflict,
        },
    )


@login_required
@require_http_methods(["GET"])
def htmx_edit_utility_form(request: HttpRequest, utility_id: int) -> HttpResponse:
    """Get utility edit form via HTMX for inline editing."""
    utility = get_object_or_404(Utility, id=utility_id, project__organization=request.user.organization)

    # Check permissions
    if not request.user.has_perm("infrastructure.change_utility"):
        return HttpResponse(
            '<div class="alert alert-danger">You do not have permission to edit utilities.</div>',
            status=403,
        )

    return render(
        request,
        "infrastructure/spatial/partials/utility_edit_form.html",
        {
            "utility": utility,
        },
    )


@login_required
@require_http_methods(["GET"])
def htmx_export_utility(request: HttpRequest, utility_id: int) -> HttpResponse:
    """Export utility data via HTMX."""
    utility = get_object_or_404(Utility, id=utility_id, project__organization=request.user.organization)

    # Trigger download
    response = HttpResponse(
        content_type="application/json",
        headers={"Content-Disposition": f'attachment; filename="utility_{utility_id}.json"'},
    )

    # Export utility data as JSON
    import json

    from django.core.serializers.json import DjangoJSONEncoder

    data = {
        "id": str(utility.id),
        "name": utility.name,
        "type": utility.type,
        "project": utility.project.name if utility.project else None,
        "geometry": utility.geometry.geojson if utility.geometry else None,
        "properties": {
            "owner": utility.owner,
            "operator": utility.operator,
            "install_date": (utility.install_date.isoformat() if utility.install_date else None),
            "condition": utility.condition,
            "description": utility.description,
        },
    }

    json.dump(data, response, cls=DjangoJSONEncoder, indent=2)
    return response


@login_required
@require_http_methods(["GET"])
def htmx_resolve_conflict_form(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Get conflict resolution form via HTMX."""
    conflict = get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

    # Check permissions
    if not request.user.has_perm("infrastructure.change_conflict"):
        return HttpResponse(
            '<div class="alert alert-danger">You do not have permission to resolve conflicts.</div>',
            status=403,
        )

    return render(
        request,
        "infrastructure/spatial/partials/conflict_resolve_form.html",
        {
            "conflict": conflict,
        },
    )


@login_required
@require_http_methods(["POST"])
def htmx_report_conflict_update(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Report an update for a conflict via HTMX."""
    get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

    # Log the update report
    logger.info(f"Update reported for conflict {conflict_id} by user {request.user.id}")

    # Return success message
    return HttpResponse(
        '<div class="alert alert-success alert-dismissible fade show" role="alert">'
        '<i class="bi bi-check-circle me-2"></i>'
        "Update reported successfully. Our team will review it shortly."
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>'
        "</div>"
    )


# Progressive Enhancement: Non-JavaScript fallback views
@login_required
def conflict_detail_view(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Full conflict detail view for progressive enhancement fallback."""
    conflict = get_object_or_404(
        Conflict.objects.select_related("project", "resolved_by", "reviewed_by"),
        id=conflict_id,
        project__organization=request.user.organization,
    )

    return render(
        request,
        "infrastructure/conflict_detail.html",
        {
            "conflict": conflict,
            "page_title": f"Conflict #{conflict.id}",
        },
    )


@login_required
def conflict_resolve_view(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Conflict resolution form for progressive enhancement fallback."""
    conflict = get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

    if request.method == "POST":
        # Handle form submission
        return redirect("infrastructure:conflict_detail", conflict_id=conflict_id)

    return render(
        request,
        "infrastructure/conflict_resolve.html",
        {
            "conflict": conflict,
            "page_title": f"Resolve Conflict #{conflict.id}",
        },
    )


@login_required
def conflict_report_view(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Conflict report form for progressive enhancement fallback."""
    conflict = get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

    if request.method == "POST":
        messages.success(request, "Update reported successfully.")
        return redirect("infrastructure:conflict_detail", conflict_id=conflict_id)

    return render(
        request,
        "infrastructure/conflict_report.html",
        {
            "conflict": conflict,
            "page_title": f"Report Update for Conflict #{conflict.id}",
        },
    )


@login_required
def conflict_map_view(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """Map view focused on specific conflict for progressive enhancement fallback."""
    conflict = get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

    return render(
        request,
        "infrastructure/map.html",
        {
            "focused_conflict_id": conflict_id,
            "conflict": conflict,
            "page_title": f"Map View - Conflict #{conflict.id}",
        },
    )


@login_required
def conflict_list_view(request: HttpRequest) -> HttpResponse:
    """Conflict list view for progressive enhancement fallback."""
    conflicts = (
        Conflict.objects.filter(project__organization=request.user.organization)
        .select_related("project", "resolved_by")
        .order_by("-created_at")
    )

    return render(
        request,
        "infrastructure/conflict_list.html",
        {
            "conflicts": conflicts,
            "page_title": "All Conflicts",
        },
    )


@login_required
def utility_detail_view(request: HttpRequest, utility_id: int) -> HttpResponse:
    """Utility detail view for progressive enhancement fallback."""
    utility = get_object_or_404(Utility, id=utility_id)

    return render(
        request,
        "infrastructure/utility_detail.html",
        {
            "utility": utility,
            "page_title": f"Utility: {utility.name}",
        },
    )


@login_required
@require_http_methods(["POST"])
def htmx_update_utility(request: HttpRequest, utility_id: int) -> HttpResponse:
    """Update utility via HTMX inline form."""
    utility = get_object_or_404(Utility, id=utility_id, project__organization=request.user.organization)

    # Check permissions
    if not request.user.has_perm("infrastructure.change_utility"):
        return HttpResponse(
            '<div class="alert alert-danger">You do not have permission to edit utilities.</div>',
            status=403,
        )

    # Update utility fields
    utility.name = request.POST.get("name", utility.name)
    utility.owner = request.POST.get("owner", utility.owner)
    utility.operator = request.POST.get("operator", utility.operator)
    utility.condition = request.POST.get("condition", utility.condition)
    utility.maintenance_contact = request.POST.get("maintenance_contact", utility.maintenance_contact)
    utility.description = request.POST.get("description", utility.description)
    utility.updated_by = request.user

    try:
        utility.save()

        # Return updated popup content
        return render(
            request,
            "infrastructure/spatial/partials/utility_popup.html",
            {
                "utility": utility,
            },
        )
    except Exception as e:
        logger.error(f"Error updating utility {utility_id}: {str(e)}")
        return HttpResponse(
            '<div class="alert alert-danger">An error occurred while updating the utility.</div>',
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def htmx_conflicts_view_mode(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for toggling conflict visualization mode."""
    try:
        # Get view mode from request
        view_mode = request.POST.get("view_mode", "markers")

        # Validate view mode
        valid_modes = ["markers", "clusters", "heatmap", "both"]
        if view_mode not in valid_modes:
            view_mode = "markers"

        # Store view mode preference in session
        request.session["conflict_view_mode"] = view_mode

        # Return toggle control partial with updated state
        context = {
            "current_view_mode": view_mode,
            "view_modes": [
                {"value": "markers", "label": "Markers", "icon": "geo-alt"},
                {"value": "clusters", "label": "Clusters", "icon": "collection"},
                {"value": "heatmap", "label": "Heat Map", "icon": "thermometer-half"},
                {"value": "both", "label": "Both", "icon": "layers"},
            ],
        }

        return render(request, "infrastructure/partials/conflict_view_toggle.html", context)

    except Exception as e:
        logger.error(f"Error switching conflict view mode: {str(e)}")
        return HttpResponse(
            '<div class="alert alert-danger">Failed to switch view mode.</div>',
            status=500,
        )


@login_required
@require_http_methods(["GET"])
def htmx_conflicts_heatmap_data(request: HttpRequest) -> JsonResponse:
    """HTMX endpoint for heat map specific data processing."""
    try:
        # Get conflicts for user's organization
        conflicts = Conflict.objects.filter(project__organization=request.user.organization).select_related("project")

        # Apply any filters
        conflict_type = request.GET.get("type", "all")
        risk_level = request.GET.get("risk_level", "all")
        status = request.GET.get("status", "all")

        if conflict_type != "all":
            conflicts = conflicts.filter(conflict_type=conflict_type)

        if risk_level != "all":
            if risk_level == "high":
                conflicts = conflicts.filter(risk_score__gte=80)
            elif risk_level == "medium":
                conflicts = conflicts.filter(risk_score__gte=40, risk_score__lt=80)
            elif risk_level == "low":
                conflicts = conflicts.filter(risk_score__lt=40)

        if status != "all":
            conflicts = conflicts.filter(status=status)

        # Generate enhanced heat map data
        features = []
        for conflict in conflicts:
            if conflict.location:
                # Ensure we have proper point geometry for heat map
                geom_point = conflict.location
                if hasattr(conflict.location, "centroid"):
                    geom_point = conflict.location.centroid

                feature = {
                    "type": "Feature",
                    "id": conflict.id,
                    "geometry": {
                        "type": "Point",
                        "coordinates": [geom_point.x, geom_point.y],
                    },
                    "properties": {
                        "id": conflict.id,
                        "risk_score": conflict.risk_score or 50,
                        "conflict_type": conflict.conflict_type,
                        "severity": conflict.severity,
                        "status": conflict.status,
                        "title": f"{conflict.conflict_type.title()} Conflict",
                        "description": (conflict.description[:100] if conflict.description else "No description"),
                        "project_name": conflict.project.name,
                        # Heat map specific weight calculation
                        "heat_weight": min(max((conflict.risk_score or 50) / 100, 0.1), 1.0),
                        "density_factor": 1.0,  # Could be enhanced based on conflict clustering
                    },
                }
                features.append(feature)

        response_data = {
            "type": "FeatureCollection",
            "features": features,
            "meta": {
                "total_count": len(features),
                "high_risk_count": len([f for f in features if f["properties"]["risk_score"] >= 80]),
                "medium_risk_count": len([f for f in features if 40 <= f["properties"]["risk_score"] < 80]),
                "low_risk_count": len([f for f in features if f["properties"]["risk_score"] < 40]),
                "view_mode": "heatmap",
            },
        }

        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"Error loading heat map data: {str(e)}")
        return JsonResponse({"error": "Failed to load heat map data"}, status=500)


@login_required
@require_http_methods(["POST"])
def htmx_zoom_to_conflict(request: HttpRequest, conflict_id: int) -> HttpResponse:
    """HTMX endpoint for zooming to specific conflict on map."""
    try:
        # Verify conflict exists and user has access
        conflict = get_object_or_404(Conflict, id=conflict_id, project__organization=request.user.organization)

        # Return HTMX response that triggers map zoom via server-side event
        response = HttpResponse("")
        response["HX-Trigger"] = f"zoom-to-conflict-{conflict_id}"

        # Add conflict coordinates as custom header for JavaScript-free map updates
        if conflict.location:
            response["HX-Trigger-Zoom-Coords"] = f"{conflict.location.x},{conflict.location.y}"

        return response

    except Exception as e:
        logger.error(f"Error zooming to conflict {conflict_id}: {str(e)}")
        return HttpResponse(
            '<div class="alert alert-warning">Unable to zoom to conflict location.</div>',
            status=500,
        )


@login_required
@require_http_methods(["POST"])
def htmx_3d_recording_export(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for handling 3D recording export requests."""
    try:
        import base64

        from django.core.files.storage import default_storage

        # Get recording data from request
        data = json.loads(request.body)
        recording_data = data.get("recordingData")
        project_id = data.get("projectId")
        export_format = data.get("format", "mp4")
        export_settings = data.get("exportSettings", {})

        if not recording_data or not project_id:
            return JsonResponse(
                {"status": "error", "error": "Missing recording data or project ID"},
                status=400,
            )

        # Verify project access
        project = get_object_or_404(Project, pk=project_id, organization=request.user.organization)

        # Decode base64 video data
        if recording_data.startswith("data:"):
            # Remove data URL prefix
            header, data = recording_data.split(",", 1)
            video_bytes = base64.b64decode(data)
        else:
            video_bytes = base64.b64decode(recording_data)

        # Generate unique filename
        timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
        filename = f"3d_recording_{project.name}_{timestamp}.{export_format}"
        safe_filename = filename.replace(" ", "_").replace("/", "_")

        # Save recording to storage
        file_path = f"3d_recordings/{project.organization.slug}/{safe_filename}"

        try:
            saved_path = default_storage.save(file_path, video_bytes)
            download_url = default_storage.url(saved_path)
        except Exception as save_error:
            logger.error(f"Failed to save 3D recording: {str(save_error)}")
            return JsonResponse(
                {"status": "error", "error": "Failed to save recording file"},
                status=500,
            )

        # Log recording export for audit trail
        logger.info(f"3D recording exported: {safe_filename} by user {request.user.id} for project {project_id}")

        response_data = {
            "status": "success",
            "message": "3D recording exported successfully",
            "filename": safe_filename,
            "downloadUrl": download_url,
            "fileSize": len(video_bytes),
            "exportSettings": export_settings,
        }

        return JsonResponse(response_data)

    except json.JSONDecodeError:
        return JsonResponse({"status": "error", "error": "Invalid JSON data"}, status=400)
    except Exception as e:
        logger.error(f"Error exporting 3D recording: {str(e)}")
        return JsonResponse({"status": "error", "error": "Failed to export recording"}, status=500)


@login_required
@require_http_methods(["POST"])
def htmx_3d_recording_log(request: HttpRequest) -> HttpResponse:
    """HTMX endpoint for logging 3D recording events and analytics."""
    try:
        data = json.loads(request.body)
        event_type = data.get("eventType")  # 'started', 'stopped', 'exported', 'error'
        project_id = data.get("projectId")
        recording_data = data.get("recordingData", {})

        if not event_type or not project_id:
            return JsonResponse(
                {"status": "error", "error": "Missing event type or project ID"},
                status=400,
            )

        # Verify project access
        project = get_object_or_404(Project, pk=project_id, organization=request.user.organization)

        # Log event with context
        log_context = {
            "user_id": request.user.id,
            "username": request.user.username,
            "project_id": project_id,
            "project_name": project.name,
            "organization": project.organization.name,
            "event_type": event_type,
            "timestamp": timezone.now().isoformat(),
            "recording_data": recording_data,
        }

        if event_type == "started":
            logger.info(
                f"3D recording started for project {project_id} by user {request.user.id}",
                extra=log_context,
            )
        elif event_type == "stopped":
            logger.info(
                f"3D recording stopped for project {project_id} by user {request.user.id}",
                extra=log_context,
            )
        elif event_type == "exported":
            logger.info(
                f"3D recording exported for project {project_id} by user {request.user.id}",
                extra=log_context,
            )
        elif event_type == "error":
            error_message = recording_data.get("error", "Unknown error")
            logger.error(
                f"3D recording error for project {project_id}: {error_message}",
                extra=log_context,
            )

        # Optional: Store in database for analytics
        # You could create a RecordingEvent model to track usage patterns

        return JsonResponse({"status": "success", "message": "Event logged successfully"})

    except json.JSONDecodeError:
        return JsonResponse({"status": "error", "error": "Invalid JSON data"}, status=400)
    except Exception as e:
        logger.error(f"Error logging 3D recording event: {str(e)}")
        return JsonResponse({"status": "error", "error": "Failed to log event"}, status=500)
