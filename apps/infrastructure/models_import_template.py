"""
Advanced Import Template Models for Infrastructure App.

Extends the existing file import system with sophisticated field mapping,
data transformation, and validation preview capabilities.
"""

import logging
import re
import uuid
from dataclasses import dataclass
from datetime import date, datetime
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, List, Union
from django.db.models import QuerySet

from django.contrib.gis.db import models
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from apps.common.models import BaseModel

logger = logging.getLogger(__name__)


@dataclass
class ValidationPreviewResult:
    """Result of validation preview for a single data row."""

    row_number: int
    is_valid: bool
    errors: List[Dict[str, Any]]
    warnings: List[Dict[str, Any]]
    transformed_data: Dict[str, Any]
    quality_score: float  # 0.0 to 100.0


@dataclass
class ValidationPreviewSummary:
    """Summary statistics for validation preview."""

    total_rows: int
    valid_rows: int
    invalid_rows: int
    error_count: int
    warning_count: int
    overall_quality_score: float
    common_errors: List[Dict[str, Any]]
    field_statistics: Dict[str, Dict[str, Any]]


class ImportTemplate(BaseModel):
    """
    Advanced import template for sophisticated field mapping and data transformation.

    Provides comprehensive import configuration with visual field mapping,
    transformation pipelines, validation rules, and preview capabilities.
    """

    # Template types
    TEMPLATE_TYPE_CHOICES = [
        ("standard", _("Standard - General purpose template")),
        ("cad", _("CAD - Specialized for CAD file imports")),
        ("gis", _("GIS - Specialized for GIS data imports")),
        ("csv", _("CSV - Structured data imports")),
        ("excel", _("Excel - Multi-sheet Excel imports")),
        ("custom", _("Custom - User-defined template")),
        ("api", _("API - For programmatic imports")),
    ]

    # Data source types
    SOURCE_TYPE_CHOICES = [
        ("file_upload", _("File Upload")),
        ("url_endpoint", _("URL Endpoint")),
        ("database_connection", _("Database Connection")),
        ("api_endpoint", _("API Endpoint")),
        ("ftp_location", _("FTP Location")),
        ("scheduled_feed", _("Scheduled Data Feed")),
    ]

    # Mapping strategy types
    MAPPING_STRATEGY_CHOICES = [
        ("manual", _("Manual - User defines all mappings")),
        ("auto_detect", _("Auto-detect - Intelligent field matching")),
        ("hybrid", _("Hybrid - Auto-detect with manual override")),
        ("template_based", _("Template-based - Use predefined template")),
    ]

    # Basic template information
    template_id = models.UUIDField(
        _("template ID"),
        default=uuid.uuid4,
        unique=True,
        help_text=_("Unique identifier for the import template"),
    )
    name = models.CharField(
        _("template name"),
        max_length=255,
        help_text=_("Descriptive name for the import template"),
    )
    description = models.TextField(
        _("description"),
        blank=True,
        help_text=_("Detailed description of the template purpose and usage"),
    )
    version = models.CharField(
        _("template version"),
        max_length=20,
        default="1.0.0",
        validators=[
            RegexValidator(
                regex=r"^\d+\.\d+\.\d+$",
                message=_("Version must be in semantic versioning format (e.g., 1.0.0)"),
            )
        ],
        help_text=_("Template version for change tracking"),
    )

    # Template configuration
    template_type = models.CharField(
        _("template type"),
        max_length=20,
        choices=TEMPLATE_TYPE_CHOICES,
        default="standard",
    )
    source_type = models.CharField(
        _("source type"),
        max_length=30,
        choices=SOURCE_TYPE_CHOICES,
        default="file_upload",
    )
    mapping_strategy = models.CharField(
        _("mapping strategy"),
        max_length=20,
        choices=MAPPING_STRATEGY_CHOICES,
        default="hybrid",
    )

    # Supported file formats (for file-based imports)
    supported_formats = models.JSONField(
        _("supported file formats"),
        default=list,
        blank=True,
        help_text=_('List of supported file formats (e.g., ["dxf", "shp", "csv", "xlsx"])'),
    )

    # Source schema definition
    source_schema = models.JSONField(
        _("source schema"),
        default=dict,
        blank=True,
        help_text=_("Definition of source data structure and field types"),
    )

    # Target schema definition
    target_schema = models.JSONField(
        _("target schema"),
        default=dict,
        blank=True,
        help_text=_("Definition of target data structure for import"),
    )

    # Field mapping configuration
    field_mappings = models.JSONField(
        _("field mappings"),
        default=list,
        blank=True,
        help_text=_("Array of field mapping configurations with transformations"),
    )

    # Transformation rules and pipeline
    transformation_rules = models.JSONField(
        _("transformation rules"),
        default=dict,
        blank=True,
        help_text=_("Data transformation rules and pipeline configuration"),
    )

    # Validation rules and thresholds
    validation_rules = models.JSONField(
        _("validation rules"),
        default=dict,
        blank=True,
        help_text=_("Validation rules for data quality and consistency checks"),
    )

    # Default values and fallbacks
    default_values = models.JSONField(
        _("default values"),
        default=dict,
        blank=True,
        help_text=_("Default values for missing or invalid fields"),
    )

    # Duplicate detection configuration
    duplicate_detection_config = models.JSONField(
        _("duplicate detection configuration"),
        default=dict,
        blank=True,
        help_text=_("Configuration for duplicate record detection and handling"),
    )

    # Error handling configuration
    error_handling_config = models.JSONField(
        _("error handling configuration"),
        default=dict,
        blank=True,
        help_text=_("Configuration for error handling and recovery strategies"),
    )

    # Preview configuration
    preview_config = models.JSONField(
        _("preview configuration"),
        default=dict,
        blank=True,
        help_text=_("Configuration for data preview and validation display"),
    )

    # Template metadata
    tags = models.JSONField(
        _("tags"),
        default=list,
        blank=True,
        help_text=_("Tags for categorizing and searching templates"),
    )

    # Usage tracking
    usage_count = models.PositiveIntegerField(
        _("usage count"),
        default=0,
        help_text=_("Number of times this template has been used"),
    )
    last_used_at = models.DateTimeField(_("last used at"), null=True, blank=True)

    # Template sharing and permissions
    is_public = models.BooleanField(
        _("is public"),
        default=False,
        help_text=_("Whether this template is publicly available"),
    )
    is_system_template = models.BooleanField(
        _("is system template"),
        default=False,
        help_text=_("Whether this is a built-in system template"),
    )

    # Ownership
    created_by = models.ForeignKey(
        "authentication.User",
        on_delete=models.CASCADE,
        related_name="import_templates",
        verbose_name=_("created by"),
    )
    organization = models.ForeignKey(
        "authentication.Organization",
        on_delete=models.CASCADE,
        related_name="import_templates",
        verbose_name=_("organization"),
        null=True,
        blank=True,
    )

    class Meta:
        app_label = "infrastructure"
        db_table = "infrastructure_import_template"
        verbose_name = _("import template")
        verbose_name_plural = _("import templates")
        ordering = ["-updated_at", "name"]
        indexes = [
            models.Index(fields=["created_by", "-updated_at"]),
            models.Index(fields=["organization", "is_public"]),
            models.Index(fields=["template_type", "source_type"]),
            models.Index(fields=["-usage_count"]),
            models.Index(fields=["is_system_template", "is_public"]),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["name", "created_by", "organization"],
                name="unique_template_name_per_user_org",
            ),
        ]

    def __str__(self):
        return f"{self.name} (v{self.version})"

    def save(self, *args, **kwargs):
        """Override save to validate configuration and update usage tracking."""
        # Validate configuration
        self.clean()

        # Update last_used_at when usage_count increases
        if self.pk:
            old_instance = ImportTemplate.objects.get(pk=self.pk)
            if self.usage_count > old_instance.usage_count:
                self.last_used_at = timezone.now()

        super().save(*args, **kwargs)

    def clean(self):
        """Validate template configuration."""
        errors = {}

        # Validate supported formats
        if self.source_type == "file_upload" and not self.supported_formats:
            errors["supported_formats"] = _("Supported formats are required for file upload templates")

        # Validate field mappings structure
        if self.field_mappings:
            for _i, mapping in enumerate(self.field_mappings):
                if not isinstance(mapping, dict):
                    errors["field_mappings"] = _("Field mappings must be objects")
                    break
                if "source_field" not in mapping or "target_field" not in mapping:
                    errors["field_mappings"] = _("Field mappings must have source_field and target_field")
                    break

        # Validate transformation rules structure
        if self.transformation_rules:
            if not isinstance(self.transformation_rules, dict):
                errors["transformation_rules"] = _("Transformation rules must be an object")

        # Validate schema structures
        for schema_field in ["source_schema", "target_schema"]:
            schema = getattr(self, schema_field)
            if schema and not isinstance(schema, dict):
                errors[schema_field] = _("Schema must be an object")

        if errors:
            raise ValidationError(errors)

    def use_template(self):
        """Mark template as used and update usage statistics."""
        self.usage_count += 1
        self.last_used_at = timezone.now()
        self.save(update_fields=["usage_count", "last_used_at"])

    def get_field_mapping_summary(self) -> Dict[str, Any]:
        """Get summary of field mappings for display."""
        if not self.field_mappings:
            return {
                "total_mappings": 0,
                "source_fields": [],
                "target_fields": [],
                "transformation_count": 0,
                "validation_count": 0,
            }

        source_fields = []
        target_fields = []
        transformation_count = 0
        validation_count = 0

        for mapping in self.field_mappings:
            if "source_field" in mapping:
                source_fields.append(mapping["source_field"])
            if "target_field" in mapping:
                target_fields.append(mapping["target_field"])
            if mapping.get("transformations"):
                transformation_count += len(mapping["transformations"])
            if mapping.get("validation"):
                validation_count += 1

        return {
            "total_mappings": len(self.field_mappings),
            "source_fields": source_fields,
            "target_fields": target_fields,
            "transformation_count": transformation_count,
            "validation_count": validation_count,
        }

    def apply_field_mappings(self, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply field mappings and transformations to source data.

        Args:
            source_data: Dictionary of source field data

        Returns:
            Dictionary of transformed target data
        """
        try:
            target_data = {}

            for mapping in self.field_mappings:
                source_field = mapping.get("source_field")
                target_field = mapping.get("target_field")

                if not source_field or not target_field:
                    continue

                # Get source value
                source_value = source_data.get(source_field)

                # Apply transformations
                transformed_value = self._apply_transformations(source_value, mapping.get("transformations", []))

                # Apply default value if needed
                if transformed_value is None and mapping.get("default_value") is not None:
                    transformed_value = mapping["default_value"]

                # Set target value
                target_data[target_field] = transformed_value

            # Apply global default values
            for field, default_value in self.default_values.items():
                if field not in target_data or target_data[field] is None:
                    target_data[field] = default_value

            return target_data

        except Exception as e:
            logger.error(f"Error applying field mappings in template {self.name}: {e}")
            raise ValidationError(f"Field mapping error: {e}")

    def _apply_transformations(self, value: Any, transformations: List[Dict[str, Any]]) -> Any:
        """
        Apply a series of transformations to a value.

        Args:
            value: Original value
            transformations: List of transformation configurations

        Returns:
            Transformed value
        """
        if not transformations:
            return value

        current_value = value

        for transform in transformations:
            transform_type = transform.get("type")
            transform_params = transform.get("params", {})

            try:
                if transform_type == "string_transform":
                    current_value = self._apply_string_transform(current_value, transform_params)
                elif transform_type == "numeric_transform":
                    current_value = self._apply_numeric_transform(current_value, transform_params)
                elif transform_type == "date_transform":
                    current_value = self._apply_date_transform(current_value, transform_params)
                elif transform_type == "regex_transform":
                    current_value = self._apply_regex_transform(current_value, transform_params)
                elif transform_type == "lookup_transform":
                    current_value = self._apply_lookup_transform(current_value, transform_params)
                elif transform_type == "conditional_transform":
                    current_value = self._apply_conditional_transform(current_value, transform_params)
                elif transform_type == "unit_conversion":
                    current_value = self._apply_unit_conversion(current_value, transform_params)
                elif transform_type == "custom_function":
                    current_value = self._apply_custom_function(current_value, transform_params)
                else:
                    logger.warning(f"Unknown transformation type: {transform_type}")

            except Exception as e:
                logger.error(f"Error applying transformation {transform_type}: {e}")
                # Continue with original value on transformation error
                pass

        return current_value

    def _apply_string_transform(self, value: Any, params: Dict[str, Any]) -> str:
        """Apply string transformations."""
        if value is None:
            return None

        str_value = str(value)
        operation = params.get("operation")

        if operation == "uppercase":
            return str_value.upper()
        elif operation == "lowercase":
            return str_value.lower()
        elif operation == "title_case":
            return str_value.title()
        elif operation == "capitalize":
            return str_value.capitalize()
        elif operation == "trim":
            return str_value.strip()
        elif operation == "trim_left":
            return str_value.lstrip()
        elif operation == "trim_right":
            return str_value.rstrip()
        elif operation == "replace":
            return str_value.replace(params.get("find", ""), params.get("replace", ""))
        elif operation == "substring":
            start = params.get("start", 0)
            end = params.get("end")
            return str_value[start:end] if end else str_value[start:]
        elif operation == "pad_left":
            width = params.get("width", 10)
            char = params.get("char", "0")
            return str_value.rjust(width, char)
        elif operation == "pad_right":
            width = params.get("width", 10)
            char = params.get("char", " ")
            return str_value.ljust(width, char)
        elif operation == "reverse":
            return str_value[::-1]
        elif operation == "remove_spaces":
            return str_value.replace(" ", "")
        elif operation == "collapse_spaces":
            return re.sub(r"\s+", " ", str_value).strip()
        elif operation == "slugify":
            # Convert to URL-friendly slug
            import unicodedata

            value = unicodedata.normalize("NFKD", str_value)
            value = value.encode("ascii", "ignore").decode("ascii")
            value = re.sub(r"[^a-zA-Z0-9\s-]", "", value).strip().lower()
            return re.sub(r"[-\s]+", "-", value)
        elif operation == "extract_numbers":
            return "".join(re.findall(r"\d", str_value))
        elif operation == "extract_letters":
            return "".join(re.findall(r"[a-zA-Z]", str_value))
        elif operation == "extract_alphanumeric":
            return "".join(re.findall(r"[a-zA-Z0-9]", str_value))
        elif operation == "camel_case":
            # Convert to camelCase
            words = re.sub(r"[^a-zA-Z0-9]", " ", str_value).split()
            if not words:
                return str_value
            return words[0].lower() + "".join(word.capitalize() for word in words[1:])
        elif operation == "snake_case":
            # Convert to snake_case
            s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", str_value)
            return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()
        elif operation == "kebab_case":
            # Convert to kebab-case
            s1 = re.sub("(.)([A-Z][a-z]+)", r"\1-\2", str_value)
            return re.sub("([a-z0-9])([A-Z])", r"\1-\2", s1).lower()
        elif operation == "encode_base64":
            import base64

            return base64.b64encode(str_value.encode()).decode()
        elif operation == "decode_base64":
            import base64

            try:
                return base64.b64decode(str_value).decode()
            except Exception:
                return str_value
        elif operation == "url_encode":
            from urllib.parse import quote

            return quote(str_value)
        elif operation == "url_decode":
            from urllib.parse import unquote

            return unquote(str_value)
        elif operation == "string_convert":
            # Simple string conversion for numeric types
            return str_value

        return str_value

    def _apply_numeric_transform(self, value: Any, params: Dict[str, Any]) -> Union[int, float, Decimal, None]:
        """Apply numeric transformations."""
        if value is None:
            return None

        try:
            # Convert to appropriate numeric type
            if params.get("type") == "integer":
                num_value = int(float(value))
            elif params.get("type") == "decimal":
                num_value = Decimal(str(value))
            else:
                num_value = float(value)

            operation = params.get("operation")

            if operation == "multiply":
                return num_value * params.get("factor", 1)
            elif operation == "divide":
                divisor = params.get("divisor", 1)
                return num_value / divisor if divisor != 0 else None
            elif operation == "add":
                return num_value + params.get("addend", 0)
            elif operation == "subtract":
                return num_value - params.get("subtrahend", 0)
            elif operation == "round":
                decimals = params.get("decimals", 0)
                return round(num_value, decimals)
            elif operation == "absolute":
                return abs(num_value)
            elif operation == "clamp":
                min_val = params.get("min_value")
                max_val = params.get("max_value")
                if min_val is not None:
                    num_value = max(num_value, min_val)
                if max_val is not None:
                    num_value = min(num_value, max_val)
                return num_value

            return num_value

        except (ValueError, InvalidOperation, TypeError):
            return None

    def _apply_date_transform(self, value: Any, params: Dict[str, Any]) -> Union[date, datetime, str, None]:
        """Apply date transformations with enhanced parsing and operations."""
        if value is None:
            return None

        try:
            # Enhanced date parsing with multiple format detection
            if isinstance(value, (date, datetime)):
                date_value = value
            elif isinstance(value, str):
                date_value = self._parse_date_auto(value, params.get("input_format"))
                if date_value is None:
                    return None
            else:
                return None

            operation = params.get("operation")

            if operation == "format":
                output_format = params.get("output_format", "%Y-%m-%d")
                return date_value.strftime(output_format)
            elif operation == "parse":
                # Just return parsed date
                return date_value
            elif operation == "add_days":
                from datetime import timedelta

                days = params.get("days", 0)
                return date_value + timedelta(days=days)
            elif operation == "add_months":
                from dateutil.relativedelta import relativedelta

                months = params.get("months", 0)
                return date_value + relativedelta(months=months)
            elif operation == "add_years":
                from dateutil.relativedelta import relativedelta

                years = params.get("years", 0)
                return date_value + relativedelta(years=years)
            elif operation == "subtract_days":
                from datetime import timedelta

                days = params.get("days", 0)
                return date_value - timedelta(days=days)
            elif operation == "extract_year":
                return date_value.year
            elif operation == "extract_month":
                return date_value.month
            elif operation == "extract_day":
                return date_value.day
            elif operation == "extract_weekday":
                return date_value.weekday()  # 0=Monday, 6=Sunday
            elif operation == "extract_quarter":
                return (date_value.month - 1) // 3 + 1
            elif operation == "to_iso":
                return date_value.isoformat()
            elif operation == "to_timestamp":
                return int(date_value.timestamp())
            elif operation == "to_epoch":
                from datetime import timezone

                return int(date_value.replace(tzinfo=timezone.utc).timestamp())
            elif operation == "start_of_month":
                return date_value.replace(day=1)
            elif operation == "end_of_month":
                from calendar import monthrange

                last_day = monthrange(date_value.year, date_value.month)[1]
                return date_value.replace(day=last_day)
            elif operation == "start_of_year":
                return date_value.replace(month=1, day=1)
            elif operation == "end_of_year":
                return date_value.replace(month=12, day=31)
            elif operation == "age_in_years":
                from datetime import date as date_class

                today = date_class.today()
                return today.year - date_value.year - ((today.month, today.day) < (date_value.month, date_value.day))
            elif operation == "business_days_from_now":
                # Calculate business days from current date
                from datetime import date as date_class

                import numpy as np

                today = date_class.today()
                return np.busday_count(
                    today,
                    (date_value.date() if isinstance(date_value, datetime) else date_value),
                )

            return date_value

        except (ValueError, TypeError, ImportError):
            return None

    def _parse_date_auto(self, date_string: str, preferred_format: str = None) -> Union[datetime, None]:
        """Auto-detect and parse date strings in various formats."""
        if not date_string or not isinstance(date_string, str):
            return None

        # Clean the date string
        date_string = date_string.strip()

        # Try preferred format first
        if preferred_format:
            try:
                return datetime.strptime(date_string, preferred_format)
            except ValueError:
                pass

        # Common date formats to try
        formats = [
            "%Y-%m-%d",  # 2023-12-25
            "%m/%d/%Y",  # 12/25/2023
            "%d/%m/%Y",  # 25/12/2023
            "%Y/%m/%d",  # 2023/12/25
            "%m-%d-%Y",  # 12-25-2023
            "%d-%m-%Y",  # 25-12-2023
            "%Y.%m.%d",  # 2023.12.25
            "%m.%d.%Y",  # 12.25.2023
            "%d.%m.%Y",  # 25.12.2023
            "%Y%m%d",  # 20231225
            "%m%d%Y",  # 12252023
            "%d%m%Y",  # 25122023
            "%B %d, %Y",  # December 25, 2023
            "%b %d, %Y",  # Dec 25, 2023
            "%d %B %Y",  # 25 December 2023
            "%d %b %Y",  # 25 Dec 2023
            "%Y-%m-%d %H:%M:%S",  # 2023-12-25 14:30:00
            "%m/%d/%Y %H:%M:%S",  # 12/25/2023 14:30:00
            "%Y-%m-%dT%H:%M:%S",  # 2023-12-25T14:30:00 (ISO format)
            "%Y-%m-%dT%H:%M:%SZ",  # 2023-12-25T14:30:00Z (ISO with Z)
        ]

        # Try each format
        for fmt in formats:
            try:
                return datetime.strptime(date_string, fmt)
            except ValueError:
                continue

        # Try parsing with dateutil as last resort
        try:
            from dateutil import parser

            return parser.parse(date_string)
        except (ImportError, ValueError, TypeError):
            pass

        return None

    def _apply_regex_transform(self, value: Any, params: Dict[str, Any]) -> Union[str, None]:
        """Apply regex transformations."""
        if value is None:
            return None

        try:
            str_value = str(value)
            pattern = params.get("pattern")
            replacement = params.get("replacement", "")
            operation = params.get("operation", "replace")

            if not pattern:
                return str_value

            if operation == "replace":
                return re.sub(pattern, replacement, str_value)
            elif operation == "extract":
                match = re.search(pattern, str_value)
                if match:
                    group = params.get("group", 0)
                    return match.group(group)
                return None
            elif operation == "split":
                return re.split(pattern, str_value)
            elif operation == "match":
                return bool(re.match(pattern, str_value))

            return str_value

        except re.error:
            return value

    def _apply_lookup_transform(self, value: Any, params: Dict[str, Any]) -> Any:
        """Apply lookup table transformations."""
        if value is None:
            return None

        lookup_table = params.get("lookup_table", {})
        default_value = params.get("default_value")
        case_sensitive = params.get("case_sensitive", True)

        lookup_key = str(value)
        if not case_sensitive:
            lookup_key = lookup_key.lower()
            lookup_table = {k.lower(): v for k, v in lookup_table.items()}

        return lookup_table.get(lookup_key, default_value)

    def _apply_conditional_transform(self, value: Any, params: Dict[str, Any]) -> Any:
        """Apply conditional transformations."""
        conditions = params.get("conditions", [])
        default_value = params.get("default_value")

        for condition in conditions:
            operator = condition.get("operator")
            compare_value = condition.get("value")
            result_value = condition.get("result")

            if self._evaluate_condition(value, operator, compare_value):
                return result_value

        return default_value

    def _evaluate_condition(self, value: Any, operator: str, compare_value: Any) -> bool:
        """Evaluate a single condition."""
        try:
            if operator == "equals":
                return value == compare_value
            elif operator == "not_equals":
                return value != compare_value
            elif operator == "greater_than":
                return float(value) > float(compare_value)
            elif operator == "less_than":
                return float(value) < float(compare_value)
            elif operator == "contains":
                return str(compare_value) in str(value)
            elif operator == "starts_with":
                return str(value).startswith(str(compare_value))
            elif operator == "ends_with":
                return str(value).endswith(str(compare_value))
            elif operator == "is_empty":
                return value is None or str(value).strip() == ""
            elif operator == "is_not_empty":
                return value is not None and str(value).strip() != ""

            return False

        except (ValueError, TypeError):
            return False

    def _apply_unit_conversion(self, value: Any, params: Dict[str, Any]) -> Union[float, None]:
        """Apply unit conversions."""
        if value is None:
            return None

        try:
            num_value = float(value)
            from_unit = params.get("from_unit")
            to_unit = params.get("to_unit")

            # Define conversion factors (base unit conversions)
            conversions = {
                # Length conversions (to meters)
                "mm": 0.001,
                "cm": 0.01,
                "m": 1.0,
                "km": 1000.0,
                "in": 0.0254,
                "ft": 0.3048,
                "yd": 0.9144,
                "mi": 1609.34,
                # Area conversions (to square meters)
                "sq_mm": 0.000001,
                "sq_cm": 0.0001,
                "sq_m": 1.0,
                "sq_km": 1000000.0,
                "sq_in": 0.00064516,
                "sq_ft": 0.092903,
                "acre": 4046.86,
                # Volume conversions (to cubic meters)
                "cu_mm": 0.000000001,
                "cu_cm": 0.000001,
                "cu_m": 1.0,
                "liter": 0.001,
                "gallon": 0.00378541,
                "cu_ft": 0.0283168,
            }

            if from_unit in conversions and to_unit in conversions:
                # Convert to base unit, then to target unit
                base_value = num_value * conversions[from_unit]
                result = base_value / conversions[to_unit]

                # Round to reasonable precision
                decimals = params.get("decimals", 6)
                return round(result, decimals)

            return num_value

        except (ValueError, TypeError):
            return None

    def _apply_custom_function(self, value: Any, params: Dict[str, Any]) -> Any:
        """Apply custom transformation functions."""
        function_name = params.get("function")
        function_params = params.get("params", {})

        # Define custom functions
        if function_name == "generate_uuid":
            return str(uuid.uuid4())
        elif function_name == "current_timestamp":
            return timezone.now().isoformat()
        elif function_name == "current_date":
            return timezone.now().date().isoformat()
        elif function_name == "hash_value":
            import hashlib

            algorithm = function_params.get("algorithm", "md5")
            if algorithm == "md5":
                return hashlib.md5(str(value).encode(), usedforsecurity=False).hexdigest()
            elif algorithm == "sha1":
                return hashlib.sha1(str(value).encode(), usedforsecurity=False).hexdigest()
            elif algorithm == "sha256":
                return hashlib.sha256(str(value).encode()).hexdigest()
            return hashlib.md5(str(value).encode(), usedforsecurity=False).hexdigest()
        elif function_name == "format_phone":
            # Enhanced phone number formatting with country support
            digits = re.sub(r"\D", "", str(value))
            country = function_params.get("country", "US")

            if country == "US" and len(digits) == 10:
                return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
            elif country == "US" and len(digits) == 11 and digits[0] == "1":
                return f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
            elif country == "UK" and len(digits) == 11:
                return f"+44 {digits[1:5]} {digits[5:8]} {digits[8:]}"
            return value
        elif function_name == "format_ssn":
            # Simple SSN formatting
            digits = re.sub(r"\D", "", str(value))
            if len(digits) == 9:
                return f"{digits[:3]}-{digits[3:5]}-{digits[5:]}"
            return value
        elif function_name == "format_currency":
            # Format as currency
            try:
                amount = float(value)
                currency = function_params.get("currency", "USD")
                if currency == "USD":
                    return f"${amount:,.2f}"
                elif currency == "EUR":
                    return f"€{amount:,.2f}"
                elif currency == "GBP":
                    return f"£{amount:,.2f}"
                return f"{amount:,.2f}"
            except (ValueError, TypeError):
                return value
        elif function_name == "format_percentage":
            # Format as percentage
            try:
                amount = float(value)
                decimals = function_params.get("decimals", 2)
                multiplier = function_params.get("multiplier", 100)  # Multiply by 100 if value is decimal
                return f"{amount * multiplier:.{decimals}f}%"
            except (ValueError, TypeError):
                return value
        elif function_name == "generate_slug":
            # Generate URL-friendly slug
            import unicodedata

            slug = str(value)
            slug = unicodedata.normalize("NFKD", slug)
            slug = slug.encode("ascii", "ignore").decode("ascii")
            slug = re.sub(r"[^a-zA-Z0-9\s-]", "", slug).strip().lower()
            slug = re.sub(r"[-\s]+", "-", slug)
            max_length = function_params.get("max_length", 50)
            return slug[:max_length].rstrip("-")
        elif function_name == "extract_domain":
            # Extract domain from email or URL
            str_value = str(value)
            if "@" in str_value:
                return str_value.split("@")[-1]
            elif "://" in str_value:
                from urllib.parse import urlparse

                return urlparse(str_value).netloc
            return value
        elif function_name == "mask_sensitive":
            # Mask sensitive data (SSN, credit card, etc.)
            str_value = str(value)
            mask_char = function_params.get("mask_char", "*")
            preserve_last = function_params.get("preserve_last", 4)

            if len(str_value) <= preserve_last:
                return str_value

            masked_part = mask_char * (len(str_value) - preserve_last)
            return masked_part + str_value[-preserve_last:]
        elif function_name == "calculate_age":
            # Calculate age from birth date
            try:
                from datetime import date

                if isinstance(value, str):
                    # Try to parse birth date
                    birth_date = self._parse_date_auto(value)
                    if birth_date:
                        birth_date = birth_date.date()
                    else:
                        return None
                elif isinstance(value, date):
                    birth_date = value
                else:
                    return None

                today = date.today()
                return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            except (ValueError, TypeError):
                return None
        elif function_name == "geocode_address":
            # Placeholder for geocoding functionality
            # In a real implementation, this would call a geocoding service
            return {
                "address": str(value),
                "latitude": None,
                "longitude": None,
                "note": "Geocoding service not configured",
            }
        elif function_name == "validate_email":
            # Basic email validation
            email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            return bool(re.match(email_pattern, str(value)))
        elif function_name == "validate_phone":
            # Basic phone validation
            digits = re.sub(r"\D", "", str(value))
            min_length = function_params.get("min_length", 10)
            max_length = function_params.get("max_length", 15)
            return min_length <= len(digits) <= max_length
        elif function_name == "generate_id":
            # Generate custom ID with prefix/suffix
            prefix = function_params.get("prefix", "")
            suffix = function_params.get("suffix", "")
            length = function_params.get("length", 8)
            import random
            import string

            random_part = "".join(random.choices(string.ascii_uppercase + string.digits, k=length))
            return f"{prefix}{random_part}{suffix}"
        elif function_name == "python_eval":
            # DANGEROUS: Evaluate Python expression (use with extreme caution)
            # This should only be used in trusted environments
            expression = function_params.get("expression", "value")
            safe_globals = {
                "__builtins__": {},
                "value": value,
                "str": str,
                "int": int,
                "float": float,
                "len": len,
                "abs": abs,
                "round": round,
                "min": min,
                "max": max,
            }
            try:
                return eval(expression, safe_globals, {})
            except Exception:
                return value

        return value

    def get_transformation_preview(self, sample_data: List[Dict[str, Any]], limit: int = 10) -> Dict[str, Any]:
        """
        Generate a preview of transformations applied to sample data.

        Args:
            sample_data: List of sample data records to transform
            limit: Maximum number of records to preview

        Returns:
            Dictionary containing preview results
        """
        preview_results = {
            "total_records": len(sample_data),
            "preview_records": [],
            "transformation_summary": {},
            "error_summary": {
                "total_errors": 0,
                "error_types": {},
            },
        }

        # Limit the number of records to preview
        limited_data = sample_data[:limit]

        for record_index, record in enumerate(limited_data):
            preview_record = {
                "record_index": record_index,
                "original_data": record.copy(),
                "transformed_data": {},
                "transformation_results": {},
                "errors": [],
            }

            # Apply field mappings and transformations
            try:
                transformed = self.apply_field_mappings(record)
                preview_record["transformed_data"] = transformed

                # Track transformation success for each field
                for mapping in self.field_mappings:
                    source_field = mapping["source_field"]
                    target_field = mapping["target_field"]
                    transformations = mapping.get("transformations", [])

                    if source_field in record:
                        original_value = record[source_field]
                        transformed_value = transformed.get(target_field)

                        preview_record["transformation_results"][target_field] = {
                            "original_value": original_value,
                            "transformed_value": transformed_value,
                            "transformations_applied": len(transformations),
                            "transformation_chain": [t.get("type") for t in transformations],
                        }

            except Exception as e:
                error_msg = f"Error transforming record {record_index}: {str(e)}"
                preview_record["errors"].append(error_msg)
                preview_results["error_summary"]["total_errors"] += 1

                error_type = type(e).__name__
                if error_type not in preview_results["error_summary"]["error_types"]:
                    preview_results["error_summary"]["error_types"][error_type] = 0
                preview_results["error_summary"]["error_types"][error_type] += 1

            preview_results["preview_records"].append(preview_record)

        # Generate transformation summary
        transformation_counts = {}
        for mapping in self.field_mappings:
            for transform in mapping.get("transformations", []):
                transform_type = transform.get("type")
                if transform_type not in transformation_counts:
                    transformation_counts[transform_type] = 0
                transformation_counts[transform_type] += 1

        preview_results["transformation_summary"] = {
            "total_mappings": len(self.field_mappings),
            "transformation_types": transformation_counts,
            "most_common_transformations": sorted(transformation_counts.items(), key=lambda x: x[1], reverse=True)[:5],
        }

        return preview_results

    def get_transformation_performance_stats(self, sample_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze transformation performance on sample data.

        Args:
            sample_data: Sample data to analyze

        Returns:
            Performance statistics
        """
        import time

        stats = {
            "total_records": len(sample_data),
            "average_time_per_record": 0,
            "total_processing_time": 0,
            "transformation_times": {},
            "memory_usage": {},
            "error_rate": 0,
        }

        start_time = time.time()
        total_errors = 0
        transformation_times = {}

        for record in sample_data[:100]:  # Limit to 100 records for performance testing
            try:
                record_start = time.time()

                # Track individual transformation times
                for mapping in self.field_mappings:
                    transformations = mapping.get("transformations", [])
                    source_field = mapping["source_field"]

                    if source_field in record:
                        value = record[source_field]

                        for transform in transformations:
                            transform_type = transform.get("type")
                            transform_start = time.time()

                            # Apply single transformation
                            self._apply_transformations(value, [transform])

                            transform_time = time.time() - transform_start

                            if transform_type not in transformation_times:
                                transformation_times[transform_type] = []
                            transformation_times[transform_type].append(transform_time)

                # Apply full transformation
                self.apply_field_mappings(record)

                time.time() - record_start

            except Exception:
                total_errors += 1

        total_time = time.time() - start_time

        stats["total_processing_time"] = total_time
        stats["average_time_per_record"] = total_time / min(len(sample_data), 100)
        stats["error_rate"] = total_errors / min(len(sample_data), 100)

        # Calculate average times for each transformation type
        for transform_type, times in transformation_times.items():
            stats["transformation_times"][transform_type] = {
                "average_time": sum(times) / len(times) if times else 0,
                "total_applications": len(times),
                "min_time": min(times) if times else 0,
                "max_time": max(times) if times else 0,
            }

        return stats

    def validate_transformation_configuration(self) -> Dict[str, Any]:
        """
        Validate the transformation configuration for potential issues.

        Returns:
            Validation results with warnings and errors
        """
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "recommendations": [],
        }

        # Check for circular dependencies in transformations
        for mapping in self.field_mappings:
            transformations = mapping.get("transformations", [])
            source_field = mapping["source_field"]
            mapping["target_field"]

            # Check for potentially problematic transformation chains
            if len(transformations) > 10:
                validation_results["warnings"].append(
                    f"Field '{source_field}' has {len(transformations)} transformations. "
                    "Consider simplifying the transformation chain for better performance."
                )

            # Check for incompatible transformation sequences
            previous_type = None
            for i, transform in enumerate(transformations):
                transform_type = transform.get("type")

                # Check for type mismatches in chain
                if i > 0 and previous_type:
                    if self._check_transformation_compatibility(previous_type, transform_type):
                        validation_results["warnings"].append(
                            f"Potential type mismatch in '{source_field}' transformation chain: "
                            f"{previous_type} -> {transform_type} (step {i})"
                        )

                previous_type = transform_type

            # Check for missing required parameters
            for transform in transformations:
                missing_params = self._get_missing_transformation_params(transform)
                if missing_params:
                    validation_results["errors"].append(
                        f"Missing required parameters for {transform.get('type')} "
                        f"in field '{source_field}': {', '.join(missing_params)}"
                    )
                    validation_results["is_valid"] = False

        # Check for unreachable mappings (missing source fields)
        # This would require sample data to validate properly

        # Performance recommendations
        complex_transformations = [
            "regex_transform",
            "custom_function",
            "lookup_transform",
        ]

        complex_count = sum(
            1
            for mapping in self.field_mappings
            for transform in mapping.get("transformations", [])
            if transform.get("type") in complex_transformations
        )

        if complex_count > 20:
            validation_results["recommendations"].append(
                f"Template has {complex_count} complex transformations. "
                "Consider optimizing for better performance with large datasets."
            )

        return validation_results

    def _check_transformation_compatibility(self, prev_type: str, current_type: str) -> bool:
        """Check if transformation types are potentially incompatible."""
        # Define incompatible transformation sequences
        incompatible_pairs = [
            ("date_transform", "numeric_transform"),
            ("numeric_transform", "date_transform"),
            ("regex_transform", "numeric_transform"),
        ]

        return (prev_type, current_type) in incompatible_pairs

    def _get_missing_transformation_params(self, transform: Dict[str, Any]) -> List[str]:
        """Get list of missing required parameters for a transformation."""
        transform_type = transform.get("type")
        params = transform.get("params", {})
        missing = []

        # Define required parameters for each transformation type
        required_params = {
            "regex_transform": ["pattern"],
            "unit_conversion": ["from_unit", "to_unit"],
            "custom_function": ["function"],
            "lookup_transform": ["lookup_table"],
            "conditional_transform": ["conditions"],
        }

        if transform_type in required_params:
            for required_param in required_params[transform_type]:
                if required_param not in params:
                    missing.append(required_param)

        return missing

    def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate data against template validation rules.

        Args:
            data: Dictionary of data to validate

        Returns:
            Dictionary containing validation results
        """
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "field_results": {},
        }

        try:
            # Apply field-level validation
            for mapping in self.field_mappings:
                target_field = mapping.get("target_field")
                validation = mapping.get("validation", {})

                if target_field and validation:
                    field_value = data.get(target_field)
                    field_result = self._validate_field(field_value, validation, target_field)
                    validation_results["field_results"][target_field] = field_result

                    if not field_result["is_valid"]:
                        validation_results["is_valid"] = False
                        validation_results["errors"].extend(field_result["errors"])

                    validation_results["warnings"].extend(field_result["warnings"])

            # Apply global validation rules
            global_validation = self.validation_rules
            if global_validation:
                global_result = self._apply_global_validation(data, global_validation)

                if not global_result["is_valid"]:
                    validation_results["is_valid"] = False
                    validation_results["errors"].extend(global_result["errors"])

                validation_results["warnings"].extend(global_result["warnings"])

            return validation_results

        except Exception as e:
            logger.error(f"Error validating data in template {self.name}: {e}")
            return {
                "is_valid": False,
                "errors": [f"Validation error: {e}"],
                "warnings": [],
                "field_results": {},
            }

    def _validate_field(self, value: Any, validation: Dict[str, Any], field_name: str) -> Dict[str, Any]:
        """Validate a single field value."""
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
        }

        # Required field validation
        if validation.get("required", False) and (value is None or str(value).strip() == ""):
            result["is_valid"] = False
            result["errors"].append(f"Field '{field_name}' is required")
            return result

        # Skip other validations if value is empty and not required
        if value is None or str(value).strip() == "":
            return result

        # Data type validation
        data_type = validation.get("type")
        if data_type:
            type_result = self._validate_data_type(value, data_type, field_name)
            if not type_result["is_valid"]:
                result["is_valid"] = False
                result["errors"].extend(type_result["errors"])

        # Range validation
        if validation.get("min_value") is not None or validation.get("max_value") is not None:
            range_result = self._validate_range(value, validation, field_name)
            if not range_result["is_valid"]:
                result["is_valid"] = False
                result["errors"].extend(range_result["errors"])

        # Length validation
        if validation.get("min_length") is not None or validation.get("max_length") is not None:
            length_result = self._validate_length(value, validation, field_name)
            if not length_result["is_valid"]:
                result["is_valid"] = False
                result["errors"].extend(length_result["errors"])

        # Pattern validation
        if validation.get("pattern"):
            pattern_result = self._validate_pattern(value, validation["pattern"], field_name)
            if not pattern_result["is_valid"]:
                result["is_valid"] = False
                result["errors"].extend(pattern_result["errors"])

        # Custom validation rules
        custom_rules = validation.get("custom_rules", [])
        for rule in custom_rules:
            custom_result = self._apply_custom_validation(value, rule, field_name)
            if not custom_result["is_valid"]:
                result["is_valid"] = False
                result["errors"].extend(custom_result["errors"])
            result["warnings"].extend(custom_result["warnings"])

        return result

    def _validate_data_type(self, value: Any, data_type: str, field_name: str) -> Dict[str, Any]:
        """Validate data type."""
        result = {"is_valid": True, "errors": []}

        try:
            if data_type == "string":
                str(value)
            elif data_type == "integer":
                int(float(value))
            elif data_type == "float":
                float(value)
            elif data_type == "decimal":
                Decimal(str(value))
            elif data_type == "boolean":
                if str(value).lower() not in ["true", "false", "1", "0", "yes", "no"]:
                    raise ValueError("Invalid boolean value")
            elif data_type == "date":
                if not isinstance(value, (date, datetime)):
                    datetime.strptime(str(value), "%Y-%m-%d")
            elif data_type == "datetime":
                if not isinstance(value, datetime):
                    datetime.fromisoformat(str(value))
            elif data_type == "email":
                from django.core.validators import validate_email

                validate_email(str(value))
            elif data_type == "url":
                from django.core.validators import URLValidator

                URLValidator()(str(value))

        except (ValueError, ValidationError):
            result["is_valid"] = False
            result["errors"].append(f"Field '{field_name}' must be of type {data_type}")

        return result

    def _validate_range(self, value: Any, validation: Dict[str, Any], field_name: str) -> Dict[str, Any]:
        """Validate numeric range."""
        result = {"is_valid": True, "errors": []}

        try:
            num_value = float(value)

            min_value = validation.get("min_value")
            max_value = validation.get("max_value")

            if min_value is not None and num_value < min_value:
                result["is_valid"] = False
                result["errors"].append(f"Field '{field_name}' must be at least {min_value}")

            if max_value is not None and num_value > max_value:
                result["is_valid"] = False
                result["errors"].append(f"Field '{field_name}' must be at most {max_value}")

        except (ValueError, TypeError):
            result["is_valid"] = False
            result["errors"].append(f"Field '{field_name}' must be numeric for range validation")

        return result

    def _validate_length(self, value: Any, validation: Dict[str, Any], field_name: str) -> Dict[str, Any]:
        """Validate string length."""
        result = {"is_valid": True, "errors": []}

        str_value = str(value)
        length = len(str_value)

        min_length = validation.get("min_length")
        max_length = validation.get("max_length")

        if min_length is not None and length < min_length:
            result["is_valid"] = False
            result["errors"].append(f"Field '{field_name}' must be at least {min_length} characters")

        if max_length is not None and length > max_length:
            result["is_valid"] = False
            result["errors"].append(f"Field '{field_name}' must be at most {max_length} characters")

        return result

    def _validate_pattern(self, value: Any, pattern: str, field_name: str) -> Dict[str, Any]:
        """Validate against regex pattern."""
        result = {"is_valid": True, "errors": []}

        try:
            if not re.match(pattern, str(value)):
                result["is_valid"] = False
                result["errors"].append(f"Field '{field_name}' does not match required pattern")
        except re.error:
            result["is_valid"] = False
            result["errors"].append(f"Invalid regex pattern for field '{field_name}'")

        return result

    def _apply_custom_validation(self, value: Any, rule: Dict[str, Any], field_name: str) -> Dict[str, Any]:
        """Apply custom validation rule."""
        result = {"is_valid": True, "errors": [], "warnings": []}

        rule_type = rule.get("type")

        if rule_type == "unique_check":
            # This would need to be implemented with database lookups
            pass
        elif rule_type == "business_rule":
            # Custom business logic validation
            pass
        elif rule_type == "warning_threshold":
            threshold = rule.get("threshold")
            if threshold and float(value) > threshold:
                result["warnings"].append(f"Field '{field_name}' exceeds warning threshold of {threshold}")

        return result

    def _apply_global_validation(self, data: Dict[str, Any], validation: Dict[str, Any]) -> Dict[str, Any]:
        """Apply global validation rules."""
        result = {"is_valid": True, "errors": [], "warnings": []}

        # Cross-field validation
        cross_field_rules = validation.get("cross_field_rules", [])
        for _rule in cross_field_rules:
            # Implement cross-field validation logic
            pass

        # Data consistency checks
        consistency_checks = validation.get("consistency_checks", [])
        for _check in consistency_checks:
            # Implement consistency checking logic
            pass

        return result

    def get_preview_data(self, source_data: List[Dict[str, Any]], limit: int = 10) -> Dict[str, Any]:
        """
        Generate preview data showing transformation and validation results.

        Args:
            source_data: List of source data records
            limit: Maximum number of records to preview

        Returns:
            Dictionary containing preview information
        """
        preview_data = {
            "total_records": len(source_data),
            "preview_records": [],
            "validation_summary": {
                "total_valid": 0,
                "total_invalid": 0,
                "total_warnings": 0,
            },
            "field_statistics": {},
            "transformation_summary": self.get_field_mapping_summary(),
        }

        # Process preview records
        for i, record in enumerate(source_data[:limit]):
            try:
                # Apply transformations
                transformed_data = self.apply_field_mappings(record)

                # Validate transformed data
                validation_results = self.validate_data(transformed_data)

                preview_record = {
                    "index": i,
                    "source_data": record,
                    "transformed_data": transformed_data,
                    "validation_results": validation_results,
                }

                preview_data["preview_records"].append(preview_record)

                # Update validation summary
                if validation_results["is_valid"]:
                    preview_data["validation_summary"]["total_valid"] += 1
                else:
                    preview_data["validation_summary"]["total_invalid"] += 1

                if validation_results["warnings"]:
                    preview_data["validation_summary"]["total_warnings"] += 1

            except Exception as e:
                logger.error(f"Error processing preview record {i}: {e}")
                preview_record = {
                    "index": i,
                    "source_data": record,
                    "transformed_data": {},
                    "validation_results": {
                        "is_valid": False,
                        "errors": [f"Processing error: {e}"],
                        "warnings": [],
                        "field_results": {},
                    },
                }
                preview_data["preview_records"].append(preview_record)
                preview_data["validation_summary"]["total_invalid"] += 1

        return preview_data

    def get_template_summary(self) -> Dict[str, Any]:
        """Get comprehensive template summary for display."""
        mapping_summary = self.get_field_mapping_summary()

        return {
            "id": str(self.template_id),
            "name": self.name,
            "description": self.description,
            "version": self.version,
            "template_type": self.template_type,
            "source_type": self.source_type,
            "mapping_strategy": self.mapping_strategy,
            "supported_formats": self.supported_formats,
            "field_mappings": mapping_summary,
            "has_transformations": bool(self.transformation_rules),
            "has_validations": bool(self.validation_rules),
            "has_defaults": bool(self.default_values),
            "usage_count": self.usage_count,
            "last_used_at": (self.last_used_at.isoformat() if self.last_used_at else None),
            "is_public": self.is_public,
            "is_system_template": self.is_system_template,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "created_by": self.created_by.get_full_name() if self.created_by else None,
        }

    @classmethod
    def create_from_configuration(cls, import_config, name: str, description: str = "") -> "ImportTemplate":
        """
        Create template from existing ImportConfiguration.

        Args:
            import_config: ImportConfiguration instance
            name: Name for the new template
            description: Description for the template

        Returns:
            ImportTemplate instance
        """
        return cls.objects.create(
            name=name,
            description=description,
            template_type="standard",
            source_type="file_upload",
            supported_formats=[import_config.file_format],
            field_mappings=import_config.field_mapping,
            validation_rules=import_config.validation_rules,
            created_by=import_config.created_by,
            organization=import_config.organization,
        )

    @classmethod
    def get_available_templates(cls, user, organization=None, template_type=None) -> "QuerySet":
        """
        Get templates available to a user.

        Args:
            user: User instance
            organization: Optional organization filter
            template_type: Optional template type filter

        Returns:
            QuerySet of available templates
        """
        queryset = cls.objects.filter(
            models.Q(created_by=user)
            | models.Q(is_public=True)
            | models.Q(is_system_template=True)
            | (models.Q(organization=organization) if organization else models.Q())
        )

        if template_type:
            queryset = queryset.filter(template_type=template_type)

        return queryset.order_by("-usage_count", "name")
