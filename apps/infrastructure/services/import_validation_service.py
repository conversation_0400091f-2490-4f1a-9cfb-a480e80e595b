"""
Import Validation Service for CAD/GIS File Import System

Provides customizable validation rules and thresholds for CAD/GIS imports
with detailed error reporting and HTMX integration.
"""

import logging
import os
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple

from django.conf import settings
from django.utils import timezone

from apps.infrastructure.models_file_import import CadGisFileUpload
from apps.infrastructure.services.error_recovery_service import log_import_error

logger = logging.getLogger(__name__)


@dataclass
class ValidationRule:
    """
    Represents a single validation rule with configuration.
    """

    name: str
    description: str
    category: str  # 'file', 'geometry', 'attribute', 'coordinate_system', etc.
    severity: str  # 'critical', 'error', 'warning', 'info'
    enabled: bool = True
    threshold: Optional[float] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    recovery_action: str = "none"


@dataclass
class ValidationResult:
    """
    Result of validation rule execution.
    """

    rule_name: str
    passed: bool
    message: str
    details: Optional[str] = None
    context_data: Dict[str, Any] = field(default_factory=dict)
    affected_features: List[int] = field(default_factory=list)
    suggested_fix: Optional[str] = None


class ImportValidationService:
    """
    Service for validating CAD/GIS imports with customizable rules.

    Provides comprehensive validation with configurable thresholds,
    detailed error reporting, and automatic recovery suggestions.
    """

    # Default validation rules configuration
    DEFAULT_RULES = {
        # File validation rules
        "file_size_limit": ValidationRule(
            name="file_size_limit",
            description="Check if file size is within acceptable limits",
            category="file_format",
            severity="error",
            threshold=500.0,  # MB
            parameters={"max_size_mb": 500},
            recovery_action="none",
        ),
        "file_format_supported": ValidationRule(
            name="file_format_supported",
            description="Verify file format is supported",
            category="file_format",
            severity="critical",
            recovery_action="none",
        ),
        "file_structure_valid": ValidationRule(
            name="file_structure_valid",
            description="Check file structure integrity",
            category="file_format",
            severity="error",
            recovery_action="fallback",
        ),
        # Geometry validation rules
        "geometry_valid": ValidationRule(
            name="geometry_valid",
            description="Validate geometry structures",
            category="geometry",
            severity="error",
            threshold=0.95,  # 95% of geometries must be valid
            parameters={"min_valid_percentage": 95},
            recovery_action="skip_feature",
        ),
        "geometry_not_empty": ValidationRule(
            name="geometry_not_empty",
            description="Check for empty geometries",
            category="geometry",
            severity="warning",
            threshold=0.9,  # 90% must have non-empty geometry
            parameters={"min_non_empty_percentage": 90},
            recovery_action="skip_feature",
        ),
        "geometry_within_bounds": ValidationRule(
            name="geometry_within_bounds",
            description="Check if geometries are within reasonable bounds",
            category="geometry",
            severity="warning",
            parameters={"min_x": -180.0, "max_x": 180.0, "min_y": -90.0, "max_y": 90.0},
            recovery_action="transform",
        ),
        # Coordinate system validation rules
        "crs_defined": ValidationRule(
            name="crs_defined",
            description="Check if coordinate reference system is defined",
            category="coordinate_system",
            severity="error",
            recovery_action="transform",
        ),
        "crs_supported": ValidationRule(
            name="crs_supported",
            description="Verify CRS is supported for transformation",
            category="coordinate_system",
            severity="error",
            recovery_action="fallback",
        ),
        # Attribute validation rules
        "required_fields_present": ValidationRule(
            name="required_fields_present",
            description="Check if required attribute fields are present",
            category="attribute",
            severity="error",
            parameters={"required_fields": []},
            recovery_action="partial_import",
        ),
        "field_types_compatible": ValidationRule(
            name="field_types_compatible",
            description="Validate field data types are compatible",
            category="attribute",
            severity="warning",
            threshold=0.9,  # 90% of fields must be compatible
            recovery_action="transform",
        ),
        "attribute_values_valid": ValidationRule(
            name="attribute_values_valid",
            description="Check attribute value validity",
            category="attribute",
            severity="warning",
            threshold=0.95,  # 95% of values must be valid
            recovery_action="skip_feature",
        ),
        # Performance validation rules
        "feature_count_reasonable": ValidationRule(
            name="feature_count_reasonable",
            description="Check if feature count is within processing limits",
            category="validation",
            severity="warning",
            threshold=100000,  # Max features before warning
            parameters={"max_features_warning": 100000},
            recovery_action="skip_chunk",
        ),
        "memory_usage_acceptable": ValidationRule(
            name="memory_usage_acceptable",
            description="Verify memory usage is within limits",
            category="memory",
            severity="error",
            threshold=0.8,  # 80% of available memory
            parameters={"max_memory_percentage": 80},
            recovery_action="skip_chunk",
        ),
    }

    def __init__(
        self,
        upload: CadGisFileUpload,
        custom_rules: Optional[Dict[str, ValidationRule]] = None,
    ):
        """Initialize validation service for specific upload."""
        self.upload = upload
        self.logger = logging.getLogger(f"{__name__}.{upload.upload_id}")

        # Load validation rules
        self.rules = self.DEFAULT_RULES.copy()
        if custom_rules:
            self.rules.update(custom_rules)

        # Load configuration from settings
        self._load_configuration()

        # Validation results storage
        self.validation_results: List[ValidationResult] = []
        self.error_count = 0
        self.warning_count = 0

    def _load_configuration(self):
        """Load validation configuration from Django settings."""
        config = getattr(settings, "CAD_GIS_VALIDATION_CONFIG", {})

        # Update rule thresholds from settings
        for rule_name, rule_config in config.get("rules", {}).items():
            if rule_name in self.rules:
                rule = self.rules[rule_name]
                rule.enabled = rule_config.get("enabled", rule.enabled)
                rule.threshold = rule_config.get("threshold", rule.threshold)
                rule.parameters.update(rule_config.get("parameters", {}))
                rule.severity = rule_config.get("severity", rule.severity)

        # Global configuration
        self.strict_mode = config.get("strict_mode", False)
        self.stop_on_critical = config.get("stop_on_critical", True)
        self.max_errors_before_stop = config.get("max_errors_before_stop", 100)

    def validate_upload(self, feature_data: Optional[List[Dict]] = None) -> Tuple[bool, Dict[str, Any]]:
        """
        Perform comprehensive validation of the upload.

        Args:
            feature_data: Optional feature data for validation

        Returns:
            Tuple of (is_valid, validation_report)
        """
        try:
            self.logger.info(f"Starting validation for upload {self.upload.upload_id}")

            # Reset validation state
            self.validation_results.clear()
            self.error_count = 0
            self.warning_count = 0

            # Run validation rules in order
            validation_passed = True

            # File-level validations
            if not self._validate_file_properties():
                validation_passed = False
                if self.stop_on_critical:
                    return False, self._generate_validation_report()

            # Feature-level validations (if feature data provided)
            if feature_data:
                if not self._validate_features(feature_data):
                    validation_passed = False
                    if self.stop_on_critical and self.error_count > self.max_errors_before_stop:
                        return False, self._generate_validation_report()

            # Coordinate system validation
            if not self._validate_coordinate_system():
                validation_passed = False

            # Performance validation
            if not self._validate_performance_constraints():
                validation_passed = False

            # Generate final report
            validation_report = self._generate_validation_report()

            # Log validation results
            if validation_passed:
                self.logger.info(f"Validation passed for upload {self.upload.upload_id}")
            else:
                self.logger.warning(
                    f"Validation failed for upload {self.upload.upload_id}: "
                    f"{self.error_count} errors, {self.warning_count} warnings"
                )

            return validation_passed, validation_report

        except Exception as e:
            self.logger.error(f"Validation failed with exception: {e}", exc_info=True)

            # Log the validation failure as an error
            log_import_error(
                upload=self.upload,
                severity="critical",
                category="validation",
                message=f"Validation process failed: {e}",
                exception=e,
                processing_stage="validation",
            )

            return False, {
                "validation_error": str(e),
                "total_errors": 1,
                "passed": False,
            }

    def _validate_file_properties(self) -> bool:
        """Validate file-level properties."""
        passed = True

        # File size validation
        if self.rules["file_size_limit"].enabled:
            result = self._check_file_size()
            self.validation_results.append(result)
            if not result.passed and result.rule_name in ["file_size_limit"]:
                passed = False
                if self.rules["file_size_limit"].severity == "critical":
                    self.error_count += 1

        # File format validation
        if self.rules["file_format_supported"].enabled:
            result = self._check_file_format()
            self.validation_results.append(result)
            if not result.passed:
                passed = False
                self.error_count += 1

        # File structure validation
        if self.rules["file_structure_valid"].enabled:
            result = self._check_file_structure()
            self.validation_results.append(result)
            if not result.passed:
                if self.rules["file_structure_valid"].severity in ["critical", "error"]:
                    passed = False
                    self.error_count += 1
                else:
                    self.warning_count += 1

        return passed

    def _validate_features(self, feature_data: List[Dict]) -> bool:
        """Validate feature-level data."""
        passed = True
        valid_geometries = 0
        non_empty_geometries = 0
        valid_attributes = 0

        for i, feature in enumerate(feature_data):
            # Geometry validation
            if "geometry" in feature:
                if self._is_geometry_valid(feature["geometry"]):
                    valid_geometries += 1
                    if not self._is_geometry_empty(feature["geometry"]):
                        non_empty_geometries += 1
                else:
                    # Log individual geometry error
                    log_import_error(
                        upload=self.upload,
                        severity="error",
                        category="geometry",
                        message=f"Invalid geometry in feature {i}",
                        feature_index=i,
                        processing_stage="validation",
                        recovery_action="skip_feature",
                    )

            # Attribute validation
            if "properties" in feature:
                if self._are_attributes_valid(feature["properties"]):
                    valid_attributes += 1

        total_features = len(feature_data)

        # Check geometry validation thresholds
        if self.rules["geometry_valid"].enabled:
            geometry_valid_percentage = (valid_geometries / total_features) * 100
            threshold = self.rules["geometry_valid"].threshold * 100

            result = ValidationResult(
                rule_name="geometry_valid",
                passed=geometry_valid_percentage >= threshold,
                message=f"{geometry_valid_percentage:.1f}% of geometries are valid (threshold: {threshold}%)",
                context_data={
                    "valid_count": valid_geometries,
                    "total_count": total_features,
                },
                suggested_fix="Review and fix invalid geometries before import",
            )
            self.validation_results.append(result)

            if not result.passed:
                passed = False
                self.error_count += 1

        # Check non-empty geometry threshold
        if self.rules["geometry_not_empty"].enabled:
            non_empty_percentage = (non_empty_geometries / total_features) * 100
            threshold = self.rules["geometry_not_empty"].threshold * 100

            result = ValidationResult(
                rule_name="geometry_not_empty",
                passed=non_empty_percentage >= threshold,
                message=f"{non_empty_percentage:.1f}% of geometries are non-empty (threshold: {threshold}%)",
                context_data={
                    "non_empty_count": non_empty_geometries,
                    "total_count": total_features,
                },
                suggested_fix="Consider removing or fixing empty geometries",
            )
            self.validation_results.append(result)

            if not result.passed:
                if self.rules["geometry_not_empty"].severity == "error":
                    passed = False
                    self.error_count += 1
                else:
                    self.warning_count += 1

        return passed

    def _validate_coordinate_system(self) -> bool:
        """Validate coordinate reference system."""
        passed = True

        # CRS defined validation
        if self.rules["crs_defined"].enabled:
            has_crs = bool(self.upload.source_crs)

            result = ValidationResult(
                rule_name="crs_defined",
                passed=has_crs,
                message=("Coordinate reference system is defined" if has_crs else "No CRS defined"),
                context_data={"source_crs": self.upload.source_crs},
                suggested_fix="Define source CRS for accurate coordinate transformation",
            )
            self.validation_results.append(result)

            if not result.passed:
                passed = False
                self.error_count += 1

        # CRS supported validation
        if self.rules["crs_supported"].enabled and self.upload.source_crs:
            is_supported = self._is_crs_supported(self.upload.source_crs)

            result = ValidationResult(
                rule_name="crs_supported",
                passed=is_supported,
                message=(
                    f"CRS {self.upload.source_crs} is supported"
                    if is_supported
                    else f"CRS {self.upload.source_crs} is not supported"
                ),
                context_data={"source_crs": self.upload.source_crs},
                suggested_fix="Use a supported CRS or provide transformation parameters",
            )
            self.validation_results.append(result)

            if not result.passed:
                passed = False
                self.error_count += 1

        return passed

    def _validate_performance_constraints(self) -> bool:
        """Validate performance-related constraints."""
        passed = True

        # Feature count validation
        if self.rules["feature_count_reasonable"].enabled:
            feature_count = self.upload.feature_count or 0
            threshold = self.rules["feature_count_reasonable"].threshold

            result = ValidationResult(
                rule_name="feature_count_reasonable",
                passed=feature_count <= threshold,
                message=(
                    f"Feature count ({feature_count:,}) is within limits"
                    if feature_count <= threshold
                    else f"High feature count ({feature_count:,}) may impact performance"
                ),
                context_data={"feature_count": feature_count, "threshold": threshold},
                suggested_fix="Consider processing in smaller chunks for better performance",
            )
            self.validation_results.append(result)

            if not result.passed:
                self.warning_count += 1

        return passed

    def _check_file_size(self) -> ValidationResult:
        """Check file size against limits."""
        file_size_mb = self.upload.file.size / (1024 * 1024) if self.upload.file else 0
        threshold_mb = self.rules["file_size_limit"].threshold

        return ValidationResult(
            rule_name="file_size_limit",
            passed=file_size_mb <= threshold_mb,
            message=(
                f"File size ({file_size_mb:.1f} MB) is within limits"
                if file_size_mb <= threshold_mb
                else f"File size ({file_size_mb:.1f} MB) exceeds limit ({threshold_mb} MB)"
            ),
            context_data={"file_size_mb": file_size_mb, "threshold_mb": threshold_mb},
            suggested_fix="Reduce file size or increase limits in configuration",
        )

    def _check_file_format(self) -> ValidationResult:
        """Check if file format is supported."""
        is_supported = self.upload.is_supported_format()

        return ValidationResult(
            rule_name="file_format_supported",
            passed=is_supported,
            message=(
                f"File format ({self.upload.detected_format}) is supported"
                if is_supported
                else f"File format ({self.upload.detected_format}) is not supported"
            ),
            context_data={"detected_format": self.upload.detected_format},
            suggested_fix="Convert file to a supported format (DXF, Shapefile, GeoJSON)",
        )

    def _check_file_structure(self) -> ValidationResult:
        """Check file structure integrity."""
        # Basic file existence and readability check
        if not self.upload.file:
            return ValidationResult(
                rule_name="file_structure_valid",
                passed=False,
                message="No file attached to upload",
                suggested_fix="Ensure file is properly uploaded",
            )

        try:
            # Check if file exists and is readable
            file_path = self.upload.file.path
            if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                return ValidationResult(
                    rule_name="file_structure_valid",
                    passed=True,
                    message="File structure is valid",
                    context_data={"file_path": file_path},
                )
            else:
                return ValidationResult(
                    rule_name="file_structure_valid",
                    passed=False,
                    message="File is not accessible",
                    suggested_fix="Verify file permissions and integrity",
                )
        except Exception as e:
            return ValidationResult(
                rule_name="file_structure_valid",
                passed=False,
                message=f"File structure check failed: {e}",
                suggested_fix="Verify file integrity and format",
            )

    def _is_geometry_valid(self, geometry: Dict) -> bool:
        """Check if geometry structure is valid."""
        if not geometry:
            return False

        # Basic GeoJSON geometry validation
        required_fields = ["type"]
        if not all(field in geometry for field in required_fields):
            return False

        geometry_type = geometry.get("type")
        if geometry_type not in [
            "Point",
            "LineString",
            "Polygon",
            "MultiPoint",
            "MultiLineString",
            "MultiPolygon",
        ]:
            return False

        return True

    def _is_geometry_empty(self, geometry: Dict) -> bool:
        """Check if geometry is empty."""
        if not geometry:
            return True

        coordinates = geometry.get("coordinates")
        if not coordinates:
            return True

        # Check for empty coordinate arrays
        if isinstance(coordinates, list) and len(coordinates) == 0:
            return True

        return False

    def _are_attributes_valid(self, properties: Dict) -> bool:
        """Check if attribute values are valid."""
        if not properties:
            return True  # Empty properties are considered valid

        # Check for null values in required fields
        required_fields = self.rules["required_fields_present"].parameters.get("required_fields", [])
        for field in required_fields:
            if field not in properties or properties[field] is None:
                return False

        return True

    def _is_crs_supported(self, crs: str) -> bool:
        """Check if CRS is supported for transformation."""
        # Common supported CRS patterns
        supported_patterns = ["EPSG:", "WGS84", "UTM", "NAD83", "NAD27"]

        return any(pattern in crs.upper() for pattern in supported_patterns)

    def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report."""
        # Count results by category
        by_category = {}
        by_severity = {"critical": 0, "error": 0, "warning": 0, "info": 0}

        for result in self.validation_results:
            rule = self.rules.get(result.rule_name)
            if rule:
                category = rule.category
                severity = rule.severity

                if category not in by_category:
                    by_category[category] = {"passed": 0, "failed": 0}

                if result.passed:
                    by_category[category]["passed"] += 1
                else:
                    by_category[category]["failed"] += 1
                    by_severity[severity] += 1

        # Generate recommendations
        recommendations = self._generate_recommendations()

        # Calculate overall score
        total_checks = len(self.validation_results)
        passed_checks = sum(1 for r in self.validation_results if r.passed)
        overall_score = (passed_checks / max(total_checks, 1)) * 100

        return {
            "upload_id": str(self.upload.upload_id),
            "validation_timestamp": timezone.now().isoformat(),
            "overall_passed": self.error_count == 0,
            "overall_score": round(overall_score, 2),
            "total_checks": total_checks,
            "passed_checks": passed_checks,
            "failed_checks": total_checks - passed_checks,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "by_category": by_category,
            "by_severity": by_severity,
            "validation_results": [
                {
                    "rule_name": r.rule_name,
                    "passed": r.passed,
                    "message": r.message,
                    "details": r.details,
                    "context_data": r.context_data,
                    "suggested_fix": r.suggested_fix,
                    "severity": self.rules.get(r.rule_name, ValidationRule("", "", "", "info")).severity,
                    "category": self.rules.get(r.rule_name, ValidationRule("", "", "", "info")).category,
                }
                for r in self.validation_results
            ],
            "recommendations": recommendations,
        }

    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        # File-related recommendations
        if any(not r.passed and r.rule_name in ["file_size_limit"] for r in self.validation_results):
            recommendations.append("Consider reducing file size or processing in chunks")

        # Geometry-related recommendations
        geometry_issues = sum(
            1 for r in self.validation_results if not r.passed and r.rule_name.startswith("geometry_")
        )
        if geometry_issues > 0:
            recommendations.append("Review and fix geometry issues before import")

        # CRS-related recommendations
        if any(not r.passed and r.rule_name.startswith("crs_") for r in self.validation_results):
            recommendations.append("Verify coordinate reference system configuration")

        # Performance recommendations
        if any(not r.passed and r.rule_name in ["feature_count_reasonable"] for r in self.validation_results):
            recommendations.append("Consider chunked processing for better performance")

        # General recommendations
        if self.error_count > 10:
            recommendations.append("High error count - consider data preprocessing")

        if not recommendations:
            recommendations.append("Validation looks good - ready for import")

        return recommendations


# Convenience function for easy validation
def validate_cad_gis_upload(
    upload: CadGisFileUpload,
    feature_data: Optional[List[Dict]] = None,
    custom_rules: Optional[Dict[str, ValidationRule]] = None,
) -> Tuple[bool, Dict[str, Any]]:
    """
    Convenience function to validate CAD/GIS upload.

    Args:
        upload: CadGisFileUpload instance
        feature_data: Optional feature data for validation
        custom_rules: Optional custom validation rules

    Returns:
        Tuple of (is_valid, validation_report)
    """
    service = ImportValidationService(upload, custom_rules)
    return service.validate_upload(feature_data)
