"""Procedural 3D Model Generation Service

Service for generating 3D utility models procedurally using JavaScript/Three.js
and server-side coordination.
"""

from __future__ import annotations

import json
from typing import TYPE_CHECKING, Any

from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile

if TYPE_CHECKING:
    from apps.infrastructure.models import (
        ModelGenerationJob,
        ModelOptimizationProfile,
        ProceduralModelTemplate,
        ThreeDModelLibrary,
    )
else:
    from apps.infrastructure.models import (
        ModelGenerationJob,
        ModelOptimizationProfile,
        ProceduralModelTemplate,
        ThreeDModelLibrary,
    )


class ProceduralModelGenerator:
    """Main service for procedural 3D model generation.

    Provides procedural generation capabilities for 3D utility models including
    parametric pipes, valves, manholes, and custom script-based generation.
    """

    def __init__(self) -> None:
        self.supported_types = [
            "extrude_2d",
            "revolve",
            "parametric_pipe",
            "parametric_valve",
            "parametric_manhole",
            "custom_script",
        ]

    def generate_model(
        self,
        template: ProceduralModelTemplate,
        parameters: dict[str, Any],
        user: Any,
        optimization_profile: ModelOptimizationProfile | None = None,
    ) -> ModelGenerationJob:
        """Generate a 3D model from a template with given parameters.

        Args:
        ----
            template: The procedural template to use
            parameters: Generation parameters
            user: User requesting the generation
            optimization_profile: Optional optimization profile

        Returns:
        -------
            ModelGenerationJob instance

        """
        # Create generation job
        job = ModelGenerationJob.objects.create(template=template, parameters=parameters, requested_by=user)

        try:
            # Start processing
            job.start_processing()

            # Validate parameters
            validated_params = self._validate_parameters(template, parameters)

            # Generate the model based on type
            model_data = self._generate_by_type(template.generation_type, template, validated_params)

            # Apply optimization if specified
            if optimization_profile:
                model_data = self._apply_optimization(model_data, optimization_profile)

            # Create 3D model library entry
            model = self._save_generated_model(template, validated_params, model_data, user)

            # Complete job successfully
            job.complete_success(model)
            template.increment_usage()

            return job

        except (ValidationError, ValueError) as e:
            # Complete job with failure
            job.complete_failure(str(e))
            return job

    def _validate_parameters(self, template: ProceduralModelTemplate, parameters: dict[str, Any]) -> dict[str, Any]:
        """Validate and normalize generation parameters.

        Args:
        ----
            template: The template with parameter constraints
            parameters: Input parameters to validate

        Returns:
        -------
            Validated and normalized parameters

        """
        validated = {}
        constraints = template.constraints or {}
        defaults = template.parameters or {}

        # Apply defaults
        for key, default_value in defaults.items():
            validated[key] = parameters.get(key, default_value)

        # Apply constraints
        for key, value in validated.items():
            if key in constraints:
                constraint = constraints[key]

                # Type validation
                if "type" in constraint:
                    expected_type = constraint["type"]
                    if expected_type == "number" and not isinstance(value, int | float):
                        raise ValueError(f"Parameter {key} must be a number")
                    if expected_type == "string" and not isinstance(value, str):
                        raise ValueError(f"Parameter {key} must be a string")

                # Range validation
                if "min" in constraint and value < constraint["min"]:
                    validated[key] = constraint["min"]
                if "max" in constraint and value > constraint["max"]:
                    validated[key] = constraint["max"]

                # Choices validation
                if "choices" in constraint and value not in constraint["choices"]:
                    raise ValueError(f"Parameter {key} must be one of {constraint['choices']}")

        return validated

    def _generate_by_type(
        self,
        generation_type: str,
        template: ProceduralModelTemplate,
        parameters: dict[str, Any],
    ) -> dict[str, Any]:
        """Generate model data based on generation type.

        Args:
        ----
            generation_type: Type of generation to perform
            template: Template configuration
            parameters: Generation parameters

        Returns:
        -------
            Generated model data

        """
        if generation_type == "parametric_pipe":
            return self._generate_parametric_pipe(parameters)
        if generation_type == "parametric_valve":
            return self._generate_parametric_valve(parameters)
        if generation_type == "parametric_manhole":
            return self._generate_parametric_manhole(parameters)
        if generation_type == "extrude_2d":
            return self._generate_extruded_shape(parameters)
        if generation_type == "revolve":
            return self._generate_revolved_shape(parameters)
        if generation_type == "custom_script":
            return self._generate_custom_script(template, parameters)
        raise ValueError(f"Unsupported generation type: {generation_type}")

    def _generate_parametric_pipe(self, params: dict[str, Any]) -> dict[str, Any]:
        """Generate a parametric pipe model.

        Args:
        ----
            params: Pipe generation parameters

        Returns:
        -------
            Pipe model data

        """
        diameter = params.get("diameter", 6.0)  # inches
        length = params.get("length", 10.0)  # feet
        material = params.get("material", "pvc")
        segments = params.get("segments", 16)

        # Convert to Three.js compatible format
        return {
            "type": "parametric_pipe",
            "geometry": {
                "type": "TubeGeometry",
                "radiusTop": diameter * 0.0254 / 2,  # Convert inches to meters
                "radiusBottom": diameter * 0.0254 / 2,
                "height": length * 0.3048,  # Convert feet to meters
                "radialSegments": segments,
                "heightSegments": 1,
                "openEnded": True,
            },
            "material": {
                "type": "MeshStandardMaterial",
                "color": self._get_material_color(material),
                "metalness": self._get_material_metalness(material),
                "roughness": self._get_material_roughness(material),
            },
            "metadata": {
                "triangle_count": segments * 2,
                "vertex_count": segments * 4,
                "real_world_scale": {
                    "diameter_m": diameter * 0.0254,
                    "length_m": length * 0.3048,
                },
            },
        }

    def _generate_parametric_valve(self, params: dict[str, Any]) -> dict[str, Any]:
        """Generate a parametric valve model.

        Args:
        ----
            params: Valve generation parameters

        Returns:
        -------
            Valve model data

        """
        valve_type = params.get("type", "gate")  # gate, ball, globe
        size = params.get("size", 6.0)  # inches
        height = params.get("height", size * 2)  # default height

        # Basic valve geometry
        body_radius = size * 0.0254 / 2
        body_height = height * 0.0254

        return {
            "type": "parametric_valve",
            "geometry": {
                "type": "CylinderGeometry",
                "radiusTop": body_radius,
                "radiusBottom": body_radius,
                "height": body_height,
                "radialSegments": 16,
                "heightSegments": 2,
            },
            "material": {
                "type": "MeshStandardMaterial",
                "color": 0x888888,  # Gray metal
                "metalness": 0.8,
                "roughness": 0.2,
            },
            "metadata": {
                "triangle_count": 32,
                "vertex_count": 68,
                "real_world_scale": {
                    "diameter_m": size * 0.0254,
                    "height_m": body_height,
                },
                "valve_type": valve_type,
            },
        }

    def _generate_parametric_manhole(self, params: dict[str, Any]) -> dict[str, Any]:
        """Generate a parametric manhole model.

        Args:
        ----
            params: Manhole generation parameters

        Returns:
        -------
            Manhole model data

        """
        diameter = params.get("diameter", 4.0)  # feet
        depth = params.get("depth", 6.0)  # feet
        cover_type = params.get("cover_type", "standard")

        radius = diameter * 0.3048 / 2  # Convert to meters
        depth_m = depth * 0.3048

        return {
            "type": "parametric_manhole",
            "geometry": {
                "type": "CylinderGeometry",
                "radiusTop": radius,
                "radiusBottom": radius,
                "height": depth_m,
                "radialSegments": 20,
                "heightSegments": 4,
                "openEnded": False,
            },
            "material": {
                "type": "MeshStandardMaterial",
                "color": 0xCCCCCC,  # Concrete color
                "metalness": 0.0,
                "roughness": 0.9,
            },
            "metadata": {
                "triangle_count": 80,
                "vertex_count": 126,
                "real_world_scale": {
                    "diameter_m": diameter * 0.3048,
                    "depth_m": depth_m,
                },
                "cover_type": cover_type,
            },
        }

    def _generate_extruded_shape(self, params: dict[str, Any]) -> dict[str, Any]:
        """Generate an extruded 2D shape.

        Args:
        ----
            params: Extrusion parameters

        Returns:
        -------
            Extruded shape model data

        """
        # This would integrate with 2D shape data from Agent 2
        shape_points = params.get("shape_points", [])
        extrude_depth = params.get("extrude_depth", 1.0)

        if not shape_points:
            # Default rectangle
            shape_points = [[-0.5, -0.5], [0.5, -0.5], [0.5, 0.5], [-0.5, 0.5]]

        return {
            "type": "extruded_shape",
            "geometry": {
                "type": "ExtrudeGeometry",
                "shape_points": shape_points,
                "settings": {"depth": extrude_depth, "bevelEnabled": False},
            },
            "material": {
                "type": "MeshStandardMaterial",
                "color": 0x666666,
                "metalness": 0.1,
                "roughness": 0.7,
            },
            "metadata": {
                "triangle_count": len(shape_points) * 4,
                "vertex_count": len(shape_points) * 8,
                "extrude_depth": extrude_depth,
            },
        }

    def _generate_revolved_shape(self, params: dict[str, Any]) -> dict[str, Any]:
        """Generate a revolved profile shape.

        Args:
        ----
            params: Revolution parameters

        Returns:
        -------
            Revolved shape model data

        """
        profile_points = params.get("profile_points", [])
        segments = params.get("segments", 16)

        if not profile_points:
            # Default pipe profile
            profile_points = [[0.5, -1], [0.5, 1]]

        return {
            "type": "revolved_shape",
            "geometry": {
                "type": "LatheGeometry",
                "points": profile_points,
                "segments": segments,
            },
            "material": {
                "type": "MeshStandardMaterial",
                "color": 0x444444,
                "metalness": 0.3,
                "roughness": 0.6,
            },
            "metadata": {
                "triangle_count": segments * len(profile_points) * 2,
                "vertex_count": segments * len(profile_points) * 2,
                "segments": segments,
            },
        }

    def _generate_custom_script(self, template: ProceduralModelTemplate, params: dict[str, Any]) -> dict[str, Any]:
        """Execute custom JavaScript generation script.

        Args:
        ----
            template: Template with custom script
            params: Script parameters

        Returns:
        -------
            Custom generated model data

        """
        # This would require a JavaScript execution environment for full functionality
        # Currently providing a fallback implementation with basic geometry

        # In a full implementation, this would:
        # 1. Set up a Node.js or V8 JavaScript runtime
        # 2. Execute the script with Three.js context
        # 3. Return the generated geometry data

        return {
            "type": "custom_script",
            "geometry": {
                "type": "BoxGeometry",
                "width": params.get("width", 1),
                "height": params.get("height", 1),
                "depth": params.get("depth", 1),
            },
            "material": {"type": "MeshStandardMaterial", "color": 0x999999},
            "metadata": {
                "triangle_count": 12,
                "vertex_count": 24,
                "script_executed": True,
            },
        }

    def _apply_optimization(self, model_data: dict[str, Any], profile: ModelOptimizationProfile) -> dict[str, Any]:
        """Apply optimization profile to model data.

        Args:
        ----
            model_data: Original model data
            profile: Optimization profile to apply

        Returns:
        -------
            Optimized model data

        """
        optimized = model_data.copy()

        # Adjust geometry based on profile
        current_triangles = model_data["metadata"].get("triangle_count", 0)
        max_triangles = profile.max_triangle_count

        if current_triangles > max_triangles:
            # Reduce triangle count
            reduction_factor = max_triangles / current_triangles

            # Apply reduction to geometry parameters
            if "radialSegments" in optimized["geometry"]:
                current_segments = optimized["geometry"]["radialSegments"]
                optimized["geometry"]["radialSegments"] = max(8, int(current_segments * reduction_factor))

            # Update metadata
            optimized["metadata"]["triangle_count"] = max_triangles
            optimized["metadata"]["optimization_applied"] = True
            optimized["metadata"]["optimization_profile"] = profile.name

        return optimized

    def _save_generated_model(
        self,
        template: ProceduralModelTemplate,
        parameters: dict[str, Any],
        model_data: dict[str, Any],
        user: Any,
    ) -> ThreeDModelLibrary:
        """Save generated model to database.

        Args:
        ----
            template: Template used for generation
            parameters: Generation parameters
            model_data: Generated model data
            user: User who generated the model

        Returns:
        -------
            Saved model library entry

        """
        # Generate GLTF content from model data
        gltf_content = self._convert_to_gltf(model_data)

        # Create model instance
        model = ThreeDModelLibrary.objects.create(
            name=f"{template.name} - Generated",
            description=f"Procedurally generated from {template.name}",
            model_type=self._infer_model_type(template.generation_type),
            utility_category=parameters.get("utility_category", "other"),
            triangle_count=model_data["metadata"].get("triangle_count", 0),
            vertex_count=model_data["metadata"].get("vertex_count", 0),
            quality_level=template.default_quality_level,
            real_world_scale=model_data["metadata"].get("real_world_scale", {}),
            is_procedural=True,
            procedural_config=parameters,
            created_by=user,
            is_approved=False,  # Requires manual approval
            is_public=False,
        )

        # Save GLTF file
        gltf_filename = f"{model.id}.gltf"
        model.gltf_file.save(gltf_filename, ContentFile(gltf_content), save=False)

        # Calculate file size
        model.file_size_bytes = len(gltf_content)
        model.save()

        return model

    def _convert_to_gltf(self, model_data: dict[str, Any]) -> str:
        """Convert model data to GLTF format.

        Args:
        ----
            model_data: Model data to convert

        Returns:
        -------
            GLTF format string

        """
        # Basic GLTF structure
        gltf = {
            "asset": {"version": "2.0", "generator": "CLEAR Procedural Generator"},
            "scene": 0,
            "scenes": [{"nodes": [0]}],
            "nodes": [{"mesh": 0}],
            "meshes": [{"primitives": [{"attributes": {"POSITION": 0}, "material": 0}]}],
            "materials": [
                {
                    "name": "GeneratedMaterial",
                    "pbrMetallicRoughness": {
                        "baseColorFactor": self._color_to_array(model_data["material"].get("color", 0x888888)),
                        "metallicFactor": model_data["material"].get("metalness", 0.1),
                        "roughnessFactor": model_data["material"].get("roughness", 0.7),
                    },
                },
            ],
            "accessors": [
                {
                    "bufferView": 0,
                    "componentType": 5126,  # FLOAT
                    "count": model_data["metadata"].get("vertex_count", 24),
                    "type": "VEC3",
                },
            ],
            "bufferViews": [
                {
                    "buffer": 0,
                    "byteOffset": 0,
                    "byteLength": model_data["metadata"].get("vertex_count", 24) * 12,
                },
            ],
            "buffers": [
                {
                    "byteLength": model_data["metadata"].get("vertex_count", 24) * 12,
                    "uri": "data:application/octet-stream;base64,AAAAAAAAAAAAAAAAAAAAAA==",
                },
            ],
        }

        return json.dumps(gltf, indent=2)

    def _color_to_array(self, color_hex: int) -> list[float]:
        """Convert hex color to RGB array.

        Args:
        ----
            color_hex: Hexadecimal color value

        Returns:
        -------
            RGBA color array

        """
        r = ((color_hex >> 16) & 255) / 255
        g = ((color_hex >> 8) & 255) / 255
        b = (color_hex & 255) / 255
        return [r, g, b, 1.0]

    def _infer_model_type(self, generation_type: str) -> str:
        """Infer model type from generation type.

        Args:
        ----
            generation_type: The generation type

        Returns:
        -------
            Inferred model type

        """
        mapping = {
            "parametric_pipe": "pipe_fitting",
            "parametric_valve": "valve",
            "parametric_manhole": "manhole",
            "extrude_2d": "generic",
            "revolve": "generic",
            "custom_script": "custom",
        }
        return mapping.get(generation_type, "generic")

    def _get_material_color(self, material: str) -> int:
        """Get material color based on type.

        Args:
        ----
            material: Material type

        Returns:
        -------
            Hexadecimal color value

        """
        colors = {
            "pvc": 0xFFFFFF,  # White
            "steel": 0x888888,  # Gray
            "copper": 0xB87333,  # Copper
            "iron": 0x444444,  # Dark gray
            "concrete": 0xCCCCCC,  # Light gray
            "plastic": 0x00FF00,  # Green
        }
        return colors.get(material, 0x888888)

    def _get_material_metalness(self, material: str) -> float:
        """Get material metalness based on type.

        Args:
        ----
            material: Material type

        Returns:
        -------
            Metalness value (0.0-1.0)

        """
        metalness = {
            "pvc": 0.0,
            "steel": 0.9,
            "copper": 0.8,
            "iron": 0.7,
            "concrete": 0.0,
            "plastic": 0.1,
        }
        return metalness.get(material, 0.1)

    def _get_material_roughness(self, material: str) -> float:
        """Get material roughness based on type.

        Args:
        ----
            material: Material type

        Returns:
        -------
            Roughness value (0.0-1.0)

        """
        roughness = {
            "pvc": 0.3,
            "steel": 0.2,
            "copper": 0.3,
            "iron": 0.4,
            "concrete": 0.9,
            "plastic": 0.5,
        }
        return roughness.get(material, 0.7)


# Singleton instance for easy access
procedural_generator: ProceduralModelGenerator = ProceduralModelGenerator()
