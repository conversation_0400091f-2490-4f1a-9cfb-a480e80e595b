"""Symbol placement and manipulation tools for map integration.

This module provides utilities for:
- Symbol placement on maps with coordinate validation
- Symbol transformation (scale, rotation, color)
- Drag-drop integration with mapping libraries
- Symbol conflict detection and clearance validation
- Real-time symbol synchronization

Complies with Django 5.2 patterns including proper type hints,
error handling, and spatial operations with PostGIS integration.
"""

import math
import re
from decimal import Decimal
from typing import TYPE_CHECKING

from django.contrib.gis.geos import Point, Polygon
from django.contrib.gis.measure import Distance
from django.core.exceptions import ValidationError

if TYPE_CHECKING:
    from django.contrib.auth.models import User

    from apps.projects.models import Project

# Import symbol models if available
try:
    from .symbol_models import APWAStandards, SymbolPlacement, UtilitySymbol

    SYMBOL_MODELS_AVAILABLE = True
except ImportError:
    SYMBOL_MODELS_AVAILABLE = False


class SymbolPlacementManager:
    """Manages symbol placement operations and validations"""

    def __init__(self, project: "Project | None" = None) -> None:
        self.project = project

    def validate_placement_coordinates(self, coordinates: list[float], coordinate_system: str = "EPSG:4326") -> bool:
        """Validate placement coordinates"""
        if not coordinates or len(coordinates) != 2:
            raise ValidationError("Coordinates must be [longitude, latitude]")

        longitude, latitude = coordinates

        # Basic coordinate validation
        if not (-180 <= longitude <= 180):
            raise ValidationError(f"Invalid longitude: {longitude}")

        if not (-90 <= latitude <= 90):
            raise ValidationError(f"Invalid latitude: {latitude}")

        # Project-specific boundary validation
        if self.project and hasattr(self.project, "geometry") and self.project.geometry:
            point = Point(longitude, latitude, srid=4326)
            if not self.project.geometry.contains(point):
                # Check if within reasonable buffer (1km)
                buffer_distance = Distance(km=1)
                buffered_boundary = self.project.geometry.buffer(buffer_distance.m)
                if not buffered_boundary.contains(point):
                    raise ValidationError("Symbol placement is outside project boundary")

        return True

    def check_symbol_clearance(
        self,
        symbol: "UtilitySymbol",
        coordinates: list[float],
        scale: float = 1.0,
    ) -> dict[str, any]:
        """Check clearance requirements for symbol placement"""
        if not SYMBOL_MODELS_AVAILABLE:
            return {"clearance_ok": True, "conflicts": []}

        point = Point(coordinates[0], coordinates[1], srid=4326)

        # Calculate symbol footprint with clearance buffer
        symbol_radius = max(symbol.base_width, symbol.base_height) * scale / 2
        clearance_buffer = float(symbol.conflict_buffer) if symbol.conflict_buffer else 0
        total_radius = symbol_radius + clearance_buffer

        # Create search area
        search_distance = Distance(m=total_radius)
        point.buffer(search_distance.m)

        # Find nearby symbol placements
        nearby_placements = SymbolPlacement.objects.filter(
            project=self.project,
            geometry__distance_lte=(point, search_distance),
        ).exclude(status="removed")

        conflicts = []

        for placement in nearby_placements:
            distance = point.distance(placement.geometry)
            other_symbol = placement.symbol
            other_radius = max(other_symbol.base_width, other_symbol.base_height) * float(placement.scale) / 2
            other_clearance = float(other_symbol.conflict_buffer) if other_symbol.conflict_buffer else 0

            required_separation = symbol_radius + other_radius + clearance_buffer + other_clearance

            if distance < required_separation:
                conflicts.append(
                    {
                        "placement_id": str(placement.id),
                        "symbol_code": other_symbol.symbol_code,
                        "symbol_name": other_symbol.name,
                        "distance": distance,
                        "required_separation": required_separation,
                        "clearance_violation": required_separation - distance,
                        "severity": ("high" if distance < (required_separation * 0.5) else "medium"),
                    },
                )

        return {
            "clearance_ok": len(conflicts) == 0,
            "conflicts": conflicts,
            "total_radius": total_radius,
            "search_radius": total_radius,
        }

    def validate_symbol_placement(
        self,
        symbol: "UtilitySymbol",
        coordinates: list[float],
        scale: float = 1.0,
        rotation: float = 0.0,
        custom_attributes: dict[str, any] | None = None,
    ) -> dict[str, any]:
        """Complete validation for symbol placement"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "clearance_check": None,
        }

        try:
            # Validate coordinates
            self.validate_placement_coordinates(coordinates)

            # Validate scale
            if not (0.1 <= scale <= 10.0):
                validation_result["errors"].append(f"Invalid scale factor: {scale}")

            # Validate rotation
            if not (0 <= rotation < 360):
                validation_result["errors"].append(f"Invalid rotation: {rotation}")

            # Check clearance
            clearance_result = self.check_symbol_clearance(symbol, coordinates, scale)
            validation_result["clearance_check"] = clearance_result

            if not clearance_result["clearance_ok"]:
                high_severity_conflicts = [c for c in clearance_result["conflicts"] if c["severity"] == "high"]
                if high_severity_conflicts:
                    validation_result["errors"].append("Symbol placement violates clearance requirements")
                else:
                    validation_result["warnings"].append("Symbol placement may have clearance issues")

            # Validate placement rules
            if symbol.placement_rules:
                rule_validation = self._validate_placement_rules(symbol, coordinates, scale, rotation)
                validation_result["warnings"].extend(rule_validation.get("warnings", []))
                validation_result["errors"].extend(rule_validation.get("errors", []))

        except ValidationError as e:
            validation_result["errors"].append(str(e))

        validation_result["valid"] = len(validation_result["errors"]) == 0
        return validation_result

    def _validate_placement_rules(
        self,
        symbol: "UtilitySymbol",
        coordinates: list[float],
        scale: float,
        rotation: float,
    ) -> dict[str, list[str]]:
        """Validate symbol-specific placement rules"""
        result = {"warnings": [], "errors": []}

        if not symbol.placement_rules:
            return result

        rules = symbol.placement_rules

        # Check scale limits
        if "scale_min" in rules and scale < rules["scale_min"]:
            result["warnings"].append(f"Scale {scale} is below recommended minimum {rules['scale_min']}")

        if "scale_max" in rules and scale > rules["scale_max"]:
            result["warnings"].append(f"Scale {scale} exceeds recommended maximum {rules['scale_max']}")

        # Check rotation constraints
        if "rotation_increment" in rules:
            increment = rules["rotation_increment"]
            if rotation % increment != 0:
                result["warnings"].append(f"Rotation should be in increments of {increment} degrees")

        # Check utility-specific rules
        if symbol.utility_type == "water" and symbol.symbol_type == "hydrant":
            # Fire hydrants should be accessible from roads
            # This would require additional spatial analysis
            pass

        return result

    def create_placement(
        self,
        symbol: "UtilitySymbol",
        coordinates: list[float],
        user: "User",
        **kwargs: any,
    ) -> "SymbolPlacement":
        """Create a new symbol placement with validation"""
        if not SYMBOL_MODELS_AVAILABLE:
            raise ValidationError("Symbol models not available")

        # Extract placement properties
        scale = kwargs.get("scale", symbol.scale_factor)
        rotation = kwargs.get("rotation", 0.0)
        custom_color = kwargs.get("custom_color")
        custom_attributes = kwargs.get("custom_attributes", {})
        label = kwargs.get("label", "")
        notes = kwargs.get("notes", "")
        utility = kwargs.get("utility")

        # Validate placement
        validation_result = self.validate_symbol_placement(symbol, coordinates, scale, rotation, custom_attributes)

        if not validation_result["valid"]:
            raise ValidationError(f"Placement validation failed: {validation_result['errors']}")

        # Create placement
        placement = SymbolPlacement.objects.create(
            symbol=symbol,
            project=self.project,
            geometry=Point(coordinates[0], coordinates[1], srid=4326),
            scale=Decimal(str(scale)),
            rotation=Decimal(str(rotation)),
            custom_color=custom_color,
            custom_attributes=custom_attributes,
            label=label,
            notes=notes,
            utility=utility,
            placed_by=user,
        )

        # Increment symbol usage
        symbol.increment_usage()

        return placement

    def update_placement(self, placement: "SymbolPlacement", **kwargs: any) -> "SymbolPlacement":
        """Update an existing symbol placement"""
        updated_fields = []

        # Update coordinates if provided
        if "coordinates" in kwargs:
            coordinates = kwargs["coordinates"]
            self.validate_placement_coordinates(coordinates)
            placement.geometry = Point(coordinates[0], coordinates[1], srid=4326)
            updated_fields.append("geometry")

        # Update transformation properties
        for field in ["scale", "rotation"]:
            if field in kwargs:
                setattr(placement, field, Decimal(str(kwargs[field])))
                updated_fields.append(field)

        # Update visual properties
        for field in ["custom_color", "label", "notes"]:
            if field in kwargs:
                setattr(placement, field, kwargs[field])
                updated_fields.append(field)

        # Update custom attributes
        if "custom_attributes" in kwargs:
            placement.custom_attributes.update(kwargs["custom_attributes"])
            updated_fields.append("custom_attributes")

        # Update status
        if "status" in kwargs:
            placement.status = kwargs["status"]
            updated_fields.append("status")

        if updated_fields:
            placement.save(update_fields=[*updated_fields, "updated_at"])

        return placement

    def get_placement_geojson(self, placements: list["SymbolPlacement"]) -> dict[str, any]:
        """Convert symbol placements to GeoJSON format for map display"""
        features = []

        for placement in placements:
            feature = {
                "type": "Feature",
                "id": str(placement.id),
                "geometry": {
                    "type": "Point",
                    "coordinates": [placement.geometry.x, placement.geometry.y],
                },
                "properties": {
                    "symbol_id": str(placement.symbol.id),
                    "symbol_code": placement.symbol.symbol_code,
                    "symbol_name": placement.symbol.name,
                    "symbol_type": placement.symbol.symbol_type,
                    "utility_type": placement.symbol.utility_type,
                    "apwa_color": placement.get_final_color(),
                    "scale": float(placement.scale),
                    "rotation": float(placement.rotation),
                    "label": placement.label,
                    "notes": placement.notes,
                    "status": placement.status,
                    "placed_by": placement.placed_by.username,
                    "placed_at": placement.placed_at.isoformat(),
                    "updated_at": placement.updated_at.isoformat(),
                    "svg_content": placement.get_transformed_svg(),
                    "custom_attributes": placement.custom_attributes,
                    "base_width": float(placement.symbol.base_width),
                    "base_height": float(placement.symbol.base_height),
                },
            }
            features.append(feature)

        return {"type": "FeatureCollection", "features": features}


class SymbolTransformationUtils:
    """Utilities for symbol transformation operations

    Provides static methods for SVG transformations, color changes,
    and spatial calculations for symbol placements.
    """

    @staticmethod
    def apply_svg_transformations(
        svg_content: str,
        scale: float = 1.0,
        rotation: float = 0.0,
        color: str | None = None,
    ) -> str:
        """Apply transformations to SVG content"""
        import xml.etree.ElementTree as ET

        try:
            # Parse SVG
            root = ET.fromstring(svg_content)

            # Apply transformations
            transform_parts = []

            if scale != 1.0:
                transform_parts.append(f"scale({scale})")

            if rotation != 0.0:
                transform_parts.append(f"rotate({rotation})")

            if transform_parts:
                transform = " ".join(transform_parts)
                existing_transform = root.get("transform", "")
                if existing_transform:
                    root.set("transform", f"{existing_transform} {transform}")
                else:
                    root.set("transform", transform)

            # Apply color changes
            if color:
                SymbolTransformationUtils._apply_color_to_svg(root, color)

            return ET.tostring(root, encoding="unicode")

        except ET.ParseError:
            # Fallback: simple string replacement
            transformed_svg = svg_content

            if scale != 1.0 or rotation != 0.0:
                transform_parts = []
                if scale != 1.0:
                    transform_parts.append(f"scale({scale})")
                if rotation != 0.0:
                    transform_parts.append(f"rotate({rotation})")

                transform = " ".join(transform_parts)
                transformed_svg = transformed_svg.replace("<svg", f'<svg transform="{transform}"', 1)

            if color:
                # Simple color replacement
                transformed_svg = SymbolTransformationUtils._simple_color_replace(transformed_svg, color)

            return transformed_svg

    @staticmethod
    def _apply_color_to_svg(element: any, color: str) -> None:
        """Apply color to SVG element and its children"""
        # Update fill and stroke attributes
        if element.get("fill") and element.get("fill") != "none":
            element.set("fill", color)

        if element.get("stroke") and element.get("stroke") != "none":
            element.set("stroke", color)

        # Recursively apply to children
        for child in element:
            SymbolTransformationUtils._apply_color_to_svg(child, color)

    @staticmethod
    def _simple_color_replace(svg_content: str, new_color: str) -> str:
        """Simple color replacement using string operations"""
        # import re (moved to top level)

        # Replace common color patterns
        color_patterns = [
            r'fill="[^"]*"',
            r'stroke="[^"]*"',
            r"fill:[^;]*;",
            r"stroke:[^;]*;",
        ]

        for pattern in color_patterns:
            if "fill" in pattern:
                svg_content = re.sub(pattern, f'fill="{new_color}"', svg_content)
            elif "stroke" in pattern:
                svg_content = re.sub(pattern, f'stroke="{new_color}"', svg_content)

        return svg_content

    @staticmethod
    def calculate_symbol_bounds(symbol: "UtilitySymbol", scale: float = 1.0) -> dict[str, float]:
        """Calculate symbol bounding box after scaling"""
        width = float(symbol.base_width) * scale
        height = float(symbol.base_height) * scale

        return {
            "width": width,
            "height": height,
            "radius": max(width, height) / 2,
            "area": width * height,
        }

    @staticmethod
    def calculate_placement_footprint(placement: "SymbolPlacement") -> Polygon:
        """Calculate the spatial footprint of a symbol placement"""
        bounds = SymbolTransformationUtils.calculate_symbol_bounds(placement.symbol, float(placement.scale))

        # Create rectangular footprint around placement point
        center_x = placement.geometry.x
        center_y = placement.geometry.y
        half_width = bounds["width"] / 2
        half_height = bounds["height"] / 2

        # Convert to meters (approximate)
        # This is a rough approximation - for precise calculations,
        # should transform to local projected coordinate system
        meters_per_degree_lat = 111000  # Approximate
        meters_per_degree_lon = meters_per_degree_lat * math.cos(math.radians(center_y))

        width_degrees = half_width / meters_per_degree_lon
        height_degrees = half_height / meters_per_degree_lat

        # Create polygon
        coords = [
            (center_x - width_degrees, center_y - height_degrees),
            (center_x + width_degrees, center_y - height_degrees),
            (center_x + width_degrees, center_y + height_degrees),
            (center_x - width_degrees, center_y + height_degrees),
            (center_x - width_degrees, center_y - height_degrees),  # Close polygon
        ]

        return Polygon(coords, srid=4326)


class SymbolConflictDetector:
    """Detects and analyzes conflicts between symbol placements

    Provides comprehensive conflict detection and analysis for symbol
    placements including clearance violations and utility separation requirements.
    """

    def __init__(self, project: "Project | None" = None) -> None:
        self.project = project

    def detect_symbol_conflicts(
        self,
        placement: "SymbolPlacement",
        check_radius: float = 100.0,
    ) -> list[dict[str, any]]:
        """Detect conflicts with nearby symbol placements"""
        if not SYMBOL_MODELS_AVAILABLE:
            return []

        # Find nearby placements
        search_distance = Distance(m=check_radius)
        nearby_placements = SymbolPlacement.objects.filter(
            project=self.project,
            geometry__distance_lte=(placement.geometry, search_distance),
        ).exclude(id=placement.id, status="removed")

        conflicts = []

        for other_placement in nearby_placements:
            conflict_analysis = self._analyze_placement_conflict(placement, other_placement)
            if conflict_analysis["has_conflict"]:
                conflicts.append(conflict_analysis)

        return conflicts

    def _analyze_placement_conflict(
        self,
        placement1: "SymbolPlacement",
        placement2: "SymbolPlacement",
    ) -> dict[str, any]:
        """Analyze conflict between two symbol placements"""
        # Calculate distance
        distance = placement1.geometry.distance(placement2.geometry)

        # Calculate required clearances
        bounds1 = SymbolTransformationUtils.calculate_symbol_bounds(placement1.symbol, float(placement1.scale))
        bounds2 = SymbolTransformationUtils.calculate_symbol_bounds(placement2.symbol, float(placement2.scale))

        clearance1 = float(placement1.symbol.conflict_buffer) if placement1.symbol.conflict_buffer else 0
        clearance2 = float(placement2.symbol.conflict_buffer) if placement2.symbol.conflict_buffer else 0

        required_separation = bounds1["radius"] + bounds2["radius"] + clearance1 + clearance2

        has_conflict = distance < required_separation

        # Determine conflict severity
        if has_conflict:
            violation_percentage = (required_separation - distance) / required_separation
            if violation_percentage > 0.75:
                severity = "critical"
            elif violation_percentage > 0.5:
                severity = "high"
            elif violation_percentage > 0.25:
                severity = "medium"
            else:
                severity = "low"
        else:
            severity = "none"

        return {
            "has_conflict": has_conflict,
            "distance": distance,
            "required_separation": required_separation,
            "clearance_violation": max(0, required_separation - distance),
            "severity": severity,
            "other_placement_id": str(placement2.id),
            "other_symbol_code": placement2.symbol.symbol_code,
            "other_symbol_name": placement2.symbol.name,
            "conflict_type": self._determine_conflict_type(placement1, placement2),
        }

    def _determine_conflict_type(self, placement1: "SymbolPlacement", placement2: "SymbolPlacement") -> str:
        """Determine the type of conflict between placements"""
        type1 = placement1.symbol.utility_type
        type2 = placement2.symbol.utility_type

        # Same utility type conflicts
        if type1 == type2:
            return f"{type1}_internal_conflict"

        # Cross-utility conflicts
        conflict_pairs = {
            ("electric", "water"): "electrical_water_separation",
            ("electric", "gas"): "electrical_gas_separation",
            ("water", "sewer"): "water_sewer_separation",
            ("gas", "sewer"): "gas_sewer_separation",
        }

        # Check both directions
        key1 = (type1, type2)
        key2 = (type2, type1)

        return conflict_pairs.get(key1, conflict_pairs.get(key2, "general_clearance_conflict"))

    def generate_conflict_report(self, placements: list["SymbolPlacement"]) -> dict[str, any]:
        """Generate comprehensive conflict report for multiple placements"""
        all_conflicts = []
        conflict_summary = {
            "total_placements": len(placements),
            "total_conflicts": 0,
            "severity_breakdown": {"critical": 0, "high": 0, "medium": 0, "low": 0},
            "conflict_types": {},
            "recommendations": [],
        }

        # Check each placement against others
        for i, placement in enumerate(placements):
            placement_conflicts = []

            for _j, other_placement in enumerate(placements[i + 1 :], i + 1):
                conflict_analysis = self._analyze_placement_conflict(placement, other_placement)
                if conflict_analysis["has_conflict"]:
                    placement_conflicts.append(conflict_analysis)

                    # Update summary
                    severity = conflict_analysis["severity"]
                    conflict_summary["severity_breakdown"][severity] += 1

                    conflict_type = conflict_analysis["conflict_type"]
                    conflict_summary["conflict_types"][conflict_type] = (
                        conflict_summary["conflict_types"].get(conflict_type, 0) + 1
                    )

            if placement_conflicts:
                all_conflicts.append(
                    {
                        "placement_id": str(placement.id),
                        "symbol_code": placement.symbol.symbol_code,
                        "conflicts": placement_conflicts,
                    },
                )

        conflict_summary["total_conflicts"] = sum(conflict_summary["severity_breakdown"].values())
        conflict_summary["all_conflicts"] = all_conflicts

        # Generate recommendations
        if conflict_summary["total_conflicts"] > 0:
            conflict_summary["recommendations"] = self._generate_conflict_recommendations(conflict_summary)

        return conflict_summary

    def _generate_conflict_recommendations(self, conflict_summary: dict[str, any]) -> list[str]:
        """Generate recommendations based on conflict analysis"""
        recommendations = []

        critical_count = conflict_summary["severity_breakdown"]["critical"]
        high_count = conflict_summary["severity_breakdown"]["high"]

        if critical_count > 0:
            recommendations.append(f"Immediate attention required: {critical_count} critical conflicts detected")

        if high_count > 0:
            recommendations.append(f"High priority: {high_count} high-severity conflicts need resolution")

        # Specific recommendations based on conflict types
        conflict_types = conflict_summary["conflict_types"]

        if "electrical_gas_separation" in conflict_types:
            recommendations.append("Electrical and gas utilities require increased separation for safety")

        if "water_sewer_separation" in conflict_types:
            recommendations.append("Water and sewer lines need proper vertical separation to prevent contamination")

        return recommendations
