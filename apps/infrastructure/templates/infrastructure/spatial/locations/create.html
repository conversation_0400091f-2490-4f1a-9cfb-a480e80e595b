{% extends "base.html" %}

{# Performance: High variable count detected - consider optimization #}
{# Performance: High variable count detected - consider optimization #}
{# Performance: High variable count detected - consider optimization #}
{# Performance: High variable count detected - consider optimization #}
{% load static %}
{% load i18n %}

{% block title %}
  {% translate "Create Location" %}
{% endblock %}

{% block head %}
  <!-- Unified Markdown Editor CSS -->
  <link rel="stylesheet" href="{% static 'css/markdown-editor.css' %}">
  <link rel="stylesheet"
        href="{% static 'css/markdown-editor-minimal.css' %}">
  <!-- Leaflet mapping library with integrity checks -->
  <link rel="stylesheet"
        href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
        crossorigin="" />
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
          integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
          crossorigin=""></script>
  <style>
    :root {
      --map-height: 500px;
      --form-border-radius: var(--bs-border-radius-lg);
      --coordinate-bg: var(--bs-body-bg);
      --coordinate-border: var(--bs-border-color);
    }

    #map {
      height: var(--map-height);
      width: 100%;
      border-radius: var(--form-border-radius);
      border: 2px solid var(--coordinate-border);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
      transition: border-color 0.3s ease;
    }

    #map:hover {
      border-color: var(--bs-primary);
    }

    .form-floating {
      margin-bottom: 1rem;
    }

    .coordinate-input {
      font-family: 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.9rem;
      background: var(--coordinate-bg);
      border: 2px solid var(--coordinate-border);
      transition: all 0.3s ease;
    }

    .coordinate-input:focus {
      border-color: var(--bs-primary);
      box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    }

    .coordinate-input.is-valid {
      border-color: var(--bs-success);
    }

    .coordinate-input.is-invalid {
      border-color: var(--bs-danger);
    }

    .map-container {
      position: relative;
      margin-bottom: 1rem;
    }

    .map-overlay {
      position: absolute;
      top: 15px;
      right: 15px;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      padding: 0.75rem;
      border-radius: var(--bs-border-radius-lg);
      font-size: 0.875rem;
      box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
      border: 1px solid var(--bs-border-color-translucent);
      min-width: 200px;
    }

    .map-instructions {
      background: var(--bs-primary-bg-subtle);
      border: 1px solid var(--bs-primary-border-subtle);
      border-radius: var(--bs-border-radius);
      padding: 0.75rem;
      margin-bottom: 1rem;
    }

    .coordinate-display {
      font-family: 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
      font-size: 0.8rem;
      background: var(--bs-dark);
      color: var(--bs-light);
      padding: 0.25rem 0.5rem;
      border-radius: var(--bs-border-radius-sm);
      margin-top: 0.5rem;
    }

    .validation-feedback {
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .location-type-badge {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: var(--bs-border-radius-pill);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }

    @d-flex (max-width: 768px) {
      :root {
        --map-height: 350px;
      }

      .map-overlay {
        position: static;
        margin-top: 1rem;
        width: 100%;
      }
    }

    /* Dark mode support */
    @d-flex (prefers-color-scheme: dark) {
      :root {
        --coordinate-bg: var(--bs-dark);
        --coordinate-border: var(--bs-border-color-translucent);
      }

      .map-overlay {
        background: rgba(33, 37, 41, 0.95);
        color: var(--bs-light);
        border-color: var(--bs-border-color-translucent);
      }

      .coordinate-display {
        background: var(--bs-body-bg);
        color: var(--bs-body-color);
        border: 1px solid var(--bs-border-color);
      }
    }

    /* Animation for marker placement */
    @keyframes markerBounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }
  </style>
{% endblock %}

{% block content %}
  <main>
    <div class="container" role="main">
      <div class="row">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h1 class="mb-1">
                <i class="lucide lucide-map-pin" aria-hidden="true"></i>
                {% translate "Create Location" %}
              </h1>
              <p class="text-muted mb-0">
                {% translate "Add a new geographic location with precise coordinates and detailed information" %}
              </p>
            </div>
            <div class="btn-group"
                 role="group"
                 aria-label="{% translate 'Navigation actions' %}">
              <a href="{% url 'infrastructure:spatial_location_list' %}"
                 class="btn btn-outline-secondary"
                 title="{% translate 'Return to location list' %}"
                 role="link">
                <i class="lucide lucide-arrow-left" aria-hidden="true"></i>
                {% translate "Back to List" %}
              </a>
              <a href="{% url 'infrastructure:spatial_map' %}"
                 class="btn btn-outline-primary"
                 title="{% translate 'View spatial map' %}"
                 role="link">
                <i class="lucide lucide-map" aria-hidden="true"></i>
                {% translate "Map View" %}
              </a>
            </div>
          </div>
          <!-- Instructions -->
          <div class="map-instructions">
            <div class="d-flex align-items-start">
              <i class="lucide lucide-lightbulb me-2 mt-1 text-primary"
                 aria-hidden="true"></i>
              <div>
                <h6 class="text-primary mb-2">{% translate "Creating a Location" %}</h6>
                <ul class="mb-0 small">
                  <li>{% translate "Click on the map to set coordinates automatically" %}</li>
                  <li>{% translate "Or enter latitude/longitude manually in the coordinate fields" %}</li>
                  <li>{% translate "Drag the marker to fine-tune the position" %}</li>
                  <li>{% translate "All coordinates use WGS84 (EPSG:4326) coordinate system" %}</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="card shadow-lg">
            <div class="card-header bg-primary text-white">
              <h5 class="card-title mb-0">
                <i class="lucide lucide-plus-circle" aria-hidden="true"></i>
                {% translate "Location Details" %}
              </h5>
            </div>
            <div class="card-body">
              <form method="post"
                    class="needs-validation"
                    novalidate
                    id="location-form"
                    novalidate>
                {% csrf_token %}
                <div class="row">
                  <div class="col-lg-6">
                    <!-- Basic Information -->
                    <div class="card border-light mb-4">
                      <div class="card-header bg-light">
                        <h6 class="mb-0">
                          <i class="lucide lucide-info" aria-hidden="true"></i>
                          {% translate "Basic Information" %}
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="form-floating mb-3">
                          {{ form.name|escape }}
                          <label for="{{ form.name.id_for_label|escape }}" class="form-label">
                            <i class="lucide lucide-tag" aria-hidden="true"></i>
                            {% translate "Name" %}
                            <span class="text-danger">*</span>
                          </label>

                          {% if form.name.errors %}<div class="invalid-feedback d-block">{{ form.name.errors.0|escape }}</div>{% endif %}

                          <div class="form-text">{% translate "Descriptive name for this location" %}</div>
                        </div>
                        <div class="form-floating mb-3">
                          {{ form.location_type|escape }}
                          <label for="{{ form.location_type.id_for_label|escape }}" class="form-label">
                            <i class="lucide lucide-layers" aria-hidden="true"></i>
                            {% translate "Location Type" %}
                            <span class="text-danger">*</span>
                          </label>

                          {% if form.location_type.errors %}
                            <div class="invalid-feedback d-block">{{ form.location_type.errors.0|escape }}</div>
                          {% endif %}

                          <div class="form-text">{% translate "Category that best describes this location" %}</div>
                        </div>
                        <div class="form-floating mb-3">
                          {{ form.project|escape }}
                          <label for="{{ form.project.id_for_label|escape }}" class="form-label">
                            <i class="lucide lucide-folder" aria-hidden="true"></i>
                            {% translate "Associated Project" %}
                          </label>

                          {% if form.project.errors %}<div class="invalid-feedback d-block">{{ form.project.errors.0|escape }}</div>{% endif %}

                          <div class="form-text">{% translate "Link this location to a specific project (optional)" %}</div>
                        </div>
                        <div class="form-floating mb-3">
                          {{ form.address|escape }}
                          <label for="{{ form.address.id_for_label|escape }}" class="form-label">
                            <i class="lucide lucide-map-pin" aria-hidden="true"></i>
                            {% translate "Street Address" %}
                          </label>

                          {% if form.address.errors %}<div class="invalid-feedback d-block">{{ form.address.errors.0|escape }}</div>{% endif %}

                          <div class="form-text">{% translate "Physical address or nearest street address" %}</div>
                        </div>
                      </div>
                    </div>
                    <!-- Coordinates -->
                    <div class="card border-warning mb-4">
                      <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">
                          <i class="lucide lucide-navigation" aria-hidden="true"></i>
                          {% translate "Geographic Coordinates" %}
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="row">
                          <div class="col-md-6">
                            <div class="form-floating mb-3">
                              {{ form.latitude|escape }}
                              <label for="{{ form.latitude.id_for_label|escape }}" class="form-label">
                                <i class="lucide lucide-globe" aria-hidden="true"></i>
                                {% translate "Latitude" %}
                                <span class="text-danger">*</span>
                              </label>

                              {% if form.latitude.errors %}
                                <div class="invalid-feedback d-block">{{ form.latitude.errors.0|escape }}</div>
                              {% endif %}

                              <div class="form-text">{% translate "Decimal degrees (-90 to 90)" %}</div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="form-floating mb-3">
                              {{ form.longitude|escape }}
                              <label for="{{ form.longitude.id_for_label|escape }}" class="form-label">
                                <i class="lucide lucide-globe" aria-hidden="true"></i>
                                {% translate "Longitude" %}
                                <span class="text-danger">*</span>
                              </label>

                              {% if form.longitude.errors %}
                                <div class="invalid-feedback d-block">{{ form.longitude.errors.0|escape }}</div>
                              {% endif %}

                              <div class="form-text">{% translate "Decimal degrees (-180 to 180)" %}</div>
                            </div>
                          </div>
                        </div>
                        <!-- Coordinate validation feedback -->
                        <div id="coordinate-validation" class="d-none">
                          <div class="alert alert-success border-success border-2 mb-0">
                            <i class="lucide lucide-check-circle" aria-hidden="true"></i>
                            <span id="validation-message">{% translate "Coordinates are valid" %}</span>
                            <div class="coordinate-display" id="formatted-coords"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- Additional Details -->
                    <div class="card border-light">
                      <div class="card-header bg-light">
                        <h6 class="mb-0">
                          <i class="lucide lucide-file-text" aria-hidden="true"></i>
                          {% translate "Additional Details" %}
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="form-floating mb-3">
                          {{ form.description|escape }}
                          <label for="{{ form.description.id_for_label|escape }}" class="form-label">
                            <i class="lucide lucide-file-text" aria-hidden="true"></i>
                            {% translate "Description" %}
                          </label>

                          {% if form.description.errors %}
                            <div class="invalid-feedback d-block">{{ form.description.errors.0|escape }}</div>
                          {% endif %}

                          <div class="form-text">{% translate "Detailed description of this location and its purpose" %}</div>
                        </div>
                        <div class="form-floating mb-3">
                          {{ form.tags|escape }}
                          <label for="{{ form.tags.id_for_label|escape }}" class="form-label">
                            <i class="lucide lucide-hash" aria-hidden="true"></i>
                            {% translate "Tags" %}
                          </label>

                          {% if form.tags.errors %}<div class="invalid-feedback d-block">{{ form.tags.errors.0|escape }}</div>{% endif %}

                          <div class="form-text">{% translate "Comma-separated keywords for easy searching and organization" %}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <!-- Interactive Map -->
                    <div class="card border-primary h-100">
                      <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                          <i class="lucide lucide-map" aria-hidden="true"></i>
                          {% translate "Interactive Map" %}
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="map-container">
                          <div id="map"
                               role="application"
                               aria-label="{% translate 'Interactive map for selecting location coordinates' %}"
                               tabindex="0"></div>
                          <div class="map-overlay">
                            <div class="d-flex align-items-center mb-2">
                              <i class="lucide lucide-mouse-pointer me-2" aria-hidden="true"></i>
                              <strong>{% translate "Map Controls" %}</strong>
                            </div>
                            <ul class="small mb-2 ps-3">
                              <li>{% translate "Click to place marker" %}</li>
                              <li>{% translate "Drag marker to adjust" %}</li>
                              <li>{% translate "Scroll to zoom" %}</li>
                            </ul>
                            <div id="current-coords" class="coordinate-display">
                              <span id="coords-display">{% translate "No coordinates set" %}</span>
                            </div>
                          </div>
                        </div>
                        <div class="mt-3">
                          <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                              <i class="lucide lucide-info" aria-hidden="true"></i>
                              {% translate "WGS84 coordinate system" %}
                            </small>
                            <div class="btn-group btn-group-sm" role="group">
                              <button type="button" class="btn btn-outline-secondary" id="center-map">
                                <i class="lucide lucide-crosshair" aria-hidden="true"></i>
                              </button>
                              <button type="button" class="btn btn-outline-secondary" id="clear-marker">
                                <i class="lucide lucide-trash-2" aria-hidden="true"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {% if form.non_field_errors %}
                  <div class="alert alert-danger border-start border-danger border-4 mt-4"
                       role="alert">
                    <div class="d-flex align-items-start">
                      <i class="lucide lucide-alert-circle me-2 mt-1" aria-hidden="true"></i>
                      <div>
                        <strong>{% translate "Form Errors" %}:</strong>

                        {% for error in form.non_field_errors %}
                          <div class="mt-1">{{ error|escape }}</div>
                        {% empty %}
                          <p>No items available.</p>
                        {% endfor %}

                      </div>
                    </div>
                  </div>
                {% endif %}

                <div class="d-flex justify-content-end gap-2 mt-4">
                  <a href="{% url 'infrastructure:spatial_location_list' %}"
                     class="btn btn-outline-secondary"
                     title="{% translate 'Cancel and return to location list' %}"
                     role="link">
                    <i class="lucide lucide-x" aria-hidden="true"></i>
                    {% translate "Cancel" %}
                  </a>
                  <button type="button" class="btn btn-outline-info" id="validate-coords">
                    <i class="lucide lucide-check-square" aria-hidden="true"></i>
                    {% translate "Validate Coordinates" %}
                  </button>
                  <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
                    <i class="lucide lucide-plus-circle" aria-hidden="true"></i>
                    {% translate "Create Location" %}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Global variables
      let map;
      let marker = null;
      const defaultCenter = [39.7684, -86.1581]; // Indianapolis, IN

      // Get form elements
      const form = document.getElementById('location-form');
      const latField = document.getElementById('{{ form.latitude.id_for_label|escape }}');
      const lngField = document.getElementById('{{ form.longitude.id_for_label|escape }}');
      const coordsDisplay = document.getElementById('coords-display');
      const submitBtn = document.getElementById('submit-btn');
      const validateBtn = document.getElementById('validate-coords');
      const centerMapBtn = document.getElementById('center-map');
      const clearMarkerBtn = document.getElementById('clear-marker');
      const coordinateValidation = document.getElementById('coordinate-validation');
      const validationMessage = document.getElementById('validation-message');
      const formattedCoords = document.getElementById('formatted-coords');

      // Add coordinate input classes for styling
      if (latField) latField.classList.add('coordinate-input');
      if (lngField) lngField.classList.add('coordinate-input');

      // Initialize map with enhanced error handling
      try {
        map = L.map('map', {
          center: defaultCenter,
          zoom: 12,
          attributionControl: true,
          zoomControl: true,
          doubleClickZoom: false // Prevent double-click zoom to allow precision clicking
        });

        // Add keyboard navigation support
        map.getContainer().setAttribute('tabindex', '0');
      } catch (error) {
        console.error('{% translate "Failed to initialize map" %}:', error);
        showToast('{% translate "Map initialization failed. Please refresh the page." %}', 'error');
        return;
      }

      // Add enhanced tile layer with error handling
      const tileLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright" role="link">OpenStreetMap</a> contributors',
        maxZoom: 19,
        detectRetina: true,
        errorTileUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNHB4IiBmaWxsPSIjOTk5Ij5UaWxlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4='
      });

      tileLayer.on('tileerror', function(error) {
        console.warn('Tile loading error:', error);
      });

      tileLayer.addTo(map);

      // Initialize marker if coordinates exist
      initializeFromExistingCoordinates();

      // Enhanced map click handler
      map.on('click', function (e) {
        const lat = e.latlng.lat;
        const lng = e.latlng.lng;
        updateCoordinates(lat, lng);
        showToast('{% translate "Coordinates updated from map click" %}', 'success');
      });

      // Enhanced coordinate update functions
      function updateCoordinates(lat, lng) {
        // Validate coordinates
        if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          showToast('{% translate "Invalid coordinates. Please check latitude (-90 to 90) and longitude (-180 to 180)." %}', 'error');
          return;
        }

        // Update form fields
        latField.value = lat.toFixed(6);
        lngField.value = lng.toFixed(6);

        // Trigger validation
        latField.dispatchEvent(new Event('input'));
        lngField.dispatchEvent(new Event('input'));

        // Update display
        updateCoordsDisplay(lat, lng);

        // Update or create marker
        updateMapMarker(lat, lng);

        // Validate coordinates
        validateCoordinates();
      }

      function updateMapMarker(lat, lng) {
        if (marker) {
          marker.setLatLng([lat, lng]);
        } else {
          marker = L.marker([lat, lng], {
            draggable: true,
            title: '{% translate "Drag to adjust position" %}',
            alt: '{% translate "Location marker" %}'
          }).addTo(map);

          // Enhanced drag event with debouncing
          let dragTimeout;
          marker.on('dragend', function(e) {
            clearTimeout(dragTimeout);
            dragTimeout = setTimeout(() => {
              const position = e.target.getLatLng();
              updateCoordinates(position.lat, position.lng);
              showToast('{% translate "Position updated by dragging marker" %}', 'info');
            }, 100);
          });

          // Add marker popup
          marker.bindPopup(`
            <div class="text-center">
              <strong>{% translate "Location Marker" %}</strong><br />
              <small class="text-muted">{% translate "Drag to adjust position" %}</small>
            </div>
          `);
        }

        // Add bounce animation
        if (typeof marker.bounce === 'function') {
          marker.bounce();
        }
      }

      function updateCoordsDisplay(lat, lng) {
        if (coordsDisplay) {
          // WARNING: innerHTML usage - ensure content is properly escaped

          // WARNING: innerHTML usage - ensure content is properly escaped


          coordsDisplay.innerHTML = `
            <div>Lat: ${lat.toFixed(6)}</div>
            <div>Lng: ${lng.toFixed(6)}</div>
          `;
        }
      }

      function initializeFromExistingCoordinates() {
        if (latField.value && lngField.value) {
          const lat = parseFloat(latField.value);
          const lng = parseFloat(lngField.value);
          if (!isNaN(lat) && !isNaN(lng) && isValidCoordinate(lat, lng)) {
            updateMapMarker(lat, lng);
            map.setView([lat, lng], 16);
            updateCoordsDisplay(lat, lng);
            validateCoordinates();
          }
        }
      }

      function updateMarkerFromFields() {
        const lat = parseFloat(latField.value);
        const lng = parseFloat(lngField.value);

        if (!isNaN(lat) && !isNaN(lng) && isValidCoordinate(lat, lng)) {
          updateMapMarker(lat, lng);
          map.setView([lat, lng], 16);
          updateCoordsDisplay(lat, lng);
          validateCoordinates();
        } else if (latField.value === '' && lngField.value === '') {
          clearMarker();
        }
      }

      function isValidCoordinate(lat, lng) {
        return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
      }

      function clearMarker() {
        if (marker) {
          map.removeLayer(marker);
          marker = null;
        }
        if (coordsDisplay) {
          coordsDisplay.textContent = '{% translate "No coordinates set" %}';
        }
        coordinateValidation.classList.add('d-none');
      }

      // Enhanced input validation with real-time feedback
      function setupCoordinateValidation(field, min, max, name) {
        field.addEventListener('input', function() {
          const value = parseFloat(this.value);
          const isEmpty = this.value.trim() === '';

          // Remove existing validation classes
          this.classList.remove('is-valid', 'is-invalid');

          if (isEmpty) {
            this.setCustomValidity('');
          } else if (isNaN(value) || value < min || value > max) {
            this.setCustomValidity(`{% translate "Please enter a valid" %} ${name} {% translate "between" %} ${min} {% translate "and" %} ${max}`);
            this.classList.add('is-invalid');
          } else {
            this.setCustomValidity('');
            this.classList.add('is-valid');
          }
        });

        field.addEventListener('blur', function() {
          if (this.value && !isNaN(parseFloat(this.value))) {
            // Format to 6 decimal places on blur
            this.value = parseFloat(this.value).toFixed(6);
          }
        });
      }

      setupCoordinateValidation(latField, -90, 90, '{% translate "latitude" %}');
      setupCoordinateValidation(lngField, -180, 180, '{% translate "longitude" %}');

      // Event listeners for coordinate field changes
      latField.addEventListener('change', updateMarkerFromFields);
      lngField.addEventListener('change', updateMarkerFromFields);

      // Coordinate validation function
      function validateCoordinates() {
        const lat = parseFloat(latField.value);
        const lng = parseFloat(lngField.value);

        if (!isNaN(lat) && !isNaN(lng) && isValidCoordinate(lat, lng)) {
          coordinateValidation.classList.remove('d-none');
          validationMessage.textContent = '{% translate "Coordinates are valid and precise" %}';
          // WARNING: innerHTML usage - ensure content is properly escaped

          // WARNING: innerHTML usage - ensure content is properly escaped


          formattedCoords.innerHTML = `
            DMS: ${convertToDMS(lat, 'lat')} ${convertToDMS(lng, 'lng')}<br />
            UTM: ${getUTMZone(lat, lng)}
          `;
        } else {
          coordinateValidation.classList.add('d-none');
        }
      }

      // Utility functions for coordinate conversion
      function convertToDMS(decimal, type) {
        const absolute = Math.abs(decimal);
        const degrees = Math.floor(absolute);
        const minutesFloat = (absolute - degrees) * 60;
        const minutes = Math.floor(minutesFloat);
        const seconds = (minutesFloat - minutes) * 60;

        const direction = type === 'lat'
          ? (decimal >= 0 ? 'N' : 'S')
          : (decimal >= 0 ? 'E' : 'W');

        return `${degrees}°${minutes}'${seconds.toFixed(2)}"${direction}`;
      }

      function getUTMZone(lat, lng) {
        const zone = Math.floor((lng + 180) / 6) + 1;
        const letter = lat >= 0 ? 'N' : 'S';
        return `Zone ${zone}${letter}`;
      }

      // Button event handlers
      validateBtn.addEventListener('click', function() {
        validateCoordinates();
        showToast('{% translate "Coordinate validation completed" %}', 'info');
      });

      centerMapBtn.addEventListener('click', function() {
        if (navigator.geolocation) {
          this.disabled = true;
          // WARNING: innerHTML usage - ensure content is properly escaped

          // WARNING: innerHTML usage - ensure content is properly escaped


          this.innerHTML = '<i class="lucide lucide-loader" aria-hidden="true"></i>';

          navigator.geolocation.getCurrentPosition(
            function(position) {
              const lat = position.coords.latitude;
              const lng = position.coords.longitude;
              map.setView([lat, lng], 16);
              updateCoordinates(lat, lng);
              showToast('{% translate "Map centered on your current location" %}', 'success');

              centerMapBtn.disabled = false;
              // WARNING: innerHTML usage - ensure content is properly escaped

              // WARNING: innerHTML usage - ensure content is properly escaped


              centerMapBtn.innerHTML = '<i class="lucide lucide-crosshair" aria-hidden="true"></i>';
            },
            function(error) {
              showToast('{% translate "Unable to get your location. Please set coordinates manually." %}', 'warning');
              centerMapBtn.disabled = false;
              // WARNING: innerHTML usage - ensure content is properly escaped

              // WARNING: innerHTML usage - ensure content is properly escaped


              centerMapBtn.innerHTML = '<i class="lucide lucide-crosshair" aria-hidden="true"></i>';
            },
            { enableHighAccuracy: true, timeout: 10000 }
          );
        } else {
          showToast('{% translate "Geolocation is not supported by this browser" %}', 'error');
        }
      });

      clearMarkerBtn.addEventListener('click', function() {
        clearMarker();
        latField.value = '';
        lngField.value = '';
        latField.classList.remove('is-valid', 'is-invalid');
        lngField.classList.remove('is-valid', 'is-invalid');
        showToast('{% translate "Marker and coordinates cleared" %}', 'info');
      });

      // Enhanced form validation and submission
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!form.checkValidity()) {
          e.stopPropagation();
          form.classList.add('was-validated');
          showToast('{% translate "Please correct the errors in the form" %}', 'error');
          return;
        }

        // Validate that coordinates are set
        const lat = parseFloat(latField.value);
        const lng = parseFloat(lngField.value);

        if (isNaN(lat) || isNaN(lng) || !isValidCoordinate(lat, lng)) {
          showToast('{% translate "Please set valid coordinates using the map or coordinate fields" %}', 'error');
          latField.focus();
          return;
        }

        // Show loading state
        submitBtn.disabled = true;
        // WARNING: innerHTML usage - ensure content is properly escaped

        // WARNING: innerHTML usage - ensure content is properly escaped


        submitBtn.innerHTML = `
          <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
          {% translate "Creating Location..." %}
        `;

        // Disable other form controls
        const formControls = form.querySelectorAll('input, select, textarea, button');
        formControls.forEach(control => control.disabled = true);

        // Submit the form
        setTimeout(() => {
          form.submit();
        }, 500);
      });

      // Toast notification system
      function showToast(message, type = 'info') {
        const toastContainer = getToastContainer();
        const toastId = 'toast-' + Date.now();
        const iconMap = {
          'success': 'check-circle',
          'error': 'alert-circle',
          'warning': 'alert-triangle',
          'info': 'info'
        };

        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = `toast align-items-center text-bg-${type === 'error' ? 'danger' : type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        // WARNING: innerHTML usage - ensure content is properly escaped


        // WARNING: innerHTML usage - ensure content is properly escaped



        toast.innerHTML = `
          <div class="d-flex">
            <div class="toast-body">
              <i class="lucide lucide-${iconMap[type] || 'info'}" aria-hidden="true"></i>
              ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Action"></button>
          </div>
        `;

        toastContainer.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast, { autohide: true, delay: 5000 });
        bsToast.show();

        return toast;
      }

      function getToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
          container = document.createElement('div');
          container.id = 'toast-container';
          container.className = 'toast-container position-fixed top-0 end-0 p-3';
          container.style.zIndex = '1080';
          document.body.appendChild(container);
        }
        return container;
      }

      // Keyboard shortcuts
      document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
          switch(e.key) {
            case 's':
              e.preventDefault();
              form.dispatchEvent(new Event('submit'));
              break;
            case 'l':
              e.preventDefault();
              centerMapBtn.click();
              break;
            case 'c':
              e.preventDefault();
              clearMarkerBtn.click();
              break;
          }
        }
      });

      // Initialize tooltips if Bootstrap is available
      if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl);
        });
      }
      
      // Initialize markdown editors
      initializeMarkdownEditors();
    });
    </script>
    <!-- Unified Markdown Editor JavaScript -->
    <script src="{% static 'js/controllers/markdown_editor_controller.js' %}"></script>
    <script>
            // Initialize markdown editors
            function initializeMarkdownEditors() {
              // Initialize all markdown editor widgets
              const editorWidgets = document.querySelectorAll('[data-editor-widget]');
              editorWidgets.forEach(widget => {
                if (window.MarkdownEditorWidget) {
                  window.MarkdownEditorWidget.initialize(widget);
                }
              });
            }
    </script>
    <!-- Toast container for notifications -->
    <div id="toast-container"
         class="toast-container position-fixed top-0 end-0 p-3"
         style="z-index: 1080"></div>
  {% endblock %}
