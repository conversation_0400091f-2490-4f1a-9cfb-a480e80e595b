"""
Django signals for automatic status tracking in infrastructure app
"""

from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone

from .models import SpatialUtility, UtilityStatus

User = get_user_model()


@receiver(pre_save, sender=SpatialUtility)
def track_status_change_pre_save(sender, instance, **kwargs):
    """
    Pre-save signal to capture the old status before saving changes.
    This stores the previous status in a temporary attribute for use in post_save.
    """
    if instance.pk:
        try:
            old_instance = SpatialUtility.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        except SpatialUtility.DoesNotExist:
            instance._old_status = None
    else:
        instance._old_status = None


@receiver(post_save, sender=SpatialUtility)
def log_status_change(sender, instance, created, **kwargs):
    """
    Post-save signal to automatically log status changes to UtilityStatus model.
    """
    # Get the previous status from the temporary attribute set in pre_save
    old_status = getattr(instance, "_old_status", None)
    current_status = instance.status

    # Only create a status entry if status actually changed or if this is a new object
    if created or (old_status and old_status != current_status):
        # Try to get the current user from the request context
        # Note: In a real application, you might want to pass the user through
        # the save method or use middleware to track the current user
        user = getattr(instance, "_changed_by", None)
        notes = getattr(instance, "_status_change_notes", None)

        # Create a new UtilityStatus entry
        UtilityStatus.objects.create(
            utility=instance,
            status=current_status,
            previous_status=old_status,
            status_date=timezone.now(),
            changed_by=user,
            notes=notes,
        )

        # Clean up temporary attributes
        if hasattr(instance, "_changed_by"):
            delattr(instance, "_changed_by")
        if hasattr(instance, "_status_change_notes"):
            delattr(instance, "_status_change_notes")
        if hasattr(instance, "_old_status"):
            delattr(instance, "_old_status")


@receiver(post_save, sender=UtilityStatus)
def update_utility_status_history(sender, instance, created, **kwargs):
    """
    Post-save signal to update the status_history JSONField in SpatialUtility
    when a new UtilityStatus entry is created.
    """
    if created:
        utility = instance.utility

        # Initialize status_history if it doesn't exist
        if not utility.status_history:
            utility.status_history = []

        # Add the new status entry to the history
        status_entry = {
            "status": instance.status,
            "previous_status": instance.previous_status,
            "status_date": instance.status_date.isoformat(),
            "changed_by": instance.changed_by.id if instance.changed_by else None,
            "changed_by_username": (instance.changed_by.username if instance.changed_by else None),
            "notes": instance.notes,
            "status_entry_id": str(instance.id),  # Reference to the UtilityStatus entry
        }

        utility.status_history.append(status_entry)

        # Update the utility's status to match the status entry
        utility.status = instance.status

        # Save without triggering signals to avoid infinite recursion
        SpatialUtility.objects.filter(pk=utility.pk).update(
            status=instance.status,
            status_history=utility.status_history,
            updated_at=timezone.now(),
        )


def utility_status_change_context(utility, changed_by=None, notes=None):
    """
    Context manager to set user and notes for status changes.

    Usage:
        with utility_status_change_context(utility, user=request.user, notes="Status updated"):
            utility.status = "approved"
            utility.save()
    """

    class UtilityStatusChangeContext:
        def __init__(self, utility, changed_by=None, notes=None):
            self.utility = utility
            self.changed_by = changed_by
            self.notes = notes

        def __enter__(self):
            self.utility._changed_by = self.changed_by
            self.utility._status_change_notes = self.notes
            return self.utility

        def __exit__(self, exc_type, exc_val, exc_tb):
            # Clean up is handled in the signal
            pass

    return UtilityStatusChangeContext(utility, changed_by, notes)


# Helper function to manually create status change with user context
def change_utility_status(utility, new_status, changed_by=None, notes=None):
    """
    Helper function to change utility status with proper user tracking and validation.

    Args:
        utility: SpatialUtility instance
        new_status: New status value
        changed_by: User who made the change
        notes: Optional notes about the change

    Returns:
        UtilityStatus: The created status entry

    Raises:
        ValueError: If the status transition is invalid
    """
    if not utility.can_transition_to(new_status):
        raise ValueError(f"Invalid status transition from '{utility.get_status_display()}' to '{new_status}'")

    # Create the status entry directly (this will trigger the signal)
    status_entry = UtilityStatus.objects.create(
        utility=utility,
        status=new_status,
        previous_status=utility.status,
        status_date=timezone.now(),
        changed_by=changed_by,
        notes=notes,
    )

    return status_entry
