{% load custom_filters %}

<!-- Timeline Metrics Panel Component -->
<main>
  <div class="row grid-cols-1  md:grid-cols-2 lg:grid-cols-4 gap-3 mb-6">
    <!-- Total Tasks Metric -->
    <div class="metrics-card p-3">
      <div class="d-flex align-items-center">
        <div class="flex-shrink-0 ">
          <div class="w-8  h-8  bg-blue-100  rounded-3 d-flex align-items-center justify-content-center">
            <i data-lucide="list-checks" class="h-4  w-4  text-blue-600 "></i>
          </div>
        </div>
        <div class="ms-3 flex-1 ">
          <p class="small font-medium text-gray-900 ">Total Tasks</p>
          <p class="fs-3 fw-bold text-gray-900 ">{{ metrics.total_tasks|default:0 }}</p>
        </div>
      </div>
    </div>
    <!-- Completion Rate Metric -->
    <div class="metrics-card p-3">
      <div class="d-flex align-items-center">
        <div class="flex-shrink-0 ">
          <div class="w-8  h-8  bg-green-100  rounded-3 d-flex align-items-center justify-content-center">
            <i class="bi bi-check-circle h-4 text-green-600 w-4"></i>
          </div>
        </div>
        <div class="ms-3 flex-1 ">
          <p class="small font-medium text-gray-900 ">Completion Rate</p>
          <div class="d-flex align-items-center space-x-2">
            <p class="fs-3 fw-bold text-gray-900 ">{{ metrics.completion_percentage|floatformat:0 }}%</p>
            <div class="flex-1 ">
              <div class="progress-bar h-2  bg-light rounded-circle">
                <div class="progress-fill h-100 bg-success rounded-circle transition duration-500"
                     style="width: {{ metrics.completion_percentage|escape }}%"></div>
              </div>
            </div>
          </div>
          <p class="text-xs  text-muted mt-1">
            {{ metrics.completed_tasks|escape }} of {{ metrics.total_tasks|escape }} completed
          </p>
        </div>
      </div>
    </div>
    <!-- Critical Path Tasks -->
    <div class="metrics-card p-3">
      <div class="d-flex align-items-center">
        <div class="flex-shrink-0 ">
          <div class="w-8  h-8  bg-red-100  rounded-3 d-flex align-items-center justify-content-center">
            <i data-lucide="route" class="h-4  w-4  text-red-600 "></i>
          </div>
        </div>
        <div class="ms-3 flex-1 ">
          <p class="small font-medium text-gray-900 ">Critical Path</p>
          <p class="fs-3 fw-bold text-gray-900 ">{{ critical_path_count|default:0 }}</p>
          <p class="text-xs  text-muted mt-1">

            {% if critical_path_count > 0 %}
              {{ critical_path_count|escape }} task{{ critical_path_count|pluralize }} on critical path
            {% else %}
              No critical path calculated
            {% endif %}

          </p>
        </div>
      </div>
    </div>
    <!-- Schedule Health -->
    <div class="metrics-card p-3">
      <div class="d-flex align-items-center">
        <div class="flex-shrink-0 ">
          <div class="w-8  h-8 
            {% if conflicts_count > 0 %}bg-yellow-100{% else %}bg-green-100{% endif %}
             rounded-3 d-flex align-items-center justify-content-center">

            {% if conflicts_count > 0 %}
              <i data-lucide="alert-triangle" class="h-4  w-4  text-yellow-600 "></i>
            {% else %}
              <i data-lucide="shield-check" class="h-4  w-4  text-green-600 "></i>
            {% endif %}

          </div>
        </div>
        <div class="ms-3 flex-1 ">
          <p class="small font-medium text-gray-900 ">Schedule Health</p>

          {% if conflicts_count > 0 %}
            <p class="fs-3 fw-bold text-yellow-600 ">{{ conflicts_count|escape }}</p>
            <p class="text-xs  text-yellow-600  mt-1">Conflict{{ conflicts_count|pluralize }} detected</p>
          {% else %}
            <p class="fs-3 fw-bold text-green-600 ">Good</p>
            <p class="text-xs  text-green-600  mt-1">No conflicts detected</p>
          {% endif %}

        </div>
      </div>
    </div>
  </div>
  <!-- Project Timeline Summary -->

  {% if metrics.project_start and metrics.project_end %}
    <div class="metrics-card p-3 mb-6">
      <div class="d-flex align-items-center justify-content-between mb-3">
        <h3 class="fs-5 fw-semibold text-gray-900 ">
          <i class="bi bi-calendar d-inline h-5 me-2 w-5"></i>
          Project Timeline
        </h3>
        <div class="small text-muted">{{ metrics.project_duration|escape }} day{{ metrics.project_duration|pluralize }}</div>
      </div>
      <div class="row grid-cols-1  md:grid-cols-3 gap-3">
        <!-- Start Date -->
        <div class="text-center">
          <div class="small font-medium text-secondary">Start Date</div>
          <div class="fs-5 fw-bold text-gray-900 ">{{ metrics.project_start|date:"M d, Y" }}</div>
        </div>
        <!-- Progress -->
        <div class="text-center">
          <div class="small font-medium text-secondary">Average Progress</div>
          <div class="fs-5 fw-bold text-gray-900 ">{{ metrics.average_progress|escape }}%</div>
          <div class="mt-2">
            <div class="progress-bar h-3  bg-light rounded-circle mx-auto  max-w-24">
              <div class="progress-fill h-100 bg-primary rounded-circle transition duration-500"
                   style="width: {{ metrics.average_progress|escape }}%"></div>
            </div>
          </div>
        </div>
        <!-- End Date -->
        <div class="text-center">
          <div class="small font-medium text-secondary">End Date</div>
          <div class="fs-5 fw-bold text-gray-900 ">{{ metrics.project_end|date:"M d, Y" }}</div>
        </div>
      </div>
    </div>
  {% endif %}

  <!-- Recent Conflicts (if any) -->

  {% if conflicts %}
    <div class="metrics-card p-3">
      <div class="d-flex align-items-center justify-content-between mb-3">
        <h3 class="small fw-semibold text-gray-900 ">
          <i data-lucide="alert-triangle"
             class="h-4  w-4  me-2 d-inline text-yellow-500"></i>
          Recent Conflicts
        </h3>

        {% if conflicts_count > conflicts|length %}
          <span class="text-xs  text-muted">+{{ conflicts_count|sub:conflicts|length }} more</span>
        {% endif %}

      </div>
      <div class="space-y-3">

        {% for conflict in conflicts %}
          <div class="d-flex align-items-start space-x-3">
            <div class="flex-shrink-0  mt-1">

              {% if conflict.type == "date_conflict" %}
                <div class="w-2  h-2  bg-danger rounded-circle"></div>
              {% elif conflict.type == "dependency_conflict" %}
                <div class="w-2  h-2  bg-orange-500  rounded-circle"></div>
              {% elif conflict.type == "resource_conflict" %}
                <div class="w-2  h-2  bg-yellow-500  rounded-circle"></div>
              {% else %}
                <div class="w-2  h-2  bg-gray-500  rounded-circle"></div>
              {% endif %}

            </div>
            <div class="flex-1  min-w-0">
              <p class="small font-medium text-gray-900 ">{{ conflict.task_title|truncatechars:30 }}</p>
              <p class="text-xs  text-secondary">{{ conflict.message|escape }}</p>
            </div>
          </div>
        {% empty %}
          <p>No items available.</p>
        {% endfor %}

      </div>

      {% if conflicts_count > 0 %}
        <div class="mt-3 pt-3 border-t  border-gray-200 ">
          <button class="text-xs  text-egis-blue  hover:text-egis-blue-dark  font-medium"
                  type="button"
                  aria-label="View All Conflicts">View All Conflicts</button>
        </div>
      {% endif %}

    </div>
  {% endif %}
