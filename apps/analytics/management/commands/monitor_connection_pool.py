"""from typing import ClassVar
from datetime import datetime
from django.conf import settings
from django.db import connection
from django.db import transaction
from django.db.models import Avg
from django.db.models import Max
from rest_framework import status
from typing import Type
import json
import re
import time

Django Management Command: Database Connection Pool Monitor
===========================================================

Comprehensive database connection pool monitoring and management command.
Provides real-time monitoring, health checks, and optimization recommendations.

Usage:
    python manage.py monitor_connection_pool                    # Start monitoring
    python manage.py monitor_connection_pool --load-test        # Run load test
    python manage.py monitor_connection_pool --health-check     # One-time health check
    python manage.py monitor_connection_pool --optimize         # Generate optimization recommendations

Author: Claude Agent 6
Date: 2025-06-30
Priority: HIGH-004
"""

import json
import time
from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand, CommandError
from django.db import connection

try:
    from config.database.connection_pooling.connection_monitoring import (
        get_connection_monitor,
    )
    from config.database.connection_pooling.load_testing import (
        LoadTestSuite,
    )
    from config.database.connection_pooling.production_pool_config import (
        DatabaseConnectionPoolOptimizer,
        DatabaseError,
        DeploymentType,
        IntegrityError,
        OperationalError,
    )

    POOLING_AVAILABLE = True
except ImportError:
    POOLING_AVAILABLE = False


class Command(BaseCommand):
    """Database connection pool monitoring and management command."""

    help = "Monitor and manage database connection pool performance"

    def add_arguments(self, parser):
        parser.add_argument(
            "--monitor",
            action="store_true",
            help="Start continuous connection pool monitoring",
        )
        parser.add_argument(
            "--interval",
            type=int,
            default=30,
            help="Monitoring interval in seconds (default: 30)",
        )
        parser.add_argument(
            "--load-test",
            action="store_true",
            help="Run comprehensive load testing suite",
        )
        parser.add_argument(
            "--scenario",
            type=str,
            choices=[
                "light_load",
                "normal_load",
                "heavy_load",
                "stress_test",
                "spatial_heavy",
                "all",
            ],
            default="normal_load",
            help="Load test scenario to run",
        )
        parser.add_argument("--health-check", action="store_true", help="Perform one-time health check")
        parser.add_argument(
            "--optimize",
            action="store_true",
            help="Generate optimization recommendations",
        )
        parser.add_argument(
            "--generate-config",
            action="store_true",
            help="Generate optimized PgBouncer configuration",
        )
        parser.add_argument(
            "--deployment-type",
            type=str,
            choices=[
                "development",
                "staging",
                "production_small",
                "production_medium",
                "production_large",
            ],
            help="Override deployment type for configuration generation",
        )
        parser.add_argument(
            "--output",
            type=str,
            help="Output file path for generated configurations or reports",
        )

    def handle(self, *args, **options):
        """Handle command execution."""
        if not POOLING_AVAILABLE:
            raise CommandError(
                "Connection pooling modules not available. "
                "Please ensure the config.database.connection_pooling package is properly installed.",
            )

        # Determine what action to take
        if options["health_check"]:
            self.perform_health_check()
        elif options["load_test"]:
            self.run_load_test(options)
        elif options["optimize"]:
            self.generate_optimization_recommendations(options)
        elif options["generate_config"]:
            self.generate_configuration(options)
        elif options["monitor"]:
            self.start_monitoring(options)
        else:
            # Default: show current status
            self.show_current_status()

    def perform_health_check(self):
        """Perform one-time health check."""
        self.stdout.write(self.style.SUCCESS("Performing database connection pool health check..."))

        try:
            # Test basic database connectivity
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            self.stdout.write("✓ Database connectivity: OK")

            # Get connection monitor
            monitor = get_connection_monitor()
            health_status = monitor.get_health_status()

            self.stdout.write(f"✓ Pool health status: {health_status['status']}")
            self.stdout.write(f"  Message: {health_status['message']}")

            if "metrics" in health_status:
                metrics = health_status["metrics"]
                self.stdout.write(f"  Active connections: {metrics.get('active_connections', 'N/A')}")
                self.stdout.write(f"  Pool utilization: {metrics.get('pool_utilization', 0):.1%}")
                self.stdout.write(f"  Memory usage: {metrics.get('memory_usage_mb', 0):.1f} MB")

            # Check configuration
            self._check_configuration()

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.stdout.write(self.style.ERROR(f"Health check failed: {e!s}"))

    def run_load_test(self, options):
        """Run load testing suite."""
        self.stdout.write(self.style.SUCCESS("Starting database connection pool load testing..."))

        try:
            if options["scenario"] == "all":
                # Run full test suite
                suite = LoadTestSuite()
                results = suite.run_full_suite()

                self.stdout.write(self.style.SUCCESS("Load testing suite completed!"))

                # Print summary
                for scenario_name, result in results.items():
                    success_rate = (1 - result.error_rate) * 100
                    self.stdout.write(f"\n{scenario_name}:")
                    self.stdout.write(f"  - Success rate: {success_rate:.1f}%")
                    self.stdout.write(f"  - Avg response time: {result.average_response_time:.2f}s")
                    self.stdout.write(f"  - Requests/second: {result.requests_per_second:.1f}")

            else:
                # Run single scenario
                suite = LoadTestSuite()
                scenario = suite.scenarios[options["scenario"]]

                # Start monitoring
                suite.monitor.start_monitoring()

                try:
                    result = suite.simulator.run_load_test(scenario)
                    suite._print_scenario_results(result)

                    if options["output"]:
                        # Save detailed results
                        with open(options["output"], "w") as f:
                            json.dump(
                                {
                                    "scenario": scenario.__dict__,
                                    "results": result.__dict__,
                                },
                                f,
                                indent=2,
                                default=str,
                            )
                        self.stdout.write(f"Detailed results saved to: {options['output']}")

                finally:
                    suite.monitor.stop_monitoring()

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stdout.write(self.style.ERROR(f"Load testing failed: {e!s}"))

    def generate_optimization_recommendations(self, options):
        """Generate optimization recommendations."""
        self.stdout.write(self.style.SUCCESS("Generating optimization recommendations..."))

        try:
            # Analyze current configuration
            deployment_type = self._detect_deployment_type(options.get("deployment_type"))
            optimizer = DatabaseConnectionPoolOptimizer(deployment_type)

            current_config = optimizer.get_optimal_config()
            recommendations = []

            # Check current settings vs optimal
            self.stdout.write(f"\nCurrent deployment type: {deployment_type.value}")
            self.stdout.write("Recommended configuration:")
            self.stdout.write(f"  - Use PgBouncer: {current_config.use_pgbouncer}")
            self.stdout.write(f"  - Connection max age: {current_config.conn_max_age}s")
            self.stdout.write(f"  - Default pool size: {current_config.default_pool_size}")
            self.stdout.write(f"  - Max client connections: {current_config.max_client_conn}")

            # Check actual vs recommended settings
            actual_conn_max_age = settings.DATABASES["default"].get("CONN_MAX_AGE", 0)
            if actual_conn_max_age != current_config.conn_max_age:
                recommendations.append(
                    f"Update CONN_MAX_AGE from {actual_conn_max_age} to {current_config.conn_max_age}",
                )

            # Check PgBouncer usage
            use_pgbouncer = settings.DATABASES["default"].get("HOST", "localhost") != "localhost"
            if use_pgbouncer != current_config.use_pgbouncer:
                if current_config.use_pgbouncer:
                    recommendations.append("Consider enabling PgBouncer for better connection management")
                else:
                    recommendations.append("PgBouncer may not be necessary for this deployment size")

            # Performance recommendations
            monitor = get_connection_monitor()
            if monitor.metrics_history:
                latest_metrics = monitor.metrics_history[-1]

                if latest_metrics.pool_utilization > 0.8:
                    recommendations.append(
                        f"High pool utilization "
                        f"({latest_metrics.pool_utilization:.1%}). "
                        "Consider increasing pool size.",
                    )

                if latest_metrics.avg_connection_time > 1.0:
                    recommendations.append(
                        f"Slow connection times "
                        f"({latest_metrics.avg_connection_time:.2f}s). "
                        "Check database performance and network latency.",
                    )

            if recommendations:
                self.stdout.write("\nRecommendations:")
                for i, rec in enumerate(recommendations, 1):
                    self.stdout.write(f"  {i}. {rec}")
            else:
                self.stdout.write(self.style.SUCCESS("\nConfiguration appears optimal!"))

            # Save recommendations if output specified
            if options["output"]:
                with open(options["output"], "w") as f:
                    json.dump(
                        {
                            "deployment_type": deployment_type.value,
                            "optimal_configuration": current_config.__dict__,
                            "recommendations": recommendations,
                            "timestamp": datetime.now().isoformat(),
                        },
                        f,
                        indent=2,
                    )
                self.stdout.write(f"Recommendations saved to: {options['output']}")

        except (FileNotFoundError, PermissionError, OSError) as e:
            self.stdout.write(self.style.ERROR(f"Optimization analysis failed: {e!s}"))

    def generate_configuration(self, options):
        """Generate optimized configuration files."""
        self.stdout.write(self.style.SUCCESS("Generating optimized configuration..."))

        try:
            deployment_type = self._detect_deployment_type(options.get("deployment_type"))
            optimizer = DatabaseConnectionPoolOptimizer(deployment_type)

            # Generate PgBouncer configuration
            output_path = options.get("output") or f"pgbouncer_{deployment_type.value}.ini"
            optimizer.generate_pgbouncer_ini(output_path)

            self.stdout.write(f"✓ PgBouncer configuration generated: {output_path}")

            # Generate Django settings snippet
            settings_path = output_path.replace(".ini", "_django_settings.py")
            django_config = optimizer.get_django_database_config()

            with open(settings_path, "w") as f:
                f.write("# Optimized Django database configuration\n")
                f.write(f"# Generated for deployment type: {deployment_type.value}\n")
                f.write(f"# Generated on: {datetime.now().isoformat()}\n\n")
                f.write("DATABASES = {\n")
                f.write("    'default': ")
                f.write(json.dumps(django_config, indent=8).replace("null", "None"))
                f.write("\n}\n")

            self.stdout.write(f"✓ Django settings generated: {settings_path}")

            # Print configuration summary
            config = optimizer.get_optimal_config()
            self.stdout.write(f"\nConfiguration summary for {deployment_type.value}:")
            self.stdout.write("  - Pool mode: transaction")
            self.stdout.write(f"  - Default pool size: {config.default_pool_size}")
            self.stdout.write(f"  - Max client connections: {config.max_client_conn}")
            self.stdout.write(f"  - Connection max age: {config.conn_max_age}s")
            self.stdout.write(f"  - Health checks enabled: {config.conn_health_checks}")

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.stdout.write(self.style.ERROR(f"Configuration generation failed: {e!s}"))

    def start_monitoring(self, options):
        """Start continuous monitoring."""
        self.stdout.write(self.style.SUCCESS("Starting database connection pool monitoring..."))
        self.stdout.write("Press Ctrl+C to stop monitoring")

        try:
            monitor = get_connection_monitor()
            monitor.monitoring_interval = options["interval"]
            monitor.start_monitoring()

            self.stdout.write(f"Monitoring every {options['interval']} seconds...")

            try:
                while True:
                    time.sleep(10)  # Check every 10 seconds for display updates

                    health = monitor.get_health_status()
                    status_color = self.style.SUCCESS if health["status"] == "healthy" else self.style.WARNING

                    self.stdout.write(
                        f"\r{datetime.now().strftime('%H:%M:%S')} - "
                        f"Status: {status_color(health['status'])} - "
                        f"{health['message']}",
                        ending="",
                    )

                    # Show detailed metrics every minute
                    if int(time.time()) % 60 == 0 and "metrics" in health:
                        metrics = health["metrics"]
                        self.stdout.write(
                            f"\n  Active: {metrics.get('active_connections', 0)}, "
                            f"Pool: {metrics.get('pool_utilization', 0):.1%}, "
                            f"QPS: {metrics.get('queries_per_second', 0):.1f}",
                        )

            except KeyboardInterrupt:
                self.stdout.write(self.style.SUCCESS("\nStopping monitoring..."))

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.stdout.write(self.style.ERROR(f"Monitoring failed: {e!s}"))
        finally:
            if "monitor" in locals():
                monitor.stop_monitoring()

    def show_current_status(self):
        """Show current connection pool status."""
        self.stdout.write(self.style.SUCCESS("Database Connection Pool Status"))
        self.stdout.write("=" * 40)

        try:
            # Basic database info
            db_config = settings.DATABASES["default"]
            self.stdout.write(f"Database: {db_config.get('NAME')}")
            self.stdout.write(f"Host: {db_config.get('HOST')}")
            self.stdout.write(f"Port: {db_config.get('PORT')}")
            self.stdout.write(f"Connection max age: {db_config.get('CONN_MAX_AGE')}s")

            # Check if using optimized configuration
            if hasattr(settings, "DATABASES") and "config.database.connection_pooling" in str(settings.DATABASES):
                self.stdout.write("✓ Using optimized connection pooling")
            else:
                self.stdout.write("⚠ Using basic configuration (consider optimizing)")

            # Current metrics
            monitor = get_connection_monitor()
            health = monitor.get_health_status()

            self.stdout.write(f"\nHealth Status: {health['status']}")
            self.stdout.write(f"Message: {health['message']}")

            if "metrics" in health:
                metrics = health["metrics"]
                self.stdout.write("\nCurrent Metrics:")
                self.stdout.write(f"  - Active connections: {metrics.get('active_connections', 'N/A')}")
                self.stdout.write(f"  - Idle connections: {metrics.get('idle_connections', 'N/A')}")
                self.stdout.write(f"  - Pool utilization: {metrics.get('pool_utilization', 0):.1%}")
                self.stdout.write(f"  - Memory usage: {metrics.get('memory_usage_mb', 0):.1f} MB")

            self.stdout.write("\nUse --help to see available monitoring options")

        except (DatabaseError, IntegrityError, OperationalError) as e:
            self.stdout.write(self.style.ERROR(f"Failed to get status: {e!s}"))

    def _detect_deployment_type(self, override_type=None):
        """Detect or override deployment type."""
        if override_type:
            return DeploymentType(override_type)

        # Auto-detect from settings
        settings_module = getattr(settings, "SETTINGS_MODULE", "")
        if "dev" in settings_module:
            return DeploymentType.DEVELOPMENT
        if "staging" in settings_module:
            return DeploymentType.STAGING
        if "production" in settings_module:
            return DeploymentType.PRODUCTION_MEDIUM
        return DeploymentType.DEVELOPMENT

    def _check_configuration(self):
        """Check current configuration for common issues."""
        issues = []
        warnings = []

        db_config = settings.DATABASES["default"]

        # Check CONN_MAX_AGE
        conn_max_age = db_config.get("CONN_MAX_AGE", 0)
        if conn_max_age > 600:
            warnings.append(f"CONN_MAX_AGE is quite high ({conn_max_age}s). Consider using connection pooling.")

        # Check SSL mode
        ssl_mode = db_config.get("OPTIONS", {}).get("sslmode", "prefer")
        if ssl_mode in ["disable", "allow"]:
            issues.append(f"SSL mode '{ssl_mode}' is insecure for production")
        elif ssl_mode == "prefer":
            warnings.append("SSL mode 'prefer' should be 'require' or stronger for production")

        # Check health checks
        health_checks = db_config.get("OPTIONS", {}).get("CONN_HEALTH_CHECKS", True)
        if not health_checks:
            warnings.append("Connection health checks are disabled")

        # Print results
        if issues:
            self.stdout.write("✗ Configuration Issues:")
            for issue in issues:
                self.stdout.write(f"  - {issue}")

        if warnings:
            self.stdout.write("⚠ Configuration Warnings:")
            for warning in warnings:
                self.stdout.write(f"  - {warning}")

        if not issues and not warnings:
            self.stdout.write("✓ Configuration looks good")
