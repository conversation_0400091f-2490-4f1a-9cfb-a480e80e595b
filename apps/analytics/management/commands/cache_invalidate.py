"""Analytics cache invalidation management command.

This management command allows invalidating cache entries by type, tag, pattern,
or for specific users, with dry run support and confirmation prompts.
"""

import time
from typing import Any

from django.core.management.base import BaseCommand, CommandParser

from apps.common.caching.config import CacheConfig
from apps.common.caching.htmx_cache_service import htmx_cache_service


class Command(BaseCommand):
    """Invalidate cache entries by type, tag, or pattern."""

    help = "Invalidate cache entries by type, tag, or pattern"
    requires_system_checks: list[str] = []

    _tag_to_cache_mapping: dict[str, str] = {
        "dashboard_stats": "DASHBOARD",
        "user_projects": "PROJECT_DATA",
        "navigation": "STATIC_CONTENT",
        "analytics": "ANALYTICS",
        "search_index": "SEARCH_RESULTS",
        "spatial_data": "SPATIAL_DATA",
        "session_data": "SESSION_DATA",
    }

    def add_arguments(self, parser: <PERSON>Parser) -> None:
        """Add command line arguments."""
        parser.add_argument(
            "--cache-type",
            type=str,
            help="Cache type to invalidate (e.g., HTMX_FRAGMENTS, DASHBOARD)",
        )
        parser.add_argument(
            "--tags",
            type=str,
            nargs="+",
            help="Cache tags to invalidate (e.g., dashboard_stats user_projects)",
        )
        parser.add_argument(
            "--user-id",
            type=int,
            help="User ID to invalidate cache for (user-specific cache only)",
        )
        parser.add_argument(
            "--pattern",
            type=str,
            help="Cache key pattern to invalidate (use with caution)",
        )
        parser.add_argument(
            "--all",
            action="store_true",
            help="Clear all cache (WARNING: This will clear everything)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be invalidated without actually doing it",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Skip confirmation prompts",
        )

    def handle(self, *args: Any, **options: Any) -> None:
        """Execute the command with the provided options."""
        if options["dry_run"]:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No cache will actually be cleared"))

        start_time = time.time()

        if options["all"]:
            self.invalidate_all_cache(options)
        elif options["cache_type"]:
            self.invalidate_cache_type(options["cache_type"], options)
        elif options["tags"]:
            self.invalidate_by_tags(options["tags"], options)
        elif options["user_id"]:
            self.invalidate_user_cache(options["user_id"], options)
        elif options["pattern"]:
            self.invalidate_by_pattern(options["pattern"], options)
        else:
            self.stdout.write(self.style.ERROR("Please specify what to invalidate using one of the options."))
            return

        elapsed = time.time() - start_time
        self.stdout.write(self.style.SUCCESS(f"Cache invalidation completed in {elapsed:.2f} seconds"))

    def _confirm_action(self, message: str) -> bool:
        """Get user confirmation for potentially destructive actions."""
        response = input(f"{message} (y/N): ")
        return response.lower() == "y"

    def invalidate_all_cache(self, options: dict[str, Any]) -> None:
        """Invalidate all cache entries across all cache types."""
        if not options["force"] and not options["dry_run"]:
            if not self._confirm_action("This will clear ALL cache entries. Are you sure?"):
                self.stdout.write("Cancelled.")
                return

        self.stdout.write(self.style.WARNING("Invalidating ALL cache entries..."))

        if not options["dry_run"]:
            self._clear_all_cache_types()
        else:
            self._simulate_clear_all_cache_types()

    def _clear_all_cache_types(self) -> None:
        """Clear all cache types with error handling."""
        for cache_type in CacheConfig.get_all_cache_types():
            try:
                cache_instance = CacheConfig.get_cache_instance(cache_type)
                cache_instance.clear()
                self.stdout.write(f"  ✓ Cleared {cache_type}")
            except (
                AttributeError,
                KeyError,
                ImportError,
                ConnectionError,
                TimeoutError,
            ) as e:
                self.stdout.write(self.style.ERROR(f"  ✗ Error clearing {cache_type}: {e}"))

    def _simulate_clear_all_cache_types(self) -> None:
        """Simulate clearing all cache types for dry run."""
        for cache_type in CacheConfig.get_all_cache_types():
            self.stdout.write(f"  Would clear: {cache_type}")

    def invalidate_cache_type(self, cache_type: str, options: dict[str, Any]) -> None:
        """Invalidate all entries for a specific cache type."""
        if not CacheConfig.validate_cache_type(cache_type):
            self.stdout.write(self.style.ERROR(f"Unknown cache type: {cache_type}"))
            return

        self.stdout.write(f"Invalidating cache type: {cache_type}")

        if not options["dry_run"]:
            try:
                cache_instance = CacheConfig.get_cache_instance(cache_type)
                cache_instance.clear()
                self.stdout.write(self.style.SUCCESS(f"✓ Cleared all {cache_type} entries"))
            except (
                AttributeError,
                KeyError,
                ImportError,
                ConnectionError,
                TimeoutError,
            ) as e:
                self.stdout.write(self.style.ERROR(f"✗ Error clearing {cache_type}: {e}"))
        else:
            self.stdout.write(f"Would clear all {cache_type} entries")

    def invalidate_by_tags(self, tags: list[str], options: dict[str, Any]) -> None:
        """Invalidate cache entries by tags."""
        self.stdout.write(f"Invalidating cache by tags: {', '.join(tags)}")

        if not options["dry_run"]:
            try:
                htmx_cache_service.invalidate_cache_by_tags(tags)
                self.stdout.write(self.style.SUCCESS(f"✓ Invalidated cache for tags: {', '.join(tags)}"))
            except (
                AttributeError,
                KeyError,
                ImportError,
                ConnectionError,
                TimeoutError,
            ) as e:
                self.stdout.write(self.style.ERROR(f"✗ Error invalidating by tags: {e}"))
        else:
            self._simulate_tag_invalidation(tags)

    def _simulate_tag_invalidation(self, tags: list[str]) -> None:
        """Simulate tag invalidation for dry run."""
        for tag in tags:
            cache_type = self._tag_to_cache_mapping.get(tag, "HTMX_FRAGMENTS")
            self.stdout.write(f"  Would invalidate {tag} in {cache_type}")

    def invalidate_user_cache(self, user_id: int, options: dict[str, Any]) -> None:
        """Invalidate cache entries for a specific user."""
        self.stdout.write(f"Invalidating cache for user ID: {user_id}")

        if not options["dry_run"]:
            try:
                htmx_cache_service.invalidate_user_cache(user_id)
                self.stdout.write(self.style.SUCCESS(f"✓ Invalidated cache for user {user_id}"))
            except (
                AttributeError,
                KeyError,
                ImportError,
                ConnectionError,
                TimeoutError,
            ) as e:
                self.stdout.write(self.style.ERROR(f"✗ Error invalidating user cache: {e}"))
        else:
            self.stdout.write(f"Would invalidate all cache entries for user {user_id}")

    def invalidate_by_pattern(self, pattern: str, options: dict[str, Any]) -> None:
        """Invalidate cache entries by key pattern."""
        if not options["force"] and not options["dry_run"]:
            message = f'This will clear all cache keys matching pattern "{pattern}". Are you sure?'
            if not self._confirm_action(message):
                self.stdout.write("Cancelled.")
                return

        self.stdout.write(f"Invalidating cache by pattern: {pattern}")

        if not options["dry_run"]:
            self._execute_pattern_invalidation(pattern)
        else:
            self.stdout.write(f"Would invalidate all keys matching: {pattern}")

    def _execute_pattern_invalidation(self, pattern: str) -> None:
        """Execute pattern invalidation across all cache types."""
        invalidated_count = 0

        for cache_type in CacheConfig.get_all_cache_types():
            try:
                cache_instance = CacheConfig.get_cache_instance(cache_type)
                if hasattr(cache_instance, "delete_pattern"):
                    result = cache_instance.delete_pattern(pattern)
                    if isinstance(result, int):
                        invalidated_count += result
                    self.stdout.write(f"  ✓ Pattern deleted from {cache_type}")
                else:
                    self.stdout.write(f"  ⚠ Pattern deletion not supported for {cache_type}")

            except (
                AttributeError,
                KeyError,
                ImportError,
                ConnectionError,
                TimeoutError,
            ) as e:
                self.stdout.write(self.style.ERROR(f"  ✗ Error in {cache_type}: {e}"))

        if invalidated_count > 0:
            self.stdout.write(self.style.SUCCESS(f"✓ Invalidated {invalidated_count} entries"))
        else:
            self.stdout.write(self.style.WARNING("No entries found matching pattern"))

    def show_invalidation_impact(self, options: dict[str, Any]) -> None:
        """Show what would be affected by invalidation."""
        self.stdout.write("\nInvalidation Impact:")
        self.stdout.write("-" * 20)

        if options["tags"]:
            invalidation_settings = CacheConfig.invalidation_settings
            patterns = invalidation_settings.get("PATTERNS", {})

            for tag in options["tags"]:
                related_actions = [action for action, action_tags in patterns.items() if tag in action_tags]

                if related_actions:
                    self.stdout.write(f"  {tag}: affects {', '.join(related_actions)}")
                else:
                    self.stdout.write(f"  {tag}: no configured patterns")
