"""
Notification throttling and aggregation service.

Prevents spam by implementing rate limiting and groups similar notifications.
"""

import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Dict, Optional, Tuple

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone

from ..models import Notification, NotificationSettings

User = get_user_model()
logger = logging.getLogger(__name__)


class NotificationThrottlingService:
    """
    Service for implementing notification rate limiting and aggregation.

    Features:
    - Rate limiting per user per notification type
    - Notification aggregation for similar events
    - Time-based throttling windows
    - Cache-based performance optimization
    """

    def __init__(self):
        """Initialize throttling service."""
        self.cache_prefix = "notification_throttle:"
        self.aggregation_cache_prefix = "notification_aggregate:"
        self.default_limits = {
            "hourly": getattr(settings, "NOTIFICATION_HOURLY_LIMIT", 60),
            "daily": getattr(settings, "NOTIFICATION_DAILY_LIMIT", 500),
            "burst": getattr(settings, "NOTIFICATION_BURST_LIMIT", 10),  # 10 per minute
        }

    def should_throttle_notification(
        self,
        user: User,
        notification_type: str,
        organization_id: int,
        priority: str = "normal",
    ) -> Tuple[bool, str]:
        """
        Check if notification should be throttled.

        Args:
            user: User receiving notification
            notification_type: Type of notification
            organization_id: Organization context
            priority: Notification priority (urgent notifications bypass some limits)

        Returns:
            Tuple of (should_throttle, reason)
        """
        # Urgent notifications bypass most throttling
        if priority == "urgent":
            return False, ""

        # Check organization settings
        org_settings = self._get_organization_settings(organization_id)
        if not org_settings:
            return False, "No organization settings found"

        # Check burst limit (per minute)
        if self._check_burst_limit_exceeded(user, notification_type):
            return True, f"Burst limit exceeded for {notification_type}"

        # Check hourly limit
        if self._check_hourly_limit_exceeded(user, notification_type, org_settings):
            return True, f"Hourly limit exceeded for {notification_type}"

        # Check daily limit
        if self._check_daily_limit_exceeded(user, notification_type, org_settings):
            return True, f"Daily limit exceeded for {notification_type}"

        return False, ""

    def should_aggregate_notification(
        self,
        user: User,
        notification_type: str,
        message: str,
        related_object: Any = None,
        organization_id: int = None,
    ) -> Optional[str]:
        """
        Check if notification should be aggregated with existing ones.

        Args:
            user: User receiving notification
            notification_type: Type of notification
            message: Notification message
            related_object: Related object if any
            organization_id: Organization context

        Returns:
            Aggregation key if should aggregate, None otherwise
        """
        # Types that should be aggregated
        aggregatable_types = {
            "comment": 15,  # Aggregate comments for 15 minutes
            "mention": 10,  # Aggregate mentions for 10 minutes
            "project_update": 30,  # Aggregate project updates for 30 minutes
            "task_assignment": 5,  # Aggregate task assignments for 5 minutes
        }

        if notification_type not in aggregatable_types:
            return None

        aggregation_window = aggregatable_types[notification_type]
        aggregation_key = self._generate_aggregation_key(user, notification_type, related_object, organization_id)

        # Check if there's an existing aggregation
        cache_key = f"{self.aggregation_cache_prefix}{aggregation_key}"
        existing_aggregation = cache.get(cache_key)

        if existing_aggregation:
            # Update the aggregation
            existing_aggregation["count"] += 1
            existing_aggregation["latest_message"] = message
            existing_aggregation["updated_at"] = timezone.now().isoformat()

            cache.set(cache_key, existing_aggregation, timeout=aggregation_window * 60)
            logger.debug(f"Aggregated notification for key {aggregation_key}, count: {existing_aggregation['count']}")
            return aggregation_key
        else:
            # Create new aggregation
            aggregation_data = {
                "count": 1,
                "notification_type": notification_type,
                "first_message": message,
                "latest_message": message,
                "created_at": timezone.now().isoformat(),
                "updated_at": timezone.now().isoformat(),
            }

            cache.set(cache_key, aggregation_data, timeout=aggregation_window * 60)
            return None  # First notification, don't aggregate

    def create_aggregated_notification(
        self, user: User, aggregation_key: str, organization: Any
    ) -> Optional[Notification]:
        """
        Create a notification from aggregated data.

        Args:
            user: User to receive notification
            aggregation_key: Key for aggregated data
            organization: Organization context

        Returns:
            Created notification or None
        """
        cache_key = f"{self.aggregation_cache_prefix}{aggregation_key}"
        aggregation_data = cache.get(cache_key)

        if not aggregation_data or aggregation_data["count"] <= 1:
            return None

        # Create aggregated notification
        count = aggregation_data["count"]
        notification_type = aggregation_data["notification_type"]

        # Generate aggregated title and message
        title, message = self._generate_aggregated_content(notification_type, count, aggregation_data)

        notification = Notification.objects.create(
            recipient=user,
            organization=organization,
            notification_type=notification_type,
            delivery_channel="in_app",  # Aggregated notifications are in-app only
            title=title,
            message=message,
            priority="normal",
            data={
                "aggregated": True,
                "count": count,
                "aggregation_key": aggregation_key,
                "first_message": aggregation_data["first_message"],
                "latest_message": aggregation_data["latest_message"],
            },
        )

        # Clear the aggregation cache
        cache.delete(cache_key)

        logger.info(f"Created aggregated notification for {user} with {count} items")
        return notification

    def increment_user_notification_count(self, user: User, notification_type: str, priority: str = "normal") -> None:
        """
        Increment notification count for rate limiting.

        Args:
            user: User receiving notification
            notification_type: Type of notification
            priority: Notification priority
        """
        now = timezone.now()

        # Increment burst count (1 minute window)
        burst_key = f"{self.cache_prefix}burst:{user.id}:{notification_type}:{now.strftime('%Y%m%d%H%M')}"
        cache.set(burst_key, cache.get(burst_key, 0) + 1, timeout=60)

        # Increment hourly count
        hourly_key = f"{self.cache_prefix}hourly:{user.id}:{notification_type}:{now.strftime('%Y%m%d%H')}"
        cache.set(hourly_key, cache.get(hourly_key, 0) + 1, timeout=3600)

        # Increment daily count
        daily_key = f"{self.cache_prefix}daily:{user.id}:{notification_type}:{now.strftime('%Y%m%d')}"
        cache.set(daily_key, cache.get(daily_key, 0) + 1, timeout=86400)

    def get_user_notification_stats(self, user: User) -> Dict[str, Any]:
        """
        Get current notification rate limiting stats for user.

        Args:
            user: User to get stats for

        Returns:
            Dict with current counts and limits
        """
        now = timezone.now()
        stats = {}

        # Get notification types from recent notifications
        notification_types = (
            Notification.objects.filter(recipient=user, created_at__gte=now - timedelta(days=1))
            .values_list("notification_type", flat=True)
            .distinct()
        )

        for notification_type in notification_types:
            # Burst count
            burst_key = f"{self.cache_prefix}burst:{user.id}:{notification_type}:{now.strftime('%Y%m%d%H%M')}"
            burst_count = cache.get(burst_key, 0)

            # Hourly count
            hourly_key = f"{self.cache_prefix}hourly:{user.id}:{notification_type}:{now.strftime('%Y%m%d%H')}"
            hourly_count = cache.get(hourly_key, 0)

            # Daily count
            daily_key = f"{self.cache_prefix}daily:{user.id}:{notification_type}:{now.strftime('%Y%m%d')}"
            daily_count = cache.get(daily_key, 0)

            stats[notification_type] = {
                "burst": {"count": burst_count, "limit": self.default_limits["burst"]},
                "hourly": {
                    "count": hourly_count,
                    "limit": self.default_limits["hourly"],
                },
                "daily": {"count": daily_count, "limit": self.default_limits["daily"]},
            }

        return stats

    def _check_burst_limit_exceeded(self, user: User, notification_type: str) -> bool:
        """Check if burst limit is exceeded."""
        now = timezone.now()
        burst_key = f"{self.cache_prefix}burst:{user.id}:{notification_type}:{now.strftime('%Y%m%d%H%M')}"
        current_count = cache.get(burst_key, 0)
        return current_count >= self.default_limits["burst"]

    def _check_hourly_limit_exceeded(self, user: User, notification_type: str, org_settings: Any) -> bool:
        """Check if hourly limit is exceeded."""
        now = timezone.now()
        hourly_key = f"{self.cache_prefix}hourly:{user.id}:{notification_type}:{now.strftime('%Y%m%d%H')}"
        current_count = cache.get(hourly_key, 0)
        limit = org_settings.max_notifications_per_hour if org_settings else self.default_limits["hourly"]
        return current_count >= limit

    def _check_daily_limit_exceeded(self, user: User, notification_type: str, org_settings: Any) -> bool:
        """Check if daily limit is exceeded."""
        now = timezone.now()
        daily_key = f"{self.cache_prefix}daily:{user.id}:{notification_type}:{now.strftime('%Y%m%d')}"
        current_count = cache.get(daily_key, 0)
        limit = org_settings.max_notifications_per_day if org_settings else self.default_limits["daily"]
        return current_count >= limit

    def _get_organization_settings(self, organization_id: int) -> Optional[Any]:
        """Get organization notification settings."""
        try:
            cache_key = f"org_notification_settings:{organization_id}"
            settings = cache.get(cache_key)

            if settings is None:
                settings = NotificationSettings.objects.filter(organization_id=organization_id).first()
                if settings:
                    cache.set(cache_key, settings, timeout=3600)  # Cache for 1 hour

            return settings
        except Exception as e:
            logger.error(f"Error fetching organization settings: {e}")
            return None

    def _generate_aggregation_key(
        self,
        user: User,
        notification_type: str,
        related_object: Any = None,
        organization_id: int = None,
    ) -> str:
        """Generate aggregation key for similar notifications."""
        key_parts = [str(user.id), notification_type]

        if organization_id:
            key_parts.append(str(organization_id))

        if related_object:
            # Include object type and ID for more specific aggregation
            content_type = f"{related_object._meta.app_label}.{related_object._meta.model_name}"
            key_parts.extend([content_type, str(related_object.pk)])

        return ":".join(key_parts)

    def _generate_aggregated_content(
        self, notification_type: str, count: int, aggregation_data: Dict[str, Any]
    ) -> Tuple[str, str]:
        """Generate title and message for aggregated notification."""
        templates = {
            "comment": {
                "title": f"You have {count} new comments",
                "message": f"There are {count} new comments on items you're following. Latest: {aggregation_data['latest_message'][:100]}...",
            },
            "mention": {
                "title": f"You were mentioned {count} times",
                "message": f"You have {count} new mentions. Latest: {aggregation_data['latest_message'][:100]}...",
            },
            "project_update": {
                "title": f"{count} project updates",
                "message": f"There are {count} updates to your projects. Latest: {aggregation_data['latest_message'][:100]}...",
            },
            "task_assignment": {
                "title": f"You have {count} new task assignments",
                "message": f"{count} tasks have been assigned to you. Latest: {aggregation_data['latest_message'][:100]}...",
            },
        }

        template = templates.get(
            notification_type,
            {
                "title": f"You have {count} notifications",
                "message": f"There are {count} notifications of type {notification_type}.",
            },
        )

        return template["title"], template["message"]

    def cleanup_expired_throttle_data(self) -> int:
        """
        Clean up expired throttle data from cache.

        Returns:
            Number of cleaned up entries
        """
        # This is implementation-dependent on the cache backend
        # For Redis, you could use SCAN to find and delete expired keys
        # For now, we rely on cache TTL to handle cleanup
        logger.info("Throttle data cleanup relies on cache TTL")
        return 0
