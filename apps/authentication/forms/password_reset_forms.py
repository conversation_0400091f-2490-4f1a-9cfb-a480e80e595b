"""Password reset forms for authentication app."""

from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import PasswordResetForm as DjangoPasswordResetForm
from django.contrib.auth.forms import SetPasswordForm as DjangoSetPasswordForm
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class PasswordResetForm(DjangoPasswordResetForm):
    """Custom password reset form with enhanced validation and styling."""

    email = forms.EmailField(
        label=_("Email address"),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                "class": "form-control",
                "placeholder": _("<EMAIL>"),
                "autocomplete": "email",
                "required": True,
            }
        ),
    )

    def clean_email(self):
        """Validate email exists in the system."""
        email = self.cleaned_data.get("email")
        if email:
            # Normalize email
            email = email.lower().strip()

            # Check if user exists but don't reveal this in error message
            # This is for internal validation only
            if not User.objects.filter(email__iexact=email, is_active=True).exists():
                # Still send the email to prevent user enumeration
                # But we can log this for security monitoring
                pass

        return email

    def save(self, **kwargs):
        """Save the form and send password reset email."""
        # Add extra context for email template
        kwargs["extra_email_context"] = kwargs.get("extra_email_context", {})
        kwargs["extra_email_context"].update(
            {
                "site_name": kwargs.get("domain_override", "CLEAR Platform"),
                "support_email": "<EMAIL>",
            }
        )

        return super().save(**kwargs)


class SetPasswordForm(DjangoSetPasswordForm):
    """Custom set password form with enhanced validation."""

    new_password1 = forms.CharField(
        label=_("New password"),
        strip=False,
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Enter new password"),
                "autocomplete": "new-password",
                "required": True,
            }
        ),
        help_text=_(
            "Your password must contain at least 8 characters and cannot be "
            "a commonly used password or too similar to your personal information."
        ),
    )

    new_password2 = forms.CharField(
        label=_("Confirm new password"),
        strip=False,
        widget=forms.PasswordInput(
            attrs={
                "class": "form-control",
                "placeholder": _("Confirm new password"),
                "autocomplete": "new-password",
                "required": True,
            }
        ),
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add HTMX attributes for real-time validation
        self.fields["new_password1"].widget.attrs.update(
            {
                "hx-post": "",
                "hx-trigger": "keyup changed delay:500ms",
                "hx-target": "#password1-validation",
                "hx-vals": '{"validate_field": "new_password1"}',
            }
        )
        self.fields["new_password2"].widget.attrs.update(
            {
                "hx-post": "",
                "hx-trigger": "keyup changed delay:500ms",
                "hx-target": "#password2-validation",
                "hx-vals": '{"validate_field": "new_password2"}',
            }
        )

    def clean_new_password2(self):
        """Ensure both passwords match."""
        password1 = self.cleaned_data.get("new_password1")
        password2 = self.cleaned_data.get("new_password2")

        if password1 and password2 and password1 != password2:
            raise forms.ValidationError(
                _("The two password fields didn't match."),
                code="password_mismatch",
            )

        return password2

    def save(self, commit=True):
        """Save the new password and update user metadata."""
        user = super().save(commit=False)

        if commit:
            # Update password change timestamp
            from django.utils import timezone

            user.last_password_change = timezone.now()

            # Clear any lockout
            user.failed_login_attempts = 0
            user.temporary_lockout_until = None

            user.save()

        return user
