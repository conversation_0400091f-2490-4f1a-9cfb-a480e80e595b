"""
Django admin configuration for authentication models.

Available Models:
- User: Email-based authentication
- Organization: Multi-tenant data isolation
- Role: Role-based access control (RBAC)
- UserProfile: Extended user information
- UserRole: User-Role-Organization relationships
"""

from django.contrib import admin
from django.contrib.auth import get_user_model

from apps.authentication.models import Organization, Role, UserProfile, UserRole

User = get_user_model()

__all__ = [
    "Organization",
    "Role",
    "User",
    "UserProfile",
    "UserRole",
]
