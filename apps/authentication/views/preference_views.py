"""
User Preference Views for HTMX-powered settings system.

Provides comprehensive user preference management with real-time updates
using HTMX for server-side validation and immediate visual feedback.
"""

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.template.loader import render_to_string
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.http import require_http_methods
from django.views.generic import FormView, TemplateView, View

from apps.authentication.forms.preference_forms import (
    DataFormatPreferenceForm,
    KeyboardShortcutForm,
    LanguagePreferenceForm,
    NotificationPreferenceForm,
    PreferenceImportExportForm,
    ThemePreferenceForm,
)
from apps.authentication.models_preferences import (
    UserPreference,
    UserPreferenceTemplate,
)
from apps.common.mixins import RoleRequiredMixin
from apps.common.mixins.htmx_form_mixins import HTMXFormMixin


class PreferenceBaseView(LoginRequiredMixin, RoleRequiredMixin):
    """Base view for preference management with organization context."""

    required_roles = ["stakeholder"]

    def get_preferences(self):
        """Get or create preferences for current user."""
        if not self.request.user.is_authenticated:
            return None
        if not hasattr(self.request.user, "organization") or not self.request.user.organization:
            return None
        return UserPreference.get_preferences_for_user(self.request.user, self.request.user.organization)

    def dispatch(self, request, *args, **kwargs):
        """Set up common context for all preference views."""
        # Call parent dispatch first to handle authentication
        response = super().dispatch(request, *args, **kwargs)
        if hasattr(response, "status_code") and response.status_code == 302:
            # Redirect to login, don't set preferences
            return response
        self.preferences = self.get_preferences()
        return response


@method_decorator(csrf_protect, name="dispatch")
class UserSettingsView(PreferenceBaseView, TemplateView):
    """Main settings page with HTMX-powered tabbed interface."""

    template_name = "authentication/settings/settings_main.html"

    def get_context_data(self, **kwargs):
        """Add preference forms and current settings to context."""
        context = super().get_context_data(**kwargs)

        # Initialize all forms with current preferences
        context.update(
            {
                "preferences": self.preferences,
                "theme_form": ThemePreferenceForm(user=self.request.user, organization=self.request.user.organization),
                "language_form": LanguagePreferenceForm(
                    user=self.request.user, organization=self.request.user.organization
                ),
                "notification_form": NotificationPreferenceForm(
                    user=self.request.user, organization=self.request.user.organization
                ),
                "data_format_form": DataFormatPreferenceForm(
                    user=self.request.user, organization=self.request.user.organization
                ),
                "keyboard_form": KeyboardShortcutForm(
                    user=self.request.user, organization=self.request.user.organization
                ),
                "import_export_form": PreferenceImportExportForm(
                    user=self.request.user, organization=self.request.user.organization
                ),
                "available_templates": UserPreferenceTemplate.objects.filter(
                    organization=self.request.user.organization, is_public=True
                ),
            }
        )

        return context


@method_decorator(csrf_protect, name="dispatch")
class ThemeSettingsView(PreferenceBaseView, HTMXFormMixin, FormView):
    """HTMX view for theme switching with immediate visual feedback."""

    form_class = ThemePreferenceForm
    template_name = "authentication/settings/partials/theme_settings.html"
    htmx_template_name = "authentication/settings/partials/theme_settings.html"
    success_message = _("Theme updated successfully")

    def get_form_kwargs(self):
        """Add user and organization to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {
                "user": self.request.user,
                "organization": self.request.user.organization,
            }
        )
        return kwargs

    def form_valid(self, form):
        """Handle valid theme change with HTMX response."""
        form.save()

        if self.request.headers.get("HX-Request"):
            # Return HTMX response that updates the body data-theme attribute
            response = HttpResponse()
            response["HX-Trigger"] = "themeChanged"
            response["HX-Trigger-Toast"] = self.success_message
            response["HX-Trigger-Toast-Type"] = "success"
            # Update body attribute
            theme = form.cleaned_data["theme"]
            response["HX-Reswap"] = "none"
            response["HX-Retarget"] = "body"
            response.content = f'<script>document.body.setAttribute("data-theme", "{theme}");</script>'
            return response

        messages.success(self.request, self.success_message)
        return super().form_valid(form)

    def get_success_url(self):
        """Return to settings page."""
        return reverse_lazy("authentication:settings")


@method_decorator(csrf_protect, name="dispatch")
class LanguageSettingsView(PreferenceBaseView, HTMXFormMixin, FormView):
    """HTMX view for language and localization preferences."""

    form_class = LanguagePreferenceForm
    template_name = "authentication/settings/partials/language_settings.html"
    htmx_template_name = "authentication/settings/partials/language_settings.html"
    success_message = _("Language settings updated successfully")

    def get_form_kwargs(self):
        """Add user and organization to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {
                "user": self.request.user,
                "organization": self.request.user.organization,
            }
        )
        return kwargs

    def form_valid(self, form):
        """Handle valid language change."""
        form.save()

        if self.request.headers.get("HX-Request"):
            # Return feedback message
            context = {"message": self.success_message, "type": "success"}
            content = render_to_string(
                "authentication/settings/partials/settings_feedback.html",
                context,
                request=self.request,
            )
            return HttpResponse(content)

        messages.success(self.request, self.success_message)
        return super().form_valid(form)


@method_decorator(csrf_protect, name="dispatch")
class NotificationSettingsView(PreferenceBaseView, HTMXFormMixin, FormView):
    """HTMX view for notification preferences with granular control."""

    form_class = NotificationPreferenceForm
    template_name = "authentication/settings/partials/notification_settings.html"
    htmx_template_name = "authentication/settings/partials/notification_settings.html"
    success_message = _("Notification preferences updated")

    def get_form_kwargs(self):
        """Add user and organization to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {
                "user": self.request.user,
                "organization": self.request.user.organization,
            }
        )
        return kwargs

    def form_valid(self, form):
        """Handle notification preference updates."""
        form.save()

        if self.request.headers.get("HX-Request"):
            # Return updated form with feedback
            context = {
                "form": form,
                "preferences": self.preferences,
                "message": self.success_message,
                "type": "success",
            }
            content = render_to_string(
                "authentication/settings/partials/settings_feedback.html",
                context,
                request=self.request,
            )
            return HttpResponse(content)

        messages.success(self.request, self.success_message)
        return super().form_valid(form)


@method_decorator(csrf_protect, name="dispatch")
class DataFormatSettingsView(PreferenceBaseView, HTMXFormMixin, FormView):
    """HTMX view for data format preferences with immediate preview."""

    form_class = DataFormatPreferenceForm
    template_name = "authentication/settings/partials/data_format_settings.html"
    htmx_template_name = "authentication/settings/partials/data_format_settings.html"
    success_message = _("Data format preferences updated")

    def get_form_kwargs(self):
        """Add user and organization to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {
                "user": self.request.user,
                "organization": self.request.user.organization,
            }
        )
        return kwargs

    def form_valid(self, form):
        """Handle data format preference updates."""
        form.save()

        if self.request.headers.get("HX-Request"):
            # Generate preview with new format settings
            preview_data = self.generate_format_preview(form.cleaned_data)
            context = {
                "preview_data": preview_data,
                "message": self.success_message,
                "type": "success",
            }
            content = render_to_string(
                "authentication/settings/partials/format_preview.html",
                context,
                request=self.request,
            )
            return HttpResponse(content)

        messages.success(self.request, self.success_message)
        return super().form_valid(form)

    def generate_format_preview(self, format_data):
        """Generate preview data showing how formats will be applied."""
        from datetime import datetime

        preview = {}

        # CSV preview
        csv_delimiter = format_data.get("csv_delimiter", ",")
        preview["csv_sample"] = (
            f"Name{csv_delimiter}Value{csv_delimiter}Date\\nProject A{csv_delimiter}1234.56{csv_delimiter}2024-01-15"
        )

        # Number format preview
        number_sep = format_data.get("number_separator", ".")
        if number_sep == ",":
            preview["number_sample"] = "1.234,56"
        else:
            preview["number_sample"] = "1,234.56"

        # Currency preview
        currency = format_data.get("currency_format", "USD")
        currency_symbols = {"USD": "$", "EUR": "€", "GBP": "£", "CAD": "C$"}
        symbol = currency_symbols.get(currency, "$")
        preview["currency_sample"] = f"{symbol}1,234.56"

        # Date format preview
        date_format = format_data.get("date_format_export", "YYYY-MM-DD")
        now = datetime.now()
        if date_format == "YYYY-MM-DD":
            preview["date_sample"] = now.strftime("%Y-%m-%d")
        elif date_format == "DD/MM/YYYY":
            preview["date_sample"] = now.strftime("%d/%m/%Y")
        elif date_format == "MM/DD/YYYY":
            preview["date_sample"] = now.strftime("%m/%d/%Y")
        else:
            preview["date_sample"] = now.strftime("%Y-%m-%d")

        return preview


@method_decorator(csrf_protect, name="dispatch")
class KeyboardShortcutSettingsView(PreferenceBaseView, HTMXFormMixin, FormView):
    """HTMX view for keyboard shortcut customization."""

    form_class = KeyboardShortcutForm
    template_name = "authentication/settings/partials/keyboard_settings.html"
    htmx_template_name = "authentication/settings/partials/keyboard_settings.html"
    success_message = _("Keyboard shortcuts updated")

    def get_form_kwargs(self):
        """Add user and organization to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {
                "user": self.request.user,
                "organization": self.request.user.organization,
            }
        )
        return kwargs

    def form_valid(self, form):
        """Handle keyboard shortcut updates."""
        form.save()

        if self.request.headers.get("HX-Request"):
            context = {"message": self.success_message, "type": "success"}
            content = render_to_string(
                "authentication/settings/partials/settings_feedback.html",
                context,
                request=self.request,
            )
            return HttpResponse(content)

        messages.success(self.request, self.success_message)
        return super().form_valid(form)


@method_decorator(csrf_protect, name="dispatch")
class DashboardWidgetSettingsView(PreferenceBaseView, View):
    """HTMX view for dashboard widget arrangement persistence."""

    def post(self, request):
        """Handle dashboard widget arrangement updates."""
        if not request.headers.get("HX-Request"):
            return JsonResponse({"error": "HTMX request required"}, status=400)

        widget_data = request.POST.get("widgets")
        if widget_data:
            try:
                import json

                widgets = json.loads(widget_data)
                self.preferences.set_dashboard_widgets(widgets)
                self.preferences.save()

                return HttpResponse(_("Dashboard layout saved"))
            except (json.JSONDecodeError, Exception):
                return HttpResponse(_("Error saving dashboard layout"), status=400)

        return HttpResponse(_("No widget data provided"), status=400)


@method_decorator(csrf_protect, name="dispatch")
class PreferenceImportExportView(PreferenceBaseView, HTMXFormMixin, FormView):
    """HTMX view for preference import/export functionality."""

    form_class = PreferenceImportExportForm
    template_name = "authentication/settings/partials/import_export_settings.html"
    htmx_template_name = "authentication/settings/partials/import_export_settings.html"

    def get_form_kwargs(self):
        """Add user and organization to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs.update(
            {
                "user": self.request.user,
                "organization": self.request.user.organization,
            }
        )
        return kwargs

    def form_valid(self, form):
        """Handle import/export operations."""
        export_format = form.cleaned_data.get("export_format")

        if export_format == "json":
            # Export preferences as JSON
            exported_data = self.preferences.export_preferences()
            if self.request.headers.get("HX-Request"):
                context = {
                    "export_data": exported_data,
                    "message": _("Preferences exported successfully"),
                    "type": "success",
                }
                content = render_to_string(
                    "authentication/settings/partials/export_result.html",
                    context,
                    request=self.request,
                )
                return HttpResponse(content)

        elif export_format == "template":
            # Save as template
            template_name = form.cleaned_data.get("template_name")
            template_description = form.cleaned_data.get("template_description", "")

            template = UserPreferenceTemplate(
                organization=self.request.user.organization,
                created_by=self.request.user,
                name=template_name,
                description=template_description,
                template_data=self.preferences.preference_data,
                is_public=True,
            )
            template.save()

            if self.request.headers.get("HX-Request"):
                context = {
                    "template": template,
                    "message": _("Template created successfully"),
                    "type": "success",
                }
                content = render_to_string(
                    "authentication/settings/partials/template_created.html",
                    context,
                    request=self.request,
                )
                return HttpResponse(content)

        # Handle import
        import_data = form.cleaned_data.get("import_data")
        if import_data:
            merge = form.cleaned_data.get("merge_preferences", True)
            try:
                self.preferences.import_preferences(import_data, merge=merge)
                self.preferences.save()

                if self.request.headers.get("HX-Request"):
                    context = {
                        "message": _("Preferences imported successfully"),
                        "type": "success",
                    }
                    content = render_to_string(
                        "authentication/settings/partials/settings_feedback.html",
                        context,
                        request=self.request,
                    )
                    return HttpResponse(content)
            except Exception as e:
                if self.request.headers.get("HX-Request"):
                    context = {
                        "message": _("Error importing preferences: {}").format(str(e)),
                        "type": "error",
                    }
                    content = render_to_string(
                        "authentication/settings/partials/settings_feedback.html",
                        context,
                        request=self.request,
                    )
                    return HttpResponse(content, status=400)

        return super().form_valid(form)


@require_http_methods(["POST"])
@csrf_protect
def apply_preference_template(request, template_id):
    """Apply a preference template to the current user."""
    if not request.headers.get("HX-Request"):
        return JsonResponse({"error": "HTMX request required"}, status=400)

    template = get_object_or_404(
        UserPreferenceTemplate,
        id=template_id,
        organization=request.user.organization,
        is_public=True,
    )

    try:
        template.apply_to_user(request.user)

        context = {
            "message": _("Template '{}' applied successfully").format(template.name),
            "type": "success",
        }
        content = render_to_string(
            "authentication/settings/partials/settings_feedback.html",
            context,
            request=request,
        )
        return HttpResponse(content)

    except Exception as e:
        context = {
            "message": _("Error applying template: {}").format(str(e)),
            "type": "error",
        }
        content = render_to_string(
            "authentication/settings/partials/settings_feedback.html",
            context,
            request=request,
        )
        return HttpResponse(content, status=400)


@require_http_methods(["GET"])
def settings_tab_content(request, tab_name):
    """Load settings tab content via HTMX."""
    if not request.headers.get("HX-Request"):
        return JsonResponse({"error": "HTMX request required"}, status=400)

    preferences = UserPreference.get_preferences_for_user(request.user, request.user.organization)

    tab_templates = {
        "general": "authentication/settings/tabs/general_tab.html",
        "appearance": "authentication/settings/tabs/appearance_tab.html",
        "notifications": "authentication/settings/tabs/notifications_tab.html",
        "data-formats": "authentication/settings/tabs/data_formats_tab.html",
        "keyboard": "authentication/settings/tabs/keyboard_tab.html",
        "import-export": "authentication/settings/tabs/import_export_tab.html",
    }

    template_name = tab_templates.get(tab_name)
    if not template_name:
        return HttpResponse(_("Invalid tab"), status=400)

    context = {
        "preferences": preferences,
        "active_tab": tab_name,
    }

    # Add appropriate form based on tab
    if tab_name == "general":
        context["language_form"] = LanguagePreferenceForm(user=request.user, organization=request.user.organization)
    elif tab_name == "appearance":
        context["theme_form"] = ThemePreferenceForm(user=request.user, organization=request.user.organization)
    elif tab_name == "notifications":
        context["notification_form"] = NotificationPreferenceForm(
            user=request.user, organization=request.user.organization
        )
    elif tab_name == "data-formats":
        context["data_format_form"] = DataFormatPreferenceForm(
            user=request.user, organization=request.user.organization
        )
    elif tab_name == "keyboard":
        context["keyboard_form"] = KeyboardShortcutForm(user=request.user, organization=request.user.organization)
    elif tab_name == "import-export":
        context["import_export_form"] = PreferenceImportExportForm(
            user=request.user, organization=request.user.organization
        )
        context["available_templates"] = UserPreferenceTemplate.objects.filter(
            organization=request.user.organization, is_public=True
        )

    return render(request, template_name, context)
