{% extends "auth_base.html" %}

{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block auth_title %}{{ page_title|default:"Sign In" }}{% endblock %}

{% block auth_content %}
  <div id="login-form-container">

    {% if htmx_partial %}
      {% include "authentication/partials/login_form_fixed.html" %}
    {% else %}
      <div class="auth-card">
        <div class="auth-header">
          <h2 class="h3 mb-0" id="login-form-heading">
            <i class="bi bi-box-arrow-in-right me-2" aria-hidden="true"></i>{{ page_title|default:"Sign In" }}
          </h2>
        </div>
        <div class="auth-body">{% include "authentication/partials/login_form_fixed.html" %}</div>

        {% if allow_registration %}
          <div class="auth-footer text-center">
            <p class="text-muted mb-0">
              {% trans "Don't have an account?" %}
              <a href="{% url 'authentication:signup' %}" class="text-decoration-none">{% trans "Sign up here" %}</a>
            </p>
          </div>
        {% endif %}

      </div>
    {% endif %}

  </div>
{% endblock %}

{% block extra_js %}
  <script>
  // Auto-focus email field
  document.addEventListener('DOMContentLoaded', function () {
    const emailField = document.getElementById('id_username');
    if (emailField) {
      emailField.focus();
    }
  });

  // Handle HTMX events for form submission
  document.body.addEventListener('htmx:afterRequest', function (event) {
    if (event.detail.successful && event.detail.target.id === 'login-form-container') {
      console.log('Login form submitted successfully');
    }
  });
  </script>
{% endblock %}
