{% extends "base.html" %}

{% load i18n crispy_forms_tags static %}

{% block title %}{{ page_title }} - {{ block.super }}{% endblock %}

{% block extra_css %}
  <style>
    .registration-container {
        max-width: 600px;
        margin: 0 auto;
    }
    .step-indicator {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
    }
    .step {
        flex: 1;
        text-align: center;
        padding: 0.5rem;
        border-bottom: 3px solid #dee2e6;
        color: #6c757d;
        transition: all 0.3s ease;
    }
    .step.active {
        border-color: #0d6efd;
        color: #0d6efd;
        font-weight: 600;
    }
    .step.completed {
        border-color: #198754;
        color: #198754;
    }
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    .invitation-feedback {
        margin-top: 0.5rem;
        font-size: 0.875rem;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="container mt-5">
    <div class="registration-container">
      <!-- Logo/Brand -->
      <div class="text-center mb-4">
        <img src="{% static 'images/logo.png' %}"
             alt="CLEAR"
             class="mb-3"
             style="height: 60px">
        <h2>{% trans "Welcome to CLEAR" %}</h2>
        <p class="text-muted">{% trans "Construction & Land Development Engineering Application Repository" %}</p>
      </div>
      <!-- Step Indicator -->
      <div class="step-indicator">
        <div class="step active" id="step-1">
          <i class="fas fa-user-plus"></i>
          <span class="d-none d-sm-inline ms-2">{% trans "Get Started" %}</span>
        </div>
        <div class="step" id="step-2">
          <i class="fas fa-building"></i>
          <span class="d-none d-sm-inline ms-2">{% trans "Organization" %}</span>
        </div>
        <div class="step" id="step-3">
          <i class="fas fa-check-circle"></i>
          <span class="d-none d-sm-inline ms-2">{% trans "Complete" %}</span>
        </div>
      </div>
      <!-- Registration Container -->
      <div class="card shadow-sm">
        <div class="card-body p-4" id="registration-container">
          <!-- Initial Form -->
          <form method="post"
                id="registration-type-form"
                hx-post="{% url 'authentication:signup' %}"
                hx-target="#registration-container"
                hx-swap="innerHTML">
            {% csrf_token %}
            <h4 class="mb-4">{% trans "How would you like to get started?" %}</h4>
            <!-- Registration Type Selection -->
            <div class="mb-4">
              <div class="form-check mb-3">
                <input class="form-check-input"
                       type="radio"
                       name="registration_type"
                       id="create-org"
                       value="create"
                       checked
                       hx-post="{% url 'authentication:check_registration_type' %}"
                       hx-target="#invitation-code-section"
                       hx-swap="outerHTML">
                <label class="form-check-label" for="create-org">
                  <strong>{% trans "Create a new organization" %}</strong>
                  <br>
                  <small class="text-muted">{% trans "Start fresh with your own organization and invite your team" %}</small>
                </label>
              </div>
              <div class="form-check">
                <input class="form-check-input"
                       type="radio"
                       name="registration_type"
                       id="join-org"
                       value="join"
                       hx-post="{% url 'authentication:check_registration_type' %}"
                       hx-target="#invitation-code-section"
                       hx-swap="outerHTML">
                <label class="form-check-label" for="join-org">
                  <strong>{% trans "Join an existing organization" %}</strong>
                  <br>
                  <small class="text-muted">{% trans "Use an invitation code to join your team's organization" %}</small>
                </label>
              </div>
            </div>
            <!-- Invitation Code Section (Hidden by default) -->
            <div id="invitation-code-section" class="mb-4" style="display: none;">
              <label for="invitation_code" class="form-label">
                {% trans "Invitation Code" %}
                <small class="text-muted">({% trans "optional" %})</small>
              </label>
              <input type="text"
                     class="form-control"
                     id="invitation_code"
                     name="invitation_code"
                     placeholder="{% trans 'Enter your invitation code' %}"
                     hx-post="{% url 'authentication:validate_invitation' %}"
                     hx-trigger="blur changed delay:500ms"
                     hx-target="#invitation-feedback"
                     hx-swap="innerHTML">
              <div id="invitation-feedback" class="invitation-feedback"></div>
              <small class="form-text text-muted">
                {% trans "If you have an invitation code from your organization, enter it here" %}
              </small>
            </div>
            <!-- Continue Button -->
            <div class="d-grid">
              <button type="submit" class="btn btn-primary btn-lg">
                {% trans "Continue" %}
                <i class="fas fa-arrow-right ms-2"></i>
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Alternative Actions -->
      <div class="text-center mt-4">
        <p class="mb-0">
          {% trans "Already have an account?" %}
          <a href="{% url 'authentication:login' %}" class="text-decoration-none">{% trans "Sign In" %}</a>
        </p>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Update step indicators based on progress
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'registration-container') {
            // Update step indicators based on current form
            const steps = document.querySelectorAll('.step');
            const currentForm = evt.detail.target.querySelector('form');
            
            if (currentForm && currentForm.id === 'complete-registration-form') {
                steps[0].classList.add('completed');
                steps[0].classList.remove('active');
                steps[1].classList.add('active');
            }
        }
    });

    // Handle registration type changes
    document.addEventListener('change', function(evt) {
        if (evt.target.name === 'registration_type') {
            const invitationSection = document.getElementById('invitation-code-section');
            if (evt.target.value === 'join') {
                invitationSection.style.display = 'block';
            } else {
                invitationSection.style.display = 'none';
                // Clear invitation code
                document.getElementById('invitation_code').value = '';
                document.getElementById('invitation-feedback').innerHTML = '';
            }
        }
    });
  </script>
{% endblock %}
