"""
Tests for RoleDetectionMiddleware.

This module tests the role detection middleware functionality including:
- Role injection into request context
- Permission detection
- Caching behavior
- Edge case handling
"""

from unittest.mock import patch

from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.test import RequestFactory, TestCase

from apps.authentication.middleware.role_detection_middleware import (
    RoleDetectionMiddleware,
    RoleHierarchy,
)
from apps.authentication.models import Organization, Role, UserRole

User = get_user_model()


class RoleDetectionMiddlewareTest(TestCase):
    """Test case for RoleDetectionMiddleware."""

    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        self.middleware = RoleDetectionMiddleware()

        # Create test organization
        self.org = Organization.objects.create(name="Test Organization", slug="test-org")

        # Create test user
        self.user = User.objects.create_user(email="<EMAIL>", password="testpass123")

        # Create test role
        self.admin_role = Role.objects.create(
            organization=self.org,
            name="Organization Admin",
            slug="organization-admin",
            level=RoleHierarchy.EXECUTIVE,
            permissions={
                "organization": ["view", "edit", "delete", "manage_users"],
                "projects": ["create", "view", "edit", "delete"],
            },
        )

        # Clear cache before each test
        cache.clear()

    def tearDown(self):
        """Clean up after each test."""
        cache.clear()

    def test_middleware_initializes_role_context(self):
        """Test that middleware initializes role context for unauthenticated users."""
        request = self.factory.get("/")

        # Create anonymous user
        from django.contrib.auth.models import AnonymousUser

        request.user = AnonymousUser()

        # Process request
        self.middleware.process_request(request)

        # Check that role context is initialized
        self.assertIsNone(request.user_role)
        self.assertEqual(request.user_roles, [])
        self.assertIsNone(request.user_role_level)
        self.assertIsNone(request.user_role_name)
        self.assertEqual(request.user_permissions, set())
        self.assertFalse(request.has_admin_access)
        self.assertFalse(request.has_manager_access)
        self.assertFalse(request.can_manage_users)

    def test_middleware_with_authenticated_user_no_organization(self):
        """Test middleware with authenticated user but no organization context."""
        request = self.factory.get("/")
        request.user = self.user

        # Process request (no organization context)
        self.middleware.process_request(request)

        # Should still initialize context
        self.assertIsNone(request.user_role)
        self.assertEqual(request.user_roles, [])

    def test_middleware_with_user_role(self):
        """Test middleware with user having a role."""
        # Assign role to user
        user_role = UserRole.objects.create(
            user=self.user, role=self.admin_role, organization=self.org, is_primary=True
        )

        request = self.factory.get("/")
        request.user = self.user
        request.organization = self.org

        # Process request
        self.middleware.process_request(request)

        # Check that role is detected and injected
        self.assertEqual(request.user_role, user_role)
        self.assertEqual(len(request.user_roles), 1)
        self.assertEqual(request.user_role_level, RoleHierarchy.EXECUTIVE)
        self.assertEqual(request.user_role_name, "Organization Admin")
        self.assertTrue(request.has_admin_access)
        self.assertTrue(request.has_manager_access)
        self.assertTrue(request.can_manage_users)
        self.assertTrue(request.is_admin)

        # Check permissions
        self.assertIn("organization.view", request.user_permissions)
        self.assertIn("projects.create", request.user_permissions)

    def test_middleware_caching_behavior(self):
        """Test that role detection results are cached."""
        # Assign role to user
        UserRole.objects.create(user=self.user, role=self.admin_role, organization=self.org, is_primary=True)

        request = self.factory.get("/")
        request.user = self.user
        request.organization = self.org

        # First request - should fetch from database
        with patch(
            "apps.authentication.middleware.role_detection_middleware.UserRole.objects.get_active_roles"
        ) as mock_get_roles:
            mock_get_roles.return_value.exists.return_value = True
            mock_get_roles.return_value.select_related.return_value.order_by.return_value = UserRole.objects.filter(
                user=self.user, organization=self.org
            )

            self.middleware.process_request(request)

            # Should have called database
            mock_get_roles.assert_called_once()

        # Second request - should use cache
        request2 = self.factory.get("/")
        request2.user = self.user
        request2.organization = self.org

        with patch(
            "apps.authentication.middleware.role_detection_middleware.UserRole.objects.get_active_roles"
        ) as mock_get_roles2:
            self.middleware.process_request(request2)

            # Should not call database this time
            mock_get_roles2.assert_not_called()

    def test_role_hierarchy_constants(self):
        """Test role hierarchy constants and helper methods."""
        # Test level constants
        self.assertEqual(RoleHierarchy.EXECUTIVE, 10)
        self.assertEqual(RoleHierarchy.DEPARTMENT_MANAGER, 20)
        self.assertEqual(RoleHierarchy.UTILITY_COORDINATOR, 30)
        self.assertEqual(RoleHierarchy.STAKEHOLDER, 40)

        # Test helper methods
        self.assertTrue(RoleHierarchy.has_admin_access(RoleHierarchy.EXECUTIVE))
        self.assertFalse(RoleHierarchy.has_admin_access(RoleHierarchy.STAKEHOLDER))

        self.assertTrue(RoleHierarchy.has_manager_access(RoleHierarchy.DEPARTMENT_MANAGER))
        self.assertFalse(RoleHierarchy.has_manager_access(RoleHierarchy.STAKEHOLDER))

        self.assertTrue(RoleHierarchy.can_manage_users(RoleHierarchy.DEPARTMENT_MANAGER))
        self.assertFalse(RoleHierarchy.can_manage_users(RoleHierarchy.STAKEHOLDER))

    def test_permission_checking_helpers(self):
        """Test static permission checking helper methods."""
        # Create request with role context
        request = self.factory.get("/")
        request.user_permissions = {"organization.view", "projects.create"}
        request.user_role_level = RoleHierarchy.EXECUTIVE

        # Test permission checking
        self.assertTrue(RoleDetectionMiddleware.has_permission(request, "organization.view"))
        self.assertFalse(RoleDetectionMiddleware.has_permission(request, "organization.delete"))

        # Test role level checking
        self.assertTrue(RoleDetectionMiddleware.has_role_level(request, RoleHierarchy.EXECUTIVE))
        self.assertTrue(RoleDetectionMiddleware.has_role_level(request, RoleHierarchy.DEPARTMENT_MANAGER))
        self.assertFalse(RoleDetectionMiddleware.has_role_level(request, 5))  # Higher authority level

    def test_cache_clearing(self):
        """Test cache clearing functionality."""
        # Set up cache
        cache_key = f"user_roles:{self.user.id}:{self.org.id}"
        cache.set(cache_key, {"test": "data"})

        # Verify cache exists
        self.assertIsNotNone(cache.get(cache_key))

        # Clear cache
        RoleDetectionMiddleware.clear_user_role_cache(self.user.id, self.org.id)

        # Verify cache is cleared
        self.assertIsNone(cache.get(cache_key))

    def test_middleware_error_handling(self):
        """Test middleware error handling."""
        request = self.factory.get("/")
        request.user = self.user
        request.organization = self.org

        # Mock database error
        with patch(
            "apps.authentication.middleware.role_detection_middleware.UserRole.objects.get_active_roles"
        ) as mock_get_roles:
            mock_get_roles.side_effect = Exception("Database error")

            # Should not raise exception, should set defaults
            self.middleware.process_request(request)

            # Should have default values
            self.assertIsNone(request.user_role)
            self.assertEqual(request.user_roles, [])
            self.assertFalse(request.has_admin_access)


class RoleHierarchyTest(TestCase):
    """Test case for RoleHierarchy utility class."""

    def test_level_name_mapping(self):
        """Test level name mapping."""
        self.assertEqual(RoleHierarchy.get_level_name(10), "executive")
        self.assertEqual(RoleHierarchy.get_level_name(20), "department_manager")
        self.assertEqual(RoleHierarchy.get_level_name(30), "utility_coordinator")
        self.assertEqual(RoleHierarchy.get_level_name(40), "stakeholder")
        self.assertEqual(RoleHierarchy.get_level_name(999), "unknown")

    def test_access_level_checks(self):
        """Test access level checking methods."""
        # Executive level should have all access
        self.assertTrue(RoleHierarchy.has_admin_access(10))
        self.assertTrue(RoleHierarchy.has_manager_access(10))
        self.assertTrue(RoleHierarchy.can_manage_users(10))

        # Department manager should have manager access but not admin
        self.assertFalse(RoleHierarchy.has_admin_access(20))
        self.assertTrue(RoleHierarchy.has_manager_access(20))
        self.assertTrue(RoleHierarchy.can_manage_users(20))

        # Stakeholder should have minimal access
        self.assertFalse(RoleHierarchy.has_admin_access(40))
        self.assertFalse(RoleHierarchy.has_manager_access(40))
        self.assertFalse(RoleHierarchy.can_manage_users(40))
