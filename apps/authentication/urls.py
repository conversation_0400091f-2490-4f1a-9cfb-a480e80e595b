"""
URL configuration for authentication app.
Core security routes with HTMX partial view support.
"""

from django.urls import include, path, reverse_lazy
from django.views.decorators.cache import never_cache
from django.views.decorators.debug import sensitive_post_parameters
from django.views.generic import RedirectView

from apps.authentication import admin_views
from apps.authentication.views import (
    auth_views,
    organization_views,
    registration_views,
    role_views,
    security_views,
    social_admin_views,
)
from apps.authentication.views.preference_views import (
    DashboardWidgetSettingsView,
    DataFormatSettingsView,
    KeyboardShortcutSettingsView,
    LanguageSettingsView,
    NotificationSettingsView,
    PreferenceImportExportView,
    ThemeSettingsView,
    UserSettingsView,
    apply_preference_template,
    settings_tab_content,
)
from apps.authentication.views.test_permission_tags import test_permission_tags

from .views.auth_views import AuthIndexView as IndexView
from .views.auth_views import UserRoleManagementView
from .views.social_admin_views import SocialAppCreateView, SocialAppListView

app_name = "authentication"

# HTMX patterns for dynamic content
htmx_patterns = [
    # Profile partials
    path(
        "_partial/profile/",
        auth_views.ProfilePartialView.as_view(
            template_name="authentication/partials/profile.html",
        ),
        name="profile_partial",
    ),
    # Role management partials
    path(
        "_partial/roles/",
        auth_views.RolesPartialView.as_view(
            template_name="authentication/partials/roles.html",
        ),
        name="roles_partial",
    ),
]

# Main URL patterns
urlpatterns = [
    # Authentication - Core login/logout/signup flows
    path(
        "login/",
        sensitive_post_parameters()(
            never_cache(
                auth_views.CustomLoginView.as_view(
                    template_name="authentication/login.html",
                ),
            ),
        ),
        name="login",
    ),
    path(
        "logout/",
        never_cache(
            auth_views.CustomLogoutView.as_view(
                template_name="authentication/logged_out.html",
            ),
        ),
        name="logout",
    ),
    # Registration Flow - Enhanced multi-step registration
    path(
        "signup/",
        sensitive_post_parameters()(
            never_cache(
                registration_views.RegistrationFlowView.as_view(),
            ),
        ),
        name="signup",
    ),
    path(
        "signup/check-type/",
        registration_views.CheckRegistrationTypeView.as_view(),
        name="check_registration_type",
    ),
    path(
        "signup/validate-code/",
        registration_views.ValidateInvitationCodeView.as_view(),
        name="validate_invitation",
    ),
    path(
        "signup/complete/",
        sensitive_post_parameters()(
            never_cache(
                registration_views.CompleteRegistrationView.as_view(),
            ),
        ),
        name="signup_complete",
    ),
    # Email Verification
    path(
        "verify-email/<uidb64>/<token>/",
        registration_views.VerifyEmailView.as_view(),
        name="email_verify",
    ),
    path(
        "verification-sent/",
        registration_views.EmailVerificationSentView.as_view(),
        name="email_verification_sent",
    ),
    path(
        "resend-verification/",
        registration_views.ResendVerificationView.as_view(),
        name="resend_verification",
    ),
    # Password Management - Secure password operations
    path(
        "password/change/",
        sensitive_post_parameters()(
            auth_views.PasswordChangeView.as_view(
                template_name="authentication/password_change.html",
                success_url=reverse_lazy("authentication:password_change_done"),
            ),
        ),
        name="password_change",
    ),
    path(
        "password/change/done/",
        auth_views.PasswordChangeDoneView.as_view(
            template_name="authentication/password_change_done.html",
        ),
        name="password_change_done",
    ),
    path(
        "password/reset/",
        sensitive_post_parameters()(
            auth_views.PasswordResetView.as_view(
                template_name="authentication/password_reset_form.html",
                email_template_name="authentication/emails/password_reset_email.html",
                subject_template_name="authentication/emails/password_reset_subject.txt",
                success_url=reverse_lazy("authentication:password_reset_done"),
            ),
        ),
        name="password_reset",
    ),
    path(
        "password/reset/done/",
        auth_views.PasswordResetDoneView.as_view(
            template_name="authentication/password_reset_done.html",
        ),
        name="password_reset_done",
    ),
    path(
        "password/reset/<uidb64>/<token>/",
        sensitive_post_parameters()(
            auth_views.PasswordResetConfirmView.as_view(
                template_name="authentication/password_reset_confirm.html",
                success_url=reverse_lazy("authentication:password_reset_complete"),
            ),
        ),
        name="password_reset_confirm",
    ),
    path(
        "password/reset/complete/",
        auth_views.PasswordResetCompleteView.as_view(
            template_name="authentication/password_reset_complete.html",
        ),
        name="password_reset_complete",
    ),
    # Multi-Factor Authentication - Enhanced security
    path(
        "mfa/setup/",
        sensitive_post_parameters()(
            never_cache(
                auth_views.MFASetupView.as_view(
                    template_name="authentication/mfa_setup.html",
                ),
            ),
        ),
        name="mfa_setup",
    ),
    path(
        "mfa/verify/",
        never_cache(
            auth_views.MFAVerifyView.as_view(
                template_name="authentication/mfa_verify.html",
            ),
        ),
        name="mfa_verify",
    ),
    path(
        "mfa/disable/",
        sensitive_post_parameters()(
            never_cache(
                auth_views.MFADisableView.as_view(
                    template_name="authentication/mfa_disable.html",
                ),
            ),
        ),
        name="mfa_disable",
    ),
    path(
        "mfa/backup/",
        never_cache(
            auth_views.MFABackupCodesView.as_view(
                template_name="authentication/mfa_backup_codes.html",
            ),
        ),
        name="mfa_backup_codes",
    ),
    # Profile Management - User profile operations
    path(
        "profile/",
        auth_views.ProfileView.as_view(
            template_name="authentication/profile.html",
        ),
        name="profile",
    ),
    path(
        "profile/edit/",
        auth_views.ProfileEditView.as_view(
            template_name="authentication/profile_edit.html",
        ),
        name="profile_edit",
    ),
    path(
        "profile/social/",
        auth_views.SocialAccountConnectionsView.as_view(),
        name="social_connections",
    ),
    path(
        "profile/api-tokens/",
        auth_views.APITokenManagementView.as_view(),
        name="api_tokens",
    ),
    # Organization & Role Management - RBAC operations
    path(
        "organizations/<uuid:org_id>/roles/",
        auth_views.UserRoleManagementView.as_view(
            template_name="authentication/role_management.html",
        ),
        name="role_management",
    ),
    # Comprehensive Role Management URLs
    path(
        "roles/",
        role_views.RoleListView.as_view(),
        name="role_list",
    ),
    path(
        "roles/create/",
        role_views.RoleCreateView.as_view(),
        name="role_create",
    ),
    path(
        "roles/<uuid:pk>/",
        role_views.RoleDetailView.as_view(),
        name="role_detail",
    ),
    path(
        "roles/<uuid:pk>/update/",
        role_views.RoleUpdateView.as_view(),
        name="role_update",
    ),
    path(
        "roles/<uuid:pk>/delete/",
        role_views.RoleDeleteView.as_view(),
        name="role_delete",
    ),
    path(
        "roles/<uuid:pk>/duplicate/",
        role_views.RoleDuplicateView.as_view(),
        name="role_duplicate",
    ),
    # User Role Assignment Management
    path(
        "user-roles/",
        role_views.UserRoleAssignmentView.as_view(),
        name="user_role_assignment",
    ),
    path(
        "user-roles/bulk/",
        role_views.BulkRoleAssignmentView.as_view(),
        name="bulk_role_assignment",
    ),
    path(
        "user-roles/<uuid:pk>/revoke/",
        role_views.RevokeRoleView.as_view(),
        name="revoke_role",
    ),
    # AJAX endpoints
    path(
        "api/users/search/",
        role_views.UserSearchView.as_view(),
        name="user_search",
    ),
    path(
        "api/roles/assign/",
        role_views.AssignRoleView.as_view(),
        name="assign_role",
    ),
    # Legacy endpoints (for backward compatibility)
    path(
        "organizations/<uuid:org_id>/roles/assign/",
        auth_views.RoleAssignmentView.as_view(
            template_name="authentication/role_assignment.html",
        ),
        name="role_assign",
    ),
    path(
        "organizations/<uuid:org_id>/roles/<uuid:role_id>/revoke/",
        auth_views.RoleRevocationView.as_view(),
        name="role_revoke",
    ),
    # Security & Verification - Account security features
    path(
        "security/",
        auth_views.SecuritySettingsView.as_view(
            template_name="authentication/security_settings.html",
        ),
        name="security_settings",
    ),
    path(
        "security/activity/",
        never_cache(security_views.SecurityActivityView.as_view()),
        name="security_activity",
    ),
    # Permission Audit and Compliance Reports
    path(
        "security/audit/permissions/",
        never_cache(security_views.PermissionAuditReportView.as_view()),
        name="permission_audit_report",
    ),
    path(
        "security/compliance/",
        never_cache(security_views.SecurityComplianceReportView.as_view()),
        name="security_compliance_report",
    ),
    path(
        "security/audit/export/",
        security_views.ExportAuditReportView.as_view(),
        name="export_audit_report",
    ),
    path(
        "activity/",
        never_cache(
            auth_views.UserActivityView.as_view(
                template_name="authentication/activity_log.html",
            ),
        ),
        name="activity_log",
    ),
    path(
        "session/extend/",
        auth_views.ExtendSessionView.as_view(),
        name="extend_session",
    ),
    # Include HTMX partial patterns
    *htmx_patterns,
    # Organization Management - Multi-tenant organization features
    path(
        "organizations/switch/",
        organization_views.OrganizationSwitchView.as_view(),
        name="organization_switch",
    ),
    path(
        "organizations/select/",
        organization_views.OrganizationSelectionView.as_view(),
        name="organization_selection",
    ),
    path(
        "api/organizations/switch/",
        organization_views.switch_organization,
        name="switch_organization_api",
    ),
    # Organization Invitations
    path(
        "organizations/invite/",
        organization_views.OrganizationInviteView.as_view(),
        name="organization_invite",
    ),
    path(
        "organizations/invitations/",
        organization_views.OrganizationInviteListView.as_view(),
        name="organization_invites",
    ),
    path(
        "invite/<str:token>/",
        organization_views.AcceptInvitationView.as_view(),
        name="accept_invitation",
    ),
    path(
        "api/invitations/<uuid:invitation_id>/cancel/",
        organization_views.cancel_invitation,
        name="cancel_invitation",
    ),
    # Add missing dashboard URLs for navigation compatibility
    path(
        "role-management/",
        auth_views.UserRoleManagementView.as_view(
            template_name="authentication/role_management_dashboard.html",
        ),
        name="role_management_dashboard",
    ),
    path(
        "stakeholder-management/",
        auth_views.ProfileView.as_view(
            template_name="authentication/stakeholder_management_dashboard.html",
        ),
        name="stakeholder_management_dashboard",
    ),
    # Admin - Tenant Management URLs for super administrators
    path(
        "admin/tenants/",
        admin_views.TenantListView.as_view(),
        name="admin_tenant_list",
    ),
    path(
        "admin/tenants/create/",
        admin_views.TenantCreateView.as_view(),
        name="admin_tenant_create",
    ),
    path(
        "admin/tenants/<slug:slug>/",
        admin_views.TenantDetailView.as_view(),
        name="admin_tenant_detail",
    ),
    path(
        "admin/tenants/<slug:slug>/edit/",
        admin_views.TenantUpdateView.as_view(),
        name="admin_tenant_update",
    ),
    path(
        "admin/tenants/<slug:slug>/delete/",
        admin_views.TenantDeleteView.as_view(),
        name="admin_tenant_delete",
    ),
    path(
        "admin/tenants/<slug:slug>/users/",
        admin_views.TenantUserManagementView.as_view(),
        name="admin_tenant_users",
    ),
    path(
        "admin/tenants/<slug:slug>/analytics/",
        admin_views.tenant_usage_analytics,
        name="admin_tenant_analytics",
    ),
    # Admin API endpoints for HTMX interactions
    path(
        "api/admin/tenants/<slug:slug>/toggle-status/",
        admin_views.toggle_organization_status,
        name="admin_toggle_organization_status",
    ),
    path(
        "api/admin/tenants/<slug:slug>/users/<uuid:user_id>/remove/",
        admin_views.remove_user_from_organization,
        name="admin_remove_user_from_organization",
    ),
    # Social Authentication Administration
    path(
        "admin/social/",
        social_admin_views.social_auth_dashboard,
        name="social_auth_dashboard",
    ),
    path(
        "admin/social/apps/",
        social_admin_views.SocialAppListView.as_view(),
        name="social_apps",
    ),
    path(
        "admin/social/apps/create/",
        social_admin_views.SocialAppCreateView.as_view(),
        name="social_app_create",
    ),
    path(
        "admin/social/apps/<int:pk>/edit/",
        social_admin_views.SocialAppUpdateView.as_view(),
        name="social_app_edit",
    ),
    path(
        "admin/social/apps/<int:pk>/delete/",
        social_admin_views.SocialAppDeleteView.as_view(),
        name="social_app_delete",
    ),
    path(
        "admin/social/apps/<int:pk>/toggle/",
        social_admin_views.toggle_social_app,
        name="social_app_toggle",
    ),
    path(
        "admin/social/apps/<int:pk>/test/",
        social_admin_views.social_app_test,
        name="social_app_test",
    ),
    # Test permission template tags (for development/testing)
    path("test-permission-tags/", test_permission_tags, name="test_permission_tags"),
    # URL redirects for common naming conventions
    path(
        "register/",
        RedirectView.as_view(pattern_name="authentication:signup", permanent=True),
        name="register_redirect",
    ),
    path(
        "password_reset/",
        RedirectView.as_view(pattern_name="authentication:password_reset", permanent=True),
        name="password_reset_redirect",
    ),
    # Django-allauth URLs for social authentication (placed last to avoid conflicts)
    path("social/", include("allauth.urls")),
    # User Preferences & Settings - HTMX-powered preference management
    path(
        "settings/",
        never_cache(UserSettingsView.as_view()),
        name="settings",
    ),
    path(
        "settings/theme/",
        ThemeSettingsView.as_view(),
        name="settings_theme",
    ),
    path(
        "settings/language/",
        LanguageSettingsView.as_view(),
        name="settings_language",
    ),
    path(
        "settings/notifications/",
        NotificationSettingsView.as_view(),
        name="settings_notifications",
    ),
    path(
        "settings/data-formats/",
        DataFormatSettingsView.as_view(),
        name="settings_data_formats",
    ),
    path(
        "settings/keyboard/",
        KeyboardShortcutSettingsView.as_view(),
        name="settings_keyboard",
    ),
    path(
        "settings/dashboard/",
        DashboardWidgetSettingsView.as_view(),
        name="settings_dashboard",
    ),
    path(
        "settings/import-export/",
        PreferenceImportExportView.as_view(),
        name="settings_import_export",
    ),
    path(
        "settings/templates/<uuid:template_id>/apply/",
        apply_preference_template,
        name="apply_preference_template",
    ),
    path(
        "settings/tabs/<str:tab_name>/",
        settings_tab_content,
        name="settings_tab_content",
    ),
    path("", SocialAppListView.as_view(), name="list"),
    path("", IndexView.as_view(), name="index"),
    path("dashboard/", auth_views.AuthIndexView.as_view(), name="dashboard"),
    path("create/", SocialAppCreateView.as_view(), name="create"),
    path("management/", UserRoleManagementView.as_view(), name="management"),
]
