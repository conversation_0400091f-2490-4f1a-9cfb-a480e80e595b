"""Project-Asset integration models for CLEAR platform.

This module implements integration between the assets and projects apps,
enabling project-based asset organization, asset allocation, and project costing.
"""

import uuid
from decimal import Decimal

from django.contrib.gis.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from apps.common.mixins import AuditMixin
from apps.common.models import OrganizationOwnedModel


class ProjectAssetAssignment(OrganizationOwnedModel, AuditMixin):
    """Associates assets with projects for specific purposes and locations.

    This model enables:
    - Project-based asset organization
    - Asset allocation and project costing
    - Spatial placement within projects
    - Asset utilization tracking
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Core relationships
    project = models.ForeignKey(
        "projects.Project",
        on_delete=models.CASCADE,
        related_name="asset_assignments",
        help_text=_("Project this asset is assigned to"),
    )

    asset = models.ForeignKey(
        "assets.Asset",
        on_delete=models.CASCADE,
        related_name="project_assignments",
        help_text=_("Asset assigned to the project"),
    )

    # Assignment details
    assignment_type = models.CharField(
        max_length=50,
        choices=[
            ("allocated", _("Allocated")),
            ("reserved", _("Reserved")),
            ("in_use", _("In Use")),
            ("maintenance", _("Under Maintenance")),
            ("retired", _("Retired from Project")),
        ],
        default="allocated",
        help_text=_("Type of assignment"),
    )

    purpose = models.CharField(max_length=200, blank=True, help_text=_("Purpose or role of asset in project"))

    # Temporal tracking
    assigned_date = models.DateTimeField(default=timezone.now, help_text=_("When asset was assigned to project"))

    start_date = models.DateTimeField(null=True, blank=True, help_text=_("When asset usage started"))

    end_date = models.DateTimeField(null=True, blank=True, help_text=_("When asset usage ended"))

    # Location within project
    location_description = models.CharField(
        max_length=500,
        blank=True,
        help_text=_("Description of asset location within project"),
    )

    # Spatial location (if applicable)
    location = models.PointField(
        srid=4326,
        null=True,
        blank=True,
        help_text=_("Precise location of asset within project boundaries"),
    )

    # Cost tracking
    allocation_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Cost allocated to project for this asset"),
    )

    hourly_rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Hourly rate for asset usage"),
    )

    # Utilization tracking
    estimated_hours = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Estimated hours of asset usage"),
    )

    actual_hours = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Actual hours of asset usage"),
    )

    # Status and notes
    is_active = models.BooleanField(default=True, help_text=_("Whether assignment is currently active"))

    notes = models.TextField(blank=True, help_text=_("Additional notes about asset assignment"))

    class Meta:
        verbose_name = _("Project Asset Assignment")
        verbose_name_plural = _("Project Asset Assignments")
        unique_together = [["project", "asset", "assignment_type"]]
        indexes = [
            models.Index(fields=["project", "assignment_type"]),
            models.Index(fields=["asset", "is_active"]),
            models.Index(fields=["assigned_date"]),
            models.Index(fields=["start_date", "end_date"]),
        ]
        ordering = ["-assigned_date"]

    def __str__(self):
        return f"{self.asset.name} → {self.project.name} ({self.assignment_type})"

    def clean(self):
        """Validate assignment data."""
        super().clean()

        # Validate date sequence
        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError(_("Start date cannot be after end date"))

        # Validate organization consistency
        if self.asset.organization_id != self.project.organization_id:
            raise ValidationError(_("Asset and project must belong to the same organization"))

        # Validate location is within project boundaries (if both have location)
        if self.location and hasattr(self.project, "location") and self.project.location:
            # This would require PostGIS spatial operations
            # Implementation depends on project boundary definition
            pass

    @property
    def total_cost(self):
        """Calculate total cost for this assignment."""
        if self.allocation_cost:
            return self.allocation_cost

        if self.hourly_rate and self.actual_hours:
            return self.hourly_rate * self.actual_hours

        if self.hourly_rate and self.estimated_hours:
            return self.hourly_rate * self.estimated_hours

        return None

    @property
    def utilization_percentage(self):
        """Calculate utilization percentage."""
        if self.estimated_hours and self.actual_hours:
            return (self.actual_hours / self.estimated_hours) * 100
        return None

    @property
    def is_overdue(self):
        """Check if assignment is past its end date."""
        return self.end_date and timezone.now() > self.end_date and self.assignment_type in ["allocated", "in_use"]

    @property
    def duration_days(self):
        """Calculate assignment duration in days."""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days
        elif self.start_date:
            return (timezone.now() - self.start_date).days
        return None


class AssetProjectCost(OrganizationOwnedModel, AuditMixin):
    """Track costs and financial impact of assets on projects.

    This model enables:
    - Project costing with asset breakdown
    - Asset ROI analysis
    - Budget tracking and forecasting
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Core relationships
    assignment = models.OneToOneField(
        "ProjectAssetAssignment",
        on_delete=models.CASCADE,
        related_name="cost_details",
        help_text=_("Asset assignment this cost relates to"),
    )

    # Cost breakdown
    acquisition_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Original acquisition cost of asset"),
    )

    operational_cost_per_hour = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Operational cost per hour"),
    )

    maintenance_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text=_("Maintenance costs during project"),
    )

    depreciation_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        null=True,
        blank=True,
        help_text=_("Annual depreciation rate (0.1 = 10%)"),
    )

    # Budget tracking
    budgeted_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_("Budgeted amount for this asset assignment"),
    )

    actual_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=Decimal("0.00"),
        help_text=_("Actual cost incurred"),
    )

    # Cost tracking
    last_calculated = models.DateTimeField(null=True, blank=True, help_text=_("When costs were last calculated"))

    notes = models.TextField(blank=True, help_text=_("Cost calculation notes"))

    class Meta:
        verbose_name = _("Asset Project Cost")
        verbose_name_plural = _("Asset Project Costs")
        indexes = [
            models.Index(fields=["actual_cost"]),
            models.Index(fields=["budgeted_amount"]),
            models.Index(fields=["last_calculated"]),
        ]

    def __str__(self):
        return f"Cost: {self.assignment}"

    @property
    def cost_variance(self):
        """Calculate cost variance (actual vs budgeted)."""
        if self.budgeted_amount:
            return self.actual_cost - self.budgeted_amount
        return None

    @property
    def cost_variance_percentage(self):
        """Calculate cost variance as percentage."""
        if self.budgeted_amount and self.budgeted_amount > 0:
            return (self.cost_variance / self.budgeted_amount) * 100
        return None

    @property
    def is_over_budget(self):
        """Check if actual cost exceeds budget."""
        return self.cost_variance and self.cost_variance > 0

    def calculate_depreciation_cost(self):
        """Calculate depreciation cost for assignment period."""
        if not self.acquisition_cost or not self.depreciation_rate:
            return Decimal("0.00")

        assignment = self.assignment
        if not assignment.duration_days:
            return Decimal("0.00")

        # Calculate annual depreciation
        annual_depreciation = self.acquisition_cost * self.depreciation_rate

        # Pro-rate for assignment duration
        daily_depreciation = annual_depreciation / 365

        return daily_depreciation * assignment.duration_days

    def calculate_operational_cost(self):
        """Calculate operational cost for assignment."""
        if not self.operational_cost_per_hour or not self.assignment.actual_hours:
            return Decimal("0.00")

        return self.operational_cost_per_hour * self.assignment.actual_hours

    def update_actual_cost(self):
        """Update actual cost based on current data."""
        self.actual_cost = (
            self.calculate_depreciation_cost() + self.calculate_operational_cost() + self.maintenance_cost
        )
        self.last_calculated = timezone.now()
        self.save(update_fields=["actual_cost", "last_calculated"])

        return self.actual_cost
