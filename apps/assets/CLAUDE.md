# CLAUDE.md - Assets App

## App Purpose and Functionality

The Assets app is a comprehensive 3D model asset management system for the CLEAR platform, designed to handle
enterprise-level 3D asset lifecycle management for utility infrastructure visualization. It provides AAA-quality 3D
model management capabilities rivaling professional CAD/BIM software.

### Core Features

- **3D Asset Management**: GLTF/GLB model support with advanced optimization
- **Multi-LOD System**: Automatic Level of Detail generation for performance optimization
- **Asset Versioning**: Complete lifecycle management with approval workflows
- **Quality Assurance**: Performance monitoring and comprehensive quality metrics
- **Spatial Integration**: Integration with the platform's PostGIS spatial visualization system
- **Enterprise Asset Libraries**: Centralized organization and access control
- **Performance Profiling**: Cross-device performance analysis and optimization

### Primary Use Cases

- Infrastructure asset visualization in utility projects
- 3D model library management for engineering teams
- Performance-optimized asset delivery for web-based 3D visualization
- Asset quality assurance and compliance checking
- Multi-tenant asset sharing and collaboration

## Models Architecture

### Core Models

#### `ThreeDAssetLibrary`

- **Purpose**: Centralized 3D asset library management with enterprise-level organization
- **Key Features**:
    - Multi-tenant organization support
    - Access control (public, organization, project, private)
    - Library categorization (infrastructure, utilities, symbols, terrain, etc.)
    - Usage statistics and analytics
    - Project and user sharing capabilities
- **Database Table**: `threed_asset_libraries`

#### `ThreeDAsset`

- **Purpose**: Core 3D asset model with comprehensive metadata and lifecycle management
- **Key Features**:
    - Multiple file format support (GLTF, GLB, OBJ, FBX, DAE)
    - Comprehensive model properties (triangle count, vertex count, bounding box)
    - Quality scoring and validation
    - Version control and approval workflows
    - Performance analytics and usage tracking
    - Spatial properties and utility type associations
- **Database Table**: `threed_assets`

#### `ThreeDAssetLOD`

- **Purpose**: Level of Detail variants for performance optimization
- **Key Features**:
    - Automatic LOD generation with configurable quality levels
    - Distance-based LOD switching
    - Performance metrics per LOD level
    - Quality loss tracking and visual similarity scoring
- **Database Table**: `threed_asset_lods`

#### `ThreeDAssetTexture`

- **Purpose**: Advanced texture management with optimization support
- **Key Features**:
    - Multiple texture types (diffuse, normal, roughness, metallic, etc.)
    - Compression format support (PNG, JPEG, WebP, KTX2, Basis Universal)
    - Quality metrics (PSNR, SSIM)
    - UV mapping optimization
- **Database Table**: `threed_asset_textures`

### Processing and Analytics Models

#### `AssetProcessingJob`

- **Purpose**: Background processing job management
- **Job Types**: Validation, optimization, LOD generation, texture compression
- **Database Table**: `asset_processing_jobs`

#### `AssetQualityMetrics`

- **Purpose**: Comprehensive quality analysis and scoring
- **Metrics**: Geometric, visual, and performance quality scores
- **Database Table**: `asset_quality_metrics`

#### `AssetUsageTracking`

- **Purpose**: Track asset usage across projects and contexts
- **Analytics**: Performance data, user behavior, device compatibility
- **Database Table**: `asset_usage_tracking`

#### `AssetPerformanceProfile`

- **Purpose**: Cross-device performance profiling
- **Profiles**: Different device categories and performance metrics
- **Database Table**: `asset_performance_profiles`

### Workflow and Integration Models

#### `AssetWorkflowStep`

- **Purpose**: Define and track asset approval workflows
- **Steps**: Upload, validation, optimization, review, approval, publication
- **Database Table**: `asset_workflow_steps`

#### `ProjectAssetMapping`

- **Purpose**: Map assets to specific project locations and contexts
- **Features**: Spatial placement, transformation matrices, utility associations
- **Database Table**: `project_asset_mappings`

## Views and URL Patterns

### Current View Implementation

Located in `/home/<USER>/Coding/CLEAR-0.5/clear_htmx/apps/assets/views/asset_views.py`

**Standard CRUD Views:**

- `AssetListView`: Paginated asset listing
- `AssetDetailView`: Detailed asset information
- `AssetCreateView`: Asset creation form
- `AssetUpdateView`: Asset modification
- `AssetDeleteView`: Asset deletion with confirmation

**HTMX-Enabled Views:**

- `AssetSearchView`: Real-time search functionality
- `AssetFilterView`: Dynamic filtering
- `AssetQuickEditView`: Inline editing capabilities

**Bulk Operations:**

- `AssetBulkImportView`: Mass asset import
- `AssetBulkExportView`: Asset export functionality

**Category Management:**

- `AssetCategoryListView`: Category listing
- `AssetCategoryCreateView`: Category creation

### URL Patterns

Defined in `/home/<USER>/Coding/CLEAR-0.5/clear_htmx/apps/assets/urls.py`

```python
# Standard CRUD
assets/                    # Asset list
assets/create/            # Asset creation
assets/<int:pk>/          # Asset detail
assets/<int:pk>/update/   # Asset update
assets/<int:pk>/delete/   # Asset deletion

# HTMX Endpoints
assets/htmx/search/                    # Search
assets/htmx/filter/                    # Filtering
assets/htmx/<int:pk>/quick-edit/       # Quick edit

# Categories
assets/categories/         # Category list
assets/categories/create/  # Category creation

# Bulk Operations
assets/bulk/import/        # Bulk import
assets/bulk/export/        # Bulk export
```

## Dependencies and Relationships

### Internal App Dependencies

- **`apps.common`**: Base mixins and utilities
- **`apps.projects`**: Project association for asset mapping
- **`apps.infrastructure`**: Utility and conflict associations
- **`apps.authentication`**: User management and permissions

### External Dependencies

- **PostGIS**: Spatial data support for asset positioning
- **Django File Storage**: Asset file management
- **Celery**: Background processing for optimization and LOD generation
- **PIL/Pillow**: Image processing for thumbnails and textures

### Model Relationships

- Assets belong to Libraries (ForeignKey)
- Assets can have multiple LOD variants (OneToMany)
- Assets can have multiple textures (OneToMany)
- Assets can be mapped to multiple projects (ManyToMany through ProjectAssetMapping)
- Assets track usage across projects and users
- Quality metrics are one-to-one with assets

## Development Guidelines

### Asset File Management

- **Storage Location**: `3d_assets/` directory with subdirectories:
    - `original/`: Original uploaded files
    - `optimized/`: Processed and optimized files
    - `lod/`: Level of detail variants
    - `thumbnails/`: Asset preview images
    - `textures/`: Texture files with optimization variants

### Model Patterns

- All models use UUID primary keys for better distributed system support
- Models follow the organization-based multi-tenancy pattern
- Comprehensive metadata storage using JSONField
- Proper indexing for performance-critical queries
- Database-level constraints for data integrity

### Performance Considerations

- **LOD Generation**: Automatic optimization for different viewing distances
- **Texture Compression**: Multiple format support for optimal delivery
- **Caching**: Implement caching for frequently accessed assets
- **Lazy Loading**: Assets should be loaded on-demand
- **Database Optimization**: Proper indexing on commonly queried fields

### Security Guidelines

- File upload validation and sanitization
- Access control through library permissions
- Audit logging for asset modifications
- Secure file storage with proper permissions
- Content Security Policy compliance for 3D content

## Testing Patterns

### Test Structure (To Be Implemented)

```
tests/
├── __init__.py
├── test_models.py          # Model unit tests
├── test_views.py           # View integration tests
├── test_processing.py      # Asset processing tests
├── test_performance.py     # Performance profiling tests
├── test_security.py        # Security validation tests
└── fixtures/               # Test asset files
    ├── sample.gltf
    ├── sample.glb
    └── test_textures/
```

### Key Testing Areas

1. **Model Validation**: Test all model constraints and relationships
2. **File Processing**: Validate asset upload and optimization
3. **LOD Generation**: Test automatic LOD creation and quality
4. **Performance Metrics**: Validate performance profiling accuracy
5. **Security**: Test access controls and file validation
6. **HTMX Integration**: Test real-time search and filtering
7. **Spatial Integration**: Test PostGIS integration for asset positioning

### Test Data Requirements

- Sample 3D models in various formats
- Test texture files with different resolutions
- Performance benchmark data
- Organization and project test fixtures

## Current Implementation Status

### Completed

- ✅ Comprehensive model architecture
- ✅ Asset lifecycle management with status transitions
- ✅ Status transition validation and enforcement
- ✅ Advanced spatial/location features with PostGIS
- ✅ Spatial query optimization with GIST indexing
- ✅ Complete CRUD views structure with organization filtering
- ✅ URL routing configuration
- ✅ Admin interface with organization isolation
- ✅ App configuration and signals framework
- ✅ Comprehensive test suite (400+ lines including spatial tests)
- ✅ Template consistency fixes
- ✅ Lifecycle documentation
- ✅ Spatial features documentation

### In Progress/Placeholder

- ⚠️ Empty migrations (no database tables created yet)
- ⚠️ Serializers directory is empty

### Not Yet Implemented

- ❌ Forms for asset creation and editing
- ❌ Asset processing services
- ❌ LOD generation algorithms
- ❌ Quality analysis engines
- ❌ Performance profiling tools
- ❌ Celery task definitions
- ❌ API endpoints (if needed)
- ❌ WebGL/3D viewer integration

## Architectural Decisions

### File Format Strategy

- **Primary Format**: GLTF 2.0 for web optimization
- **Input Formats**: Multiple format support (OBJ, FBX, DAE) with conversion
- **Optimization**: Automatic DRACO compression for smaller file sizes
- **Textures**: Modern format support (WebP, KTX2, Basis Universal)

### Performance Optimization

- **LOD System**: Automatic generation with configurable quality levels
- **Caching Strategy**: Multi-level caching for assets and metadata
- **Database Optimization**: Comprehensive indexing strategy
- **Async Processing**: Background processing for heavy operations

### Quality Assurance

- **Automated Validation**: Comprehensive model validation
- **Quality Scoring**: Multi-dimensional quality assessment
- **Performance Profiling**: Cross-device performance analysis
- **Recommendation Engine**: Optimization suggestions

### Integration Points

- **Spatial Systems**: PostGIS integration for geographical placement
- **Project Management**: Asset-project mapping for utility visualization
- **User Management**: Organization-based access control
- **Analytics**: Comprehensive usage tracking and analytics

## Future Enhancements

### Planned Features

1. **AI-Powered Optimization**: Machine learning for LOD generation
2. **Real-time Collaboration**: Multi-user asset editing
3. **Advanced Analytics**: Predictive performance analysis
4. **Mobile Optimization**: Progressive loading for mobile devices
5. **VR/AR Support**: Extended reality compatibility
6. **Asset Marketplace**: Community asset sharing
7. **Advanced Search**: Semantic search with ML
8. **Automated Tagging**: AI-powered asset categorization

### Technical Improvements

- WebAssembly integration for client-side processing
- Advanced compression algorithms
- Real-time quality monitoring
- Automated asset optimization pipelines
- Enhanced spatial integration
- Advanced caching strategies

This app represents a critical component of the CLEAR platform's 3D visualization capabilities, providing
enterprise-grade asset management for utility infrastructure projects.
