"""
Import/Export service for asset management.

This service provides comprehensive import/export functionality for assets including:
- CSV/Excel import with validation and error handling
- Bulk asset creation and updates from imports
- Export functionality for various formats
- Template generation for imports
- Import preview and validation
"""

import logging
from datetime import datetime
from decimal import Decimal, InvalidOperation
from io import StringIO
from typing import Any, Dict, List, Optional, Union

from django.contrib.auth import get_user_model
from django.contrib.gis.geos import GEOSException, Point
from django.db import transaction

from apps.common.services.export_service import ExportService

from ..models import Asset
from ..models.financial import AssetFinancial

User = get_user_model()
logger = logging.getLogger(__name__)


class AssetImportExportService:
    """Service class for asset import/export operations."""

    # Standard field mappings for asset imports
    ASSET_FIELD_MAPPINGS = {
        # Standard field names that might appear in imports
        "name": ["name", "asset_name", "title", "asset_title"],
        "description": ["description", "desc", "notes", "comments"],
        "category": ["category", "asset_category", "type", "asset_type"],
        "status": ["status", "asset_status", "state"],
        "manufacturer": ["manufacturer", "make", "vendor", "supplier"],
        "model_number": ["model_number", "model", "model_no", "part_number"],
        "serial_number": ["serial_number", "serial", "serial_no", "sn"],
        "asset_type": ["asset_type", "type", "equipment_type"],
        # Location fields
        "latitude": ["latitude", "lat", "y", "coord_y"],
        "longitude": ["longitude", "lng", "lon", "x", "coord_x"],
        # Financial fields
        "acquisition_cost": [
            "acquisition_cost",
            "cost",
            "purchase_cost",
            "value",
            "price",
        ],
        "purchase_date": [
            "purchase_date",
            "acquired_date",
            "date_purchased",
            "buy_date",
        ],
        "useful_life_years": [
            "useful_life_years",
            "useful_life",
            "life_years",
            "lifespan",
        ],
        "salvage_value": ["salvage_value", "residual_value", "scrap_value"],
        "depreciation_method": ["depreciation_method", "depreciation", "dep_method"],
    }

    # Valid status values
    VALID_STATUS_VALUES = {
        "active": ["active", "operational", "online", "running", "in_service"],
        "inactive": ["inactive", "offline", "stopped", "out_of_service"],
        "maintenance": ["maintenance", "repair", "servicing", "under_maintenance"],
        "retired": ["retired", "disposed", "decommissioned", "scrapped", "end_of_life"],
    }

    # Valid depreciation methods
    VALID_DEPRECIATION_METHODS = {
        "straight_line": ["straight_line", "straight-line", "linear", "sl"],
        "declining_balance": ["declining_balance", "declining-balance", "db"],
        "double_declining": [
            "double_declining",
            "double-declining",
            "double_declining_balance",
            "ddb",
        ],
        "units_of_production": [
            "units_of_production",
            "units-of-production",
            "uop",
            "activity",
        ],
        "sum_of_years": ["sum_of_years", "sum-of-years", "syd", "sum_of_years_digits"],
        "none": ["none", "no_depreciation", "non_depreciable", "n/a"],
    }

    def __init__(self):
        self.export_service = ExportService()

    def import_assets_from_file(
        self,
        file_content: Union[str, bytes],
        file_type: str,
        organization,
        user: User,
        preview_only: bool = False,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Import assets from uploaded file.

        Args:
            file_content: File content as string or bytes
            file_type: File type ('csv', 'excel')
            organization: Organization context
            user: User performing import
            preview_only: If True, only validate and preview, don't create
            **kwargs: Additional options

        Returns:
            Dict with import results and validation errors
        """
        try:
            logger.info(f"Starting asset import for {organization.name} by {user.username}")

            # Parse file content based on type
            if file_type.lower() == "csv":
                data = self._parse_csv_content(file_content)
            elif file_type.lower() in ["excel", "xlsx", "xls"]:
                data = self._parse_excel_content(file_content, kwargs.get("sheet_name"))
            else:
                return {
                    "success": False,
                    "error": f"Unsupported file type: {file_type}",
                    "preview_data": None,
                    "validation_errors": [],
                }

            if not data:
                return {
                    "success": False,
                    "error": "No data found in file",
                    "preview_data": None,
                    "validation_errors": [],
                }

            # Validate and map data
            validation_result = self._validate_import_data(data, organization, user)

            if preview_only:
                return {
                    "success": True,
                    "preview_data": validation_result["mapped_data"],
                    "validation_errors": validation_result["errors"],
                    "valid_count": validation_result["valid_count"],
                    "total_count": len(data),
                }

            # Create assets if validation passed
            if validation_result["valid_count"] > 0:
                creation_result = self._create_assets_from_data(validation_result["mapped_data"], organization, user)

                return {
                    "success": True,
                    "created_count": creation_result["created_count"],
                    "updated_count": creation_result["updated_count"],
                    "failed_count": creation_result["failed_count"],
                    "validation_errors": validation_result["errors"],
                    "creation_errors": creation_result["errors"],
                    "total_count": len(data),
                }
            else:
                return {
                    "success": False,
                    "error": "No valid assets to import",
                    "validation_errors": validation_result["errors"],
                    "total_count": len(data),
                }

        except Exception as e:
            logger.error(f"Error importing assets: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "validation_errors": [],
                "total_count": 0,
            }

    def export_assets(
        self,
        queryset=None,
        organization=None,
        export_format: str = "csv",
        include_financial: bool = False,
        user: Optional[User] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Export assets to specified format.

        Args:
            queryset: Asset queryset to export
            organization: Organization context for filtering
            export_format: Format ('csv', 'excel', 'json')
            include_financial: Whether to include financial data
            user: User requesting export
            **kwargs: Additional export options

        Returns:
            Dict with export results
        """
        try:
            logger.info(f"Starting asset export for {organization.name if organization else 'all'}")

            # Build queryset if not provided
            if queryset is None:
                queryset = Asset.objects.all()
                if organization:
                    queryset = queryset.filter(organization=organization)

            # Prepare export data
            export_data = self._prepare_export_data(
                queryset,
                include_financial=include_financial,
                include_location=kwargs.get("include_location", True),
            )

            if not export_data:
                return {
                    "success": False,
                    "error": "No assets to export",
                    "content": None,
                }

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            org_name = organization.name.replace(" ", "_") if organization else "all_orgs"
            filename_base = f"assets_{org_name}_{timestamp}"

            # Export based on format
            if export_format.lower() == "csv":
                return self.export_service.export_csv(
                    data=export_data,
                    filename=f"{filename_base}.csv",
                    organization=organization,
                    user=user,
                )
            elif export_format.lower() in ["excel", "xlsx"]:
                return self.export_service.export_excel(
                    data=export_data,
                    filename=f"{filename_base}.xlsx",
                    sheet_name="Assets",
                    organization=organization,
                    user=user,
                )
            elif export_format.lower() == "json":
                return self.export_service.export_json(
                    data=export_data,
                    filename=f"{filename_base}.json",
                    organization=organization,
                    user=user,
                )
            elif export_format.lower() == "shapefile" and kwargs.get("include_location"):
                # Only export assets with location data for shapefiles
                geo_data = [item for item in export_data if item.get("location")]
                if not geo_data:
                    return {
                        "success": False,
                        "error": "No assets with location data to export as shapefile",
                        "content": None,
                    }

                return self.export_service.export_shapefile(
                    data=geo_data,
                    filename=f"{filename_base}_spatial",
                    geometry_field="location",
                    organization=organization,
                    user=user,
                )
            else:
                return {
                    "success": False,
                    "error": f"Unsupported export format: {export_format}",
                    "content": None,
                }

        except Exception as e:
            logger.error(f"Error exporting assets: {str(e)}")
            return {"success": False, "error": str(e), "content": None}

    def generate_import_template(
        self,
        template_type: str = "basic",
        format: str = "csv",
        include_sample_data: bool = False,
    ) -> Dict[str, Any]:
        """
        Generate import template with headers and sample data.

        Args:
            template_type: Template type ('basic', 'financial', 'complete')
            format: Output format ('csv', 'excel')
            include_sample_data: Whether to include sample rows

        Returns:
            Dict with template content
        """
        try:
            # Define template columns based on type
            if template_type == "basic":
                columns = [
                    "name",
                    "description",
                    "category",
                    "status",
                    "asset_type",
                    "manufacturer",
                    "model_number",
                    "serial_number",
                    "latitude",
                    "longitude",
                ]
            elif template_type == "financial":
                columns = [
                    "name",
                    "description",
                    "category",
                    "status",
                    "asset_type",
                    "manufacturer",
                    "model_number",
                    "serial_number",
                    "latitude",
                    "longitude",
                    "acquisition_cost",
                    "purchase_date",
                    "useful_life_years",
                    "salvage_value",
                    "depreciation_method",
                ]
            else:  # complete
                columns = [
                    "name",
                    "description",
                    "category",
                    "status",
                    "asset_type",
                    "manufacturer",
                    "model_number",
                    "serial_number",
                    "latitude",
                    "longitude",
                    "acquisition_cost",
                    "purchase_date",
                    "useful_life_years",
                    "salvage_value",
                    "depreciation_method",
                    "vendor",
                    "purchase_order_number",
                    "invoice_number",
                ]

            # Create template data
            template_data = []

            # Add header descriptions as first row
            descriptions = {
                "name": "Asset Name (Required)",
                "description": "Asset Description",
                "category": "Asset Category",
                "status": "active, inactive, maintenance, retired",
                "asset_type": "Type of Asset",
                "manufacturer": "Manufacturer Name",
                "model_number": "Model Number",
                "serial_number": "Serial Number",
                "latitude": "Latitude (Decimal Degrees)",
                "longitude": "Longitude (Decimal Degrees)",
                "acquisition_cost": "Purchase Cost (Numbers Only)",
                "purchase_date": "YYYY-MM-DD Format",
                "useful_life_years": "Years (Number)",
                "salvage_value": "End-of-life Value (Numbers Only)",
                "depreciation_method": "straight_line, declining_balance, etc.",
                "vendor": "Vendor/Supplier Name",
                "purchase_order_number": "PO Number",
                "invoice_number": "Invoice Number",
            }

            # Add description row
            desc_row = {col: descriptions.get(col, "") for col in columns}
            template_data.append(desc_row)

            # Add sample data if requested
            if include_sample_data:
                sample_rows = [
                    {
                        "name": "Generator Unit 001",
                        "description": "Emergency backup generator",
                        "category": "Electrical",
                        "status": "active",
                        "asset_type": "Generator",
                        "manufacturer": "Caterpillar",
                        "model_number": "C9",
                        "serial_number": "CAT001234",
                        "latitude": 40.7128,
                        "longitude": -74.0060,
                        "acquisition_cost": 25000.00,
                        "purchase_date": "2023-01-15",
                        "useful_life_years": 10,
                        "salvage_value": 2500.00,
                        "depreciation_method": "straight_line",
                        "vendor": "Caterpillar Inc",
                        "purchase_order_number": "PO-2023-001",
                        "invoice_number": "INV-12345",
                    },
                    {
                        "name": "Pump Station A",
                        "description": "Main water pump station",
                        "category": "Water",
                        "status": "active",
                        "asset_type": "Pump",
                        "manufacturer": "Grundfos",
                        "model_number": "CR64-2",
                        "serial_number": "GRU789012",
                        "latitude": 40.7589,
                        "longitude": -73.9851,
                        "acquisition_cost": 15000.00,
                        "purchase_date": "2023-03-20",
                        "useful_life_years": 15,
                        "salvage_value": 1500.00,
                        "depreciation_method": "double_declining",
                        "vendor": "Grundfos USA",
                        "purchase_order_number": "PO-2023-002",
                        "invoice_number": "INV-67890",
                    },
                ]

                for sample_row in sample_rows:
                    # Only include columns that exist in template
                    filtered_row = {col: sample_row.get(col, "") for col in columns}
                    template_data.append(filtered_row)

            # Generate template based on format
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"asset_import_template_{template_type}_{timestamp}"

            if format.lower() == "csv":
                return self.export_service.export_csv(data=template_data, filename=f"{filename}.csv")
            else:  # excel
                return self.export_service.export_excel(
                    data=template_data,
                    filename=f"{filename}.xlsx",
                    sheet_name="Asset Import Template",
                )

        except Exception as e:
            logger.error(f"Error generating import template: {str(e)}")
            return {"success": False, "error": str(e), "content": None}

    def _parse_csv_content(self, content: Union[str, bytes]) -> List[Dict[str, Any]]:
        """Parse CSV content into list of dictionaries."""
        import csv

        if isinstance(content, bytes):
            content = content.decode("utf-8-sig")  # Handle BOM

        # Parse CSV
        reader = csv.DictReader(StringIO(content))
        data = []

        for row_num, row in enumerate(reader, start=2):  # Start at 2 for header row
            # Skip empty rows
            if not any(row.values()):
                continue

            # Add row number for error reporting
            row["_row_number"] = row_num
            data.append(row)

        return data

    def _parse_excel_content(self, content: bytes, sheet_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Parse Excel content into list of dictionaries."""
        try:
            from io import BytesIO

            from openpyxl import load_workbook
        except ImportError:
            raise ValueError("openpyxl library required for Excel imports")

        # Load workbook
        workbook = load_workbook(BytesIO(content), read_only=True)

        # Get worksheet
        if sheet_name:
            if sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
            else:
                raise ValueError(f"Sheet '{sheet_name}' not found")
        else:
            worksheet = workbook.active

        # Get data
        data = []
        rows = list(worksheet.rows)

        if not rows:
            return data

        # Get headers from first row
        headers = [cell.value for cell in rows[0]]
        headers = [str(h).strip() if h is not None else f"Column_{i}" for i, h in enumerate(headers)]

        # Process data rows
        for row_num, row in enumerate(rows[1:], start=2):
            row_data = {}
            has_data = False

            for col_num, cell in enumerate(row):
                if col_num < len(headers):
                    value = cell.value
                    if value is not None:
                        # Convert to string and strip
                        value = str(value).strip()
                        if value:  # Only add non-empty values
                            has_data = True
                            row_data[headers[col_num]] = value

            # Only add rows with actual data
            if has_data:
                row_data["_row_number"] = row_num
                data.append(row_data)

        workbook.close()
        return data

    def _validate_import_data(self, data: List[Dict[str, Any]], organization, user: User) -> Dict[str, Any]:
        """Validate and map import data."""
        errors = []
        mapped_data = []
        valid_count = 0

        for row in data:
            row_number = row.get("_row_number", "unknown")

            try:
                # Map and validate row
                mapped_row = self._map_row_fields(row)
                row_validation = self._validate_row_data(mapped_row, organization)

                if row_validation["valid"]:
                    mapped_data.append(
                        {
                            "data": row_validation["data"],
                            "row_number": row_number,
                            "valid": True,
                        }
                    )
                    valid_count += 1
                else:
                    mapped_data.append(
                        {
                            "data": row_validation["data"],
                            "row_number": row_number,
                            "valid": False,
                            "errors": row_validation["errors"],
                        }
                    )
                    errors.extend([f"Row {row_number}: {error}" for error in row_validation["errors"]])

            except Exception as e:
                error_msg = f"Row {row_number}: Unexpected error - {str(e)}"
                errors.append(error_msg)
                mapped_data.append(
                    {
                        "data": row,
                        "row_number": row_number,
                        "valid": False,
                        "errors": [str(e)],
                    }
                )

        return {
            "mapped_data": mapped_data,
            "errors": errors,
            "valid_count": valid_count,
        }

    def _map_row_fields(self, row: Dict[str, Any]) -> Dict[str, Any]:
        """Map row fields using field mappings."""
        mapped = {}

        # Convert all keys to lowercase for matching
        row_lower = {k.lower().strip(): v for k, v in row.items() if k != "_row_number"}

        # Map each field
        for field, possible_names in self.ASSET_FIELD_MAPPINGS.items():
            for possible_name in possible_names:
                if possible_name.lower() in row_lower:
                    mapped[field] = row_lower[possible_name.lower()]
                    break

        # Add any unmapped fields as metadata
        mapped_keys = set()
        for possible_names in self.ASSET_FIELD_MAPPINGS.values():
            mapped_keys.update(name.lower() for name in possible_names)

        extra_fields = {}
        for key, value in row_lower.items():
            if key not in mapped_keys and key != "_row_number":
                extra_fields[key] = value

        if extra_fields:
            mapped["metadata"] = extra_fields

        # Preserve row number
        if "_row_number" in row:
            mapped["_row_number"] = row["_row_number"]

        return mapped

    def _validate_row_data(self, row: Dict[str, Any], organization) -> Dict[str, Any]:
        """Validate individual row data."""
        errors = []
        cleaned_data = {}

        # Required field: name
        name = row.get("name", "").strip()
        if not name:
            errors.append("Asset name is required")
        else:
            cleaned_data["name"] = name

        # Optional fields with validation
        if "description" in row:
            cleaned_data["description"] = row["description"].strip()

        if "category" in row:
            cleaned_data["category"] = row["category"].strip()

        # Status validation
        if "status" in row:
            status = self._normalize_status(row["status"])
            if status:
                cleaned_data["status"] = status
            else:
                errors.append(f"Invalid status: {row['status']}")

        # Asset identification fields
        for field in ["asset_type", "manufacturer", "model_number", "serial_number"]:
            if field in row and row[field]:
                cleaned_data[field] = str(row[field]).strip()

        # Location validation
        lat_lon_errors = self._validate_coordinates(row)
        if lat_lon_errors:
            errors.extend(lat_lon_errors)
        else:
            if "latitude" in row and "longitude" in row:
                try:
                    lat = float(row["latitude"])
                    lon = float(row["longitude"])
                    cleaned_data["location"] = Point(lon, lat, srid=4326)
                except (ValueError, GEOSException) as e:
                    errors.append(f"Invalid coordinates: {str(e)}")

        # Financial fields validation
        financial_data = self._validate_financial_fields(row)
        if financial_data.get("errors"):
            errors.extend(financial_data["errors"])
        if financial_data.get("data"):
            cleaned_data["financial"] = financial_data["data"]

        # Metadata
        if "metadata" in row:
            cleaned_data["metadata"] = row["metadata"]

        return {"valid": len(errors) == 0, "data": cleaned_data, "errors": errors}

    def _validate_coordinates(self, row: Dict[str, Any]) -> List[str]:
        """Validate latitude/longitude coordinates."""
        errors = []

        lat = row.get("latitude")
        lon = row.get("longitude")

        # Both or neither should be provided
        if (lat and not lon) or (lon and not lat):
            errors.append("Both latitude and longitude must be provided together")
            return errors

        if lat and lon:
            try:
                lat_val = float(lat)
                lon_val = float(lon)

                # Validate ranges
                if not (-90 <= lat_val <= 90):
                    errors.append("Latitude must be between -90 and 90")

                if not (-180 <= lon_val <= 180):
                    errors.append("Longitude must be between -180 and 180")

            except (ValueError, TypeError):
                errors.append("Latitude and longitude must be numeric values")

        return errors

    def _validate_financial_fields(self, row: Dict[str, Any]) -> Dict[str, Any]:
        """Validate financial fields if present."""
        errors = []
        financial_data = {}

        # Acquisition cost
        if "acquisition_cost" in row and row["acquisition_cost"]:
            try:
                cost = Decimal(str(row["acquisition_cost"]).replace(",", ""))
                if cost < 0:
                    errors.append("Acquisition cost cannot be negative")
                else:
                    financial_data["acquisition_cost"] = cost
            except (InvalidOperation, ValueError):
                errors.append("Invalid acquisition cost format")

        # Purchase date
        if "purchase_date" in row and row["purchase_date"]:
            try:
                from dateutil import parser

                date_val = parser.parse(str(row["purchase_date"])).date()
                financial_data["purchase_date"] = date_val
            except (ValueError, TypeError):
                errors.append("Invalid purchase date format (use YYYY-MM-DD)")

        # Useful life years
        if "useful_life_years" in row and row["useful_life_years"]:
            try:
                years = int(float(str(row["useful_life_years"])))
                if years <= 0:
                    errors.append("Useful life must be positive")
                else:
                    financial_data["useful_life_years"] = years
            except (ValueError, TypeError):
                errors.append("Useful life years must be a number")

        # Salvage value
        if "salvage_value" in row and row["salvage_value"]:
            try:
                salvage = Decimal(str(row["salvage_value"]).replace(",", ""))
                if salvage < 0:
                    errors.append("Salvage value cannot be negative")
                else:
                    financial_data["salvage_value"] = salvage
            except (InvalidOperation, ValueError):
                errors.append("Invalid salvage value format")

        # Depreciation method
        if "depreciation_method" in row and row["depreciation_method"]:
            method = self._normalize_depreciation_method(row["depreciation_method"])
            if method:
                financial_data["depreciation_method"] = method
            else:
                errors.append(f"Invalid depreciation method: {row['depreciation_method']}")

        # Additional financial fields
        for field in ["vendor", "purchase_order_number", "invoice_number"]:
            if field in row and row[field]:
                financial_data[field] = str(row[field]).strip()

        return {"data": financial_data if financial_data else None, "errors": errors}

    def _normalize_status(self, status: str) -> Optional[str]:
        """Normalize status value."""
        if not status:
            return None

        status_lower = status.lower().strip()

        for valid_status, variations in self.VALID_STATUS_VALUES.items():
            if status_lower in variations:
                return valid_status

        return None

    def _normalize_depreciation_method(self, method: str) -> Optional[str]:
        """Normalize depreciation method value."""
        if not method:
            return None

        method_lower = method.lower().strip()

        for valid_method, variations in self.VALID_DEPRECIATION_METHODS.items():
            if method_lower in variations:
                return valid_method

        return None

    def _create_assets_from_data(self, mapped_data: List[Dict[str, Any]], organization, user: User) -> Dict[str, Any]:
        """Create assets from validated data."""
        created_count = 0
        updated_count = 0
        failed_count = 0
        errors = []

        with transaction.atomic():
            for item in mapped_data:
                if not item["valid"]:
                    failed_count += 1
                    continue

                try:
                    row_data = item["data"]
                    row_number = item.get("row_number", "unknown")

                    # Check for existing asset by serial number
                    existing_asset = None
                    if row_data.get("serial_number"):
                        existing_asset = Asset.objects.filter(
                            organization=organization,
                            serial_number=row_data["serial_number"],
                        ).first()

                    # Prepare asset data
                    asset_data = {
                        "organization": organization,
                        "created_by": user,
                        "name": row_data["name"],
                        "description": row_data.get("description", ""),
                        "category": row_data.get("category", ""),
                        "status": row_data.get("status", Asset.STATUS_ACTIVE),
                        "asset_type": row_data.get("asset_type", ""),
                        "manufacturer": row_data.get("manufacturer", ""),
                        "model_number": row_data.get("model_number", ""),
                        "serial_number": row_data.get("serial_number", ""),
                        "location": row_data.get("location"),
                        "metadata": row_data.get("metadata", {}),
                    }

                    # Create or update asset
                    if existing_asset:
                        # Update existing asset
                        for field, value in asset_data.items():
                            if field not in [
                                "organization",
                                "created_by",
                            ]:  # Don't overwrite these
                                setattr(existing_asset, field, value)
                        existing_asset.save()
                        asset = existing_asset
                        updated_count += 1
                        logger.info(f"Updated asset: {asset.name} (Row {row_number})")
                    else:
                        # Create new asset
                        asset = Asset.objects.create(**asset_data)
                        created_count += 1
                        logger.info(f"Created asset: {asset.name} (Row {row_number})")

                    # Create financial record if financial data provided
                    financial_data = row_data.get("financial")
                    if financial_data and not hasattr(asset, "financial_info"):
                        try:
                            # Ensure required fields for financial record
                            if financial_data.get("acquisition_cost") and financial_data.get("purchase_date"):
                                AssetFinancial.objects.create(
                                    asset=asset,
                                    organization=organization,
                                    created_by=user,
                                    acquisition_cost=financial_data["acquisition_cost"],
                                    purchase_date=financial_data["purchase_date"],
                                    useful_life_years=financial_data.get("useful_life_years", 5),
                                    salvage_value=financial_data.get("salvage_value", Decimal("0.00")),
                                    depreciation_method=financial_data.get("depreciation_method", "straight_line"),
                                    vendor=financial_data.get("vendor", ""),
                                    purchase_order_number=financial_data.get("purchase_order_number", ""),
                                    invoice_number=financial_data.get("invoice_number", ""),
                                )
                                logger.info(f"Created financial record for asset: {asset.name}")
                        except Exception as e:
                            # Log financial record creation error but don't fail asset creation
                            logger.warning(f"Failed to create financial record for {asset.name}: {str(e)}")

                except Exception as e:
                    failed_count += 1
                    row_number = item.get("row_number", "unknown")
                    error_msg = f"Row {row_number}: Failed to create asset - {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)

        return {
            "created_count": created_count,
            "updated_count": updated_count,
            "failed_count": failed_count,
            "errors": errors,
        }

    def _prepare_export_data(
        self, queryset, include_financial: bool = False, include_location: bool = True
    ) -> List[Dict[str, Any]]:
        """Prepare asset data for export."""
        export_data = []

        # Optimize queryset
        queryset = queryset.select_related("organization", "created_by")
        if include_financial:
            queryset = queryset.prefetch_related("financial_info")

        for asset in queryset:
            # Basic asset data
            row = {
                "id": str(asset.id),
                "name": asset.name,
                "description": asset.description,
                "category": asset.category,
                "status": asset.get_status_display(),
                "asset_type": asset.asset_type,
                "manufacturer": asset.manufacturer,
                "model_number": asset.model_number,
                "serial_number": asset.serial_number,
                "is_active": asset.is_active,
                "created_at": asset.created_at,
                "updated_at": asset.updated_at,
                "organization": asset.organization.name if asset.organization else "",
                "created_by": (asset.created_by.get_full_name() if asset.created_by else ""),
            }

            # Location data
            if include_location and asset.location:
                row["latitude"] = asset.location.y
                row["longitude"] = asset.location.x
                row["location_wkt"] = asset.location.wkt
            else:
                row["latitude"] = None
                row["longitude"] = None
                row["location_wkt"] = None

            # Financial data
            if include_financial and hasattr(asset, "financial_info"):
                financial = asset.financial_info
                row.update(
                    {
                        "acquisition_cost": (float(financial.acquisition_cost) if financial.acquisition_cost else None),
                        "purchase_date": financial.purchase_date,
                        "current_book_value": (
                            float(financial.current_book_value) if financial.current_book_value else None
                        ),
                        "accumulated_depreciation": (
                            float(financial.accumulated_depreciation) if financial.accumulated_depreciation else None
                        ),
                        "useful_life_years": financial.useful_life_years,
                        "salvage_value": (float(financial.salvage_value) if financial.salvage_value else None),
                        "depreciation_method": financial.get_depreciation_method_display(),
                        "is_fully_depreciated": financial.is_fully_depreciated,
                        "vendor": financial.vendor,
                        "purchase_order_number": financial.purchase_order_number,
                        "invoice_number": financial.invoice_number,
                    }
                )

            # Metadata (flattened)
            if asset.metadata:
                for key, value in asset.metadata.items():
                    row[f"metadata_{key}"] = value

            # Tags
            if asset.tags:
                row["tags"] = ", ".join(asset.tags) if isinstance(asset.tags, list) else str(asset.tags)

            export_data.append(row)

        return export_data
