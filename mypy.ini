[mypy]
python_version = 3.12
strict = True
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
show_error_codes = True
show_column_numbers = True
pretty = True
color_output = True
error_summary = True
exclude = (?x)(
    ^resources/
    | migrations/
    | venv/
    | venv_wsl/
    | __pycache__/
    | \.git/
    | node_modules/
    | staticfiles/
    | media/
    | tests/fixtures/
)

# Django-specific settings
plugins = mypy_django_plugin.main

[mypy.plugins.django-stubs]
django_settings_module = config.settings

# Ignore missing imports for third-party packages only
[mypy-celery.*]
ignore_missing_imports = True

[mypy-requests.*]
ignore_missing_imports = True

[mypy-rest_framework.*]
ignore_missing_imports = True

[mypy-django_extensions.*]
ignore_missing_imports = True

[mypy-django_filters.*]
ignore_missing_imports = True

[mypy-crispy_forms.*]
ignore_missing_imports = True

[mypy-channels.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-psycopg2.*]
ignore_missing_imports = True

[mypy-PIL.*]
ignore_missing_imports = True

[mypy-reportlab.*]
ignore_missing_imports = True

# PostGIS and spatial libraries
[mypy-osgeo.*]
ignore_missing_imports = True

[mypy-shapely.*]
ignore_missing_imports = True

[mypy-geojson.*]
ignore_missing_imports = True
