#!/usr/bin/env python
"""
Simplified test script to verify the auth/settings/ endpoint works.
"""

import os
import sys

import django

# Setup Django environment
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.dev_settings")
django.setup()

from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.urls import reverse

from apps.authentication.models import Organization, Role, UserRole
from apps.authentication.views.security_settings_views import OrganizationSecuritySettingsView

User = get_user_model()


def test_security_settings_view_directly():
    """Test the security settings view directly without middleware complications."""

    print("🔧 Testing security settings view directly...")

    # Create test data
    org, created = Organization.objects.get_or_create(
        domain="directtest.com",
        defaults={"name": "Direct Test Organization", "slug": "direct-test-org", "is_active": True},
    )

    admin_role, created = Role.objects.get_or_create(
        name="Administrator", slug="administrator", organization=org, defaults={"level": 10}
    )

    user, created = User.objects.get_or_create(
        email="<EMAIL>", defaults={"is_active": True, "is_verified": True, "primary_organization": org}
    )

    if created:
        user.set_password("testpass123")
        user.save()

    UserRole.objects.get_or_create(user=user, role=admin_role, organization=org, defaults={"is_primary": True})

    # Create a request factory
    factory = RequestFactory()

    # Test GET request
    request = factory.get("/auth/auth/settings/")
    request.user = user

    # Initialize the view
    view = OrganizationSecuritySettingsView()
    view.setup(request)

    try:
        response = view.get(request)

        if response.status_code == 200:
            print("✅ View returns 200 OK")

            # Check if context contains expected data
            context = view.get_context_data()

            expected_keys = [
                "password_policy_form",
                "session_security_form",
                "mfa_requirements_form",
                "login_security_form",
                "organization",
            ]

            missing_keys = []
            for key in expected_keys:
                if key not in context:
                    missing_keys.append(key)

            if not missing_keys:
                print("✅ All expected context keys found")
            else:
                print(f"⚠️  Missing context keys: {missing_keys}")

            # Test that forms are initialized with organization
            if context.get("organization") == org:
                print("✅ Organization correctly set in context")
            else:
                print("❌ Organization not correctly set")

            # Test POST request with form data
            print("🔧 Testing POST request...")

            post_request = factory.post(
                "/auth/auth/settings/",
                data={
                    "save_password_policy": "Save",
                    "min_length": "10",
                    "require_uppercase": "on",
                    "require_lowercase": "on",
                    "require_numbers": "on",
                    "require_special_chars": "on",
                    "password_history_count": "3",
                    "password_expiry_days": "60",
                },
            )
            post_request.user = user

            view_post = OrganizationSecuritySettingsView()
            view_post.setup(post_request)

            post_response = view_post.post(post_request)

            print(f"📊 POST response status: {post_response.status_code}")

            if post_response.status_code == 200:
                print("✅ POST request successful")

                # Check if settings were saved
                org.refresh_from_db()
                password_policy = org.get_setting("password_policy", {})

                if password_policy.get("min_length") == 10:
                    print("✅ Settings saved correctly")
                    return True
                else:
                    print(f"❌ Settings not saved correctly: {password_policy}")
                    return False
            else:
                print(f"❌ POST request failed with status {post_response.status_code}")
                return False

        else:
            print(f"❌ View returned status {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ View failed with exception: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_permission_denied():
    """Test that non-admin users are denied access."""

    print("\n🔧 Testing permission denied for non-admin...")

    org = Organization.objects.get(domain="directtest.com")

    user_role, created = Role.objects.get_or_create(
        name="Regular User", slug="regular-user", organization=org, defaults={"level": 40}
    )

    regular_user, created = User.objects.get_or_create(
        email="<EMAIL>", defaults={"is_active": True, "is_verified": True, "primary_organization": org}
    )

    UserRole.objects.get_or_create(user=regular_user, role=user_role, organization=org, defaults={"is_primary": True})

    factory = RequestFactory()
    request = factory.get("/auth/auth/settings/")
    request.user = regular_user

    view = OrganizationSecuritySettingsView()

    try:
        response = view.dispatch(request)

        if response.status_code == 403:
            print("✅ Non-admin correctly denied access")
            return True
        else:
            print(f"❌ Expected 403, got {response.status_code}")
            return False

    except Exception as e:
        # PermissionDenied exceptions are expected
        if "organization administrators" in str(e):
            print("✅ Permission denied exception correctly raised")
            return True
        else:
            print(f"❌ Unexpected exception: {e}")
            return False


def test_url_configuration():
    """Test that URL is properly configured."""

    print("\n🔧 Testing URL configuration...")

    try:
        url = reverse("authentication:organization_security_settings")
        expected_url = "/auth/auth/settings/"

        if url == expected_url:
            print(f"✅ URL correctly configured: {url}")
            return True
        else:
            print(f"❌ URL mismatch. Expected: {expected_url}, Got: {url}")
            return False

    except Exception as e:
        print(f"❌ URL reverse failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Testing security settings endpoint (direct approach)...\n")

    try:
        # Test URL configuration
        url_test_passed = test_url_configuration()

        # Test view functionality
        view_test_passed = test_security_settings_view_directly()

        # Test permissions
        permission_test_passed = test_permission_denied()

        print("\n📊 Test Results:")
        print(f"   URL configuration: {'✅ PASSED' if url_test_passed else '❌ FAILED'}")
        print(f"   View functionality: {'✅ PASSED' if view_test_passed else '❌ FAILED'}")
        print(f"   Permission control: {'✅ PASSED' if permission_test_passed else '❌ FAILED'}")

        all_tests_passed = url_test_passed and view_test_passed and permission_test_passed

        if all_tests_passed:
            print("\n🎉 All tests passed! The auth/settings/ endpoint is working correctly.")
            print("\n📋 Summary:")
            print("   ✅ URL /auth/auth/settings/ is properly configured")
            print("   ✅ View returns 200 OK for admin users")
            print("   ✅ Forms are properly initialized")
            print("   ✅ Settings can be saved via POST")
            print("   ✅ Non-admin users are correctly denied access")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed. Check the implementation.")
            sys.exit(1)

    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
