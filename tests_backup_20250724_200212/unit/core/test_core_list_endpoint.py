#!/usr/bin/env python
"""
Simple test script to verify the core/list/ endpoint works correctly.
"""

import os
import sys

import django
from django.contrib.auth import get_user_model
from django.test import Client

# Add the project directory to Python path
sys.path.insert(0, "/home/<USER>/coding/clear_htmx")

# Set Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.dev_settings")

# Setup Django
django.setup()


def test_core_list_endpoint():
    """Test the core/list/ endpoint functionality."""
    print("Testing core/list/ endpoint...")

    client = Client()
    User = get_user_model()

    # Test 1: Unauthenticated access (should redirect to login)
    print("1. Testing unauthenticated access...")
    response = client.get("/core/list/")
    print(f"   Status: {response.status_code}")
    print(f"   Redirected: {response.status_code in [302, 403]}")

    # Test 2: Create a test superuser and authenticate
    print("2. Testing with admin user...")
    try:
        # Create or get superuser
        user, created = User.objects.get_or_create(
            username="testadmin",
            defaults={
                "email": "<EMAIL>",
                "is_superuser": True,
                "is_staff": True,
                "is_active": True,
            },
        )

        if created:
            user.set_password("testpass123")
            user.save()
            print("   Created test admin user")
        else:
            print("   Using existing test admin user")

        # Login as superuser
        client.force_login(user)

        # Test authenticated access
        response = client.get("/core/list/")
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ SUCCESS: Endpoint returns 200 OK")
            print(f"   Response content length: {len(response.content)} bytes")

            # Check if response contains expected content
            content = response.content.decode("utf-8")
            if "Core System Resources" in content:
                print("   ✅ Page title found in response")
            else:
                print("   ⚠️  Page title not found in response")

            if "Management Commands" in content or "Core Services" in content:
                print("   ✅ Resource categories found in response")
            else:
                print("   ⚠️  No resource categories found in response")

        elif response.status_code == 500:
            print("   ❌ INTERNAL SERVER ERROR")
            print("   Check Django logs for details")
        else:
            print(f"   ❌ UNEXPECTED STATUS: {response.status_code}")

    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        import traceback

        traceback.print_exc()

    # Test 3: HTMX request
    print("3. Testing HTMX request...")
    try:
        response = client.get("/core/list/", HTTP_HX_REQUEST="true")
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ HTMX request successful")
        else:
            print(f"   ❌ HTMX request failed: {response.status_code}")

    except Exception as e:
        print(f"   ❌ HTMX ERROR: {e}")

    # Test 4: Search endpoint
    print("4. Testing search endpoint...")
    try:
        response = client.get("/core/list/search/?search=command")
        print(f"   Status: {response.status_code}")

        if response.status_code == 200:
            print("   ✅ Search endpoint successful")
        else:
            print(f"   ❌ Search endpoint failed: {response.status_code}")

    except Exception as e:
        print(f"   ❌ SEARCH ERROR: {e}")

    # Cleanup
    try:
        if "user" in locals() and user.username == "testadmin":
            user.delete()
            print("   🧹 Cleaned up test user")
    except:
        pass

    print("\n✅ Test completed!")


if __name__ == "__main__":
    test_core_list_endpoint()
