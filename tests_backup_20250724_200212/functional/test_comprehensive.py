#!/usr/bin/env python
"""
Comprehensive functional testing of CLEAR HTMX application
"""

import json
import os

import django
from django.apps import apps
from django.contrib.auth import get_user_model
from django.test.client import Client

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.development")
django.setup()


def test_app_imports():
    """Test that all app models can be imported"""
    print("=== Testing App Model Imports ===")
    results = {}

    for app_config in apps.get_app_configs():
        app_name = app_config.name
        if app_name.startswith("apps."):
            try:
                models = list(app_config.get_models())
                results[app_name] = {"status": "pass", "models": len(models)}
                print(f"✅ {app_name}: {len(models)} models imported")
            except Exception as e:
                results[app_name] = {"status": "fail", "error": str(e)}
                print(f"❌ {app_name}: {e!s}")

    return results


def test_url_patterns():
    """Test URL pattern resolution"""
    print("\n=== Testing URL Pattern Resolution ===")
    results = {}

    # Test key URLs
    test_urls = [
        "/",
        "/admin/",
        "/analytics/",
        "/projects/",
        "/documents/",
        "/infrastructure/",
        "/knowledge/",
        "/messaging/",
        "/financial/",
        "/users/",
        "/auth/login/",
    ]

    for url in test_urls:
        try:
            from django.urls import resolve

            resolved = resolve(url)
            results[url] = {"status": "pass", "view": str(resolved.func)}
            print(f"✅ {url}: {resolved.func}")
        except Exception as e:
            results[url] = {"status": "fail", "error": str(e)}
            print(f"❌ {url}: {e!s}")

    return results


def test_page_loads():
    """Test that key pages load without 500 errors"""
    print("\n=== Testing Page Load Responses ===")
    results = {}

    client = Client()

    # Test pages that should work without authentication
    test_pages = [
        ("/", "Home Page"),
        ("/auth/login/", "Login Page"),
        ("/admin/", "Admin Login"),
    ]

    for url, name in test_pages:
        try:
            response = client.get(url)
            if response.status_code < 500:
                results[name] = {"status": "pass", "code": response.status_code}
                print(f"✅ {name} ({url}): {response.status_code}")
            else:
                results[name] = {"status": "fail", "code": response.status_code}
                print(f"❌ {name} ({url}): {response.status_code}")
        except Exception as e:
            results[name] = {"status": "fail", "error": str(e)}
            print(f"❌ {name} ({url}): {e!s}")

    return results


def test_model_operations():
    """Test basic model operations"""
    print("\n=== Testing Model Operations ===")
    results = {}

    # Test User model
    try:
        User = get_user_model()
        user_count = User.objects.count()
        results["User Model"] = {"status": "pass", "count": user_count}
        print(f"✅ User Model: {user_count} users in database")
    except Exception as e:
        results["User Model"] = {"status": "fail", "error": str(e)}
        print(f"❌ User Model: {e!s}")

    # Test other key models
    key_models = [
        ("apps.projects.models", "Project"),
        ("apps.documents.models", "Document"),
        ("apps.infrastructure.models", "InfrastructureProject"),
        ("apps.analytics.models", "AnalyticsModel"),
    ]

    for module_path, model_name in key_models:
        try:
            module = __import__(module_path, fromlist=[model_name])
            model = getattr(module, model_name)
            count = model.objects.count()
            results[f"{model_name} Model"] = {"status": "pass", "count": count}
            print(f"✅ {model_name} Model: {count} records")
        except Exception as e:
            results[f"{model_name} Model"] = {"status": "fail", "error": str(e)}
            print(f"❌ {model_name} Model: {e!s}")

    return results


def test_template_rendering():
    """Test that key templates can be rendered"""
    print("\n=== Testing Template Rendering ===")
    results = {}

    from django.template.loader import get_template

    key_templates = [
        "base.html",
        "home.html",
        "authentication/login_htmx_compliant.html",
        "projects/projects.html",
        "analytics/dashboard.html",
    ]

    for template_name in key_templates:
        try:
            template = get_template(template_name)
            # Basic render test
            rendered = template.render({})
            results[template_name] = {"status": "pass", "length": len(rendered)}
            print(f"✅ {template_name}: {len(rendered)} characters")
        except Exception as e:
            results[template_name] = {"status": "fail", "error": str(e)}
            print(f"❌ {template_name}: {e!s}")

    return results


def test_forms():
    """Test form imports and instantiation"""
    print("\n=== Testing Form Imports ===")
    results = {}

    form_tests = [
        ("apps.authentication.forms.password_forms", "PasswordResetForm"),
        ("apps.projects.forms", "ProjectForm"),
        ("apps.documents.forms", "DocumentForm"),
    ]

    for module_path, form_name in form_tests:
        try:
            module = __import__(module_path, fromlist=[form_name])
            form_class = getattr(module, form_name)
            form = form_class()
            results[f"{form_name}"] = {"status": "pass", "fields": len(form.fields)}
            print(f"✅ {form_name}: {len(form.fields)} fields")
        except Exception as e:
            results[f"{form_name}"] = {"status": "fail", "error": str(e)}
            print(f"❌ {form_name}: {e!s}")

    return results


def test_static_files():
    """Test static file access"""
    print("\n=== Testing Static File Access ===")
    results = {}

    from django.contrib.staticfiles.finders import find

    key_static_files = [
        "css/bootstrap.min.css",
        "js/htmx.min.js",
        "css/custom.css",
        "js/custom.js",
    ]

    for static_file in key_static_files:
        try:
            found = find(static_file)
            if found:
                results[static_file] = {"status": "pass", "path": found}
                print(f"✅ {static_file}: Found at {found}")
            else:
                results[static_file] = {"status": "fail", "error": "File not found"}
                print(f"❌ {static_file}: Not found")
        except Exception as e:
            results[static_file] = {"status": "fail", "error": str(e)}
            print(f"❌ {static_file}: {e!s}")

    return results


def test_database_connection():
    """Test database connection and basic operations"""
    print("\n=== Testing Database Connection ===")
    results = {}

    try:
        from django.db import connection

        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            results["Database Connection"] = {"status": "pass", "result": result[0]}
            print("✅ Database Connection: Working")
    except Exception as e:
        results["Database Connection"] = {"status": "fail", "error": str(e)}
        print(f"❌ Database Connection: {e!s}")

    return results


def main():
    """Run all tests and generate report"""
    print("🔍 CLEAR HTMX Comprehensive Functional Testing")
    print("=" * 50)

    all_results = {}

    # Run all tests
    all_results["app_imports"] = test_app_imports()
    all_results["url_patterns"] = test_url_patterns()
    all_results["page_loads"] = test_page_loads()
    all_results["model_operations"] = test_model_operations()
    all_results["template_rendering"] = test_template_rendering()
    all_results["forms"] = test_forms()
    all_results["static_files"] = test_static_files()
    all_results["database_connection"] = test_database_connection()

    # Generate summary
    print("\n" + "=" * 50)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 50)

    total_tests = 0
    passed_tests = 0
    failed_tests = 0

    for test_category, results in all_results.items():
        print(f"\n{test_category.upper().replace('_', ' ')}:")
        for test_name, result in results.items():
            total_tests += 1
            if result["status"] == "pass":
                passed_tests += 1
                print(f"  ✅ {test_name}")
            else:
                failed_tests += 1
                print(f"  ❌ {test_name}: {result.get('error', 'Unknown error')}")

    print("\n📈 OVERALL RESULTS:")
    print(f"  Total Tests: {total_tests}")
    print(f"  Passed: {passed_tests}")
    print(f"  Failed: {failed_tests}")
    print(f"  Success Rate: {(passed_tests / total_tests) * 100:.1f}%")

    # Save detailed results
    with open("/home/<USER>/Coding/CLEAR-0.5/clear_htmx/comprehensive_test_results.json", "w") as f:
        json.dump(all_results, f, indent=2)

    print("\n💾 Detailed results saved to: comprehensive_test_results.json")


if __name__ == "__main__":
    main()
