const { test, expect } = require('../utils/base-test');
const { DashboardPage } = require('../utils/page-objects');
const { TestUtils } = require('../utils/base-test');

test.describe('Dashboard Page', () => {
  let dashboardPage;
  let utils;

  test.beforeEach(async ({ authenticatedPage }) => {
    dashboardPage = new DashboardPage(authenticatedPage);
    utils = new TestUtils(authenticatedPage);
    await dashboardPage.goto('/dashboard/');
  });

  test('should display dashboard correctly', async ({ authenticatedPage }) => {
    // Check main dashboard elements
    await expect(authenticatedPage.locator('h1')).toContainText('Dashboard');
    await expect(dashboardPage.statsCards).toHaveCount(4); // Adjust based on actual count
    await expect(dashboardPage.projectsList).toBeVisible();
    await expect(dashboardPage.teamChat).toBeVisible();
    await expect(dashboardPage.timerWidget).toBeVisible();
  });

  test('should display user-specific information', async ({ authenticatedPage }) => {
    // Check user menu
    await expect(authenticatedPage.locator('.user-menu')).toContainText('Test User');

    // Check personalized content
    await expect(authenticatedPage.locator('.welcome-message')).toContainText('Welcome back, Test');

    // Check user avatar
    await expect(authenticatedPage.locator('.user-avatar')).toBeVisible();
  });

  test('should load stats cards with real-time data', async ({ authenticatedPage }) => {
    // Wait for stats to load
    await utils.waitForHTMX();

    // Check stats cards content
    const statsCards = authenticatedPage.locator('.stats-card');
    await expect(statsCards.nth(0)).toContainText('Active Projects');
    await expect(statsCards.nth(1)).toContainText('Total Tasks');
    await expect(statsCards.nth(2)).toContainText('Messages');
    await expect(statsCards.nth(3)).toContainText('Team Members');

    // Verify numeric values are displayed
    await expect(statsCards.nth(0).locator('.stat-value')).toMatch(/\d+/);
    await expect(statsCards.nth(1).locator('.stat-value')).toMatch(/\d+/);
  });

  test('should refresh stats when clicked', async ({ authenticatedPage }) => {
    // Get initial values
    const initialProjects = await authenticatedPage
      .locator('.stats-card:first-child .stat-value')
      .textContent();

    // Click refresh button
    await authenticatedPage.click('[data-refresh="stats"]');
    await utils.waitForHTMX();

    // Stats should reload (even if values are the same)
    await expect(authenticatedPage.locator('.stats-card:first-child .stat-value')).toBeVisible();
  });

  test('should display recent projects list', async ({ authenticatedPage }) => {
    const projectsList = dashboardPage.projectsList;

    // Should show projects
    await expect(projectsList).toBeVisible();
    await expect(projectsList.locator('.project-item')).toHaveCountGreaterThan(0);

    // Check project item structure
    const firstProject = projectsList.locator('.project-item').first();
    await expect(firstProject.locator('.project-name')).toBeVisible();
    await expect(firstProject.locator('.project-status')).toBeVisible();
    await expect(firstProject.locator('.project-progress')).toBeVisible();
  });

  test('should navigate to project from dashboard', async ({ authenticatedPage }) => {
    // Click on first project
    await authenticatedPage.click('.project-item:first-child .project-link');

    // Should navigate to project detail
    await expect(authenticatedPage).toHaveURL(/\/projects\/[^\/]+\//);
  });

  test('should display team chat widget', async ({ authenticatedPage }) => {
    const teamChat = dashboardPage.teamChat;

    // Should show team chat
    await expect(teamChat).toBeVisible();
    await expect(teamChat.locator('.chat-header')).toContainText('Team Chat');

    // Should show recent messages
    await expect(teamChat.locator('.message-item')).toHaveCountGreaterThanOrEqual(0);

    // Should have message input
    await expect(teamChat.locator('#team-message-input')).toBeVisible();
    await expect(teamChat.locator('#send-team-message')).toBeVisible();
  });

  test('should send team chat message', async ({ authenticatedPage }) => {
    const messageInput = authenticatedPage.locator('#team-message-input');
    const sendButton = authenticatedPage.locator('#send-team-message');

    // Type and send message
    await messageInput.fill('Test dashboard message');
    await sendButton.click();
    await utils.waitForHTMX();

    // Should see message in chat
    await expect(authenticatedPage.locator('.message-item').last()).toContainText(
      'Test dashboard message'
    );

    // Input should be cleared
    await expect(messageInput).toHaveValue('');
  });

  test('should display timer widget functionality', async ({ authenticatedPage }) => {
    const timerWidget = dashboardPage.timerWidget;

    // Should show timer widget
    await expect(timerWidget).toBeVisible();
    await expect(timerWidget.locator('.timer-display')).toBeVisible();

    // Should have start/stop controls
    await expect(timerWidget.locator('#timer-start-btn, #timer-stop-btn')).toBeVisible();
  });

  test('should start and stop timer', async ({ authenticatedPage }) => {
    // Start timer
    await authenticatedPage.click('#timer-start-btn');
    await utils.waitForHTMX();

    // Should show running timer
    await expect(authenticatedPage.locator('.timer-running')).toBeVisible();
    await expect(authenticatedPage.locator('#timer-stop-btn')).toBeVisible();

    // Stop timer
    await authenticatedPage.click('#timer-stop-btn');
    await utils.waitForHTMX();

    // Should show stopped timer
    await expect(authenticatedPage.locator('.timer-stopped')).toBeVisible();
    await expect(authenticatedPage.locator('#timer-start-btn')).toBeVisible();
  });

  test('should display notifications dropdown', async ({ authenticatedPage }) => {
    // Click notifications button
    await authenticatedPage.click('#notifications-toggle');
    await utils.waitForHTMX();

    // Should show notifications dropdown
    await expect(authenticatedPage.locator('#notifications-dropdown')).toBeVisible();

    // Should show notification items or empty state
    const notificationItems = authenticatedPage.locator('.notification-item');
    const emptyState = authenticatedPage.locator('.notifications-empty');

    // Either notifications or empty state should be visible
    await expect(notificationItems.first().or(emptyState)).toBeVisible();
  });

  test('should mark notifications as read', async ({ authenticatedPage }) => {
    // Open notifications
    await authenticatedPage.click('#notifications-toggle');
    await utils.waitForHTMX();

    // Check if there are unread notifications
    const unreadNotifications = authenticatedPage.locator('.notification-item.unread');
    const unreadCount = await unreadNotifications.count();

    if (unreadCount > 0) {
      // Click first unread notification
      await unreadNotifications.first().click();
      await utils.waitForHTMX();

      // Should be marked as read
      await expect(unreadNotifications.first()).not.toHaveClass(/unread/);
    }
  });

  test('should display recent activity feed', async ({ authenticatedPage }) => {
    const activityFeed = authenticatedPage.locator('#recent-activity');

    // Should show activity feed
    await expect(activityFeed).toBeVisible();
    await expect(activityFeed.locator('.activity-header')).toContainText('Recent Activity');

    // Should show activity items
    const activityItems = activityFeed.locator('.activity-item');
    await expect(activityItems).toHaveCountGreaterThanOrEqual(0);

    // Check activity item structure
    if ((await activityItems.count()) > 0) {
      const firstActivity = activityItems.first();
      await expect(firstActivity.locator('.activity-time')).toBeVisible();
      await expect(firstActivity.locator('.activity-description')).toBeVisible();
    }
  });

  test('should be responsive on different screen sizes', async ({ authenticatedPage }) => {
    await utils.testResponsive();

    // Test mobile layout
    await authenticatedPage.setViewportSize({ width: 375, height: 667 });

    // Should show mobile-optimized layout
    await expect(authenticatedPage.locator('.mobile-dashboard')).toBeVisible();
    await expect(dashboardPage.statsCards).toBeVisible();

    // Cards should stack vertically on mobile
    const card1 = dashboardPage.statsCards.nth(0);
    const card2 = dashboardPage.statsCards.nth(1);

    const box1 = await card1.boundingBox();
    const box2 = await card2.boundingBox();

    // Second card should be below first card on mobile
    expect(box2.y).toBeGreaterThan(box1.y + box1.height - 10);
  });

  test('should handle dashboard shortcuts', async ({ authenticatedPage }) => {
    // Test keyboard shortcuts
    await authenticatedPage.keyboard.press('Control+n'); // New project shortcut
    await utils.waitForHTMX();

    // Should open new project modal or navigate to create page
    await expect(authenticatedPage.locator('.modal, .create-project-form')).toBeVisible();
  });

  test('should refresh dashboard data automatically', async ({ authenticatedPage }) => {
    // Wait for auto-refresh (if implemented)
    await authenticatedPage.waitForTimeout(5000);

    // Dashboard should still be functional
    await expect(dashboardPage.statsCards).toBeVisible();
    await expect(dashboardPage.projectsList).toBeVisible();
  });

  test('should handle dashboard errors gracefully', async ({ authenticatedPage }) => {
    // Mock network failure for stats endpoint
    await authenticatedPage.route('**/htmx/dashboard-stats/', route => {
      route.abort('failed');
    });

    // Refresh dashboard
    await authenticatedPage.reload();

    // Should show error state or fallback content
    await expect(authenticatedPage.locator('.error-message, .stats-error')).toBeVisible();
  });

  test('should provide accessibility features', async ({ authenticatedPage }) => {
    await utils.testAccessibility();

    // Test keyboard navigation
    await utils.testKeyboardNavigation('.stats-card:first-child', [
      '.stats-card:nth-child(2)',
      '.stats-card:nth-child(3)',
      '.stats-card:nth-child(4)',
    ]);

    // Test ARIA labels
    await expect(authenticatedPage.locator('[aria-label]')).toHaveCountGreaterThan(0);

    // Test screen reader support
    await expect(authenticatedPage.locator('[role="main"]')).toBeVisible();
  });
});
