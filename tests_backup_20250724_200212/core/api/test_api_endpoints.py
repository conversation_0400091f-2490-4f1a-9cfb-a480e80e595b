"""
from django.contrib.auth import login
from django.contrib.auth import logout
from rest_framework import permissions
from typing import ClassVar
API Endpoint Tests for CLEAR

Tests API endpoints using Django's test client.
"""

from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse

from apps.authentication.models import Organization
from apps.documents.models import Document
from apps.projects.models import Project

User = get_user_model()


class APITestCase(TestCase):
    """Base test case for API testing."""

    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        # Create test organization
        self.org = Organization.objects.create(name="Test Organization")

        # Create test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            organization=self.org,
        )

        # Login the user
        self.client.login(username="testuser", password="testpass123")

    def tearDown(self):
        """Clean up after tests."""
        self.client.logout()
        super().tearDown()


class AuthenticationTests(APITestCase):
    """Test authentication endpoints."""

    def test_login_view(self):
        """Test login functionality."""
        # Logout first
        self.client.logout()

        # Test GET request to login page
        response = self.client.get(reverse("CLEAR:login"))
        self.assertEqual(response.status_code, 200)

        # Test POST with valid credentials
        response = self.client.post(reverse("CLEAR:login"), {"username": "testuser", "password": "testpass123"})
        self.assertEqual(response.status_code, 302)  # Redirect after login

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials."""
        self.client.logout()

        response = self.client.post(
            reverse("CLEAR:login"),
            {"username": "testuser", "password": "wrongpassword"},
        )
        self.assertEqual(response.status_code, 200)  # Stay on login page
        self.assertContains(response, "error")

    def test_logout_view(self):
        """Test logout functionality."""
        response = self.client.post(reverse("CLEAR:logout"))
        self.assertEqual(response.status_code, 302)  # Redirect after logout

        # Verify user is logged out
        response = self.client.get(reverse("CLEAR:dashboard"))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_user_profile(self):
        """Test user profile access."""
        response = self.client.get(reverse("CLEAR:profile"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.user.username)


class ProjectTests(APITestCase):
    """Test project-related functionality."""

    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(
            name="Test Project",
            description="A test project",
            organization=self.org,
            created_by=self.user,
            status="active",
        )

    def test_project_list(self):
        """Test project list view."""
        response = self.client.get(reverse("CLEAR:projects"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Project")

    def test_project_detail(self):
        """Test project detail view."""
        response = self.client.get(reverse("CLEAR:project_detail", kwargs={"project_id": self.project.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.project.name)
        self.assertContains(response, self.project.description)

    def test_project_create(self):
        """Test project creation."""
        response = self.client.post(
            reverse("CLEAR:project_create"),
            {
                "name": "New Project",
                "description": "A new test project",
                "status": "active",
                "priority": "medium",
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
            },
        )

        # Should redirect after successful creation
        self.assertEqual(response.status_code, 302)

        # Verify project was created
        self.assertTrue(Project.objects.filter(name="New Project").exists())

    def test_project_update(self):
        """Test project update."""
        response = self.client.post(
            reverse("CLEAR:project_update", kwargs={"project_id": self.project.pk}),
            {
                "name": "Updated Project",
                "description": "Updated description",
                "status": "completed",
            },
        )

        # Should redirect after successful update
        self.assertEqual(response.status_code, 302)

        # Verify project was updated
        self.project.refresh_from_db()
        self.assertEqual(self.project.name, "Updated Project")
        self.assertEqual(self.project.status, "completed")

    def test_project_permissions(self):
        """Test project access permissions."""
        # Create another user in different org
        other_org = Organization.objects.create(name="Other Organization")
        User.objects.create_user(
            username="otheruser",
            email="<EMAIL>",
            password="testpass123",
            organization=other_org,
        )

        # Login as other user
        self.client.login(username="otheruser", password="testpass123")

        # Try to access project from different org
        response = self.client.get(reverse("CLEAR:project_detail", kwargs={"project_id": self.project.pk}))

        # Should be forbidden or redirect
        self.assertIn(response.status_code, [403, 404, 302])


class DocumentTests(APITestCase):
    """Test document-related functionality."""

    def setUp(self):
        """Set up test fixtures."""
        super().setUp()
        self.project = Project.objects.create(name="Test Project", organization=self.org, created_by=self.user)
        self.document = Document.objects.create(
            title="Test Document",
            content="Test document content",
            uploaded_by=self.user,
            project=self.project,
            file_type="pdf",
            file_size=1024,
        )

    def test_document_list(self):
        """Test document list view."""
        response = self.client.get(reverse("CLEAR:documents"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Document")

    def test_document_upload(self):
        """Test document upload."""
        # Create a test file
        from io import BytesIO

        test_file = BytesIO(b"Test file content")
        test_file.name = "test.pdf"

        response = self.client.post(
            reverse("CLEAR:document_upload"),
            {
                "title": "New Document",
                "project": self.project.pk,
                "file": test_file,
                "is_private": False,
            },
        )

        # Should redirect after successful upload
        self.assertEqual(response.status_code, 302)

        # Verify document was created
        self.assertTrue(Document.objects.filter(title="New Document").exists())

    def test_document_search(self):
        """Test document search functionality."""
        response = self.client.get(reverse("CLEAR:document_search"), {"q": "Test"})

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.document.title)


class HTMXTests(APITestCase):
    """Test HTMX-specific endpoints."""

    def test_htmx_request_headers(self):
        """Test HTMX request handling."""
        response = self.client.get(reverse("CLEAR:dashboard_stats"), HTTP_HX_REQUEST="true")

        self.assertEqual(response.status_code, 200)
        # HTMX responses should be HTML fragments
        self.assertNotContains(response, "<!DOCTYPE html>")

    def test_htmx_trigger_events(self):
        """Test HTMX trigger events in response."""
        response = self.client.post(
            reverse("CLEAR:message_create_htmx"),
            {"content": "Test message"},
            HTTP_HX_REQUEST="true",
        )

        # Check for HTMX response headers
        if response.status_code == 200:
            # May have HX-Trigger header
            self.assertIsNotNone(response.headers.get("HX-Trigger", None))


# Run tests with: python manage.py test tests.core.api
