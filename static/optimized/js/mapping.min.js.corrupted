/*! CLEAR Mapping Bundle*/class MapPerformanceTracker{constructor(mapInstance,mapType='leaflet'){this.map=mapInstance;this.mapType=mapType;this.initializeTracking();}initializeTracking(){const loadStartTime=performance.now();if(this.mapType==='leaflet'){this.trackLeafletOperations();}else{this.trackOpenLayersOperations();}this.onMapReady(()=>{const loadTime=performance.now()-loadStartTime;if(window.trackLoadTime){window.trackLoadTime(loadTime);}});}trackLeafletOperations(){let zoomStartTime;this.map.on('zoomstart',()=>{zoomStartTime=performance.now();});this.map.on('zoomend',()=>{if(zoomStartTime && window.trackMapOperation){window.trackMapOperation('zoom',performance.now()-zoomStartTime);}});let panStartTime;this.map.on('movestart',()=>{panStartTime=performance.now();});this.map.on('moveend',()=>{if(panStartTime && window.trackMapOperation){window.trackMapOperation('pan',performance.now()-panStartTime);}});if(this.map.pm){this.map.on('pm:create',(e)=>{const drawTime=e.workingLayer._pmCreateTime || 0;if(window.trackMapOperation){window.trackMapOperation('draw',drawTime);}});}this.map.on('layeradd',(e)=>{const startTime=e.layer._addStartTime || performance.now();if(window.trackMapOperation){window.trackMapOperation('layerToggle',performance.now()-startTime);}});this.updateFeatureCounts();this.map.on('layeradd layerremove',()=>{this.updateFeatureCounts();});}trackOpenLayersOperations(){const view=this.map.getView();let zoomStartTime;view.on('change:resolution',()=>{if(!zoomStartTime){zoomStartTime=performance.now();this.map.once('postrender',()=>{if(window.trackMapOperation){window.trackMapOperation('zoom',performance.now()-zoomStartTime);}zoomStartTime=null;});}});let panStartTime;view.on('change:center',()=>{if(!panStartTime){panStartTime=performance.now();this.map.once('postrender',()=>{if(window.trackMapOperation){window.trackMapOperation('pan',performance.now()-panStartTime);}panStartTime=null;});}});this.map.on('postrender',()=>{this.updateFeatureCounts();});}updateFeatureCounts(){let totalFeatures=0;let renderedFeatures=0;let queuedFeatures=0;if(this.mapType==='leaflet'){this.map.eachLayer((layer)=>{if(layer instanceof L.Path || layer instanceof L.Marker){totalFeatures++;if(this.map.getBounds().contains(layer.getLatLng ? layer.getLatLng(): layer.getBounds())){renderedFeatures++;}else{queuedFeatures++;}}});}else{this.map.getLayers().forEach((layer)=>{if(layer instanceof ol.layer.Vector){const source=layer.getSource();const features=source.getFeatures();totalFeatures+=features.length;const extent=this.map.getView().calculateExtent();features.forEach((feature)=>{if(ol.extent.intersects(extent,feature.getGeometry().getExtent())){renderedFeatures++;}else{queuedFeatures++;}});}});}if(window.updateFeatureCount){window.updateFeatureCount(totalFeatures,renderedFeatures,queuedFeatures);}}onMapReady(callback){if(this.mapType==='leaflet'){if(this.map._loaded){callback();}else{this.map.on('load',callback);}}else{setTimeout(()=>{this.map.once('postrender',callback);},100);}}measureOperation(operationName,operation){const startTime=performance.now();const result=operation();const duration=performance.now()-startTime;if(window.trackMapOperation){window.trackMapOperation(operationName,duration);}return result;}}function integratePerformanceTracking(map,mapType){const tracker=new MapPerformanceTracker(map,mapType);map.performanceTracker=tracker;map.measureOperation=(name,op)=> tracker.measureOperation(name,op);return tracker;}document.addEventListener('DOMContentLoaded',()=>{if(window.map && window.L && window.map instanceof L.Map){console.log('Integrating performance tracking for Leaflet map');integratePerformanceTracking(window.map,'leaflet');}if(window.map && window.ol && window.map instanceof ol.Map){console.log('Integrating performance tracking for OpenLayers map');integratePerformanceTracking(window.map,'openlayers');}});window.MapPerformanceTracker=MapPerformanceTracker;window.integratePerformanceTracking=integratePerformanceTracking;class TimelineChart{constructor(containerId){this.container=document.getElementById(containerId);this.currentZoom=100;this.isDragging=false;this.draggedTask=null;this.viewMode='gantt';this.dependencies=new Map();this.init();}init(){this.setupEventListeners();this.initializeDragAndDrop();this.drawDependencyLines();this.setupZoomControls();this.setupViewModeToggle();}setupEventListeners(){window.addEventListener('resize',()=>{this.drawDependencyLines();this.adjustTimelineScale();});document.body.addEventListener('htmx:afterSwap',(event)=>{if(event.target.closest('#timeline-chart')){this.refreshTimeline();}});document.addEventListener('mouseover',(event)=>{if(event.target.closest('.task-row')){this.highlightTaskDependencies(event.target.closest('.task-row'));}});document.addEventListener('mouseout',(event)=>{if(event.target.closest('.task-row')){this.clearDependencyHighlights();}});}initializeDragAndDrop(){document.addEventListener('mousedown',(event)=>{const taskBar=event.target.closest('.task-bar');if(taskBar && !event.target.closest('button')){this.startDrag(taskBar,event);}});document.addEventListener('mousemove',(event)=>{if(this.isDragging){this.handleDrag(event);}});document.addEventListener('mouseup',()=>{if(this.isDragging){this.endDrag();}});}startDrag(taskBar,event){this.isDragging=true;this.draggedTask=taskBar;this.dragStartX=event.clientX;this.dragStartPosition=taskBar.offsetLeft;taskBar.style.opacity='0.7';taskBar.style.cursor='grabbing';document.body.style.cursor='grabbing';this.createDragGhost(taskBar,event);event.preventDefault();}handleDrag(event){if(!this.draggedTask)return;const deltaX=event.clientX-this.dragStartX;const newPosition=this.dragStartPosition+deltaX;if(this.dragGhost){this.dragGhost.style.left=`${newPosition}px`;}const newDates=this.calculateNewDatesFromPosition(newPosition);if(newDates){this.showDatePreview(newDates);}}endDrag(){if(!this.draggedTask)return;this.draggedTask.style.opacity='1';this.draggedTask.style.cursor='pointer';document.body.style.cursor='default';if(this.dragGhost){this.dragGhost.remove();this.dragGhost=null;}const finalPosition=this.draggedTask.style.left || this.dragStartPosition;const newDates=this.calculateNewDatesFromPosition(parseInt(finalPosition));if(newDates){this.updateTaskDates(this.draggedTask,newDates);}this.isDragging=false;this.draggedTask=null;this.hideDatePreview();}createDragGhost(taskBar,event){this.dragGhost=taskBar.cloneNode(true);this.dragGhost.style.position='absolute';this.dragGhost.style.zIndex='1000';this.dragGhost.style.opacity='0.8';this.dragGhost.style.pointerEvents='none';this.dragGhost.style.transform='rotate(2deg)';document.body.appendChild(this.dragGhost);}calculateNewDatesFromPosition(position){const timelineContainer=this.container.querySelector('.timeline-content');if(!timelineContainer)return null;const containerWidth=timelineContainer.offsetWidth;const pixelsPerDay=containerWidth/365;const dayOffset=Math.round(position/pixelsPerDay);const projectStartDate=new Date();const newStartDate=new Date(projectStartDate);newStartDate.setDate(newStartDate.getDate()+dayOffset);const taskDuration=7;const newEndDate=new Date(newStartDate);newEndDate.setDate(newEndDate.getDate()+taskDuration);return{startDate: newStartDate.toISOString().split('T')[0],endDate: newEndDate.toISOString().split('T')[0]};}updateTaskDates(taskBar,newDates){const taskId=taskBar.closest('[data-task-id]').dataset.taskId;htmx.ajax('POST','/htmx/timeline/task-move/',{values:{task_id: taskId,new_start: newDates.startDate,new_end: newDates.endDate},target: `[data-task-id="${taskId}"]`,swap: 'outerHTML'}).then(()=>{document.body.dispatchEvent(new CustomEvent('timeline-update'));}).catch((error)=>{console.error('Error updating task dates:',error);this.refreshTimeline();});}showDatePreview(dates){let preview=document.getElementById('date-preview');if(!preview){preview=document.createElement('div');preview.id='date-preview';preview.className='fixed top-4 right-4 bg-black text-white px-3 py-2 rounded shadow-lg text-sm z-50';document.body.appendChild(preview);}preview.innerHTML=` <div>Start: ${new Date(dates.startDate).toLocaleDateString()}</div> <div>End: ${new Date(dates.endDate).toLocaleDateString()}</div> `;preview.style.display='block';}hideDatePreview(){const preview=document.getElementById('date-preview');if(preview){preview.style.display='none';}}drawDependencyLines(){const existingSvg=this.container.querySelector('.dependency-svg');if(existingSvg){existingSvg.remove();}//Create SVG overlay const svg=document.createElementNS('http: svg.classList.add('dependency-svg');svg.style.position='absolute';svg.style.top='0';svg.style.left='0';svg.style.width='100%';svg.style.height='100%';svg.style.pointerEvents='none';svg.style.zIndex='5';//Add arrow marker definition const defs=document.createElementNS('http: const marker=document.createElementNS('http: marker.setAttribute('id','arrowhead');marker.setAttribute('markerWidth','10');marker.setAttribute('markerHeight','7');marker.setAttribute('refX','10');marker.setAttribute('refY','3.5');marker.setAttribute('orient','auto');const polygon=document.createElementNS('http: polygon.setAttribute('points','0 0,10 3.5,0 7');polygon.setAttribute('fill','#6b7280');marker.appendChild(polygon);defs.appendChild(marker);svg.appendChild(defs);this.drawAllDependencyLines(svg);this.container.appendChild(svg);}drawAllDependencyLines(svg){const taskRows=this.container.querySelectorAll('[data-task-id]');taskRows.forEach(taskRow=>{const taskId=taskRow.dataset.taskId;const taskBar=taskRow.querySelector('.task-bar');if(!taskBar)return;const dependencies=this.getTaskDependencies(taskRow);dependencies.forEach(depId=>{const depTaskRow=this.container.querySelector(`[data-task-id="${depId}"]`);const depTaskBar=depTaskRow?.querySelector('.task-bar');if(depTaskBar){this.drawDependencyLine(svg,depTaskBar,taskBar);}});});}getTaskDependencies(taskRow){const dependencyElements=taskRow.querySelectorAll('.dependencies-list [data-lucide="git-merge"]');const dependencies=[];//This would need to be adapted based on how dependencies are stored//For now,we'll use a simple approach return dependencies;}drawDependencyLine(svg,fromElement,toElement){const fromRect=fromElement.getBoundingClientRect();const toRect=toElement.getBoundingClientRect();const containerRect=this.container.getBoundingClientRect();//Calculate line coordinates const x1=fromRect.right-containerRect.left;const y1=fromRect.top+fromRect.height/2-containerRect.top;const x2=toRect.left-containerRect.left;const y2=toRect.top+toRect.height/2-containerRect.top;//Create curved path for better visualization const midX=(x1+x2)/2;const controlOffset=20;const path=document.createElementNS('http: path.setAttribute('d',`M ${x1}${y1}Q ${midX}${y1-controlOffset}${midX}${(y1+y2)/2}Q ${midX}${y2+controlOffset}${x2}${y2}`);path.setAttribute('stroke','#6b7280');path.setAttribute('stroke-width','2');path.setAttribute('fill','none');path.setAttribute('marker-end','url(#arrowhead)');path.classList.add('dependency-line');svg.appendChild(path);}highlightTaskDependencies(taskRow){const taskId=taskRow.dataset.taskId;taskRow.classList.add('bg-blue-50','border-blue-200');const dependencyLines=this.container.querySelectorAll('.dependency-line');dependencyLines.forEach(line=>{if(line.dataset.from===taskId || line.dataset.to===taskId){line.setAttribute('stroke','#3b82f6');line.setAttribute('stroke-width','3');}});}clearDependencyHighlights(){const highlightedRows=this.container.querySelectorAll('.bg-blue-50');highlightedRows.forEach(row=>{row.classList.remove('bg-blue-50','border-blue-200');});const dependencyLines=this.container.querySelectorAll('.dependency-line');dependencyLines.forEach(line=>{line.setAttribute('stroke','#6b7280');line.setAttribute('stroke-width','2');});}setupZoomControls(){const zoomInBtn=document.querySelector('[data-zoom="in"]');const zoomOutBtn=document.querySelector('[data-zoom="out"]');const zoomDisplay=document.querySelector('.timeline-controls span');if(zoomInBtn){zoomInBtn.addEventListener('click',()=>{if(this.currentZoom < 200){this.currentZoom+=25;this.applyZoom();if(zoomDisplay)zoomDisplay.textContent=this.currentZoom+'%';}});}if(zoomOutBtn){zoomOutBtn.addEventListener('click',()=>{if(this.currentZoom > 50){this.currentZoom-=25;this.applyZoom();if(zoomDisplay)zoomDisplay.textContent=this.currentZoom+'%';}});}}applyZoom(){const timelineContent=this.container.querySelector('.timeline-content');if(timelineContent){timelineContent.style.transform=`scale(${this.currentZoom/100})`;timelineContent.style.transformOrigin='top left';const containerHeight=timelineContent.scrollHeight*(this.currentZoom/100);this.container.style.minHeight=`${containerHeight}px`;}setTimeout(()=> this.drawDependencyLines(),100);}setupViewModeToggle(){const viewModeButtons=document.querySelectorAll('.view-mode-btn');viewModeButtons.forEach(button=>{button.addEventListener('click',()=>{this.viewMode=button.dataset.mode;viewModeButtons.forEach(btn=>{btn.classList.remove('bg-egis-blue','text-white');btn.classList.add('text-gray-600');});button.classList.add('bg-egis-blue','text-white');button.classList.remove('text-gray-600');this.applyViewMode();});});}applyViewMode(){const taskRows=this.container.querySelectorAll('.task-row');switch(this.viewMode){case 'critical': taskRows.forEach(row=>{const isCritical=row.querySelector('.task-bar.critical-path');row.style.display=isCritical ? 'block' : 'none';});break;case 'timeline': this.sortTasksByDate();break;case 'gantt': default: taskRows.forEach(row=>{row.style.display='block';});break;}this.drawDependencyLines();}sortTasksByDate(){const taskContainer=this.container.querySelector('.timeline-rows');if(!taskContainer)return;const taskRows=Array.from(taskContainer.querySelectorAll('.task-row'));taskRows.sort((a,b)=>{const aDate=this.getTaskStartDate(a);const bDate=this.getTaskStartDate(b);if(!aDate && !bDate)return 0;if(!aDate)return 1;if(!bDate)return-1;return new Date(aDate)-new Date(bDate);});taskRows.forEach(row=> taskContainer.appendChild(row));}getTaskStartDate(taskRow){const dateText=taskRow.querySelector('.col-span-2 .text-xs')?.textContent;if(dateText && dateText.includes('-')){return dateText.split('-')[0];}return null;}adjustTimelineScale(){const containerWidth=this.container.offsetWidth;const optimalScale=Math.min(Math.max(containerWidth/1200,0.5),2);const taskBars=this.container.querySelectorAll('.task-bar');taskBars.forEach(bar=>{const baseWidth=80;const newWidth=Math.max(baseWidth*optimalScale,20);bar.style.width=`${newWidth}%`;});}refreshTimeline(){this.drawDependencyLines();this.adjustTimelineScale();this.applyViewMode();this.setupProgressSliders();}setupProgressSliders(){const progressSliders=this.container.querySelectorAll('.progress-slider');progressSliders.forEach(slider=>{slider.addEventListener('input',(event)=>{const value=event.target.value;const taskRow=event.target.closest('.task-row');const progressFill=taskRow.querySelector('.progress-fill');const progressText=taskRow.querySelector('.col-span-2 .text-xs.font-medium');if(progressFill){progressFill.style.width=value+'%';}if(progressText){progressText.textContent=value+'%';}});});}exportTimeline(format='png'){switch(format){case 'png': this.exportAsPNG();break;case 'pdf': this.exportAsPDF();break;case 'csv': this.exportAsCSV();break;default: console.warn('Unsupported export format:',format);}}exportAsPNG(){console.log('PNG export not implemented yet');}exportAsPDF(){console.log('PDF export not implemented yet');}exportAsCSV(){const taskRows=this.container.querySelectorAll('.task-row');const csvData=['Task,Start Date,End Date,Duration,Progress,Assigned To,Status'];taskRows.forEach(row=>{const title=row.querySelector('.text-sm.font-medium')?.textContent || '';const dates=row.querySelector('.text-xs.text-gray-500')?.textContent || '';const duration=row.querySelector('.col-span-2 .text-sm')?.textContent || '';const progress=row.querySelector('.text-xs.font-medium')?.textContent || '';const assignee=row.querySelector('.col-span-2 .text-sm.text-gray-700')?.textContent || '';const status=row.querySelector('.inline-flex')?.textContent || '';const [startDate,endDate]=dates.includes('-')? dates.split('-'): ['',''];csvData.push(`"${title}","${startDate}","${endDate}","${duration}","${progress}","${assignee}","${status}"`);});const csvContent=csvData.join('\n');const blob=new Blob([csvContent],{type: 'text/csv'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download='timeline_export.csv';document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);}}document.addEventListener('DOMContentLoaded',function(){const timelineContainer=document.getElementById('timeline-chart');if(timelineContainer){window.timelineChart=new TimelineChart('timeline-chart');}});window.TimelineChart=TimelineChart;