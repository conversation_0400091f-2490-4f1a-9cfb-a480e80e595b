/**
 * CLEAR Accessibility CSS Framework
 * WCAG 2.1 AA Compliant Styles
 * Focus management, high contrast, and reduced motion support
 */

/* ==========================================================================
   Screen Reader Only Content
   ========================================================================== */

.sr-only,
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    clip-path: polygon(0px 0px, 0px 0px, 0px 0px) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:focus,
.sr-only-focusable:active,
.visually-hidden-focusable:focus,
.visually-hidden-focusable:active {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    clip-path: none !important;
    white-space: inherit !important;
}

/* ==========================================================================
   Skip Links
   ========================================================================== */

.skip-links {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
}

.skip-link {
    position: absolute;
    top: -50px;
    left: 10px;
    background: #000;
    color: #fff;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    font-weight: 500;
    font-size: 14px;
    z-index: 10000;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 0;
    color: #fff;
}

.skip-link:hover {
    background: #333;
    color: #fff;
}

/* ==========================================================================
   Enhanced Focus Indicators
   ========================================================================== */

/* Remove default outline and add consistent focus styles */
*:focus {
    outline: none;
}

/* High-visibility focus indicator */
button:focus,
a:focus,
input:focus,
select:focus,
textarea:focus,
[tabindex]:focus,
[contenteditable]:focus,
.dropdown-toggle:focus,
.btn:focus {
    outline: 3px solid #0066cc;
    outline-offset: 2px;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 102, 204, 0.3);
}

/* Special focus for buttons */
.btn:focus,
.btn-primary:focus,
.btn-secondary:focus,
.btn-outline-primary:focus,
.btn-outline-secondary:focus {
    outline: 3px solid #0066cc;
    outline-offset: 2px;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 102, 204, 0.3);
}

/* Form control focus */
.form-control:focus,
.form-select:focus {
    border-color: #0066cc;
    outline: 3px solid #0066cc;
    outline-offset: 2px;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(0, 102, 204, 0.3);
}

/* Modal focus trap */
.modal-content {
    border: 2px solid transparent;
}

.modal.show .modal-content {
    border-color: #0066cc;
}

/* Focus within containers */
.focus-within-highlight:focus-within {
    outline: 2px solid #0066cc;
    outline-offset: 1px;
}

/* ==========================================================================
   High Contrast Support
   ========================================================================== */

@media (prefers-contrast: high) {
    /* Enhanced contrast ratios */
    body {
        background: #ffffff;
        color: #000000;
    }

    .text-muted {
        color: #333333 !important;
    }

    .btn-outline-secondary {
        border-color: #000000;
        color: #000000;
    }

    .btn-outline-secondary:hover {
        background-color: #000000;
        color: #ffffff;
    }

    .border {
        border-color: #000000 !important;
    }

    .dropdown-menu {
        border: 2px solid #000000;
        background: #ffffff;
    }

    .dropdown-item {
        color: #000000;
    }

    .dropdown-item:hover,
    .dropdown-item:focus {
        background-color: #000000;
        color: #ffffff;
    }
}

/* High contrast mode class for manual toggling */
.high-contrast {
    filter: contrast(150%);
}

.high-contrast .text-muted {
    color: #000000 !important;
}

.high-contrast .btn-outline-secondary {
    border-color: #000000;
    color: #000000;
}

/* ==========================================================================
   Reduced Motion Support
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .fade {
        transition: none;
    }

    .collapsing {
        transition: none;
    }

    .carousel-fade .carousel-item {
        opacity: 1;
        transition: none;
    }

    .spinner-border,
    .spinner-grow {
        animation-duration: 2s !important;
    }
}

/* Reduced motion class for manual control */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
}

/* ==========================================================================
   ARIA Live Regions Styling
   ========================================================================== */

[aria-live] {
    /* Ensure live regions are not inadvertently hidden */
    position: relative;
}

/* Status messages */
.aria-status {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.aria-status[aria-live="polite"] {
    border-color: #0066cc;
    background-color: rgba(0, 102, 204, 0.1);
    color: #004080;
}

.aria-status[aria-live="assertive"] {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

/* ==========================================================================
   Loading States and Indicators
   ========================================================================== */

/* HTMX loading states */
.htmx-request {
    opacity: 0.7;
    transition: opacity 0.2s ease-in-out;
    position: relative;
}

.htmx-request::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.htmx-request[aria-busy="true"]::before {
    content: 'Loading...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #000;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    z-index: 11;
}

/* Loading spinner accessibility */
.loading-spinner,
.spinner-border,
.spinner-grow {
    --spinner-color: #0066cc;
}

.loading-spinner[aria-label]::after,
.spinner-border[aria-label]::after,
.spinner-grow[aria-label]::after {
    content: attr(aria-label);
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* ==========================================================================
   Form Accessibility Enhancements
   ========================================================================== */

/* Required field indicators */
.required::after {
    content: ' *';
    color: #dc3545;
    font-weight: bold;
}

/* Error state styling */
.form-control[aria-invalid="true"] {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='M5.8 5.8L6.2 6.2M6.2 5.8L5.8 6.2'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem 1rem;
}

.form-control[aria-invalid="true"]:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

/* Valid state styling */
.form-control[aria-invalid="false"] {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 **********.87-.86 2.14-2.14-.86-.87L2.86 6.25 1.47 4.86l-.86.87z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem 1rem;
}

/* Field descriptions */
.form-text {
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #6c757d;
}

/* Error messages */
.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.form-control[aria-invalid="true"] ~ .invalid-feedback {
    display: block;
}

/* ==========================================================================
   Modal and Dialog Accessibility
   ========================================================================== */

/* Ensure modals are properly contained */
.modal {
    --bs-modal-border-width: 2px;
}

.modal-content {
    border: var(--bs-modal-border-width) solid #dee2e6;
}

.modal.show .modal-content {
    border-color: #0066cc;
}

/* Modal title styling */
.modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

/* ==========================================================================
   Navigation and Menu Accessibility
   ========================================================================== */

/* Dropdown menu enhancements */
.dropdown-menu {
    border: 2px solid #dee2e6;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    transition: none;
}

.dropdown-item:focus,
.dropdown-item:hover {
    background-color: #e9ecef;
    color: #212529;
    outline: 2px solid #0066cc;
    outline-offset: -2px;
}

/* Navigation focus indicators */
.navbar-nav .nav-link:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

/* Breadcrumb accessibility */
.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    font-weight: bold;
    color: #6c757d;
}

/* ==========================================================================
   Table Accessibility
   ========================================================================== */

/* Table headers */
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

/* Sortable table headers */
.table th[role="columnheader"] {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.table th[role="columnheader"]:hover {
    background-color: #f8f9fa;
}

.table th[role="columnheader"]:focus {
    outline: 2px solid #0066cc;
    outline-offset: -2px;
}

.table th[aria-sort="ascending"]::after {
    content: " ↑";
    font-weight: bold;
}

.table th[aria-sort="descending"]::after {
    content: " ↓";
    font-weight: bold;
}

/* ==========================================================================
   Status and Progress Indicators
   ========================================================================== */

/* Progress bars */
.progress {
    border: 1px solid #dee2e6;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* Status badges */
.badge {
    font-weight: 500;
    font-size: 0.75em;
}

/* Alert enhancements */
.alert {
    border-width: 2px;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

.alert-dismissible .btn-close:focus {
    outline: 2px solid #000;
    outline-offset: -2px;
}

/* ==========================================================================
   Utility Classes for Accessibility
   ========================================================================== */

/* Focus management */
.focus-trap {
    position: relative;
}

.focus-first:first-child {
    position: relative;
}

/* Landmark helpers */
.landmark {
    position: relative;
}

.landmark::before {
    content: attr(aria-label);
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Color helpers that maintain contrast */
.text-contrast-safe {
    color: #212529;
}

.bg-contrast-safe {
    background-color: #ffffff;
    color: #212529;
}

/* ==========================================================================
   HTMX Specific Accessibility Enhancements
   ========================================================================== */

/* HTMX swap animations with accessibility considerations */
.htmx-settling {
    transition: all 0.3s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
    .htmx-settling {
        transition: none;
    }
}

/* HTMX error states */
.htmx-error {
    border: 2px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
    padding: 1rem;
    border-radius: 0.375rem;
    color: #721c24;
}

/* HTMX success states */
.htmx-success {
    border: 2px solid #198754;
    background-color: rgba(25, 135, 84, 0.1);
    padding: 1rem;
    border-radius: 0.375rem;
    color: #0f5132;
}

/* ==========================================================================
   Print Accessibility
   ========================================================================== */

@media print {
    .skip-links,
    .btn,
    .dropdown,
    .modal,
    .toast,
    [aria-hidden="true"] {
        display: none !important;
    }

    .sr-only {
        position: static !important;
        width: auto !important;
        height: auto !important;
        overflow: visible !important;
        clip: auto !important;
        clip-path: none !important;
    }

    a::after {
        content: " (" attr(href) ")";
        font-size: 0.8em;
        color: #666;
    }

    a[href^="#"]::after,
    a[href^="javascript:"]::after {
        content: "";
    }
}

/* ==========================================================================
   Component-Specific Accessibility Enhancements
   ========================================================================== */

/* Toast notifications */
.toast {
    border: 2px solid #dee2e6;
}

.toast.show {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (prefers-reduced-motion: reduce) {
    .toast.show {
        animation: none;
    }
}

/* Card components */
.card {
    border: 1px solid #dee2e6;
}

.card:focus-within {
    outline: 2px solid #0066cc;
    outline-offset: 1px;
}

/* List groups */
.list-group-item:focus {
    z-index: 2;
    outline: 2px solid #0066cc;
    outline-offset: -2px;
}

/* Pagination */
.page-link:focus {
    z-index: 3;
    outline: 2px solid #0066cc;
    outline-offset: -2px;
    box-shadow: none;
}

/* ==========================================================================
   Custom Component Accessibility
   ========================================================================== */

/* CLEAR specific component enhancements */
.lucide-icon {
    /* Ensure icons have proper contrast */
    opacity: 0.8;
}

.lucide-icon:focus {
    opacity: 1;
}

/* Dashboard widgets */
.widget {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.widget:focus-within {
    outline: 2px solid #0066cc;
    outline-offset: 1px;
}

/* Project cards */
.project-card:focus-within {
    outline: 2px solid #0066cc;
    outline-offset: 1px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Search results */
.search-result-item:focus {
    outline: 2px solid #0066cc;
    outline-offset: -2px;
    background-color: #f8f9fa;
}