/* EGIS Color System
   Comprehensive color system for the CLEAR platform 
   Based on EGIS brand guidelines with accessibility compliance
*/

:root {
  /* Primary Brand Colors */
  --egis-midnight-blue: #1e3a8a;
  --egis-bright-blue: #3b82f6;
  --egis-sky-blue: #60a5fa;
  --egis-light-blue: #93c5fd;
  
  /* Secondary Colors */
  --egis-teal: #0d9488;
  --egis-green: #059669;
  --egis-orange: #ea580c;
  --egis-red: #dc2626;
  
  /* Neutral Colors */
  --egis-dark-gray: #374151;
  --egis-medium-gray: #6b7280;
  --egis-light-gray: #d1d5db;
  --egis-extra-light-gray: #f3f4f6;
  
  /* Background Colors */
  --egis-white: #ffffff;
  --egis-off-white: #fafafa;
  --egis-light-background: #f9fafb;
  
  /* Status Colors */
  --egis-success: #10b981;
  --egis-warning: #f59e0b;
  --egis-danger: #ef4444;
  --egis-info: #3b82f6;
  
  /* Shadow System */
  --egis-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --egis-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --egis-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --egis-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Border Radius */
  --egis-radius-sm: 0.125rem;
  --egis-radius: 0.25rem;
  --egis-radius-md: 0.375rem;
  --egis-radius-lg: 0.5rem;
  --egis-radius-xl: 0.75rem;
}

/* Color Utility Classes */
.bg-egis-midnight { background-color: var(--egis-midnight-blue); }
.bg-egis-bright { background-color: var(--egis-bright-blue); }
.bg-egis-sky { background-color: var(--egis-sky-blue); }
.bg-egis-light { background-color: var(--egis-light-blue); }
.bg-egis-teal { background-color: var(--egis-teal); }
.bg-egis-green { background-color: var(--egis-green); }
.bg-egis-orange { background-color: var(--egis-orange); }
.bg-egis-red { background-color: var(--egis-red); }

.text-egis-midnight { color: var(--egis-midnight-blue); }
.text-egis-bright { color: var(--egis-bright-blue); }
.text-egis-sky { color: var(--egis-sky-blue); }
.text-egis-light { color: var(--egis-light-blue); }
.text-egis-teal { color: var(--egis-teal); }
.text-egis-green { color: var(--egis-green); }
.text-egis-orange { color: var(--egis-orange); }
.text-egis-red { color: var(--egis-red); }

.border-egis-midnight { border-color: var(--egis-midnight-blue); }
.border-egis-bright { border-color: var(--egis-bright-blue); }
.border-egis-light-gray { border-color: var(--egis-light-gray); }

/* Component Styles */
.egis-card {
  background: var(--egis-white);
  border: 1px solid var(--egis-light-gray);
  border-radius: var(--egis-radius-lg);
  box-shadow: var(--egis-shadow);
}

.egis-button-primary {
  background-color: var(--egis-midnight-blue);
  color: var(--egis-white);
  border: none;
  border-radius: var(--egis-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
}

.egis-button-primary:hover {
  background-color: var(--egis-bright-blue);
}

.egis-button-secondary {
  background-color: var(--egis-white);
  color: var(--egis-midnight-blue);
  border: 1px solid var(--egis-midnight-blue);
  border-radius: var(--egis-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.egis-button-secondary:hover {
  background-color: var(--egis-midnight-blue);
  color: var(--egis-white);
}

/* Status Indicators */
.status-success {
  color: var(--egis-success);
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-warning {
  color: var(--egis-warning);
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-danger {
  color: var(--egis-danger);
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-info {
  color: var(--egis-info);
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --egis-white: #1f2937;
    --egis-off-white: #374151;
    --egis-light-background: #111827;
    --egis-dark-gray: #f9fafb;
    --egis-medium-gray: #d1d5db;
    --egis-light-gray: #4b5563;
    --egis-extra-light-gray: #374151;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --egis-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.3);
    --egis-shadow-md: 0 4px 8px 0 rgba(0, 0, 0, 0.3);
    --egis-shadow-lg: 0 8px 16px 0 rgba(0, 0, 0, 0.3);
  }
  
  .egis-card {
    border-width: 2px;
  }
  
  .egis-button-primary,
  .egis-button-secondary {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .egis-button-primary,
  .egis-button-secondary {
    transition: none;
  }
}