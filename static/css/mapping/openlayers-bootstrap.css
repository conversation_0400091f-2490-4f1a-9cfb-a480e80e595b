/**
 * OpenLayers Bootstrap Integration Styles
 * 
 * This stylesheet provides Bootstrap-compatible styling for OpenLayers maps
 * and ensures responsive behavior across different screen sizes.
 */

/* Base Map Container Styles */
.ol-map-container {
    position: relative;
    width: 100%;
    min-height: 400px;
    border-radius: var(--bs-border-radius);
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Responsive Height Classes */
.ol-map-sm { height: 300px; }
.ol-map-md { height: 450px; }
.ol-map-lg { height: 600px; }
.ol-map-xl { height: 750px; }
.ol-map-full { height: calc(100vh - 200px); }

/* Responsive adjustments */
@media (max-width: 576px) {
    .ol-map-container { min-height: 250px; }
    .ol-map-sm { height: 200px; }
    .ol-map-md { height: 300px; }
    .ol-map-lg { height: 400px; }
    .ol-map-xl { height: 500px; }
    .ol-map-full { height: calc(100vh - 150px); }
}

@media (max-width: 768px) {
    .ol-map-container { min-height: 300px; }
    .ol-map-md { height: 350px; }
    .ol-map-lg { height: 450px; }
    .ol-map-xl { height: 550px; }
}

/* OpenLayers Control Overrides */
.ol-control {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border-radius: var(--bs-border-radius);
    padding: 0;
}

.ol-control:hover {
    background-color: rgba(255, 255, 255, 0.95);
}

.ol-control button {
    background-color: var(--bs-primary);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    padding: 0;
    border-radius: var(--bs-border-radius);
    transition: all 0.2s ease-in-out;
}

.ol-control button:hover,
.ol-control button:focus {
    background-color: var(--bs-primary-dark, #0056b3);
    transform: scale(1.05);
}

.ol-control button:active {
    transform: scale(0.95);
}

/* Zoom Controls */
.ol-zoom {
    top: 0.5rem;
    left: 0.5rem;
    right: auto;
}

.ol-zoom button {
    margin: 0;
    border-radius: 0;
}

.ol-zoom .ol-zoom-in {
    border-radius: var(--bs-border-radius) var(--bs-border-radius) 0 0;
}

.ol-zoom .ol-zoom-out {
    border-radius: 0 0 var(--bs-border-radius) var(--bs-border-radius);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Full Screen Control */
.ol-full-screen {
    top: 0.5rem;
    right: 0.5rem;
    left: auto;
}

/* Attribution */
.ol-attribution {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border-radius: var(--bs-border-radius);
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.ol-attribution:not(.ol-collapsed) {
    background-color: rgba(255, 255, 255, 0.9);
}

.ol-attribution a {
    color: var(--bs-primary);
    text-decoration: none;
}

.ol-attribution a:hover {
    text-decoration: underline;
}

/* Scale Line */
.ol-scale-line {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border-radius: var(--bs-border-radius);
    padding: 0.125rem 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    bottom: 2.5rem;
    left: 0.5rem;
}

.ol-scale-line-inner {
    border: 1px solid var(--bs-dark);
    border-top: none;
    color: var(--bs-dark);
    font-size: 0.75rem;
}

/* Mouse Position */
.ol-mouse-position {
    top: auto;
    bottom: 0.5rem;
    right: 0.5rem;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--bs-border-radius);
    font-family: var(--bs-font-monospace);
    font-size: 0.75rem;
}

/* Overview Map */
.ol-overviewmap {
    bottom: 0.5rem;
    left: 0.5rem;
    border-radius: var(--bs-border-radius);
    overflow: hidden;
}

.ol-overviewmap-map {
    border: 2px solid var(--bs-primary);
}

.ol-overviewmap button {
    bottom: 0;
    left: 0;
    right: auto;
    top: auto;
}

/* Popup Styling */
.ol-popup {
    position: absolute;
    background-color: white;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 1rem;
    border-radius: var(--bs-border-radius);
    border: 1px solid rgba(0, 0, 0, 0.125);
    bottom: 12px;
    left: -50px;
    min-width: 280px;
}

.ol-popup:after,
.ol-popup:before {
    top: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.ol-popup:after {
    border-top-color: white;
    border-width: 10px;
    left: 48px;
    margin-left: -10px;
}

.ol-popup:before {
    border-top-color: rgba(0, 0, 0, 0.125);
    border-width: 11px;
    left: 48px;
    margin-left: -11px;
}

.ol-popup-closer {
    text-decoration: none;
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    font-size: 1.25rem;
    line-height: 1;
    color: var(--bs-secondary);
    opacity: 0.5;
    transition: opacity 0.2s;
}

.ol-popup-closer:hover {
    opacity: 1;
}

.ol-popup-closer:after {
    content: "×";
}

/* Custom Map Tools */
.ol-map-tools {
    position: absolute;
    top: 0.5rem;
    left: 4rem;
    display: flex;
    gap: 0.25rem;
    z-index: 1000;
}

.ol-map-tool {
    background-color: white;
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.ol-map-tool:hover {
    background-color: var(--bs-light);
    border-color: var(--bs-primary);
}

.ol-map-tool.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

/* Loading Overlay */
.ol-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.ol-loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.25rem solid var(--bs-light);
    border-top-color: var(--bs-primary);
    border-radius: 50%;
    animation: ol-spin 1s linear infinite;
}

@keyframes ol-spin {
    to { transform: rotate(360deg); }
}

/* Touch Device Adjustments */
@media (pointer: coarse) {
    .ol-control button {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .ol-map-tool {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
}

/* Dark Mode Support */
[data-theme="dark"] .ol-control {
    background-color: rgba(33, 37, 41, 0.9);
}

[data-theme="dark"] .ol-control:hover {
    background-color: rgba(33, 37, 41, 0.95);
}

[data-theme="dark"] .ol-attribution {
    background-color: rgba(33, 37, 41, 0.8);
    color: var(--bs-light);
}

[data-theme="dark"] .ol-scale-line {
    background-color: rgba(33, 37, 41, 0.8);
}

[data-theme="dark"] .ol-scale-line-inner {
    border-color: var(--bs-light);
    color: var(--bs-light);
}

[data-theme="dark"] .ol-popup {
    background-color: var(--bs-dark);
    color: var(--bs-light);
    border-color: var(--bs-gray-700);
}

[data-theme="dark"] .ol-popup:after {
    border-top-color: var(--bs-dark);
}

[data-theme="dark"] .ol-popup:before {
    border-top-color: var(--bs-gray-700);
}

/* Print Styles */
@media print {
    .ol-control {
        display: none !important;
    }
    
    .ol-map-tools {
        display: none !important;
    }
    
    .ol-loading-overlay {
        display: none !important;
    }
}