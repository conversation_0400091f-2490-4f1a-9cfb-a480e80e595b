/**
 * Service Worker for Push Notifications
 * ====================================
 * 
 * Handles push notification events, displays notifications,
 * and manages notification actions with SSE integration.
 */

const CACHE_NAME = 'clear-notifications-v1';
const NOTIFICATION_CACHE = 'notification-cache-v1';

// Configuration
const CONFIG = {
    API_BASE: '/activity/',
    NOTIFICATION_CENTER_URL: '/activity/notifications/',
    BADGE_UPDATE_ENDPOINT: '/activity/push/badge/',
    TRACK_ENDPOINT: '/activity/push/track/',
    DEFAULT_ICON: '/static/images/notification-icon.svg',
    DEFAULT_BADGE: '/static/images/notification-badge.svg',
    NOTIFICATION_TTL: 24 * 60 * 60 * 1000, // 24 hours
};

/**
 * Install event - cache essential resources
 */
self.addEventListener('install', event => {
    console.log('Push notifications service worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME).then(cache => {
            return cache.addAll([
                CONFIG.DEFAULT_ICON,
                CONFIG.DEFAULT_BADGE,
                CONFIG.NOTIFICATION_CENTER_URL,
                '/static/css/notifications.css',
                '/static/js/notifications.js'
            ]).catch(error => {
                console.warn('Failed to cache some resources:', error);
            });
        })
    );
    
    self.skipWaiting();
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', event => {
    console.log('Push notifications service worker activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME && cacheName !== NOTIFICATION_CACHE) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            return self.clients.claim();
        })
    );
});

/**
 * Push event - handle incoming push notifications
 */
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    if (!event.data) {
        console.warn('Push event has no data');
        return;
    }
    
    try {
        const data = event.data.json();
        console.log('Push notification data:', data);
        
        event.waitUntil(
            handlePushNotification(data)
        );
    } catch (error) {
        console.error('Error parsing push notification data:', error);
    }
});

/**
 * Notification click event - handle notification interactions
 */
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    const notification = event.notification;
    const action = event.action;
    const data = notification.data || {};
    
    event.waitUntil(
        handleNotificationClick(notification, action, data)
    );
});

/**
 * Notification close event - track dismissals
 */
self.addEventListener('notificationclose', event => {
    console.log('Notification closed:', event);
    
    const notification = event.notification;
    const data = notification.data || {};
    
    if (data.notification_id) {
        // Track notification dismissal
        trackNotificationEvent('dismissed', data.notification_id);
    }
});

/**
 * Handle push notification display
 */
async function handlePushNotification(data) {
    try {
        // Validate notification data
        if (!data.title) {
            console.warn('Push notification missing title');
            return;
        }
        
        // Check if notification already exists
        const existingNotifications = await self.registration.getNotifications({
            tag: data.tag
        });
        
        // Close existing notification with same tag
        existingNotifications.forEach(notification => {
            notification.close();
        });
        
        // Prepare notification options
        const options = {
            body: data.body || '',
            icon: data.icon || CONFIG.DEFAULT_ICON,
            badge: data.badge || CONFIG.DEFAULT_BADGE,
            tag: data.tag || `notification-${Date.now()}`,
            data: data.data || {},
            requireInteraction: data.requireInteraction || false,
            timestamp: data.timestamp || Date.now(),
            silent: false,
            vibrate: [200, 100, 200]
        };
        
        // Add actions if provided
        if (data.actions && Array.isArray(data.actions)) {
            options.actions = data.actions.slice(0, 3); // Limit to 3 actions
        }
        
        // Add image if provided
        if (data.image) {
            options.image = data.image;
        }
        
        // Show notification
        await self.registration.showNotification(data.title, options);
        
        // Cache notification for offline access
        await cacheNotification(data);
        
        // Update badge count
        await updateBadgeCount();
        
        // Notify SSE connections about new notification
        await notifySSEClients('notification_received', data);
        
        console.log('Push notification displayed successfully');
        
    } catch (error) {
        console.error('Error handling push notification:', error);
    }
}

/**
 * Handle notification click events
 */
async function handleNotificationClick(notification, action, data) {
    try {
        // Close the notification
        notification.close();
        
        // Track click event
        if (data.notification_id) {
            await trackNotificationEvent('clicked', data.notification_id, action);
        }
        
        // Handle different actions
        switch (action) {
            case 'view':
            case '':  // Default click (no action)
                await openNotificationUrl(data.url || CONFIG.NOTIFICATION_CENTER_URL);
                break;
                
            case 'reply':
                await openNotificationUrl(data.url || CONFIG.NOTIFICATION_CENTER_URL, { reply: true });
                break;
                
            case 'dismiss':
                // Just track the dismissal (already done above)
                break;
                
            default:
                console.warn('Unknown notification action:', action);
                await openNotificationUrl(data.url || CONFIG.NOTIFICATION_CENTER_URL);
        }
        
        // Update badge count after interaction
        await updateBadgeCount();
        
        // Notify SSE connections about notification interaction
        await notifySSEClients('notification_clicked', { action, data });
        
    } catch (error) {
        console.error('Error handling notification click:', error);
    }
}

/**
 * Open URL in existing or new window/tab
 */
async function openNotificationUrl(url, params = {}) {
    try {
        // Add query parameters if provided
        const urlObj = new URL(url, self.location.origin);
        Object.entries(params).forEach(([key, value]) => {
            urlObj.searchParams.set(key, value);
        });
        
        const finalUrl = urlObj.toString();
        
        // Get all clients (windows/tabs)
        const clients = await self.clients.matchAll({
            type: 'window',
            includeUncontrolled: true
        });
        
        // Try to focus existing client with same origin
        for (const client of clients) {
            if (client.url.startsWith(self.location.origin)) {
                await client.focus();
                client.postMessage({
                    type: 'notification_navigate',
                    url: finalUrl
                });
                return;
            }
        }
        
        // No existing client found, open new window
        await self.clients.openWindow(finalUrl);
        
    } catch (error) {
        console.error('Error opening notification URL:', error);
    }
}

/**
 * Cache notification for offline access
 */
async function cacheNotification(data) {
    try {
        const cache = await caches.open(NOTIFICATION_CACHE);
        const cacheKey = `notification-${data.data?.notification_id || Date.now()}`;
        
        const response = new Response(JSON.stringify(data), {
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': `max-age=${CONFIG.NOTIFICATION_TTL / 1000}`
            }
        });
        
        await cache.put(cacheKey, response);
        
    } catch (error) {
        console.error('Error caching notification:', error);
    }
}

/**
 * Update notification badge count
 */
async function updateBadgeCount() {
    try {
        // Get unread notification count from server
        const response = await fetch(CONFIG.BADGE_UPDATE_ENDPOINT, {
            method: 'GET',
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            const count = data.unread_count || 0;
            
            // Update badge
            if ('setAppBadge' in navigator) {
                if (count > 0) {
                    await navigator.setAppBadge(count);
                } else {
                    await navigator.clearAppBadge();
                }
            }
            
            // Notify clients about badge update
            await notifySSEClients('badge_updated', { count });
        }
        
    } catch (error) {
        console.error('Error updating badge count:', error);
    }
}

/**
 * Track notification events for analytics
 */
async function trackNotificationEvent(event, notificationId, action = null) {
    try {
        const payload = {
            event,
            notification_id: notificationId,
            action,
            timestamp: Date.now()
        };
        
        // Send to analytics endpoint
        await fetch(CONFIG.TRACK_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload),
            credentials: 'include'
        });
        
    } catch (error) {
        console.error('Error tracking notification event:', error);
    }
}

/**
 * Notify SSE clients about events
 */
async function notifySSEClients(type, data) {
    try {
        const clients = await self.clients.matchAll({
            type: 'window',
            includeUncontrolled: true
        });
        
        const message = {
            type: 'push_notification_event',
            event: type,
            data,
            timestamp: Date.now()
        };
        
        clients.forEach(client => {
            client.postMessage(message);
        });
        
    } catch (error) {
        console.error('Error notifying SSE clients:', error);
    }
}

/**
 * Background sync for failed requests
 */
self.addEventListener('sync', event => {
    if (event.tag === 'notification-sync') {
        event.waitUntil(
            syncNotificationEvents()
        );
    }
});

/**
 * Sync pending notification events
 */
async function syncNotificationEvents() {
    try {
        // This would sync any pending notification events
        // when the connection is restored
        console.log('Syncing notification events...');
        
    } catch (error) {
        console.error('Error syncing notification events:', error);
    }
}

/**
 * Periodic background sync for cache cleanup
 */
self.addEventListener('periodicsync', event => {
    if (event.tag === 'notification-cleanup') {
        event.waitUntil(
            cleanupNotificationCache()
        );
    }
});

/**
 * Clean up old notifications from cache
 */
async function cleanupNotificationCache() {
    try {
        const cache = await caches.open(NOTIFICATION_CACHE);
        const requests = await cache.keys();
        const now = Date.now();
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const cacheControl = response.headers.get('Cache-Control');
                const maxAge = cacheControl ? 
                    parseInt(cacheControl.match(/max-age=(\d+)/)?.[1] || '0') * 1000 : 
                    CONFIG.NOTIFICATION_TTL;
                
                const responseDate = new Date(response.headers.get('Date') || now);
                const expiryDate = responseDate.getTime() + maxAge;
                
                if (now > expiryDate) {
                    await cache.delete(request);
                }
            }
        }
        
        console.log('Notification cache cleanup completed');
        
    } catch (error) {
        console.error('Error cleaning up notification cache:', error);
    }
}

console.log('Push notifications service worker loaded successfully');