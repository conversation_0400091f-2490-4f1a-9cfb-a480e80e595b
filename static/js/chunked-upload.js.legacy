/**
 * Chunked File Upload System for Document Management
 * 
 * Provides robust file upload functionality with:
 * - Chunked upload for large files
 * - Progress tracking and resume capability
 * - Error handling and retry logic
 * - Real-time progress updates
 * - Virus scanning and security validation
 */

class ChunkedUploader {
    constructor(options = {}) {
        this.options = {
            chunkSize: options.chunkSize || 1024 * 1024, // 1MB default
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000, // 1 second
            progressUpdateInterval: options.progressUpdateInterval || 500, // 0.5 seconds
            autoRetry: options.autoRetry !== false,
            uploadUrl: options.uploadUrl || '/documents/chunked-upload/',
            completeUrl: options.completeUrl || '/documents/chunked-upload/{upload_id}/complete/',
            progressUrl: options.progressUrl || '/documents/chunked-upload/{upload_id}/progress/',
            cancelUrl: options.cancelUrl || '/documents/chunked-upload/{upload_id}/cancel/',
            resumeUrl: options.resumeUrl || '/documents/chunked-upload/{upload_id}/resume/',
            errorUrl: options.errorUrl || '/documents/chunked-upload/{upload_id}/error/',
            ...options
        };
        
        this.uploads = new Map(); // Track active uploads
        this.sessionId = this.generateSessionId();
        
        // Bind methods
        this.upload = this.upload.bind(this);
        this.resumeUpload = this.resumeUpload.bind(this);
        this.cancelUpload = this.cancelUpload.bind(this);
    }
    
    /**
     * Start uploading a file
     */
    async upload(file, metadata = {}) {
        if (!file || !file.size) {
            throw new Error('Invalid file provided');
        }
        
        const uploadId = this.generateUploadId();
        const totalChunks = Math.ceil(file.size / this.options.chunkSize);
        
        const uploadInfo = {
            uploadId,
            file,
            metadata,
            totalChunks,
            uploadedChunks: 0,
            currentChunk: 0,
            startTime: Date.now(),
            lastChunkTime: Date.now(),
            retryCount: 0,
            status: 'starting',
            speeds: [], // Track upload speeds for averaging
            paused: false,
            cancelled: false,
        };
        
        this.uploads.set(uploadId, uploadInfo);
        
        try {
            // Initialize upload on server
            const initResponse = await this.initializeUpload(uploadInfo);
            uploadInfo.serverUploadId = initResponse.upload_id;
            uploadInfo.status = 'uploading';
            
            // Start uploading chunks
            await this.uploadChunks(uploadInfo);
            
            // Complete upload
            const result = await this.completeUpload(uploadInfo);
            uploadInfo.status = 'completed';
            
            this.uploads.delete(uploadId);
            return result;
            
        } catch (error) {
            uploadInfo.status = 'error';
            uploadInfo.error = error.message;
            
            // Report error to server
            if (uploadInfo.serverUploadId) {
                await this.reportError(uploadInfo.serverUploadId, error.message);
            }
            
            throw error;
        }
    }
    
    /**
     * Resume an interrupted upload
     */
    async resumeUpload(uploadId) {
        try {
            const response = await fetch(
                this.options.resumeUrl.replace('{upload_id}', uploadId),
                {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCSRFToken(),
                    },
                }
            );
            
            if (!response.ok) {
                throw new Error(`Failed to resume upload: ${response.statusText}`);
            }
            
            const resumeData = await response.json();
            
            // Reconstruct upload info for resuming
            const uploadInfo = {
                uploadId: resumeData.upload_id,
                serverUploadId: resumeData.upload_id,
                file: null, // Will need to be provided separately
                totalChunks: resumeData.total_chunks,
                uploadedChunks: resumeData.chunks_uploaded,
                currentChunk: resumeData.chunks_uploaded,
                startTime: Date.now(),
                lastChunkTime: Date.now(),
                retryCount: 0,
                status: 'resuming',
                speeds: [],
                paused: false,
                cancelled: false,
            };
            
            this.uploads.set(uploadId, uploadInfo);
            return uploadInfo;
            
        } catch (error) {
            console.error('Failed to resume upload:', error);
            throw error;
        }
    }
    
    /**
     * Cancel an active upload
     */
    async cancelUpload(uploadId) {
        const uploadInfo = this.uploads.get(uploadId);
        if (!uploadInfo) {
            throw new Error('Upload not found');
        }
        
        uploadInfo.cancelled = true;
        uploadInfo.status = 'cancelled';
        
        try {
            if (uploadInfo.serverUploadId) {
                await fetch(
                    this.options.cancelUrl.replace('{upload_id}', uploadInfo.serverUploadId),
                    {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': this.getCSRFToken(),
                        },
                    }
                );
            }
        } catch (error) {
            console.error('Failed to cancel upload on server:', error);
        }
        
        this.uploads.delete(uploadId);
    }
    
    /**
     * Pause an upload
     */
    pauseUpload(uploadId) {
        const uploadInfo = this.uploads.get(uploadId);
        if (uploadInfo) {
            uploadInfo.paused = true;
            uploadInfo.status = 'paused';
        }
    }
    
    /**
     * Resume a paused upload
     */
    async resumePausedUpload(uploadId) {
        const uploadInfo = this.uploads.get(uploadId);
        if (uploadInfo && uploadInfo.paused) {
            uploadInfo.paused = false;
            uploadInfo.status = 'uploading';
            await this.uploadChunks(uploadInfo);
        }
    }
    
    /**
     * Get upload progress
     */
    getProgress(uploadId) {
        const uploadInfo = this.uploads.get(uploadId);
        if (!uploadInfo) {
            return null;
        }
        
        const progress = (uploadInfo.uploadedChunks / uploadInfo.totalChunks) * 100;
        const elapsedTime = Date.now() - uploadInfo.startTime;
        const uploadedBytes = uploadInfo.uploadedChunks * this.options.chunkSize;
        const totalBytes = uploadInfo.file ? uploadInfo.file.size : uploadInfo.totalChunks * this.options.chunkSize;
        
        // Calculate average speed
        let averageSpeed = 0;
        if (uploadInfo.speeds.length > 0) {
            averageSpeed = uploadInfo.speeds.reduce((a, b) => a + b, 0) / uploadInfo.speeds.length;
        }
        
        // Estimate time remaining
        let timeRemaining = null;
        if (averageSpeed > 0) {
            const remainingBytes = totalBytes - uploadedBytes;
            timeRemaining = remainingBytes / averageSpeed;
        }
        
        return {
            uploadId,
            progress: Math.min(progress, 100),
            uploadedChunks: uploadInfo.uploadedChunks,
            totalChunks: uploadInfo.totalChunks,
            uploadedBytes,
            totalBytes,
            elapsedTime,
            averageSpeed,
            timeRemaining,
            status: uploadInfo.status,
            error: uploadInfo.error,
        };
    }
    
    /**
     * Initialize upload on server
     */
    async initializeUpload(uploadInfo) {
        const formData = new FormData();
        
        // Create a small chunk to initialize
        const firstChunk = uploadInfo.file.slice(0, Math.min(this.options.chunkSize, uploadInfo.file.size));
        formData.append('file', firstChunk);
        formData.append('filename', uploadInfo.file.name);
        formData.append('total_chunks', uploadInfo.totalChunks.toString());
        formData.append('chunk_index', '0');
        formData.append('session_id', this.sessionId);
        
        // Add metadata
        Object.entries(uploadInfo.metadata).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                formData.append(key, value);
            }
        });
        
        const response = await fetch(this.options.uploadUrl, {
            method: 'POST',
            headers: {
                'X-CSRFToken': this.getCSRFToken(),
            },
            body: formData,
        });
        
        if (!response.ok) {
            throw new Error(`Failed to initialize upload: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (result.error) {
            throw new Error(result.error);
        }
        
        uploadInfo.uploadedChunks = 1;
        uploadInfo.currentChunk = 1;
        
        return result;
    }
    
    /**
     * Upload file chunks
     */
    async uploadChunks(uploadInfo) {
        while (uploadInfo.currentChunk < uploadInfo.totalChunks && !uploadInfo.cancelled) {
            if (uploadInfo.paused) {
                await this.delay(100);
                continue;
            }
            
            try {
                await this.uploadChunk(uploadInfo, uploadInfo.currentChunk);
                uploadInfo.uploadedChunks++;
                uploadInfo.currentChunk++;
                uploadInfo.retryCount = 0; // Reset retry count on success
                
            } catch (error) {
                if (this.options.autoRetry && uploadInfo.retryCount < this.options.maxRetries) {
                    uploadInfo.retryCount++;
                    console.warn(`Chunk ${uploadInfo.currentChunk} failed, retrying (${uploadInfo.retryCount}/${this.options.maxRetries}):`, error);
                    await this.delay(this.options.retryDelay * uploadInfo.retryCount);
                } else {
                    throw error;
                }
            }
            
            // Update progress callbacks
            if (this.options.onProgress) {
                this.options.onProgress(this.getProgress(uploadInfo.uploadId));
            }
        }
    }
    
    /**
     * Upload a single chunk
     */
    async uploadChunk(uploadInfo, chunkIndex) {
        const start = chunkIndex * this.options.chunkSize;
        const end = Math.min(start + this.options.chunkSize, uploadInfo.file.size);
        const chunk = uploadInfo.file.slice(start, end);
        
        const formData = new FormData();
        formData.append('file', chunk);
        formData.append('filename', uploadInfo.file.name);
        formData.append('chunk_index', chunkIndex.toString());
        formData.append('total_chunks', uploadInfo.totalChunks.toString());
        formData.append('session_id', this.sessionId);
        
        const chunkStartTime = Date.now();
        
        const response = await fetch(
            `${this.options.uploadUrl}${uploadInfo.serverUploadId}/`,
            {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: formData,
            }
        );
        
        if (!response.ok) {
            throw new Error(`Chunk ${chunkIndex} upload failed: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (result.error) {
            throw new Error(result.error);
        }
        
        // Calculate and store upload speed
        const chunkTime = Date.now() - chunkStartTime;
        const chunkSpeed = chunk.size / (chunkTime / 1000); // bytes per second
        uploadInfo.speeds.push(chunkSpeed);
        
        // Keep only recent speeds for more accurate averaging
        if (uploadInfo.speeds.length > 10) {
            uploadInfo.speeds.shift();
        }
        
        uploadInfo.lastChunkTime = Date.now();
    }
    
    /**
     * Complete upload on server
     */
    async completeUpload(uploadInfo) {
        const response = await fetch(
            this.options.completeUrl.replace('{upload_id}', uploadInfo.serverUploadId),
            {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: uploadInfo.file.name,
                }),
            }
        );
        
        if (!response.ok) {
            throw new Error(`Failed to complete upload: ${response.statusText}`);
        }
        
        const result = await response.json();
        if (result.error) {
            throw new Error(result.error);
        }
        
        return result;
    }
    
    /**
     * Report error to server
     */
    async reportError(uploadId, errorMessage) {
        try {
            await fetch(
                this.options.errorUrl.replace('{upload_id}', uploadId),
                {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCSRFToken(),
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        error: errorMessage,
                    }),
                }
            );
        } catch (error) {
            console.error('Failed to report error to server:', error);
        }
    }
    
    /**
     * Get server-side progress
     */
    async getServerProgress(uploadId) {
        try {
            const response = await fetch(
                this.options.progressUrl.replace('{upload_id}', uploadId)
            );
            
            if (response.ok) {
                return await response.json();
            }
        } catch (error) {
            console.error('Failed to get server progress:', error);
        }
        
        return null;
    }
    
    /**
     * Utility methods
     */
    generateUploadId() {
        return 'upload_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatTime(seconds) {
        if (!seconds || seconds === Infinity) return '∞';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
}

/**
 * File Upload Component for UI Integration
 */
class FileUploadComponent {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = options;
        
        this.uploader = new ChunkedUploader({
            ...options,
            onProgress: this.updateProgress.bind(this),
        });
        
        this.activeUploads = new Map();
        this.init();
    }
    
    init() {
        this.createUI();
        this.bindEvents();
    }
    
    createUI() {
        this.container.innerHTML = `
            <div class="file-upload-component">
                <div class="upload-area" id="upload-area">
                    <div class="upload-zone">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>Drop files here or click to upload</h5>
                        <p class="text-muted">Supports multiple files up to ${this.uploader.formatFileSize(this.options.maxFileSize || 500 * 1024 * 1024)} each</p>
                        <input type="file" id="file-input" multiple style="display: none;">
                    </div>
                </div>
                
                <div class="upload-progress-container" id="progress-container" style="display: none;">
                    <h6>Upload Progress</h6>
                    <div id="upload-list"></div>
                </div>
                
                <div class="upload-metadata" id="metadata-form" style="display: none;">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="document-title" class="form-label">Title (Optional)</label>
                            <input type="text" class="form-control" id="document-title" placeholder="Override filename">
                        </div>
                        <div class="col-md-6">
                            <label for="document-folder" class="form-label">Folder</label>
                            <select class="form-select" id="document-folder">
                                <option value="">Root Folder</option>
                                <!-- Folders will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <label for="document-description" class="form-label">Description</label>
                            <textarea class="form-control" id="document-description" rows="3" placeholder="Optional description"></textarea>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label for="document-tags" class="form-label">Tags</label>
                            <input type="text" class="form-control" id="document-tags" placeholder="tag1, tag2, tag3">
                        </div>
                        <div class="col-md-6">
                            <label for="document-project" class="form-label">Project (Optional)</label>
                            <select class="form-select" id="document-project">
                                <option value="">No Project</option>
                                <!-- Projects will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    bindEvents() {
        const uploadArea = this.container.querySelector('#upload-area');
        const fileInput = this.container.querySelector('#file-input');
        
        // Click to upload
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            this.handleFiles(e.dataTransfer.files);
        });
        
        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });
    }
    
    async handleFiles(files) {
        if (!files.length) return;
        
        // Show progress container
        const progressContainer = this.container.querySelector('#progress-container');
        progressContainer.style.display = 'block';
        
        // Upload each file
        for (const file of files) {
            await this.uploadFile(file);
        }
    }
    
    async uploadFile(file) {
        // Validate file
        if (!this.validateFile(file)) {
            return;
        }
        
        // Get metadata
        const metadata = this.getMetadata();
        
        try {
            // Create progress UI
            const progressId = this.createProgressUI(file);
            
            // Start upload
            const result = await this.uploader.upload(file, metadata);
            
            // Update UI on completion
            this.updateProgressUI(progressId, {
                progress: 100,
                status: 'completed',
                message: 'Upload completed successfully',
            });
            
            // Trigger success callback
            if (this.options.onSuccess) {
                this.options.onSuccess(result, file);
            }
            
        } catch (error) {
            console.error('Upload failed:', error);
            
            // Update UI on error
            if (progressId) {
                this.updateProgressUI(progressId, {
                    progress: 0,
                    status: 'error',
                    message: error.message,
                });
            }
            
            // Trigger error callback
            if (this.options.onError) {
                this.options.onError(error, file);
            }
        }
    }
    
    validateFile(file) {
        // Check file size
        const maxSize = this.options.maxFileSize || 500 * 1024 * 1024;
        if (file.size > maxSize) {
            alert(`File "${file.name}" is too large. Maximum size is ${this.uploader.formatFileSize(maxSize)}.`);
            return false;
        }
        
        // Check file extension
        const allowedExtensions = this.options.allowedExtensions;
        if (allowedExtensions && allowedExtensions.length > 0) {
            const extension = file.name.split('.').pop().toLowerCase();
            if (!allowedExtensions.includes(extension)) {
                alert(`File type "${extension}" is not allowed.`);
                return false;
            }
        }
        
        return true;
    }
    
    getMetadata() {
        return {
            title: this.container.querySelector('#document-title')?.value || '',
            description: this.container.querySelector('#document-description')?.value || '',
            tags: this.container.querySelector('#document-tags')?.value || '',
            folder_id: this.container.querySelector('#document-folder')?.value || '',
            project_id: this.container.querySelector('#document-project')?.value || '',
        };
    }
    
    createProgressUI(file) {
        const progressId = 'progress_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const uploadList = this.container.querySelector('#upload-list');
        
        const progressHtml = `
            <div class="upload-item" id="${progressId}">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="filename">${file.name}</span>
                    <span class="file-size">${this.uploader.formatFileSize(file.size)}</span>
                </div>
                <div class="progress mb-2">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="upload-status">
                    <small class="text-muted">Preparing upload...</small>
                </div>
            </div>
        `;
        
        uploadList.insertAdjacentHTML('beforeend', progressHtml);
        return progressId;
    }
    
    updateProgress(progressInfo) {
        const progressId = this.findProgressId(progressInfo.uploadId);
        if (!progressId) return;
        
        this.updateProgressUI(progressId, {
            progress: progressInfo.progress,
            uploadedBytes: progressInfo.uploadedBytes,
            totalBytes: progressInfo.totalBytes,
            averageSpeed: progressInfo.averageSpeed,
            timeRemaining: progressInfo.timeRemaining,
            status: progressInfo.status,
        });
    }
    
    updateProgressUI(progressId, data) {
        const progressElement = this.container.querySelector(`#${progressId}`);
        if (!progressElement) return;
        
        const progressBar = progressElement.querySelector('.progress-bar');
        const statusElement = progressElement.querySelector('.upload-status');
        
        // Update progress bar
        if (data.progress !== undefined) {
            progressBar.style.width = `${data.progress}%`;
            
            // Update progress bar color based on status
            progressBar.className = 'progress-bar';
            if (data.status === 'completed') {
                progressBar.classList.add('bg-success');
            } else if (data.status === 'error') {
                progressBar.classList.add('bg-danger');
            } else if (data.status === 'paused') {
                progressBar.classList.add('bg-warning');
            }
        }
        
        // Update status text
        let statusText = '';
        if (data.message) {
            statusText = data.message;
        } else if (data.status === 'uploading' && data.averageSpeed && data.timeRemaining) {
            statusText = `${this.uploader.formatFileSize(data.averageSpeed)}/s - ${this.uploader.formatTime(data.timeRemaining)} remaining`;
        } else if (data.status === 'completed') {
            statusText = 'Upload completed successfully';
        } else if (data.status === 'error') {
            statusText = 'Upload failed';
        }
        
        statusElement.innerHTML = `<small class="text-muted">${statusText}</small>`;
    }
    
    findProgressId(uploadId) {
        // This would need to be improved to properly map upload IDs to progress IDs
        // For now, assume only one upload at a time
        const uploadItems = this.container.querySelectorAll('.upload-item');
        return uploadItems.length > 0 ? uploadItems[uploadItems.length - 1].id : null;
    }
}

// Export for use in other modules
window.ChunkedUploader = ChunkedUploader;
window.FileUploadComponent = FileUploadComponent;

// Initialize upload components on page load
document.addEventListener('DOMContentLoaded', function() {
    // Auto-initialize upload components with data-chunked-upload attribute
    document.querySelectorAll('[data-chunked-upload]').forEach(element => {
        const options = JSON.parse(element.dataset.chunkedUpload || '{}');
        new FileUploadComponent(element, options);
    });
});