/**
 * HTMX Loading States and User Feedback System for CLEAR Application
 *
 * Provides comprehensive loading indicators and user feedback for all HTMX operations:
 * - Loading spinners and progress indicators
 * - User feedback messages and notifications
 * - Visual state management during async operations
 * - Accessibility support for screen readers
 * - Performance monitoring and optimization
 *
 * Addresses the 2507 missing indicators identified in the HTMX audit.
 */

class HTMXLoadingStates {
    constructor() {
        this.loadingElements = new Map();
        this.loadingTemplates = new Map();
        this.defaultConfig = {
            showSpinner: true,
            showProgress: false,
            showMessage: true,
            disableElement: true,
            fadeOpacity: 0.6,
            spinnerSize: 'sm',
            position: 'inline',
            timeout: 30000, // 30 seconds
            retryAttempts: 3
        };

        this.init();
    }

    init() {
        this.createLoadingTemplates();
        this.registerEventListeners();
        this.setupGlobalIndicators();
        this.initializeAccessibility();
    }

    createLoadingTemplates() {
        // Inline spinner template
        this.loadingTemplates.set('inline-spinner', `
            <div class="htmx-loading-inline d-inline-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status" aria-hidden="true"></div>
                <span class="loading-text">Loading...</span>
            </div>
        `);

        // Block spinner template
        this.loadingTemplates.set('block-spinner', `
            <div class="htmx-loading-block d-flex align-items-center justify-content-center p-3">
                <div class="spinner-border text-primary me-2" role="status" aria-hidden="true"></div>
                <span class="loading-text">Loading...</span>
            </div>
        `);

        // Overlay spinner template
        this.loadingTemplates.set('overlay-spinner', `
            <div class="htmx-loading-overlay">
                <div class="htmx-loading-content">
                    <div class="spinner-border text-primary mb-2" role="status" aria-hidden="true"></div>
                    <div class="loading-text">Loading...</div>
                </div>
            </div>
        `);

        // Progress bar template
        this.loadingTemplates.set('progress-bar', `
            <div class="htmx-loading-progress">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="loading-text">Processing...</span>
                    <span class="progress-percent">0%</span>
                </div>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar"
                         style="width: 0%"
                         aria-valuenow="0"
                         aria-valuemin="0"
                         aria-valuemax="100">
                    </div>
                </div>
            </div>
        `);

        // Skeleton loader template
        this.loadingTemplates.set('skeleton', `
            <div class="htmx-loading-skeleton">
                <div class="skeleton-line skeleton-line-title"></div>
                <div class="skeleton-line skeleton-line-text"></div>
                <div class="skeleton-line skeleton-line-text"></div>
                <div class="skeleton-line skeleton-line-short"></div>
            </div>
        `);

        // Pulse loader template
        this.loadingTemplates.set('pulse', `
            <div class="htmx-loading-pulse">
                <div class="pulse-dot"></div>
                <div class="pulse-dot"></div>
                <div class="pulse-dot"></div>
            </div>
        `);
    }

    registerEventListeners() {
        // Handle request start
        document.body.addEventListener('htmx:beforeRequest', (event) => {
            this.handleRequestStart(event);
        });

        // Handle request progress
        document.body.addEventListener('htmx:xhr:progress', (event) => {
            this.handleRequestProgress(event);
        });

        // Handle request completion
        document.body.addEventListener('htmx:afterRequest', (event) => {
            this.handleRequestComplete(event);
        });

        // Handle request errors
        document.body.addEventListener('htmx:responseError', (event) => {
            this.handleRequestError(event);
        });

        // Handle request timeout
        document.body.addEventListener('htmx:timeout', (event) => {
            this.handleRequestTimeout(event);
        });

        // Handle request abort
        document.body.addEventListener('htmx:abort', (event) => {
            this.handleRequestAbort(event);
        });
    }

    setupGlobalIndicators() {
        // Create global loading indicator container
        if (!document.getElementById('htmx-global-indicator')) {
            const globalIndicator = document.createElement('div');
            globalIndicator.id = 'htmx-global-indicator';
            globalIndicator.className = 'htmx-global-indicator';
            globalIndicator.innerHTML = `
                <div class="global-loading-content">
                    <div class="spinner-border text-primary" role="status" aria-hidden="true"></div>
                    <div class="loading-message">Processing request...</div>
                </div>
            `;
            document.body.appendChild(globalIndicator);
        }

        // Create toast container for notifications
        if (!document.getElementById('htmx-toast-container')) {
            const toastContainer = document.createElement('div');
            toastContainer.id = 'htmx-toast-container';
            toastContainer.className = 'htmx-toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }
    }

    initializeAccessibility() {
        // Create live region for screen reader announcements
        if (!document.getElementById('htmx-live-region')) {
            const liveRegion = document.createElement('div');
            liveRegion.id = 'htmx-live-region';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'visually-hidden';
            document.body.appendChild(liveRegion);
        }
    }

    handleRequestStart(event) {
        const element = event.detail.elt;
        const config = this.getElementConfig(element);

        // Store original content and state
        const elementId = this.getElementId(element);
        this.loadingElements.set(elementId, {
            element: element,
            originalContent: element.innerHTML,
            originalDisabled: element.disabled,
            startTime: Date.now(),
            config: config
        });

        // Show loading state
        this.showLoadingState(element, config);

        // Announce to screen readers
        this.announceToScreenReader(`Loading ${config.message || 'content'}...`);

        // Set timeout for long-running requests
        if (config.timeout > 0) {
            setTimeout(() => {
                if (this.loadingElements.has(elementId)) {
                    this.handleRequestTimeout({ detail: { elt: element } });
                }
            }, config.timeout);
        }
    }

    handleRequestProgress(event) {
        const element = event.detail.elt;
        const progress = event.detail.progress;

        if (progress && progress.lengthComputable) {
            const percent = Math.round((progress.loaded / progress.total) * 100);
            this.updateProgress(element, percent);
        }
    }

    handleRequestComplete(event) {
        const element = event.detail.elt;
        const elementId = this.getElementId(element);
        const loadingData = this.loadingElements.get(elementId);

        if (loadingData) {
            const duration = Date.now() - loadingData.startTime;

            // Hide loading state
            this.hideLoadingState(element, loadingData);

            // Show success feedback if configured
            if (loadingData.config.showSuccess) {
                this.showSuccessFeedback(element, duration);
            }

            // Announce completion to screen readers
            this.announceToScreenReader(`Content loaded successfully`);

            // Clean up
            this.loadingElements.delete(elementId);
        }
    }

    handleRequestError(event) {
        const element = event.detail.elt;
        const elementId = this.getElementId(element);
        const loadingData = this.loadingElements.get(elementId);

        if (loadingData) {
            // Hide loading state
            this.hideLoadingState(element, loadingData);

            // Show error feedback
            this.showErrorFeedback(element, event.detail.xhr);

            // Announce error to screen readers
            this.announceToScreenReader(`Error loading content. Please try again.`);

            // Clean up
            this.loadingElements.delete(elementId);
        }
    }

    handleRequestTimeout(event) {
        const element = event.detail.elt;
        const elementId = this.getElementId(element);
        const loadingData = this.loadingElements.get(elementId);

        if (loadingData) {
            // Hide loading state
            this.hideLoadingState(element, loadingData);

            // Show timeout feedback
            this.showTimeoutFeedback(element);

            // Announce timeout to screen readers
            this.announceToScreenReader(`Request timed out. Please try again.`);

            // Clean up
            this.loadingElements.delete(elementId);
        }
    }

    handleRequestAbort(event) {
        const element = event.detail.elt;
        const elementId = this.getElementId(element);
        const loadingData = this.loadingElements.get(elementId);

        if (loadingData) {
            // Hide loading state
            this.hideLoadingState(element, loadingData);

            // Clean up
            this.loadingElements.delete(elementId);
        }
    }

    showLoadingState(element, config) {
        // Disable element if configured
        if (config.disableElement && element.tagName === 'BUTTON') {
            element.disabled = true;
        }

        // Add loading class
        element.classList.add('htmx-loading');

        // Reduce opacity if configured
        if (config.fadeOpacity < 1) {
            element.style.opacity = config.fadeOpacity;
        }

        // Show loading indicator based on type
        switch (config.type) {
            case 'inline':
                this.showInlineLoader(element, config);
                break;
            case 'overlay':
                this.showOverlayLoader(element, config);
                break;
            case 'replace':
                this.showReplaceLoader(element, config);
                break;
            case 'progress':
                this.showProgressLoader(element, config);
                break;
            default:
                this.showInlineLoader(element, config);
        }

        // Show global indicator for important operations
        if (config.showGlobal) {
            this.showGlobalIndicator(config.message);
        }
    }

    showInlineLoader(element, config) {
        const template = config.template || 'inline-spinner';
        const loaderHtml = this.loadingTemplates.get(template);

        if (element.tagName === 'BUTTON') {
            // For buttons, replace content but keep structure
            const originalText = element.textContent.trim();
            element.innerHTML = loaderHtml.replace('Loading...', config.message || originalText);
        } else {
            // For other elements, add loader alongside content
            const loader = document.createElement('div');
            loader.className = 'htmx-inline-loader';
            loader.innerHTML = loaderHtml.replace('Loading...', config.message || 'Loading...');
            element.insertBefore(loader, element.firstChild);
        }
    }

    showOverlayLoader(element, config) {
        const overlay = document.createElement('div');
        overlay.className = 'htmx-loading-overlay-container';
        overlay.innerHTML = this.loadingTemplates.get('overlay-spinner')
            .replace('Loading...', config.message || 'Loading...');

        // Position overlay relative to element
        element.style.position = 'relative';
        element.appendChild(overlay);
    }

    showReplaceLoader(element, config) {
        const template = config.template || 'block-spinner';
        element.innerHTML = this.loadingTemplates.get(template)
            .replace('Loading...', config.message || 'Loading...');
    }

    showProgressLoader(element, config) {
        element.innerHTML = this.loadingTemplates.get('progress-bar')
            .replace('Processing...', config.message || 'Processing...');
    }

    showGlobalIndicator(message) {
        const indicator = document.getElementById('htmx-global-indicator');
        if (indicator) {
            indicator.querySelector('.loading-message').textContent = message || 'Processing request...';
            indicator.classList.add('show');
        }
    }

    hideLoadingState(element, loadingData) {
        // Restore original content
        if (loadingData.config.type === 'replace') {
            element.innerHTML = loadingData.originalContent;
        } else if (loadingData.config.type === 'inline') {
            const loader = element.querySelector('.htmx-inline-loader');
            if (loader) {
                loader.remove();
            }
            if (element.tagName === 'BUTTON') {
                element.innerHTML = loadingData.originalContent;
            }
        } else if (loadingData.config.type === 'overlay') {
            const overlay = element.querySelector('.htmx-loading-overlay-container');
            if (overlay) {
                overlay.remove();
            }
        }

        // Restore element state
        element.classList.remove('htmx-loading');
        element.style.opacity = '';

        if (loadingData.config.disableElement) {
            element.disabled = loadingData.originalDisabled;
        }

        // Hide global indicator
        this.hideGlobalIndicator();
    }

    hideGlobalIndicator() {
        const indicator = document.getElementById('htmx-global-indicator');
        if (indicator) {
            indicator.classList.remove('show');
        }
    }

    updateProgress(element, percent) {
        const progressBar = element.querySelector('.progress-bar');
        const progressPercent = element.querySelector('.progress-percent');

        if (progressBar) {
            progressBar.style.width = `${percent}%`;
            progressBar.setAttribute('aria-valuenow', percent);
        }

        if (progressPercent) {
            progressPercent.textContent = `${percent}%`;
        }
    }

    showSuccessFeedback(element, duration) {
        this.showToast('success', `Operation completed successfully in ${duration}ms`, 3000);
    }

    showErrorFeedback(element, xhr) {
        const message = xhr.status === 0 ? 'Network error occurred' : `Error ${xhr.status}: ${xhr.statusText}`;
        this.showToast('error', message, 5000);
    }

    showTimeoutFeedback(element) {
        this.showToast('warning', 'Request timed out. Please try again.', 5000);
    }

    showToast(type, message, duration = 3000) {
        const container = document.getElementById('htmx-toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'success'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : 'check-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        container.appendChild(toast);

        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toast, { delay: duration });
        bsToast.show();

        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    announceToScreenReader(message) {
        const liveRegion = document.getElementById('htmx-live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
        }
    }

    getElementConfig(element) {
        const config = { ...this.defaultConfig };

        // Read configuration from data attributes
        if (element.dataset.loadingType) {
            config.type = element.dataset.loadingType;
        }
        if (element.dataset.loadingMessage) {
            config.message = element.dataset.loadingMessage;
        }
        if (element.dataset.loadingTemplate) {
            config.template = element.dataset.loadingTemplate;
        }
        if (element.dataset.loadingDisable !== undefined) {
            config.disableElement = element.dataset.loadingDisable !== 'false';
        }
        if (element.dataset.loadingGlobal !== undefined) {
            config.showGlobal = element.dataset.loadingGlobal !== 'false';
        }
        if (element.dataset.loadingSuccess !== undefined) {
            config.showSuccess = element.dataset.loadingSuccess !== 'false';
        }
        if (element.dataset.loadingTimeout) {
            config.timeout = parseInt(element.dataset.loadingTimeout);
        }

        return config;
    }

    getElementId(element) {
        if (element.id) {
            return element.id;
        }

        // Generate unique ID based on element properties
        const tagName = element.tagName.toLowerCase();
        const className = element.className.replace(/\s+/g, '-');
        const textContent = element.textContent.trim().substring(0, 20).replace(/\s+/g, '-');

        return `htmx-${tagName}-${className}-${textContent}-${Date.now()}`;
    }
}

// Initialize loading states system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.htmxLoadingStates = new HTMXLoadingStates();
});

// Add CSS styles for loading states
const style = document.createElement('style');
style.textContent = `
    /* HTMX Loading States Styles */
    .htmx-loading {
        position: relative;
    }

    .htmx-loading-inline {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .htmx-loading-block {
        padding: 2rem 1rem;
        text-align: center;
    }

    .htmx-loading-overlay-container {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .htmx-loading-content {
        text-align: center;
        padding: 1rem;
    }

    .htmx-loading-progress {
        padding: 1rem;
    }

    .htmx-loading-skeleton {
        padding: 1rem;
    }

    .skeleton-line {
        height: 1rem;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 0.25rem;
        margin-bottom: 0.5rem;
    }

    .skeleton-line-title {
        height: 1.5rem;
        width: 60%;
    }

    .skeleton-line-text {
        width: 100%;
    }

    .skeleton-line-short {
        width: 40%;
    }

    @keyframes skeleton-loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }

    .htmx-loading-pulse {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.25rem;
        padding: 1rem;
    }

    .pulse-dot {
        width: 0.5rem;
        height: 0.5rem;
        background: #007bff;
        border-radius: 50%;
        animation: pulse-animation 1.4s infinite ease-in-out both;
    }

    .pulse-dot:nth-child(1) { animation-delay: -0.32s; }
    .pulse-dot:nth-child(2) { animation-delay: -0.16s; }
    .pulse-dot:nth-child(3) { animation-delay: 0s; }

    @keyframes pulse-animation {
        0%, 80%, 100% {
            transform: scale(0);
        }
        40% {
            transform: scale(1);
        }
    }

    .htmx-global-indicator {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        z-index: 9999;
        display: none;
        text-align: center;
    }

    .htmx-global-indicator.show {
        display: block;
    }

    .global-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .htmx-toast-container {
        z-index: 9999;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .htmx-loading-block {
            padding: 1rem 0.5rem;
            font-size: 0.875rem;
        }

        .htmx-global-indicator {
            padding: 1.5rem;
            margin: 1rem;
            left: 1rem;
            right: 1rem;
            transform: translateY(-50%);
        }
    }

    /* Dark mode support */
    [data-theme="dark"] .htmx-loading-overlay-container {
        background: rgba(0, 0, 0, 0.9);
        color: white;
    }

    [data-theme="dark"] .skeleton-line {
        background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
        background-size: 200% 100%;
    }
`;

document.head.appendChild(style);
