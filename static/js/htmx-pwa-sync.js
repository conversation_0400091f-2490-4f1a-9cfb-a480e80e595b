/**
 * HTMX PWA Synchronization Manager
 * 
 * Handles online/offline synchronization between HTMX requests and service worker
 * background sync, ensuring seamless user experience across connection states.
 */

class HTMXPWASync {
    constructor() {
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.syncInProgress = false;
        this.retryAttempts = new Map();
        this.maxRetries = 3;
        
        this.init();
    }

    init() {
        // Set up HTMX event listeners
        this.setupHTMXEvents();
        
        // Set up connection monitoring
        this.setupConnectionMonitoring();
        
        // Set up service worker communication
        this.setupServiceWorkerCommunication();
        
        // Initialize sync indicators
        this.createSyncIndicators();
        
        console.log('HTMX PWA Sync Manager initialized');
    }

    setupHTMXEvents() {
        // Intercept HTMX requests for offline handling
        document.addEventListener('htmx:beforeRequest', (event) => {
            if (!this.isOnline) {
                // Queue the request for later sync
                this.queueRequest(event);
                event.preventDefault();
                
                // Show offline notification
                this.showOfflineNotification(event.detail.elt);
            }
        });

        // Handle successful HTMX requests
        document.addEventListener('htmx:afterRequest', (event) => {
            const xhr = event.detail.xhr;
            
            if (xhr.status >= 200 && xhr.status < 300) {
                // Request succeeded
                this.handleSuccessfulRequest(event);
            } else if (xhr.status === 0 || xhr.status >= 500) {
                // Network error or server error - queue for retry
                this.queueRequest(event);
            }
        });

        // Handle HTMX errors
        document.addEventListener('htmx:responseError', (event) => {
            // Queue failed requests for retry
            this.queueRequest(event);
        });

        // Handle connection restored events
        document.addEventListener('connection-restored', () => {
            this.processSyncQueue();
        });
    }

    setupConnectionMonitoring() {
        // Monitor online/offline events
        window.addEventListener('online', () => {
            console.log('Connection restored');
            this.isOnline = true;
            this.updateConnectionStatus();
            this.processSyncQueue();
        });

        window.addEventListener('offline', () => {
            console.log('Connection lost');
            this.isOnline = false;
            this.updateConnectionStatus();
        });

        // Periodic connectivity check
        setInterval(() => {
            this.checkConnectivity();
        }, 30000); // Check every 30 seconds
    }

    setupServiceWorkerCommunication() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                const { type, data } = event.data;
                
                switch (type) {
                    case 'connection-restored':
                        this.handleConnectionRestored();
                        break;
                    case 'htmx-sync-start':
                        this.handleSyncStart();
                        break;
                    case 'htmx-sync-complete':
                        this.handleSyncComplete();
                        break;
                    case 'sw-error':
                        this.handleServiceWorkerError(data);
                        break;
                }
            });
        }
    }

    queueRequest(event) {
        const element = event.detail.elt;
        const requestInfo = this.extractRequestInfo(event);
        
        if (!requestInfo) return;

        // Add to sync queue
        const queueItem = {
            id: this.generateRequestId(),
            timestamp: Date.now(),
            element: element,
            request: requestInfo,
            retries: 0,
            originalEvent: event
        };

        this.syncQueue.push(queueItem);
        this.saveSyncQueue();
        
        // Show queued state
        this.showQueuedState(element, queueItem);
        
        console.log('Request queued for sync:', queueItem);
    }

    extractRequestInfo(event) {
        const element = event.detail.elt;
        const xhr = event.detail.xhr;
        
        // Extract HTMX request information
        return {
            method: element.getAttribute('hx-post') ? 'POST' : 
                   element.getAttribute('hx-put') ? 'PUT' :
                   element.getAttribute('hx-delete') ? 'DELETE' : 'GET',
            url: this.getRequestURL(element),
            headers: this.getRequestHeaders(element),
            body: this.getRequestBody(element, event),
            target: element.getAttribute('hx-target'),
            swap: element.getAttribute('hx-swap'),
            trigger: element.getAttribute('hx-trigger')
        };
    }

    getRequestURL(element) {
        return element.getAttribute('hx-post') ||
               element.getAttribute('hx-get') ||
               element.getAttribute('hx-put') ||
               element.getAttribute('hx-delete') ||
               element.action ||
               element.href;
    }

    getRequestHeaders(element) {
        const headers = {
            'HX-Request': 'true',
            'X-CSRFToken': this.getCSRFToken()
        };

        // Add custom headers from hx-headers
        const hxHeaders = element.getAttribute('hx-headers');
        if (hxHeaders) {
            try {
                const customHeaders = JSON.parse(hxHeaders);
                Object.assign(headers, customHeaders);
            } catch (e) {
                console.warn('Failed to parse hx-headers:', e);
            }
        }

        return headers;
    }

    getRequestBody(element, event) {
        if (element.tagName === 'FORM') {
            return new FormData(element);
        }

        // Get hx-vals data
        const hxVals = element.getAttribute('hx-vals');
        if (hxVals) {
            try {
                return JSON.stringify(JSON.parse(hxVals));
            } catch (e) {
                console.warn('Failed to parse hx-vals:', e);
            }
        }

        return null;
    }

    async processSyncQueue() {
        if (this.syncInProgress || !this.isOnline || this.syncQueue.length === 0) {
            return;
        }

        this.syncInProgress = true;
        this.showSyncProgress();

        const queueCopy = [...this.syncQueue];
        this.syncQueue = [];

        for (const item of queueCopy) {
            try {
                await this.replayRequest(item);
                this.handleRequestSuccess(item);
            } catch (error) {
                this.handleRequestFailure(item, error);
            }
        }

        this.syncInProgress = false;
        this.hideSyncProgress();
        this.saveSyncQueue();
    }

    async replayRequest(item) {
        const { request, element } = item;

        // Create fetch request
        const fetchOptions = {
            method: request.method,
            headers: request.headers,
            body: request.body
        };

        const response = await fetch(request.url, fetchOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Process HTMX response
        const responseText = await response.text();
        this.processHTMXResponse(element, responseText, request);

        return response;
    }

    processHTMXResponse(element, responseText, request) {
        // Find target element
        const targetSelector = request.target || '#' + element.id;
        const targetElement = targetSelector === 'this' ? element : 
                            document.querySelector(targetSelector);

        if (targetElement) {
            // Apply swap strategy
            const swapStrategy = request.swap || 'innerHTML';
            this.applySwap(targetElement, responseText, swapStrategy);

            // Trigger HTMX events
            this.triggerHTMXEvent(targetElement, 'htmx:afterSwap');
        }
    }

    applySwap(targetElement, content, strategy) {
        switch (strategy) {
            case 'innerHTML':
                targetElement.innerHTML = content;
                break;
            case 'outerHTML':
                targetElement.outerHTML = content;
                break;
            case 'beforebegin':
                targetElement.insertAdjacentHTML('beforebegin', content);
                break;
            case 'afterbegin':
                targetElement.insertAdjacentHTML('afterbegin', content);
                break;
            case 'beforeend':
                targetElement.insertAdjacentHTML('beforeend', content);
                break;
            case 'afterend':
                targetElement.insertAdjacentHTML('afterend', content);
                break;
            default:
                targetElement.innerHTML = content;
        }
    }

    triggerHTMXEvent(element, eventName, detail = {}) {
        const event = new CustomEvent(eventName, {
            detail: detail,
            bubbles: true
        });
        element.dispatchEvent(event);
    }

    handleRequestSuccess(item) {
        this.clearQueuedState(item.element);
        this.showSyncSuccess(item.element);
        
        // Remove from retry tracking
        this.retryAttempts.delete(item.id);
        
        console.log('Request synced successfully:', item.id);
    }

    handleRequestFailure(item, error) {
        const retries = this.retryAttempts.get(item.id) || 0;
        
        if (retries < this.maxRetries) {
            // Retry later
            this.retryAttempts.set(item.id, retries + 1);
            item.retries = retries + 1;
            this.syncQueue.push(item);
            
            console.log(`Request retry ${retries + 1}/${this.maxRetries}:`, item.id);
        } else {
            // Max retries reached
            this.handleRequestPermanentFailure(item, error);
        }
    }

    handleSuccessfulRequest(event) {
        // Handle successful HTMX request
        const element = event.detail.elt;
        const xhr = event.detail.xhr;
        
        // Log successful request
        console.log('HTMX request successful:', xhr.responseURL);
        
        // Clear any pending retry attempts for this element
        const syncId = element.getAttribute('data-sync-id');
        if (syncId) {
            this.retryAttempts.delete(syncId);
            this.clearQueuedState(element);
        }
        
        // Update performance metrics if available
        if (window.CLEAR_PERFORMANCE_MONITOR) {
            window.CLEAR_PERFORMANCE_MONITOR.recordHTMXSuccess(xhr);
        }
    }

    handleRequestPermanentFailure(item, error) {
        this.showSyncError(item.element, error);
        this.retryAttempts.delete(item.id);
        
        console.error('Request permanently failed:', item.id, error);
    }

    // UI State Management
    createSyncIndicators() {
        // Create global sync status indicator
        const syncStatus = document.createElement('div');
        syncStatus.id = 'htmx-sync-status';
        syncStatus.className = 'htmx-sync-status';
        syncStatus.style.display = 'none';
        document.body.appendChild(syncStatus);

        // Create connection status indicator
        const connectionStatus = document.createElement('div');
        connectionStatus.id = 'connection-status';
        connectionStatus.className = 'connection-status';
        this.updateConnectionStatusElement(connectionStatus);
        document.body.appendChild(connectionStatus);
    }

    showOfflineNotification(element) {
        const notification = document.createElement('div');
        notification.className = 'htmx-offline-notification';
        notification.innerHTML = `
            <span class="offline-icon">📡</span>
            <span>Saved for when you're back online</span>
        `;
        
        element.parentNode.insertBefore(notification, element.nextSibling);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    showQueuedState(element, item) {
        element.classList.add('htmx-queued');
        element.setAttribute('data-sync-id', item.id);
        
        // Add visual indicator
        const indicator = document.createElement('span');
        indicator.className = 'htmx-queue-indicator';
        indicator.innerHTML = '⏳';
        indicator.title = 'Queued for sync';
        element.appendChild(indicator);
    }

    clearQueuedState(element) {
        element.classList.remove('htmx-queued', 'htmx-syncing');
        element.removeAttribute('data-sync-id');
        
        const indicator = element.querySelector('.htmx-queue-indicator, .htmx-sync-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    showSyncProgress() {
        const statusElement = document.getElementById('htmx-sync-status');
        if (statusElement) {
            statusElement.innerHTML = `
                <span class="sync-icon">🔄</span>
                <span>Syncing ${this.syncQueue.length} items...</span>
            `;
            statusElement.style.display = 'block';
        }

        // Mark queued elements as syncing
        this.syncQueue.forEach(item => {
            item.element.classList.add('htmx-syncing');
        });
    }

    hideSyncProgress() {
        const statusElement = document.getElementById('htmx-sync-status');
        if (statusElement) {
            statusElement.style.display = 'none';
        }
    }

    showSyncSuccess(element) {
        const indicator = document.createElement('span');
        indicator.className = 'htmx-success-indicator';
        indicator.innerHTML = '✅';
        indicator.title = 'Synced successfully';
        element.appendChild(indicator);
        
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 2000);
    }

    showSyncError(element, error) {
        const indicator = document.createElement('span');
        indicator.className = 'htmx-error-indicator';
        indicator.innerHTML = '❌';
        indicator.title = `Sync failed: ${error.message}`;
        element.appendChild(indicator);
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            this.updateConnectionStatusElement(statusElement);
        }
    }

    updateConnectionStatusElement(element) {
        element.className = `connection-status ${this.isOnline ? 'online' : 'offline'}`;
        element.innerHTML = `
            <span class="connection-icon">${this.isOnline ? '🟢' : '🔴'}</span>
            <span class="connection-text">${this.isOnline ? 'Online' : 'Offline'}</span>
        `;
    }

    // Service Worker Communication Handlers
    handleConnectionRestored() {
        console.log('Service worker reports connection restored');
        this.isOnline = true;
        this.updateConnectionStatus();
        
        // Trigger custom event for HTMX elements
        document.dispatchEvent(new CustomEvent('connection-restored'));
    }

    handleSyncStart() {
        console.log('Background sync started');
        this.showSyncProgress();
    }

    handleSyncComplete() {
        console.log('Background sync completed');
        this.hideSyncProgress();
        
        // Refresh any HTMX elements that might need updates
        this.refreshAfterSync();
    }

    handleServiceWorkerError(error) {
        console.error('Service worker error:', error);
        // Handle service worker errors gracefully
    }

    // Utility Methods
    generateRequestId() {
        return 'htmx-req-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    getCSRFToken() {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        return csrfToken ? csrfToken.value : '';
    }

    saveSyncQueue() {
        // Save sync queue to localStorage for persistence
        const queueData = this.syncQueue.map(item => ({
            id: item.id,
            timestamp: item.timestamp,
            request: item.request,
            retries: item.retries
        }));
        
        localStorage.setItem('htmx-sync-queue', JSON.stringify(queueData));
    }

    loadSyncQueue() {
        // Load sync queue from localStorage
        const queueData = localStorage.getItem('htmx-sync-queue');
        if (queueData) {
            try {
                const parsedQueue = JSON.parse(queueData);
                // Note: Elements won't be available on page load, so we'll need to reattach them
                console.log('Loaded sync queue from storage:', parsedQueue.length, 'items');
            } catch (e) {
                console.warn('Failed to load sync queue:', e);
            }
        }
    }

    checkConnectivity() {
        // Perform actual connectivity check beyond navigator.onLine
        fetch('/api/ping', { 
            method: 'HEAD',
            cache: 'no-cache'
        })
        .then(() => {
            if (!this.isOnline) {
                this.isOnline = true;
                this.updateConnectionStatus();
                this.handleConnectionRestored();
            }
        })
        .catch(() => {
            if (this.isOnline) {
                this.isOnline = false;
                this.updateConnectionStatus();
            }
        });
    }

    refreshAfterSync() {
        // Refresh elements that have hx-trigger="connection-restored"
        const elementsToRefresh = document.querySelectorAll('[hx-trigger*="connection-restored"]');
        elementsToRefresh.forEach(element => {
            if (typeof htmx !== 'undefined') {
                htmx.trigger(element, 'connection-restored');
            }
        });
    }

    // Public API
    getQueueStatus() {
        return {
            isOnline: this.isOnline,
            queueLength: this.syncQueue.length,
            syncInProgress: this.syncInProgress,
            retryAttempts: Array.from(this.retryAttempts.entries())
        };
    }

    forcSync() {
        if (this.isOnline) {
            this.processSyncQueue();
        }
    }

    clearQueue() {
        this.syncQueue = [];
        this.retryAttempts.clear();
        this.saveSyncQueue();
    }
}

// CSS for sync indicators
const htmxSyncCSS = `
    .htmx-sync-status {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .connection-status {
        position: fixed;
        top: 20px;
        left: 20px;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
    }

    .connection-status.online {
        background: rgba(140, 198, 63, 0.1);
        color: #7ab534;
        border: 1px solid rgba(140, 198, 63, 0.3);
    }

    .connection-status.offline {
        background: rgba(255, 127, 0, 0.1);
        color: #ff7f00;
        border: 1px solid rgba(255, 127, 0, 0.3);
    }

    .htmx-queued {
        position: relative;
        opacity: 0.7;
    }

    .htmx-syncing {
        position: relative;
    }

    .htmx-queue-indicator,
    .htmx-sync-indicator,
    .htmx-success-indicator,
    .htmx-error-indicator {
        position: absolute;
        top: -8px;
        right: -8px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        z-index: 1;
    }

    .htmx-offline-notification {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        background: rgba(255, 127, 0, 0.1);
        color: #ff7f00;
        border: 1px solid rgba(255, 127, 0, 0.3);
        border-radius: 6px;
        font-size: 13px;
        margin-top: 8px;
        animation: slideIn 0.3s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 768px) {
        .htmx-sync-status,
        .connection-status {
            font-size: 12px;
            padding: 8px 12px;
        }
        
        .htmx-sync-status {
            right: 10px;
            top: 10px;
        }
        
        .connection-status {
            left: 10px;
            top: 10px;
        }
    }
`;

// Inject CSS
const htmxSyncStyle = document.createElement('style');
htmxSyncStyle.textContent = htmxSyncCSS;
document.head.appendChild(htmxSyncStyle);

// Initialize HTMX PWA Sync when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.htmxPWASync = new HTMXPWASync();
    });
} else {
    window.htmxPWASync = new HTMXPWASync();
}

// Export for use in other scripts
window.HTMXPWASync = HTMXPWASync;