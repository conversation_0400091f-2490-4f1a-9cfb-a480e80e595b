/**
 * Core Web Vitals Performance Monitor - CLEAR Platform
 * 
 * Measures and reports performance metrics for optimization tracking:
 * - Largest Contentful Paint (LCP)
 * - First Input Delay (FID) 
 * - Cumulative Layout Shift (CLS)
 * - First Contentful Paint (FCP)
 * - Time to First Byte (TTFB)
 */

class PerformanceVitals {
    constructor() {
        this.metrics = {};
        this.observers = [];
        this.TARGET_LCP = 2500; // 2.5s
        this.TARGET_FID = 100;  // 100ms
        this.TARGET_CLS = 0.1;  // 0.1
        this.TARGET_FCP = 1800; // 1.8s
        this.TARGET_TTFB = 600; // 600ms
        
        this.init();
    }

    init() {
        // Only run in supporting browsers
        if (!('performance' in window) || !('PerformanceObserver' in window)) {
            console.warn('Performance monitoring not supported in this browser');
            return;
        }

        this.measureTTFB();
        this.measureLCP();
        this.measureFID();
        this.measureCLS();
        this.measureFCP();
        
        // Report metrics after page load
        window.addEventListener('load', () => {
            setTimeout(() => this.reportMetrics(), 1000);
        });
    }

    /**
     * Measure Time to First Byte (TTFB)
     */
    measureTTFB() {
        try {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                const ttfb = navigation.responseStart - navigation.fetchStart;
                this.metrics.ttfb = Math.round(ttfb);
                this.logMetric('TTFB', ttfb, this.TARGET_TTFB);
            }
        } catch (e) {
            console.warn('Failed to measure TTFB:', e);
        }
    }

    /**
     * Measure Largest Contentful Paint (LCP)
     */
    measureLCP() {
        try {
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                const lcp = Math.round(lastEntry.startTime);
                
                this.metrics.lcp = lcp;
                this.logMetric('LCP', lcp, this.TARGET_LCP);
            });
            
            observer.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.push(observer);
        } catch (e) {
            console.warn('Failed to measure LCP:', e);
        }
    }

    /**
     * Measure First Input Delay (FID)
     */
    measureFID() {
        try {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    const fid = Math.round(entry.processingStart - entry.startTime);
                    this.metrics.fid = fid;
                    this.logMetric('FID', fid, this.TARGET_FID);
                });
            });
            
            observer.observe({ entryTypes: ['first-input'] });
            this.observers.push(observer);
        } catch (e) {
            console.warn('Failed to measure FID:', e);
        }
    }

    /**
     * Measure Cumulative Layout Shift (CLS)
     */
    measureCLS() {
        try {
            let clsValue = 0;
            
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                    }
                });
                
                this.metrics.cls = Math.round(clsValue * 1000) / 1000;
                this.logMetric('CLS', this.metrics.cls, this.TARGET_CLS);
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
            this.observers.push(observer);
        } catch (e) {
            console.warn('Failed to measure CLS:', e);
        }
    }

    /**
     * Measure First Contentful Paint (FCP)
     */
    measureFCP() {
        try {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.name === 'first-contentful-paint') {
                        const fcp = Math.round(entry.startTime);
                        this.metrics.fcp = fcp;
                        this.logMetric('FCP', fcp, this.TARGET_FCP);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['paint'] });
            this.observers.push(observer);
        } catch (e) {
            console.warn('Failed to measure FCP:', e);
        }
    }

    /**
     * Log individual metric with pass/fail status
     */
    logMetric(name, value, target) {
        const status = value <= target ? '✅ PASS' : '❌ FAIL';
        const unit = name === 'CLS' ? '' : 'ms';
        
        console.log(`${name}: ${value}${unit} (target: ${target}${unit}) ${status}`);
        
        // Store for potential analytics
        this.storeMetric(name, value, target, value <= target);
    }

    /**
     * Store metric for analytics or reporting
     */
    storeMetric(name, value, target, passed) {
        const metric = {
            name,
            value,
            target,
            passed,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        // Store in localStorage for debugging
        try {
            const stored = JSON.parse(localStorage.getItem('clear-performance-metrics') || '[]');
            stored.push(metric);
            
            // Keep only last 50 measurements
            if (stored.length > 50) {
                stored.splice(0, stored.length - 50);
            }
            
            localStorage.setItem('clear-performance-metrics', JSON.stringify(stored));
        } catch (e) {
            console.warn('Failed to store performance metric:', e);
        }

        // Send to analytics if available
        this.sendToAnalytics(metric);
    }

    /**
     * Send performance data to analytics
     */
    sendToAnalytics(metric) {
        // Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', 'performance_metric', {
                metric_name: metric.name,
                metric_value: metric.value,
                metric_target: metric.target,
                metric_passed: metric.passed,
                custom_parameter_1: window.location.pathname
            });
        }

        // Custom analytics endpoint (if implemented)
        if (window.clearAnalytics && typeof window.clearAnalytics.track === 'function') {
            window.clearAnalytics.track('performance_metric', metric);
        }
    }

    /**
     * Generate comprehensive performance report
     */
    reportMetrics() {
        const report = {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            metrics: this.metrics,
            summary: this.generateSummary()
        };

        console.group('🚀 CLEAR Performance Report');
        console.log('Timestamp:', report.timestamp);
        console.log('URL:', report.url);
        console.log('Metrics:', report.metrics);
        console.log('Summary:', report.summary);
        console.groupEnd();

        // Store complete report
        try {
            sessionStorage.setItem('clear-performance-report', JSON.stringify(report));
        } catch (e) {
            console.warn('Failed to store performance report:', e);
        }

        return report;
    }

    /**
     * Generate performance summary
     */
    generateSummary() {
        const passed = [];
        const failed = [];

        if (this.metrics.ttfb <= this.TARGET_TTFB) passed.push('TTFB');
        else failed.push('TTFB');

        if (this.metrics.fcp <= this.TARGET_FCP) passed.push('FCP');
        else failed.push('FCP');

        if (this.metrics.lcp <= this.TARGET_LCP) passed.push('LCP');
        else failed.push('LCP');

        if (this.metrics.fid <= this.TARGET_FID) passed.push('FID');
        else failed.push('FID');

        if (this.metrics.cls <= this.TARGET_CLS) passed.push('CLS');
        else failed.push('CLS');

        const totalMetrics = passed.length + failed.length;
        const score = Math.round((passed.length / totalMetrics) * 100);

        return {
            score,
            passed,
            failed,
            grade: this.getPerformanceGrade(score)
        };
    }

    /**
     * Get performance grade based on score
     */
    getPerformanceGrade(score) {
        if (score >= 90) return 'A+';
        if (score >= 80) return 'A';
        if (score >= 70) return 'B';
        if (score >= 60) return 'C';
        if (score >= 50) return 'D';
        return 'F';
    }

    /**
     * Get stored performance history
     */
    getPerformanceHistory() {
        try {
            return JSON.parse(localStorage.getItem('clear-performance-metrics') || '[]');
        } catch (e) {
            console.warn('Failed to get performance history:', e);
            return [];
        }
    }

    /**
     * Clear performance history
     */
    clearPerformanceHistory() {
        try {
            localStorage.removeItem('clear-performance-metrics');
            sessionStorage.removeItem('clear-performance-report');
            console.log('Performance history cleared');
        } catch (e) {
            console.warn('Failed to clear performance history:', e);
        }
    }

    /**
     * Cleanup observers
     */
    cleanup() {
        this.observers.forEach(observer => {
            try {
                observer.disconnect();
            } catch (e) {
                console.warn('Failed to disconnect performance observer:', e);
            }
        });
        this.observers = [];
    }
}

// Initialize performance monitoring
window.performanceVitals = new PerformanceVitals();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.performanceVitals) {
        window.performanceVitals.cleanup();
    }
});

// Debug utilities (development only)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.performanceDebug = {
        getReport: () => window.performanceVitals.reportMetrics(),
        getHistory: () => window.performanceVitals.getPerformanceHistory(),
        clearHistory: () => window.performanceVitals.clearPerformanceHistory(),
        getMetrics: () => window.performanceVitals.metrics
    };
}

export default PerformanceVitals;