/**
 * Drag and Drop File Upload Component
 * 
 * Features:
 * - Multiple file support with progress tracking
 * - Accessibility compliant (ARIA, keyboard navigation, screen reader support)
 * - File type and size validation
 * - Real-time upload progress with speed and ETA
 * - Chunked upload support for large files
 * - Auto-retry for failed uploads
 * - HTMX integration for server communication
 * - Touch device support
 * 
 * Dependencies:
 * - Bootstrap 5.3+
 * - HTMX (for server communication)
 * 
 * Usage:
 * new DragDropUpload('.drag-drop-upload-container', options);
 */

class DragDropUpload {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        if (!this.container) {
            console.error('DragDropUpload: Container not found');
            return;
        }

        // Configuration
        this.config = {
            maxFiles: parseInt(this.container.dataset.maxFiles) || 5,
            maxSizeMB: parseInt(this.container.dataset.maxSize) || 100,
            allowedTypes: this.container.dataset.allowedTypes || '*',
            uploadUrl: this.container.dataset.uploadUrl || '/api/upload/',
            csrfToken: this.container.dataset.csrfToken || '',
            chunkSize: 1024 * 1024, // 1MB chunks
            maxRetries: 3,
            retryDelay: 1000,
            autoUpload: false,
            ...options
        };

        // State
        this.files = [];
        this.uploading = false;
        this.uploadQueue = [];
        this.completedUploads = [];
        this.failedUploads = [];

        // DOM elements
        this.dropZone = this.container.querySelector('.drag-drop-zone');
        this.fileInput = this.container.querySelector('.file-input-hidden');
        this.fileQueue = this.container.querySelector('.file-queue');
        this.fileItems = this.container.querySelector('.file-items');
        this.queueHeader = this.container.querySelector('.queue-header');
        this.uploadControls = this.container.querySelector('.upload-controls');
        this.uploadResults = this.container.querySelector('.upload-results');
        this.uploadErrors = this.container.querySelector('.upload-errors');

        // Templates
        this.fileItemTemplate = document.getElementById('file-item-template');
        this.successTemplate = document.getElementById('success-message-template');
        this.errorTemplate = document.getElementById('error-message-template');

        // Initialize
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAccessibility();
        this.setupFileTypeValidation();
    }

    setupEventListeners() {
        // Drop zone events
        this.dropZone.addEventListener('click', (e) => {
            e.preventDefault();
            this.fileInput.click();
        });

        this.dropZone.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.fileInput.click();
            }
        });

        // File select trigger
        const fileSelectTrigger = this.container.querySelector('.file-select-trigger');
        if (fileSelectTrigger) {
            fileSelectTrigger.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.fileInput.click();
            });
        }

        // File input change
        this.fileInput.addEventListener('change', (e) => {
            this.handleFiles(Array.from(e.target.files));
            e.target.value = ''; // Reset input
        });

        // Drag and drop events
        this.setupDragAndDrop();

        // Queue management
        this.setupQueueControls();

        // Upload controls
        this.setupUploadControls();

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, this.preventDefaults.bind(this), false);
        });
    }

    setupDragAndDrop() {
        let dragCounter = 0;

        this.dropZone.addEventListener('dragenter', (e) => {
            e.preventDefault();
            dragCounter++;
            this.dropZone.classList.add('drag-active');
            this.updateDropZoneState(e);
        });

        this.dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
            this.updateDropZoneState(e);
        });

        this.dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dragCounter--;
            if (dragCounter === 0) {
                this.dropZone.classList.remove('drag-active', 'drag-invalid');
            }
        });

        this.dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dragCounter = 0;
            this.dropZone.classList.remove('drag-active', 'drag-invalid');
            
            const files = Array.from(e.dataTransfer.files);
            this.handleFiles(files);
        });
    }

    updateDropZoneState(e) {
        const files = Array.from(e.dataTransfer.files || []);
        const validFiles = this.validateFiles(files, false);
        
        if (validFiles.length !== files.length || files.length === 0) {
            this.dropZone.classList.add('drag-invalid');
        } else {
            this.dropZone.classList.remove('drag-invalid');
        }
    }

    setupQueueControls() {
        const clearQueueBtn = this.container.querySelector('.clear-queue');
        if (clearQueueBtn) {
            clearQueueBtn.addEventListener('click', () => {
                this.clearQueue();
            });
        }
    }

    setupUploadControls() {
        const startUploadBtn = this.container.querySelector('.start-upload');
        const pauseUploadBtn = this.container.querySelector('.pause-upload');
        const cancelUploadBtn = this.container.querySelector('.cancel-upload');

        if (startUploadBtn) {
            startUploadBtn.addEventListener('click', () => {
                this.startUpload();
            });
        }

        if (pauseUploadBtn) {
            pauseUploadBtn.addEventListener('click', () => {
                this.pauseUpload();
            });
        }

        if (cancelUploadBtn) {
            cancelUploadBtn.addEventListener('click', () => {
                this.cancelUpload();
            });
        }
    }

    setupAccessibility() {
        // Announce file additions to screen readers
        this.fileQueue.setAttribute('aria-live', 'polite');
        
        // Set up proper focus management
        this.dropZone.setAttribute('tabindex', '0');
        
        // Add keyboard navigation support
        this.fileQueue.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });
    }

    setupFileTypeValidation() {
        if (this.config.allowedTypes !== '*') {
            this.allowedTypes = this.config.allowedTypes.split(',').map(type => type.trim());
        }
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    handleFiles(files) {
        if (!files || files.length === 0) return;

        const validFiles = this.validateFiles(files);
        if (validFiles.length === 0) return;

        // Check file limit
        if (this.files.length + validFiles.length > this.config.maxFiles) {
            this.showError(`Maximum ${this.config.maxFiles} files allowed. Selected ${validFiles.length}, current ${this.files.length}.`);
            return;
        }

        // Add files to queue
        validFiles.forEach(file => {
            const fileObj = this.createFileObject(file);
            this.files.push(fileObj);
            this.renderFileItem(fileObj);
        });

        this.updateUI();
        this.announceToScreenReader(`${validFiles.length} file(s) added to upload queue`);

        // Auto-upload if enabled
        if (this.config.autoUpload && !this.uploading) {
            this.startUpload();
        }
    }

    validateFiles(files, showErrors = true) {
        const validFiles = [];
        const errors = [];

        files.forEach(file => {
            // Check file size
            if (file.size > this.config.maxSizeMB * 1024 * 1024) {
                errors.push(`${file.name}: File too large (${this.formatFileSize(file.size)} > ${this.config.maxSizeMB}MB)`);
                return;
            }

            // Check file type
            if (this.allowedTypes && !this.isAllowedType(file)) {
                errors.push(`${file.name}: File type not allowed`);
                return;
            }

            // Check for duplicates
            const isDuplicate = this.files.some(existingFile => 
                existingFile.file.name === file.name && 
                existingFile.file.size === file.size
            );

            if (isDuplicate) {
                errors.push(`${file.name}: File already in queue`);
                return;
            }

            validFiles.push(file);
        });

        if (showErrors && errors.length > 0) {
            errors.forEach(error => this.showError(error));
        }

        return validFiles;
    }

    isAllowedType(file) {
        if (!this.allowedTypes) return true;

        const fileExtension = this.getFileExtension(file.name);
        const mimeType = file.type;

        return this.allowedTypes.some(allowedType => {
            if (allowedType.startsWith('.')) {
                return fileExtension === allowedType.substring(1);
            }
            if (allowedType.includes('/')) {
                return mimeType === allowedType || mimeType.startsWith(allowedType.replace('*', ''));
            }
            return false;
        });
    }

    getFileExtension(filename) {
        return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
    }

    createFileObject(file) {
        return {
            id: this.generateId(),
            file: file,
            status: 'queued',
            progress: 0,
            uploaded: 0,
            speed: 0,
            eta: null,
            retries: 0,
            error: null,
            xhr: null,
            startTime: null
        };
    }

    generateId() {
        return 'file_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    }

    renderFileItem(fileObj) {
        if (!this.fileItemTemplate) return;

        const template = this.fileItemTemplate.content.cloneNode(true);
        const fileItem = template.querySelector('.file-item');
        
        fileItem.dataset.fileId = fileObj.id;
        
        // File info
        const fileName = fileItem.querySelector('.file-name');
        const fileSize = fileItem.querySelector('.file-size');
        const fileType = fileItem.querySelector('.file-type');
        const fileIcon = fileItem.querySelector('.file-icon');
        
        fileName.textContent = fileObj.file.name;
        fileName.title = fileObj.file.name;
        fileSize.textContent = this.formatFileSize(fileObj.file.size);
        fileType.textContent = fileObj.file.type || 'Unknown';
        
        // Set appropriate icon
        this.setFileIcon(fileIcon, fileObj.file);
        
        // Remove button
        const removeBtn = fileItem.querySelector('.remove-file');
        removeBtn.addEventListener('click', () => {
            this.removeFile(fileObj.id);
        });
        
        // Progress elements (initially hidden)
        const progressContainer = fileItem.querySelector('.file-progress');
        const progressBar = fileItem.querySelector('.progress-bar');
        const progressText = fileItem.querySelector('.progress-text');
        const uploadSpeed = fileItem.querySelector('.upload-speed');
        const uploadEta = fileItem.querySelector('.upload-eta');
        
        // Status elements
        const statusBadge = fileItem.querySelector('.status-badge');
        const statusMessage = fileItem.querySelector('.status-message');
        
        // Store references for easy access
        fileObj.elements = {
            item: fileItem,
            progressContainer,
            progressBar,
            progressText,
            uploadSpeed,
            uploadEta,
            statusBadge,
            statusMessage
        };
        
        this.fileItems.appendChild(fileItem);
    }

    setFileIcon(iconElement, file) {
        const extension = this.getFileExtension(file.name);
        const mimeType = file.type;
        
        // Remove existing classes
        iconElement.className = 'file-icon';
        
        // Set icon based on file type
        if (mimeType.startsWith('image/')) {
            this.setImagePreview(iconElement, file);
        } else {
            const iconClass = this.getFileIconClass(extension, mimeType);
            iconElement.innerHTML = `<i class="bi ${iconClass}"></i>`;
        }
    }

    setImagePreview(iconElement, file) {
        iconElement.classList.add('image-preview');
        
        const reader = new FileReader();
        reader.onload = (e) => {
            iconElement.innerHTML = `<img src="${e.target.result}" alt="Preview of ${file.name}">`;
        };
        reader.readAsDataURL(file);
    }

    getFileIconClass(extension, mimeType) {
        const iconMap = {
            // Documents
            'pdf': 'bi-file-earmark-pdf',
            'doc': 'bi-file-earmark-word',
            'docx': 'bi-file-earmark-word',
            'xls': 'bi-file-earmark-excel',
            'xlsx': 'bi-file-earmark-excel',
            'ppt': 'bi-file-earmark-ppt',
            'pptx': 'bi-file-earmark-ppt',
            'txt': 'bi-file-earmark-text',
            
            // Images
            'jpg': 'bi-file-earmark-image',
            'jpeg': 'bi-file-earmark-image',
            'png': 'bi-file-earmark-image',
            'gif': 'bi-file-earmark-image',
            'svg': 'bi-file-earmark-image',
            'webp': 'bi-file-earmark-image',
            
            // Archives
            'zip': 'bi-file-earmark-zip',
            'rar': 'bi-file-earmark-zip',
            '7z': 'bi-file-earmark-zip',
            'tar': 'bi-file-earmark-zip',
            'gz': 'bi-file-earmark-zip',
            
            // Code
            'js': 'bi-file-earmark-code',
            'html': 'bi-file-earmark-code',
            'css': 'bi-file-earmark-code',
            'php': 'bi-file-earmark-code',
            'py': 'bi-file-earmark-code',
            'java': 'bi-file-earmark-code',
            
            // Video/Audio
            'mp4': 'bi-file-earmark-play',
            'avi': 'bi-file-earmark-play',
            'mov': 'bi-file-earmark-play',
            'mp3': 'bi-file-earmark-music',
            'wav': 'bi-file-earmark-music',
            'flac': 'bi-file-earmark-music',
        };
        
        return iconMap[extension] || 'bi-file-earmark';
    }

    updateUI() {
        const hasFiles = this.files.length > 0;
        
        // Show/hide queue sections
        this.queueHeader.classList.toggle('d-none', !hasFiles);
        this.uploadControls.classList.toggle('d-none', !hasFiles);
        
        // Update counts
        const queueCount = this.container.querySelector('.queue-count');
        if (queueCount) {
            queueCount.textContent = this.files.length;
        }
        
        // Update upload button state
        const startUploadBtn = this.container.querySelector('.start-upload');
        if (startUploadBtn) {
            const hasQueuedFiles = this.files.some(f => f.status === 'queued');
            startUploadBtn.disabled = !hasQueuedFiles || this.uploading;
        }
    }

    removeFile(fileId) {
        const fileIndex = this.files.findIndex(f => f.id === fileId);
        if (fileIndex === -1) return;
        
        const fileObj = this.files[fileIndex];
        
        // Cancel upload if in progress
        if (fileObj.xhr) {
            fileObj.xhr.abort();
        }
        
        // Remove from DOM
        if (fileObj.elements && fileObj.elements.item) {
            fileObj.elements.item.remove();
        }
        
        // Remove from array
        this.files.splice(fileIndex, 1);
        
        this.updateUI();
        this.announceToScreenReader(`${fileObj.file.name} removed from queue`);
    }

    clearQueue() {
        // Cancel all uploads
        this.files.forEach(fileObj => {
            if (fileObj.xhr) {
                fileObj.xhr.abort();
            }
        });
        
        // Clear DOM
        this.fileItems.innerHTML = '';
        
        // Clear array
        this.files = [];
        
        // Reset upload state
        this.uploading = false;
        this.uploadQueue = [];
        
        this.updateUI();
        this.updateUploadButtons();
        this.announceToScreenReader('Upload queue cleared');
    }

    async startUpload() {
        if (this.uploading) return;
        
        const queuedFiles = this.files.filter(f => f.status === 'queued');
        if (queuedFiles.length === 0) return;
        
        this.uploading = true;
        this.uploadQueue = [...queuedFiles];
        this.completedUploads = [];
        this.failedUploads = [];
        
        this.updateUploadButtons();
        this.showOverallProgress();
        this.announceToScreenReader(`Starting upload of ${queuedFiles.length} files`);
        
        // Process uploads sequentially or in parallel
        try {
            await this.processUploadQueue();
        } catch (error) {
            console.error('Upload error:', error);
            this.showError('Upload process failed: ' + error.message);
        } finally {
            this.uploading = false;
            this.updateUploadButtons();
            this.hideOverallProgress();
            this.showUploadResults();
        }
    }

    async processUploadQueue() {
        const maxConcurrent = 3; // Adjust based on server capacity
        const promises = [];
        
        for (let i = 0; i < Math.min(maxConcurrent, this.uploadQueue.length); i++) {
            promises.push(this.processNextUpload());
        }
        
        await Promise.all(promises);
    }

    async processNextUpload() {
        while (this.uploadQueue.length > 0 && this.uploading) {
            const fileObj = this.uploadQueue.shift();
            if (fileObj) {
                await this.uploadFile(fileObj);
            }
        }
    }

    async uploadFile(fileObj) {
        if (fileObj.status !== 'queued') return;
        
        fileObj.status = 'uploading';
        fileObj.startTime = Date.now();
        this.updateFileStatus(fileObj);
        
        try {
            await this.performUpload(fileObj);
            fileObj.status = 'completed';
            this.completedUploads.push(fileObj);
            this.announceToScreenReader(`${fileObj.file.name} uploaded successfully`);
        } catch (error) {
            fileObj.status = 'error';
            fileObj.error = error.message;
            this.failedUploads.push(fileObj);
            
            // Retry logic
            if (fileObj.retries < this.config.maxRetries) {
                fileObj.retries++;
                fileObj.status = 'retrying';
                this.updateFileStatus(fileObj);
                
                await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
                return this.uploadFile(fileObj); // Recursive retry
            } else {
                this.announceToScreenReader(`${fileObj.file.name} upload failed: ${error.message}`);
            }
        } finally {
            this.updateFileStatus(fileObj);
            this.updateOverallProgress();
        }
    }

    performUpload(fileObj) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', fileObj.file);
            formData.append('csrfmiddlewaretoken', this.config.csrfToken);
            
            // Additional metadata
            formData.append('file_id', fileObj.id);
            formData.append('original_name', fileObj.file.name);
            
            const xhr = new XMLHttpRequest();
            fileObj.xhr = xhr;
            
            // Upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percent = (e.loaded / e.total) * 100;
                    fileObj.progress = percent;
                    fileObj.uploaded = e.loaded;
                    
                    // Calculate speed and ETA
                    const elapsed = Date.now() - fileObj.startTime;
                    fileObj.speed = e.loaded / (elapsed / 1000); // bytes per second
                    
                    if (fileObj.speed > 0) {
                        const remaining = e.total - e.loaded;
                        fileObj.eta = remaining / fileObj.speed; // seconds
                    }
                    
                    this.updateFileProgress(fileObj);
                }
            });
            
            // Handle response
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        fileObj.response = response;
                        resolve(response);
                    } catch (e) {
                        reject(new Error('Invalid server response'));
                    }
                } else {
                    reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
                }
            });
            
            xhr.addEventListener('error', () => {
                reject(new Error('Network error during upload'));
            });
            
            xhr.addEventListener('abort', () => {
                reject(new Error('Upload cancelled'));
            });
            
            // Send request
            xhr.open('POST', this.config.uploadUrl);
            xhr.send(formData);
        });
    }

    updateFileStatus(fileObj) {
        if (!fileObj.elements) return;
        
        const { statusBadge, statusMessage, progressContainer } = fileObj.elements;
        
        const statusConfig = {
            'queued': { badge: 'secondary', text: 'Queued' },
            'uploading': { badge: 'primary', text: 'Uploading' },
            'completed': { badge: 'success', text: 'Complete' },
            'error': { badge: 'danger', text: 'Failed' },
            'retrying': { badge: 'warning', text: 'Retrying' }
        };
        
        const config = statusConfig[fileObj.status] || statusConfig.queued;
        
        statusBadge.className = `badge bg-${config.badge} status-badge`;
        statusBadge.textContent = config.text;
        
        if (fileObj.error) {
            statusMessage.textContent = fileObj.error;
            fileObj.elements.item.classList.add('error');
        } else if (fileObj.status === 'completed') {
            statusMessage.textContent = 'Upload successful';
            fileObj.elements.item.classList.add('success');
        } else if (fileObj.status === 'retrying') {
            statusMessage.textContent = `Retry ${fileObj.retries}/${this.config.maxRetries}`;
        }
        
        // Show/hide progress
        const showProgress = ['uploading', 'retrying'].includes(fileObj.status);
        progressContainer.style.display = showProgress ? 'block' : 'none';
    }

    updateFileProgress(fileObj) {
        if (!fileObj.elements) return;
        
        const { progressBar, progressText, uploadSpeed, uploadEta } = fileObj.elements;
        
        // Update progress bar
        progressBar.style.width = `${fileObj.progress}%`;
        progressBar.setAttribute('aria-valuenow', fileObj.progress);
        progressText.textContent = `${Math.round(fileObj.progress)}% uploaded`;
        
        // Update speed and ETA
        if (fileObj.speed > 0) {
            uploadSpeed.textContent = this.formatSpeed(fileObj.speed);
        }
        
        if (fileObj.eta) {
            uploadEta.textContent = this.formatETA(fileObj.eta);
        }
    }

    updateOverallProgress() {
        const progressContainer = this.container.querySelector('.overall-progress');
        const progressBar = progressContainer.querySelector('.progress-bar');
        const progressSrText = progressContainer.querySelector('.progress-sr-text');
        const completedCount = this.container.querySelector('.completed-count');
        const totalCount = this.container.querySelector('.total-count');
        
        const total = this.files.length;
        const completed = this.completedUploads.length + this.failedUploads.length;
        const percent = total > 0 ? (completed / total) * 100 : 0;
        
        progressBar.style.width = `${percent}%`;
        progressBar.setAttribute('aria-valuenow', percent);
        progressSrText.textContent = `${Math.round(percent)}% complete`;
        
        if (completedCount) completedCount.textContent = completed;
        if (totalCount) totalCount.textContent = total;
    }

    showOverallProgress() {
        const progressContainer = this.container.querySelector('.overall-progress');
        if (progressContainer) {
            progressContainer.style.display = 'block';
        }
        
        const statusText = this.container.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = 'Uploading files...';
        }
    }

    hideOverallProgress() {
        const progressContainer = this.container.querySelector('.overall-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
        
        const statusText = this.container.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = 'Ready to upload';
        }
    }

    updateUploadButtons() {
        const startBtn = this.container.querySelector('.start-upload');
        const pauseBtn = this.container.querySelector('.pause-upload');
        const cancelBtn = this.container.querySelector('.cancel-upload');
        
        if (this.uploading) {
            startBtn.classList.add('d-none');
            pauseBtn.classList.remove('d-none');
            cancelBtn.classList.remove('d-none');
        } else {
            startBtn.classList.remove('d-none');
            pauseBtn.classList.add('d-none');
            cancelBtn.classList.add('d-none');
        }
    }

    pauseUpload() {
        // Implementation for pause functionality
        this.uploading = false;
        this.updateUploadButtons();
        this.announceToScreenReader('Upload paused');
    }

    cancelUpload() {
        this.uploading = false;
        
        // Cancel all active uploads
        this.files.forEach(fileObj => {
            if (fileObj.xhr && fileObj.status === 'uploading') {
                fileObj.xhr.abort();
                fileObj.status = 'queued';
                this.updateFileStatus(fileObj);
            }
        });
        
        this.uploadQueue = [];
        this.updateUploadButtons();
        this.hideOverallProgress();
        this.announceToScreenReader('Upload cancelled');
    }

    showUploadResults() {
        // Clear previous results
        this.uploadResults.innerHTML = '';
        
        if (this.completedUploads.length > 0) {
            this.showSuccessMessage();
        }
        
        if (this.failedUploads.length > 0) {
            this.showFailureMessage();
        }
    }

    showSuccessMessage() {
        if (!this.successTemplate) return;
        
        const template = this.successTemplate.content.cloneNode(true);
        const successCount = template.querySelector('.success-count');
        const successFiles = template.querySelector('.success-files');
        
        successCount.textContent = this.completedUploads.length;
        
        // Add file links if available
        this.completedUploads.forEach(fileObj => {
            if (fileObj.response && fileObj.response.url) {
                const link = document.createElement('a');
                link.href = fileObj.response.url;
                link.textContent = fileObj.file.name;
                link.className = 'me-3';
                successFiles.appendChild(link);
            }
        });
        
        this.uploadResults.appendChild(template);
    }

    showFailureMessage() {
        this.failedUploads.forEach(fileObj => {
            this.showError(`${fileObj.file.name}: ${fileObj.error}`);
        });
    }

    showError(message) {
        if (!this.errorTemplate) {
            console.error(message);
            return;
        }
        
        const template = this.errorTemplate.content.cloneNode(true);
        const errorMessage = template.querySelector('.error-message');
        
        errorMessage.textContent = message;
        
        this.uploadErrors.appendChild(template);
        
        // Auto-dismiss after 10 seconds
        setTimeout(() => {
            const alertElement = this.uploadErrors.querySelector('.alert');
            if (alertElement) {
                alertElement.remove();
            }
        }, 10000);
    }

    // Utility methods
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatSpeed(bytesPerSecond) {
        return this.formatFileSize(bytesPerSecond) + '/s';
    }

    formatETA(seconds) {
        if (seconds < 60) {
            return Math.round(seconds) + 's';
        } else if (seconds < 3600) {
            return Math.round(seconds / 60) + 'm';
        } else {
            return Math.round(seconds / 3600) + 'h';
        }
    }

    announceToScreenReader(message) {
        // Create temporary element for screen reader announcement
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'visually-hidden';
        announcement.textContent = message;
        
        document.body.appendChild(announcement);
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    handleKeyboardNavigation(e) {
        // Implementation for keyboard navigation within file queue
        const focusableElements = this.fileQueue.querySelectorAll('button, [tabindex="0"]');
        const currentIndex = Array.from(focusableElements).indexOf(e.target);
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex < focusableElements.length - 1) {
                    focusableElements[currentIndex + 1].focus();
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex > 0) {
                    focusableElements[currentIndex - 1].focus();
                }
                break;
        }
    }

    // Public API methods
    addFiles(files) {
        this.handleFiles(files);
    }

    removeAllFiles() {
        this.clearQueue();
    }

    startUploadProgrammatically() {
        this.startUpload();
    }

    getFiles() {
        return this.files;
    }

    getUploadStatus() {
        return {
            uploading: this.uploading,
            completed: this.completedUploads.length,
            failed: this.failedUploads.length,
            total: this.files.length
        };
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all drag-drop upload containers
    const containers = document.querySelectorAll('.drag-drop-upload-container');
    containers.forEach(container => {
        new DragDropUpload(container);
    });
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DragDropUpload;
}

// Global assignment for direct script inclusion
window.DragDropUpload = DragDropUpload;