{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Analysis Results Schema",
  "description": "Schema for storing comprehensive UI/UX analysis results",
  "type": "object",
  "properties": {
    "analysis_id": {
      "type": "string",
      "description": "Unique identifier for this analysis run"
    },
    "analysis_metadata": {
      "type": "object",
      "properties": {
        "start_time": {"type": "string", "format": "date-time"},
        "end_time": {"type": "string", "format": "date-time"},
        "duration_seconds": {"type": "number"},
        "analyzer_version": {"type": "string"},
        "configuration_hash": {"type": "string"},
        "total_pages_analyzed": {"type": "integer"},
        "successful_analyses": {"type": "integer"},
        "failed_analyses": {"type": "integer"}
      },
      "required": ["start_time", "analyzer_version"]
    },
    "global_summary": {
      "type": "object",
      "properties": {
        "overall_score": {
          "type": "number",
          "minimum": 0,
          "maximum": 100,
          "description": "Application-wide overall score"
        },
        "category_averages": {
          "type": "object",
          "properties": {
            "visual_design": {"type": "number", "minimum": 0, "maximum": 100},
            "user_experience": {"type": "number", "minimum": 0, "maximum": 100},
            "accessibility": {"type": "number", "minimum": 0, "maximum": 100},
            "technical_implementation": {"type": "number", "minimum": 0, "maximum": 100},
            "performance": {"type": "number", "minimum": 0, "maximum": 100},
            "seo": {"type": "number", "minimum": 0, "maximum": 100}
          }
        },
        "score_distribution": {
          "type": "object",
          "properties": {
            "excellent": {"type": "integer", "description": "Pages scoring 90-100"},
            "good": {"type": "integer", "description": "Pages scoring 70-89"},
            "needs_improvement": {"type": "integer", "description": "Pages scoring 50-69"},
            "poor": {"type": "integer", "description": "Pages scoring below 50"}
          }
        },
        "critical_issues": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "issue_type": {"type": "string"},
              "affected_pages": {"type": "integer"},
              "severity": {"type": "string", "enum": ["low", "medium", "high", "critical"]},
              "description": {"type": "string"},
              "resolution_priority": {"type": "integer", "minimum": 1, "maximum": 10}
            }
          }
        }
      }
    },
    "section_analysis": {
      "type": "object",
      "description": "Analysis grouped by application sections",
      "patternProperties": {
        "^[a-zA-Z0-9_-]+$": {
          "type": "object",
          "properties": {
            "section_name": {"type": "string"},
            "page_count": {"type": "integer"},
            "average_score": {"type": "number"},
            "best_performing_page": {"type": "string"},
            "worst_performing_page": {"type": "string"},
            "common_issues": {
              "type": "array",
              "items": {"type": "string"}
            },
            "recommendations": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "recommendation": {"type": "string"},
                  "impact_level": {"type": "string", "enum": ["low", "medium", "high"]},
                  "implementation_effort": {"type": "string", "enum": ["low", "medium", "high"]},
                  "affected_pages": {"type": "array", "items": {"type": "string"}}
                }
              }
            }
          }
        }
      }
    },
    "component_analysis": {
      "type": "object",
      "description": "Analysis of reusable components",
      "properties": {
        "components_identified": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "component_name": {"type": "string"},
              "template_path": {"type": "string"},
              "usage_count": {"type": "integer"},
              "consistency_score": {"type": "number"},
              "accessibility_score": {"type": "number"},
              "variations": {
                "type": "array",
                "items": {
                  "type": "object",
                  "properties": {
                    "variation_name": {"type": "string"},
                    "usage_locations": {"type": "array", "items": {"type": "string"}},
                    "differences": {"type": "array", "items": {"type": "string"}}
                  }
                }
              }
            }
          }
        },
        "component_consistency": {
          "type": "object",
          "properties": {
            "overall_consistency": {"type": "number", "minimum": 0, "maximum": 100},
            "inconsistent_components": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "component_name": {"type": "string"},
                  "inconsistency_type": {"type": "string"},
                  "affected_pages": {"type": "array", "items": {"type": "string"}}
                }
              }
            }
          }
        }
      }
    },
    "accessibility_analysis": {
      "type": "object",
      "properties": {
        "wcag_compliance": {
          "type": "object",
          "properties": {
            "level_a": {"type": "number", "minimum": 0, "maximum": 100},
            "level_aa": {"type": "number", "minimum": 0, "maximum": 100},
            "level_aaa": {"type": "number", "minimum": 0, "maximum": 100}
          }
        },
        "common_violations": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "violation_type": {"type": "string"},
              "occurrence_count": {"type": "integer"},
              "affected_pages": {"type": "array", "items": {"type": "string"}},
              "severity": {"type": "string", "enum": ["minor", "moderate", "serious", "critical"]},
              "remediation_guidance": {"type": "string"}
            }
          }
        },
        "assistive_technology": {
          "type": "object",
          "properties": {
            "screen_reader_compatibility": {"type": "number", "minimum": 0, "maximum": 100},
            "keyboard_navigation": {"type": "number", "minimum": 0, "maximum": 100},
            "voice_control": {"type": "number", "minimum": 0, "maximum": 100}
          }
        }
      }
    },
    "performance_analysis": {
      "type": "object",
      "properties": {
        "core_web_vitals": {
          "type": "object",
          "properties": {
            "average_fcp": {"type": "number"},
            "average_lcp": {"type": "number"},
            "average_cls": {"type": "number"},
            "average_fid": {"type": "number"},
            "average_tti": {"type": "number"}
          }
        },
        "resource_optimization": {
          "type": "object",
          "properties": {
            "total_page_weight": {"type": "integer"},
            "compression_opportunities": {"type": "integer"},
            "unused_css": {"type": "number"},
            "unused_js": {"type": "number"},
            "image_optimization_potential": {"type": "number"}
          }
        },
        "caching_analysis": {
          "type": "object",
          "properties": {
            "cache_hit_rate": {"type": "number"},
            "static_asset_caching": {"type": "boolean"},
            "dynamic_content_caching": {"type": "boolean"}
          }
        }
      }
    },
    "htmx_analysis": {
      "type": "object",
      "properties": {
        "implementation_quality": {
          "type": "object",
          "properties": {
            "pattern_adherence": {"type": "number", "minimum": 0, "maximum": 100},
            "progressive_enhancement": {"type": "number", "minimum": 0, "maximum": 100},
            "accessibility_integration": {"type": "number", "minimum": 0, "maximum": 100"}
          }
        },
        "interaction_patterns": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "pattern_name": {"type": "string"},
              "usage_count": {"type": "integer"},
              "implementation_quality": {"type": "number"},
              "user_experience_impact": {"type": "string", "enum": ["positive", "neutral", "negative"]},
              "accessibility_concerns": {"type": "array", "items": {"type": "string"}}
            }
          }
        },
        "fallback_behavior": {
          "type": "object",
          "properties": {
            "graceful_degradation": {"type": "number", "minimum": 0, "maximum": 100},
            "non_js_functionality": {"type": "number", "minimum": 0, "maximum": 100},
            "error_handling": {"type": "number", "minimum": 0, "maximum": 100"}
          }
        }
      }
    },
    "bootstrap_analysis": {
      "type": "object",
      "properties": {
        "integration_quality": {
          "type": "object",
          "properties": {
            "component_usage": {"type": "number", "minimum": 0, "maximum": 100},
            "customization_level": {"type": "number", "minimum": 0, "maximum": 100},
            "consistency": {"type": "number", "minimum": 0, "maximum": 100}
          }
        },
        "responsive_implementation": {
          "type": "object",
          "properties": {
            "grid_usage": {"type": "number", "minimum": 0, "maximum": 100},
            "breakpoint_consistency": {"type": "number", "minimum": 0, "maximum": 100},
            "mobile_optimization": {"type": "number", "minimum": 0, "maximum": 100"}
          }
        },
        "customization_analysis": {
          "type": "object",
          "properties": {
            "override_percentage": {"type": "number"},
            "custom_components": {"type": "integer"},
            "theme_consistency": {"type": "number", "minimum": 0, "maximum": 100"}
          }
        }
      }
    },
    "comparative_analysis": {
      "type": "object",
      "properties": {
        "industry_benchmarks": {
          "type": "object",
          "properties": {
            "compared_against": {
              "type": "array",
              "items": {"type": "string"}
            },
            "relative_performance": {
              "type": "object",
              "properties": {
                "above_average": {"type": "array", "items": {"type": "string"}},
                "average": {"type": "array", "items": {"type": "string"}},
                "below_average": {"type": "array", "items": {"type": "string"}}
              }
            }
          }
        },
        "best_practices_compliance": {
          "type": "object",
          "properties": {
            "design_systems": {"type": "number", "minimum": 0, "maximum": 100},
            "accessibility_guidelines": {"type": "number", "minimum": 0, "maximum": 100},
            "performance_guidelines": {"type": "number", "minimum": 0, "maximum": 100},
            "security_practices": {"type": "number", "minimum": 0, "maximum": 100}
          }
        }
      }
    },
    "recommendations": {
      "type": "object",
      "properties": {
        "high_priority": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "title": {"type": "string"},
              "description": {"type": "string"},
              "category": {"type": "string"},
              "impact_level": {"type": "string", "enum": ["low", "medium", "high"]},
              "implementation_effort": {"type": "string", "enum": ["low", "medium", "high"]},
              "affected_pages": {"type": "array", "items": {"type": "string"}},
              "implementation_steps": {"type": "array", "items": {"type": "string"}},
              "expected_improvement": {"type": "number"}
            }
          }
        },
        "medium_priority": {
          "type": "array",
          "items": {
            "$ref": "#/properties/recommendations/properties/high_priority/items"
          }
        },
        "low_priority": {
          "type": "array",
          "items": {
            "$ref": "#/properties/recommendations/properties/high_priority/items"
          }
        }
      }
    },
    "page_details": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "page_id": {"type": "string"},
          "url": {"type": "string"},
          "template": {"type": "string"},
          "scores": {
            "type": "object",
            "properties": {
              "overall": {"type": "number"},
              "visual_design": {"type": "number"},
              "user_experience": {"type": "number"},
              "accessibility": {"type": "number"},
              "technical_implementation": {"type": "number"},
              "performance": {"type": "number"},
              "seo": {"type": "number"}
            }
          },
          "issues": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "type": {"type": "string"},
                "severity": {"type": "string"},
                "description": {"type": "string"},
                "element": {"type": "string"},
                "recommendation": {"type": "string"}
              }
            }
          }
        }
      }
    }
  },
  "required": [
    "analysis_id",
    "analysis_metadata",
    "global_summary"
  ]
}