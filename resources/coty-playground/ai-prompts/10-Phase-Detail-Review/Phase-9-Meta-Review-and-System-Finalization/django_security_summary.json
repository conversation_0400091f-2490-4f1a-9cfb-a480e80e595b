{"timestamp": "2025-06-30T15:26:02.093240", "django_version": "5.2.3", "settings_module": "clear_htmx.settings", "critical_issues": [{"id": "SECURITY-001", "title": "DEBUG mode enabled", "description": "DEBUG=True in production exposes sensitive information", "severity": "critical", "recommendation": "Set DEBUG=False in production"}], "high_issues": [{"id": "SECURITY-010", "title": "Weak database SSL configuration", "description": "Database SSL mode is 'prefer', should be 'require' or stronger", "severity": "high", "recommendation": "Use sslmode='require' or stronger for production"}], "medium_issues": [{"id": "SECURITY-004", "title": "Short SECRET_KEY", "description": "SECRET_KEY length is 29, recommended minimum 50", "severity": "medium", "recommendation": "Use a longer SECRET_KEY (50+ characters)"}], "low_issues": [], "security_config": {"debug": true, "secret_key_configured": true, "allowed_hosts_configured": true, "ssl_redirect": false, "secure_cookies": false, "hsts_enabled": false, "database_ssl": false, "redis_ssl": false, "csrf_protection": true}, "recommendations": ["Disable DEBUG mode in production", "Strengthen database SSL configuration"], "overall_security_level": "CRITICAL"}