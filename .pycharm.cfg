# PyCharm Django configuration
DJANGO_SETTINGS_MODULE=clear_htmx.dev_settings
DJANGO_PROJECT_ROOT=.
PYTHONPATH=.
DJANGO_MANAGE_PY=manage.py
DJANGO_DEBUG=True

# Database settings (for autocomplete and inspections)
DATABASE_ENGINE=django.db.backends.postgresql
DATABASE_NAME=clear_htmx
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432

# Additional settings
DEBUG_TOOLBAR=True
DJANGO_LIVE_TEST_SERVER_ADDRESS=localhost:8000
