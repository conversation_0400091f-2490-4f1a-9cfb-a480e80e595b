# Security Scanning Configuration for CLEAR HTMX
# Comprehensive OWASP Top 10 compliance and vulnerability management

security_scanning:
  # Global configuration
  enabled: true
  severity_threshold: "medium" # minimum severity to report
  fail_on_severity: "high" # minimum severity to fail CI/CD

  # OWASP Top 10 2021 Configuration
  owasp_top10:
    A01_broken_access_control:
      tools: ["semgrep", "bandit"]
      patterns:
        - "@login_required"
        - "permission_required"
        - "user_passes_test"
      checks:
        - "django_admin_authentication"
        - "api_authentication"
        - "object_level_permissions"

    A02_cryptographic_failures:
      tools: ["bandit", "semgrep", "detect-secrets"]
      patterns:
        - "md5|sha1|des|rc4"
        - "SECRET_KEY|API_KEY|PASSWORD"
        - "ssl_context|tls_version"
      checks:
        - "weak_crypto_algorithms"
        - "hardcoded_secrets"
        - "ssl_tls_configuration"

    A03_injection:
      tools: ["semgrep", "bandit"]
      patterns:
        - "raw|extra|cursor.execute"
        - "eval|exec|subprocess"
        - "format|%s|%d"
      checks:
        - "sql_injection"
        - "command_injection"
        - "template_injection"
        - "ldap_injection"

    A04_insecure_design:
      tools: ["custom_analysis"]
      checks:
        - "threat_modeling"
        - "secure_design_patterns"
        - "privacy_controls"

    A05_security_misconfiguration:
      tools: ["django_check", "checkov"]
      checks:
        - "debug_mode"
        - "security_headers"
        - "cors_configuration"
        - "cookie_security"
        - "ssl_redirect"

    A06_vulnerable_components:
      tools: ["safety", "pip-audit", "osv-scanner"]
      checks:
        - "outdated_dependencies"
        - "vulnerable_packages"
        - "license_compliance"
        - "sbom_generation"

    A07_identification_auth_failures:
      tools: ["semgrep", "bandit"]
      patterns:
        - "session|authentication|login"
        - "password|token|jwt"
      checks:
        - "session_management"
        - "password_policies"
        - "mfa_implementation"
        - "brute_force_protection"

    A08_software_integrity_failures:
      tools: ["pip-audit", "cyclonedx"]
      checks:
        - "dependency_integrity"
        - "supply_chain_security"
        - "code_signing"

    A09_logging_monitoring_failures:
      tools: ["custom_analysis"]
      checks:
        - "security_logging"
        - "audit_trails"
        - "monitoring_detection"
        - "incident_response"

    A10_server_side_request_forgery:
      tools: ["semgrep", "bandit"]
      patterns:
        - "requests.|urllib|httplib"
        - "url|uri|endpoint"
      checks:
        - "url_validation"
        - "network_isolation"
        - "input_validation"

  # Tool-specific configurations
  tools:
    safety:
      enabled: true
      config:
        ignore_ids: [] # CVE IDs to ignore
        report_format: "json"
        include_dev_dependencies: true

    pip_audit:
      enabled: true
      config:
        report_format: "json"
        vulnerability_service: "osv"
        require_hashes: false

    osv_scanner:
      enabled: true
      config:
        format: "json"
        recursive: true
        skip_git: false

    bandit:
      enabled: true
      config:
        severity: "medium"
        confidence: "medium"
        exclude_paths:
          - "*/migrations/*"
          - "*/tests/*"
          - "*/venv/*"
        skip_tests:
          - "B101" # assert_used
          - "B601" # paramiko_calls

    semgrep:
      enabled: true
      config:
        rules:
          - "p/django"
          - "p/python"
          - "p/security-audit"
          - "p/owasp-top-ten"
        exclude_paths:
          - "migrations/"
          - "tests/"
          - "__pycache__/"

    detect_secrets:
      enabled: true
      config:
        plugins:
          - "ArtifactoryDetector"
          - "AWSKeyDetector"
          - "Base64HighEntropyString"
          - "BasicAuthDetector"
          - "CloudantDetector"
          - "DiscordBotTokenDetector"
          - "GitHubTokenDetector"
          - "HexHighEntropyString"
          - "IbmCloudIamDetector"
          - "IbmCosHmacDetector"
          - "JwtTokenDetector"
          - "KeywordDetector"
          - "MailchimpDetector"
          - "NpmDetector"
          - "PrivateKeyDetector"
          - "SendGridDetector"
          - "SlackDetector"
          - "SoftlayerDetector"
          - "SquareOAuthDetector"
          - "StripeDetector"
          - "TwilioKeyDetector"
        exclude_files: ".*\\.lock$|.*\\.min\\.js$"

    truffleHog:
      enabled: true
      config:
        entropy_threshold: 3.0
        exclude_paths:
          - ".git/"
          - "node_modules/"
          - "__pycache__/"

    checkov:
      enabled: true
      config:
        framework:
          - "dockerfile"
          - "kubernetes"
          - "terraform"
        check_types:
          - "CKV_DOCKER_*"
          - "CKV_K8S_*"

  # Reporting configuration
  reporting:
    formats: ["json", "sarif", "html"]
    destinations:
      - "github_security_tab"
      - "artifacts"
      - "pr_comments"

    notification:
      enabled: true
      channels:
        - "github_issues"
        - "security_team_email"

      thresholds:
        critical: "immediate"
        high: "daily"
        medium: "weekly"

    metrics:
      track_trends: true
      historical_comparison: true
      compliance_scoring: true

  # Compliance requirements
  compliance:
    standards:
      - "OWASP_TOP_10_2021"
      - "NIST_CYBERSECURITY_FRAMEWORK"
      - "ISO_27001"

    required_score: 80 # minimum compliance score

    exemptions:
      # Temporary exemptions with expiration dates
      - id: "DEV_ONLY_DEBUG"
        description: "DEBUG=True in development settings only"
        expiration: "2025-12-31"
        risk_assessment: "low"

  # Integration settings
  integrations:
    github:
      create_issues: true
      security_advisories: true
      dependency_graph: true

    ci_cd:
      fail_on_high: true
      fail_on_critical: true
      allow_warnings: true

    external_services:
      snyk: false
      veracode: false
      checkmarx: false

# Environment-specific overrides
environments:
  development:
    security_scanning:
      severity_threshold: "low"
      fail_on_severity: "critical"

  staging:
    security_scanning:
      severity_threshold: "medium"
      fail_on_severity: "high"

  production:
    security_scanning:
      severity_threshold: "medium"
      fail_on_severity: "medium"
      compliance:
        required_score: 95
