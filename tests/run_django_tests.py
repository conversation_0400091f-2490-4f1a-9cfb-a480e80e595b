#!/usr/bin/env python
"""
Comprehensive Django Test Runner for CLEAR Platform

This script provides a unified interface for running Django test client tests
across all apps with proper configuration, reporting, and optional coverage analysis.

Usage:
    python tests/run_django_tests.py                    # Run all tests
    python tests/run_django_tests.py --app core         # Run tests for specific app
    python tests/run_django_tests.py --coverage         # Run with coverage report
    python tests/run_django_tests.py --fast             # Run only fast tests
    python tests/run_django_tests.py --integration      # Run integration tests
    python tests/run_django_tests.py --verbose          # Verbose output
    python tests/run_django_tests.py --parallel         # Run tests in parallel
"""

import argparse
import os
import sys
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Django setup
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.test")

import django
from django.conf import settings
from django.test.utils import get_runner


class DjangoTestRunner:
    """Comprehensive test runner for Django test client tests."""

    def __init__(self):
        # Initialize Django
        django.setup()

        # Available test categories
        self.test_categories = {
            "core": [
                "tests.django.core.test_views_comprehensive",
            ],
            "authentication": [
                "tests.django.authentication.test_auth_comprehensive",
            ],
            "projects": [
                "tests.django.projects.test_projects_comprehensive",
            ],
            "assets": [
                "tests.django.assets",
            ],
            "infrastructure": [
                "tests.django.infrastructure",
            ],
            "api": [
                "tests.django.api",
            ],
            "unit": [
                "tests.unit",
            ],
            "integration": [
                "tests.integration",
            ],
            "functional": [
                "tests.functional",
            ],
        }

        # Fast tests (quick unit tests)
        self.fast_tests = [
            "tests.unit",
            "tests.django.core.test_views_comprehensive.TestDashboardViews",
            "tests.django.authentication.test_auth_comprehensive.TestAuthenticationFlows",
            "tests.django.projects.test_projects_comprehensive.TestProjectViews",
        ]

        # Integration tests (slower, more comprehensive)
        self.integration_tests = [
            "tests.integration",
            "tests.django.core.test_views_comprehensive.TestSecurityFeatures",
            "tests.django.authentication.test_auth_comprehensive.TestOrganizationIsolation",
            "tests.django.projects.test_projects_comprehensive.TestProjectPermissions",
        ]

    def get_test_runner(self):
        """Get the configured Django test runner."""
        TestRunner = get_runner(settings)
        return TestRunner(verbosity=1, interactive=False, keepdb=True)

    def run_tests(self, test_labels=None, **options):
        """Run Django tests with specified labels and options."""

        if not test_labels:
            test_labels = []

        # Configure test runner options
        runner_options = {
            "verbosity": options.get("verbosity", 1),
            "interactive": False,
            "keepdb": options.get("keepdb", True),
            "parallel": options.get("parallel", 1) if options.get("parallel") else 1,
            "debug_mode": options.get("debug", False),
            "shuffle": options.get("shuffle", False),
            "reverse": options.get("reverse", False),
        }

        # Create test runner
        TestRunner = get_runner(settings)
        test_runner = TestRunner(**runner_options)

        # Run tests
        print(f"Running Django tests: {test_labels or 'All tests'}")
        print(f"Settings module: {settings.SETTINGS_MODULE}")
        print(f"Database: {settings.DATABASES['default']['NAME']}")
        print("-" * 60)

        start_time = time.time()

        try:
            failures = test_runner.run_tests(test_labels)

            end_time = time.time()
            duration = end_time - start_time

            print("-" * 60)
            print(f"Tests completed in {duration:.2f} seconds")

            if failures:
                print(f"❌ {failures} test(s) failed")
                return False
            else:
                print("✅ All tests passed!")
                return True

        except Exception as e:
            print(f"❌ Error running tests: {e}")
            return False

    def run_with_coverage(self, test_labels=None, **options):
        """Run tests with coverage analysis."""
        try:
            import coverage
        except ImportError:
            print("❌ Coverage.py not installed. Install with: pip install coverage")
            return False

        # Initialize coverage
        cov = coverage.Coverage(
            source=["apps", "config"],
            omit=[
                "*/migrations/*",
                "*/tests/*",
                "*/venv/*",
                "*/env/*",
                "manage.py",
                "*/settings/*",
                "*/asgi.py",
                "*/wsgi.py",
            ],
        )

        print("🔍 Running tests with coverage analysis...")
        cov.start()

        # Run tests
        success = self.run_tests(test_labels, **options)

        # Stop coverage and generate report
        cov.stop()
        cov.save()

        print("\n" + "=" * 60)
        print("📊 COVERAGE REPORT")
        print("=" * 60)

        # Console report
        cov.report()

        # HTML report
        html_dir = project_root / "reports" / "coverage"
        html_dir.mkdir(parents=True, exist_ok=True)
        cov.html_report(directory=str(html_dir))

        print(f"\n📄 Detailed HTML coverage report: {html_dir}/index.html")

        return success

    def list_available_tests(self):
        """List all available test categories and modules."""
        print("📋 AVAILABLE TEST CATEGORIES")
        print("=" * 50)

        for category, modules in self.test_categories.items():
            print(f"\n🏷️  {category.upper()}")
            for module in modules:
                print(f"   • {module}")

        print("\n🚀 FAST TESTS")
        for test in self.fast_tests:
            print(f"   • {test}")

        print("\n🔗 INTEGRATION TESTS")
        for test in self.integration_tests:
            print(f"   • {test}")

    def validate_test_environment(self):
        """Validate that the test environment is properly configured."""
        print("🔧 Validating test environment...")

        issues = []

        # Check Django settings
        if not hasattr(settings, "DATABASES"):
            issues.append("DATABASES not configured")

        if not settings.DEBUG:
            print("ℹ️  Running in production mode (DEBUG=False)")

        # Check test database
        db_config = settings.DATABASES.get("default", {})
        if "test" not in db_config.get("NAME", "").lower():
            print("⚠️  Warning: Database name doesn't contain 'test'")

        # Check required apps
        required_apps = [
            "django.contrib.auth",
            "django.contrib.contenttypes",
            "django.contrib.sessions",
            "apps.core",
            "apps.authentication",
            "apps.projects",
        ]

        for app in required_apps:
            if app not in settings.INSTALLED_APPS:
                issues.append(f"Required app not installed: {app}")

        if issues:
            print("❌ Environment validation failed:")
            for issue in issues:
                print(f"   • {issue}")
            return False

        print("✅ Test environment validation passed")
        return True


def main():
    """Main test runner entry point."""
    parser = argparse.ArgumentParser(
        description="Django Test Runner for CLEAR Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__,
    )

    parser.add_argument(
        "--app",
        choices=["core", "authentication", "projects", "assets", "infrastructure", "api"],
        help="Run tests for specific app",
    )

    parser.add_argument("--fast", action="store_true", help="Run only fast unit tests")

    parser.add_argument("--integration", action="store_true", help="Run integration tests")

    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage analysis")

    parser.add_argument("--parallel", type=int, help="Run tests in parallel with N processes")

    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    parser.add_argument("--keepdb", action="store_true", default=True, help="Keep test database between runs")

    parser.add_argument("--shuffle", action="store_true", help="Shuffle test order")

    parser.add_argument("--reverse", action="store_true", help="Run tests in reverse order")

    parser.add_argument("--debug", action="store_true", help="Run tests in debug mode")

    parser.add_argument("--list", action="store_true", help="List available test categories")

    parser.add_argument("--validate", action="store_true", help="Validate test environment")

    parser.add_argument("test_labels", nargs="*", help="Specific test labels to run")

    args = parser.parse_args()

    # Create test runner
    runner = DjangoTestRunner()

    # Handle special commands
    if args.list:
        runner.list_available_tests()
        return 0

    if args.validate:
        if runner.validate_test_environment():
            return 0
        else:
            return 1

    # Validate environment before running tests
    if not runner.validate_test_environment():
        print("❌ Environment validation failed. Use --validate for details.")
        return 1

    # Determine test labels
    test_labels = args.test_labels or []

    if args.app:
        test_labels.extend(runner.test_categories.get(args.app, []))
    elif args.fast:
        test_labels.extend(runner.fast_tests)
    elif args.integration:
        test_labels.extend(runner.integration_tests)

    # Configure options
    options = {
        "verbosity": 2 if args.verbose else 1,
        "parallel": args.parallel,
        "keepdb": args.keepdb,
        "shuffle": args.shuffle,
        "reverse": args.reverse,
        "debug": args.debug,
    }

    # Run tests
    if args.coverage:
        success = runner.run_with_coverage(test_labels, **options)
    else:
        success = runner.run_tests(test_labels, **options)

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
