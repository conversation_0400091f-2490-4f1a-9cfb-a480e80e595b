"""
Unit tests for authentication functionality.
"""

import pytest
from django.contrib.auth import get_user_model
from django.urls import reverse

from tests.utils.base import BaseTestCase, HTMXTestCase

User = get_user_model()


@pytest.mark.unit
class TestUserModel(BaseTestCase):
    """Test User model functionality."""

    def test_create_user(self):
        """Test creating a regular user."""
        user = self.create_user(username="testuser", email="<EMAIL>")
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.check_password("testpass123"))

    def test_create_superuser(self):
        """Test creating a superuser."""
        admin = self.create_admin_user()
        self.assertTrue(admin.is_active)
        self.assertTrue(admin.is_staff)
        self.assertTrue(admin.is_superuser)

    def test_user_string_representation(self):
        """Test user string representation."""
        user = self.create_user(username="john_doe")
        self.assertEqual(str(user), "john_doe")


@pytest.mark.unit
@pytest.mark.htmx
class TestLoginView(HTMXTestCase):
    """Test login view functionality."""

    def test_login_page_renders(self):
        """Test that login page renders correctly."""
        response = self.client.get(reverse("authentication:login"))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Login")

    def test_htmx_login_form(self):
        """Test HTMX login form submission."""
        # Create test user
        self.create_user()

        # Submit login form via HTMX
        login_data = {
            "username": "<EMAIL>",
            "password": "testpass123",
        }
        response = self.htmx_post(reverse("authentication:login"), login_data)

        # Check response
        self.assert_htmx_response(response)
        # In a real app, might check for HX-Redirect or success message

    def test_invalid_login(self):
        """Test login with invalid credentials."""
        login_data = {
            "username": "<EMAIL>",
            "password": "wrongpass",
        }
        response = self.client.post(reverse("authentication:login"), login_data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Invalid credentials")


@pytest.mark.unit
@pytest.mark.db
class TestUserPermissions(BaseTestCase):
    """Test user permissions and groups."""

    def test_user_permissions(self):
        """Test that users can be assigned permissions."""
        user = self.create_user()
        self.assertEqual(user.user_permissions.count(), 0)

        # Add permission logic here when implemented
        # user.user_permissions.add(some_permission)
        # self.assertTrue(user.has_perm('app.permission'))
