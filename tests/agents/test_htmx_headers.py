#!/usr/bin/env python3
"""
Test for HTMX header handling validation.

This validator checks:
- Proper handling of HTMX request headers
- Correct HTMX response headers
- CSRF token handling for HTMX requests
- Content-Type headers for HTMX responses
- Proper use of HTMX-specific Django middleware
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Optional

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from tests.agents.base_validator import BaseValidator, parse_args


class HTMXHeaderUsage:
    """Represents HTMX header usage in code."""

    def __init__(self, header_name: str, usage_type: str, file_path: Path, line_number: int, context: str = ""):
        self.header_name = header_name
        self.usage_type = usage_type  # 'request' or 'response'
        self.file_path = file_path
        self.line_number = line_number
        self.context = context


class HTMXHeaderValidator(BaseValidator):
    """Validator for HTMX header handling."""

    def __init__(self):
        super().__init__(
            name="HTMX Header Validator", description="Validates proper HTMX header handling in views and middleware"
        )
        self.header_usages: List[HTMXHeaderUsage] = []

        # HTMX request headers
        self.htmx_request_headers = {
            "HX-Request": "Indicates this is an HTMX request",
            "HX-Trigger": "The ID of the element that triggered the request",
            "HX-Trigger-Name": "The name of the element that triggered the request",
            "HX-Target": "The ID of the target element",
            "HX-Current-URL": "The current URL of the browser",
            "HX-Prompt": "The user response to hx-prompt",
            "HX-Boosted": "Indicates the request is via hx-boost",
        }

        # HTMX response headers
        self.htmx_response_headers = {
            "HX-Trigger": "Triggers events on the client side",
            "HX-Trigger-After-Settle": "Triggers events after settling",
            "HX-Trigger-After-Swap": "Triggers events after swapping",
            "HX-Redirect": "Redirects the browser to a new location",
            "HX-Refresh": "Refreshes the current page",
            "HX-Retarget": "Updates the target element",
            "HX-Reswap": "Updates the swap strategy",
            "HX-Push-Url": "Pushes a URL to the browser history",
            "HX-Replace-Url": "Replaces the current URL in browser history",
        }

        # Common header access patterns
        self.header_patterns = [
            # request.META pattern
            (r'request\.META\.get\([\'"]HTTP_([A-Z_]+)[\'"]', "META"),
            # request.headers pattern
            (r'request\.headers\.get\([\'"]([A-Za-z-]+)[\'"]', "headers"),
            # Direct header access
            (r'request\.META\[[\'"]HTTP_([A-Z_]+)[\'"]\]', "META_direct"),
            (r'request\.headers\[[\'"]([A-Za-z-]+)[\'"]\]', "headers_direct"),
            # Response header setting
            (r'response\[[\'"]([A-Za-z-]+)[\'"]\]\s*=', "response_set"),
            (r'response\.headers\[[\'"]([A-Za-z-]+)[\'"]\]\s*=', "response_headers_set"),
            # HttpResponse with headers
            (r'HttpResponse\([^)]*headers\s*=\s*{[^}]*[\'"]([A-Za-z-]+)[\'"]', "HttpResponse_init"),
        ]

    def normalize_header_name(self, header_name: str, source: str) -> str:
        """Normalize header name based on how it was accessed."""
        if source in ["META", "META_direct"]:
            # Convert HTTP_HX_REQUEST to HX-Request
            if header_name.startswith("HTTP_"):
                header_name = header_name[5:]
            header_name = header_name.replace("_", "-").title()
        return header_name

    def check_htmx_request_handling(self, file_path: Path) -> None:
        """Check for proper HTMX request header handling."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Check for request.htmx usage (django-htmx package)
            if "request.htmx" in content:
                # Good - using django-htmx package
                lines = content.split("\n")
                for i, line in enumerate(lines):
                    if "request.htmx" in line:
                        self.header_usages.append(
                            HTMXHeaderUsage(
                                header_name="request.htmx",
                                usage_type="request",
                                file_path=file_path,
                                line_number=i + 1,
                                context="django-htmx package",
                            )
                        )

            # Check for manual header access
            for pattern, source in self.header_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    header_name = match.group(1)
                    header_name = self.normalize_header_name(header_name, source)
                    line_number = content[: match.start()].count("\n") + 1

                    # Check if it's an HTMX header
                    if header_name.startswith("HX-") or header_name.startswith("Hx-"):
                        usage_type = "response" if "response" in source else "request"

                        usage = HTMXHeaderUsage(
                            header_name=header_name,
                            usage_type=usage_type,
                            file_path=file_path,
                            line_number=line_number,
                            context=source,
                        )
                        self.header_usages.append(usage)

                        # Check for issues
                        self.check_header_usage_issues(usage, content, line_number)

        except Exception as e:
            self.add_issue(
                file_path=file_path,
                issue_type="file_read_error",
                message=f"Failed to read file: {str(e)}",
                severity="medium",
            )

    def check_header_usage_issues(self, usage: HTMXHeaderUsage, content: str, line_number: int) -> None:
        """Check for common issues with header usage."""
        lines = content.split("\n")
        if line_number > 0 and line_number <= len(lines):
            line = lines[line_number - 1]

            # Check for missing django-htmx middleware
            if usage.usage_type == "request" and usage.context in ["META", "headers"]:
                # Manual header checking instead of using request.htmx
                if "HX-Request" in usage.header_name:
                    self.add_issue(
                        file_path=usage.file_path,
                        line_number=usage.line_number,
                        issue_type="manual_htmx_detection",
                        message="Consider using django-htmx middleware instead of manual header checking",
                        severity="low",
                        code_snippet=line.strip(),
                    )

            # Check for incorrect header names
            if usage.usage_type == "request":
                correct_name = None
                for correct_header in self.htmx_request_headers.keys():
                    if usage.header_name.lower() == correct_header.lower() and usage.header_name != correct_header:
                        correct_name = correct_header
                        break

                if correct_name:
                    self.add_issue(
                        file_path=usage.file_path,
                        line_number=usage.line_number,
                        issue_type="incorrect_header_case",
                        message=f"HTMX header '{usage.header_name}' should be '{correct_name}'",
                        severity="medium",
                        code_snippet=line.strip(),
                    )

            # Check for CSRF issues with HTMX
            if usage.usage_type == "request" and "post" in line.lower():
                # Check if CSRF is handled nearby
                context_start = max(0, line_number - 10)
                context_end = min(len(lines), line_number + 10)
                context_lines = lines[context_start:context_end]
                context_text = "\n".join(context_lines)

                csrf_patterns = ["csrf_exempt", "csrf_token", "csrftoken", "@csrf_exempt"]
                if not any(pattern in context_text for pattern in csrf_patterns):
                    self.add_issue(
                        file_path=usage.file_path,
                        line_number=usage.line_number,
                        issue_type="htmx_csrf_check",
                        message="HTMX POST request detected - ensure CSRF token is properly handled",
                        severity="medium",
                        code_snippet=line.strip(),
                    )

    def check_middleware_configuration(self) -> None:
        """Check if HTMX middleware is properly configured."""
        settings_files = list(self.project_root.glob("**/settings*.py"))
        settings_files.extend(self.project_root.glob("config/**/settings*.py"))

        htmx_middleware_found = False

        for settings_file in settings_files:
            try:
                with open(settings_file, "r", encoding="utf-8") as f:
                    content = f.read()

                if "django_htmx.middleware.HtmxMiddleware" in content:
                    htmx_middleware_found = True
                    break

            except Exception:
                pass

        if not htmx_middleware_found and self.header_usages:
            # Only report if we found HTMX usage
            htmx_request_usages = [u for u in self.header_usages if u.usage_type == "request"]
            if htmx_request_usages:
                self.add_issue(
                    file_path=self.project_root / "config/settings",
                    issue_type="missing_htmx_middleware",
                    message="django-htmx middleware not found in settings but HTMX headers are being used",
                    severity="medium",
                )

    def check_response_headers(self) -> None:
        """Check for proper use of HTMX response headers."""
        response_usages = [u for u in self.header_usages if u.usage_type == "response"]

        for usage in response_usages:
            # Check if header name is valid
            if not any(usage.header_name.startswith(h) for h in self.htmx_response_headers.keys()):
                self.add_issue(
                    file_path=usage.file_path,
                    line_number=usage.line_number,
                    issue_type="unknown_htmx_response_header",
                    message=f"Unknown HTMX response header: {usage.header_name}",
                    severity="medium",
                )

            # Check for HX-Trigger with complex values
            if usage.header_name == "HX-Trigger":
                # Could check if JSON encoding is used for complex triggers
                pass

    def scan(self, target_path: Optional[str] = None, **kwargs) -> None:
        """Scan for HTMX header usage issues."""
        # Get all Python files
        python_files = self.get_python_files(target_path)

        # Focus on views and middleware
        relevant_files = []
        for py_file in python_files:
            if any(pattern in str(py_file) for pattern in ["views", "middleware", "handlers"]):
                relevant_files.append(py_file)

        print(f"Scanning {len(relevant_files)} files for HTMX header usage...")

        for py_file in relevant_files:
            self.check_htmx_request_handling(py_file)
            self.summary["total_files_scanned"] += 1

        # Check middleware configuration
        self.check_middleware_configuration()

        # Check response headers
        self.check_response_headers()

        print(f"Found {len(self.header_usages)} HTMX header usages")

        # Summary statistics
        request_headers = [u for u in self.header_usages if u.usage_type == "request"]
        response_headers = [u for u in self.header_usages if u.usage_type == "response"]

        if request_headers or response_headers:
            print(f"  Request headers: {len(request_headers)}")
            print(f"  Response headers: {len(response_headers)}")


def main():
    """Main entry point."""
    args = parse_args()

    validator = HTMXHeaderValidator()
    success = validator.run(target_path=args.target, output_format=args.format, output_file=args.output)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
