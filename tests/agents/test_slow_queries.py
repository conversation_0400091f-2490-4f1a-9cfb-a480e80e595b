#!/usr/bin/env python3
"""
Test for slow database queries.

This validator detects:
- Queries without proper indexes
- Complex joins that could be optimized
- Queries that might benefit from database-level optimization
- Missing db_index on frequently queried fields
- Complex aggregations that could be cached
"""

import ast
import os
import re
import sys
from pathlib import Path
from typing import Dict, Optional

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from tests.agents.base_validator import BaseValidator, parse_args


class ModelFieldAnalyzer(ast.NodeVisitor):
    """Analyze model fields for index optimization opportunities."""

    def __init__(self, file_path: Path):
        self.file_path = file_path
        self.model_name = None
        self.fields = {}  # field_name -> field_info
        self.current_class = None

    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Track class definitions to find models."""
        old_class = self.current_class
        self.current_class = node.name

        # Check if this is a Django model
        is_model = False
        for base in node.bases:
            if isinstance(base, ast.Attribute) and base.attr == "Model":
                is_model = True
            elif isinstance(base, ast.Name) and "Model" in base.id:
                is_model = True

        if is_model:
            self.model_name = node.name
            self.analyze_model_class(node)

        self.generic_visit(node)
        self.current_class = old_class

    def analyze_model_class(self, node: ast.ClassDef) -> None:
        """Analyze model class for field definitions."""
        for item in node.body:
            if isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name):
                        field_info = self.extract_field_info(item.value)
                        if field_info:
                            field_info["name"] = target.id
                            field_info["line"] = item.lineno
                            self.fields[target.id] = field_info

            # Check Meta class for indexes
            elif isinstance(item, ast.ClassDef) and item.name == "Meta":
                self.analyze_meta_class(item)

    def extract_field_info(self, node: ast.AST) -> Optional[Dict]:
        """Extract field information from field definition."""
        if not isinstance(node, ast.Call):
            return None

        field_type = None
        if isinstance(node.func, ast.Name):
            field_type = node.func.id
        elif isinstance(node.func, ast.Attribute):
            field_type = node.func.attr

        if not field_type or not field_type.endswith("Field"):
            return None

        field_info = {
            "type": field_type,
            "has_db_index": False,
            "is_foreign_key": field_type in ["ForeignKey", "OneToOneField"],
            "is_many_to_many": field_type == "ManyToManyField",
            "unique": False,
        }

        # Check for db_index argument
        for keyword in node.keywords:
            if keyword.arg == "db_index" and isinstance(keyword.value, ast.Constant):
                field_info["has_db_index"] = keyword.value.value
            elif keyword.arg == "unique" and isinstance(keyword.value, ast.Constant):
                field_info["unique"] = keyword.value.value

        return field_info

    def analyze_meta_class(self, node: ast.ClassDef) -> None:
        """Analyze Meta class for index definitions."""
        for item in node.body:
            if isinstance(item, ast.Assign):
                for target in item.targets:
                    if isinstance(target, ast.Name) and target.id in ["indexes", "index_together"]:
                        # Model has custom indexes defined
                        pass


class QueryComplexityAnalyzer(ast.NodeVisitor):
    """Analyze queries for complexity and optimization opportunities."""

    def __init__(self, file_path: Path):
        self.file_path = file_path
        self.complex_queries = []
        self.current_function = None

        # Patterns that indicate complex queries
        self.complex_patterns = {
            "annotate": "Complex aggregation",
            "aggregate": "Aggregation query",
            "extra": "Raw SQL usage",
            "raw": "Raw SQL query",
            "distinct": "Distinct operation",
            "union": "Union operation",
            "intersection": "Intersection operation",
            "difference": "Difference operation",
        }

        # Chained methods that increase complexity
        self.chain_methods = {
            "filter",
            "exclude",
            "order_by",
            "values",
            "values_list",
            "select_related",
            "prefetch_related",
            "annotate",
            "aggregate",
        }

    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Track function context."""
        old_function = self.current_function
        self.current_function = node.name
        self.generic_visit(node)
        self.current_function = old_function

    def visit_Call(self, node: ast.Call) -> None:
        """Analyze function calls for complex queries."""
        if isinstance(node.func, ast.Attribute):
            method_name = node.func.attr

            # Check for complex query patterns
            if method_name in self.complex_patterns:
                self.analyze_complex_query(node, method_name)

            # Check for chained queries
            chain_length = self.get_chain_length(node)
            if chain_length >= 3:  # Arbitrary threshold for "complex"
                self.complex_queries.append(
                    {
                        "type": "long_chain",
                        "chain_length": chain_length,
                        "line": node.lineno,
                        "context": self.current_function,
                        "description": f"Long query chain ({chain_length} methods)",
                    }
                )

        self.generic_visit(node)

    def analyze_complex_query(self, node: ast.Call, query_type: str) -> None:
        """Analyze a complex query for optimization opportunities."""
        complexity_info = {
            "type": query_type,
            "line": node.lineno,
            "context": self.current_function,
            "description": self.complex_patterns[query_type],
        }

        # Check for specific complexity indicators
        if query_type == "annotate":
            # Check if using Count, Sum, etc. without proper indexes
            complexity_info["suggestion"] = "Consider database indexes on aggregated fields"
        elif query_type == "raw":
            complexity_info["suggestion"] = "Raw SQL queries bypass ORM optimizations"
        elif query_type in ["union", "intersection", "difference"]:
            complexity_info["suggestion"] = "Set operations can be expensive on large datasets"

        self.complex_queries.append(complexity_info)

    def get_chain_length(self, node: ast.Call) -> int:
        """Count the length of a method chain."""
        count = 0
        current = node

        while isinstance(current, ast.Call) and isinstance(current.func, ast.Attribute):
            if current.func.attr in self.chain_methods:
                count += 1

            if isinstance(current.func.value, ast.Call):
                current = current.func.value
            else:
                break

        return count


class SlowQueryValidator(BaseValidator):
    """Validator for detecting potentially slow database queries."""

    def __init__(self):
        super().__init__(
            name="Slow Query Validator", description="Detects potentially slow database queries and missing indexes"
        )

        # Common field names that often need indexes
        self.common_filter_fields = {
            "created_at",
            "updated_at",
            "created",
            "modified",
            "status",
            "state",
            "type",
            "category",
            "slug",
            "email",
            "username",
            "is_active",
            "is_deleted",
            "organization",
            "tenant",
            "user",
            "owner",
        }

    def analyze_model_file(self, file_path: Path) -> None:
        """Analyze model files for index opportunities."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            if "models.Model" not in content and "Model" not in content:
                return

            tree = ast.parse(content, filename=str(file_path))
            analyzer = ModelFieldAnalyzer(file_path)
            analyzer.visit(tree)

            # Check for missing indexes on common filter fields
            for field_name, field_info in analyzer.fields.items():
                if self.should_have_index(field_name, field_info):
                    self.add_issue(
                        file_path=file_path,
                        line_number=field_info["line"],
                        issue_type="missing_index",
                        message=f"Field '{field_name}' is commonly used in queries but lacks db_index=True",
                        severity="medium",
                        code_snippet=f"{field_name} = models.{field_info['type']}(db_index=True, ...)",
                    )

        except Exception as e:
            self.add_issue(
                file_path=file_path,
                issue_type="parse_error",
                message=f"Failed to analyze model: {str(e)}",
                severity="low",
            )

    def analyze_view_file(self, file_path: Path) -> None:
        """Analyze view files for complex queries."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Quick check for query-related content
            if not any(term in content for term in [".objects", "QuerySet", "annotate", "aggregate"]):
                return

            tree = ast.parse(content, filename=str(file_path))
            analyzer = QueryComplexityAnalyzer(file_path)
            analyzer.visit(tree)

            # Report complex queries
            for query in analyzer.complex_queries:
                severity = "high" if query["type"] in ["raw", "extra"] else "medium"

                self.add_issue(
                    file_path=file_path,
                    line_number=query["line"],
                    issue_type="complex_query",
                    message=f"{query['description']} detected in {query['context'] or 'module'}",
                    severity=severity,
                    code_snippet=query.get("suggestion", ""),
                )

        except Exception as e:
            self.add_issue(
                file_path=file_path,
                issue_type="parse_error",
                message=f"Failed to analyze queries: {str(e)}",
                severity="low",
            )

    def should_have_index(self, field_name: str, field_info: Dict) -> bool:
        """Determine if a field should have an index."""
        # Skip if already indexed or unique (unique implies index)
        if field_info["has_db_index"] or field_info["unique"]:
            return False

        # Skip foreign keys and many-to-many (they have indexes by default)
        if field_info["is_foreign_key"] or field_info["is_many_to_many"]:
            return False

        # Check if field name matches common filter patterns
        field_lower = field_name.lower()

        # Direct matches
        if field_lower in self.common_filter_fields:
            return True

        # Pattern matches
        patterns = ["_id$", "_at$", "_date$", "_time$", "^is_", "_status$", "_type$"]
        for pattern in patterns:
            if re.search(pattern, field_lower):
                return True

        return False

    def check_for_count_queries(self, file_path: Path) -> None:
        """Check for inefficient count() usage."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Look for patterns like len(queryset) instead of queryset.count()
            len_pattern = re.compile(r"len\s*\(\s*\w+\.objects\.(?:all|filter|exclude)\s*\([^)]*\)\s*\)")

            for match in len_pattern.finditer(content):
                line_num = content[: match.start()].count("\n") + 1

                self.add_issue(
                    file_path=file_path,
                    line_number=line_num,
                    issue_type="inefficient_count",
                    message="Using len() on queryset forces evaluation - use .count() instead",
                    severity="high",
                    code_snippet="queryset.count() instead of len(queryset)",
                )

        except Exception:
            pass

    def scan(self, target_path: Optional[str] = None, **kwargs) -> None:
        """Scan for slow query patterns."""
        python_files = self.get_python_files(target_path)

        # Separate model files from other files
        model_files = []
        view_files = []

        for py_file in python_files:
            if "models" in py_file.name or "model" in py_file.name:
                model_files.append(py_file)
            elif any(pattern in str(py_file) for pattern in ["views", "api", "serializers"]):
                view_files.append(py_file)

        print(f"Scanning {len(model_files)} model files for index opportunities...")
        for file_path in model_files:
            self.analyze_model_file(file_path)
            self.summary["total_files_scanned"] += 1

        print(f"Scanning {len(view_files)} view files for complex queries...")
        for file_path in view_files:
            self.analyze_view_file(file_path)
            self.check_for_count_queries(file_path)
            self.summary["total_files_scanned"] += 1


def main():
    """Main entry point."""
    args = parse_args()

    validator = SlowQueryValidator()
    success = validator.run(target_path=args.target, output_format=args.format, output_file=args.output)

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
