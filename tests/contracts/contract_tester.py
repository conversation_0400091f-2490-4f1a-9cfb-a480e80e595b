"""
Contract Testing System for API Integrations

This module provides comprehensive contract testing capabilities to ensure
API integrations work correctly by validating contracts between services.
It supports both consumer-driven contracts and provider verification.

Features:
- Contract definition and validation
- Consumer-driven contract testing
- Provider verification
- Schema validation
- Integration with existing test infrastructure
- Comprehensive reporting and analysis
"""

import json
import requests
import jsonschema
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime
import yaml

logger = logging.getLogger(__name__)


class ContractType(Enum):
    """Types of contracts that can be tested."""
    HTTP_API = "http_api"
    GRAPHQL = "graphql"
    WEBSOCKET = "websocket"
    MESSAGE_QUEUE = "message_queue"


class TestResult(Enum):
    """Contract test result status."""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


@dataclass
class ContractInteraction:
    """Represents a single interaction in a contract."""
    description: str
    request: Dict[str, Any]
    response: Dict[str, Any]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class Contract:
    """Represents a contract between consumer and provider."""
    consumer: str
    provider: str
    contract_type: ContractType
    interactions: List[ContractInteraction]
    version: str = "1.0.0"
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert contract to dictionary for serialization."""
        return {
            'consumer': self.consumer,
            'provider': self.provider,
            'contract_type': self.contract_type.value,
            'version': self.version,
            'interactions': [
                {
                    'description': interaction.description,
                    'request': interaction.request,
                    'response': interaction.response,
                    'metadata': interaction.metadata
                }
                for interaction in self.interactions
            ],
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Contract':
        """Create contract from dictionary."""
        interactions = [
            ContractInteraction(
                description=interaction['description'],
                request=interaction['request'],
                response=interaction['response'],
                metadata=interaction.get('metadata')
            )
            for interaction in data['interactions']
        ]

        return cls(
            consumer=data['consumer'],
            provider=data['provider'],
            contract_type=ContractType(data['contract_type']),
            interactions=interactions,
            version=data.get('version', '1.0.0'),
            metadata=data.get('metadata')
        )


@dataclass
class ContractTestResult:
    """Result of a contract test execution."""
    contract_id: str
    interaction_id: str
    result: TestResult
    description: str
    expected: Dict[str, Any]
    actual: Optional[Dict[str, Any]]
    error_message: Optional[str] = None
    execution_time: float = 0.0
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class ContractValidator:
    """Validates contract interactions against schemas and rules."""

    def __init__(self):
        self.validators = {
            'json_schema': self._validate_json_schema,
            'status_code': self._validate_status_code,
            'headers': self._validate_headers,
            'content_type': self._validate_content_type,
        }

    def validate_response(self, expected: Dict[str, Any], actual: Dict[str, Any]) -> List[str]:
        """
        Validate actual response against expected contract.

        Returns:
            List of validation errors (empty if valid)
        """
        errors = []

        for validation_type, expected_value in expected.items():
            if validation_type in self.validators:
                try:
                    self.validators[validation_type](expected_value, actual)
                except Exception as e:
                    errors.append(f"{validation_type}: {str(e)}")

        return errors

    def _validate_json_schema(self, schema: Dict[str, Any], actual: Dict[str, Any]):
        """Validate JSON response against schema."""
        response_body = actual.get('body', {})
        jsonschema.validate(response_body, schema)

    def _validate_status_code(self, expected_code: int, actual: Dict[str, Any]):
        """Validate HTTP status code."""
        actual_code = actual.get('status_code')
        if actual_code != expected_code:
            raise ValueError(f"Expected status code {expected_code}, got {actual_code}")

    def _validate_headers(self, expected_headers: Dict[str, str], actual: Dict[str, Any]):
        """Validate HTTP headers."""
        actual_headers = actual.get('headers', {})

        for header_name, expected_value in expected_headers.items():
            actual_value = actual_headers.get(header_name)
            if actual_value != expected_value:
                raise ValueError(f"Expected header {header_name}={expected_value}, got {actual_value}")

    def _validate_content_type(self, expected_type: str, actual: Dict[str, Any]):
        """Validate content type header."""
        actual_headers = actual.get('headers', {})
        actual_type = actual_headers.get('content-type', '').split(';')[0]

        if actual_type != expected_type:
            raise ValueError(f"Expected content-type {expected_type}, got {actual_type}")


class ContractTester:
    """
    Main contract testing system that executes contract tests.
    """

    def __init__(self, contracts_dir: Optional[str] = None):
        self.contracts_dir = Path(contracts_dir or "tests/contracts")
        self.validator = ContractValidator()
        self.results: List[ContractTestResult] = []

    def load_contracts(self, pattern: str = "*.json") -> List[Contract]:
        """Load contracts from files."""
        contracts = []

        for contract_file in self.contracts_dir.glob(pattern):
            try:
                with open(contract_file, 'r') as f:
                    if contract_file.suffix == '.json':
                        data = json.load(f)
                    elif contract_file.suffix in ['.yml', '.yaml']:
                        data = yaml.safe_load(f)
                    else:
                        continue

                contract = Contract.from_dict(data)
                contracts.append(contract)
                logger.info(f"Loaded contract: {contract.consumer} -> {contract.provider}")

            except Exception as e:
                logger.error(f"Failed to load contract from {contract_file}: {e}")

        return contracts

    def run_consumer_tests(self, contracts: List[Contract], mock_server_url: str) -> Dict[str, Any]:
        """
        Run consumer-driven contract tests.

        Args:
            contracts: List of contracts to test
            mock_server_url: URL of mock server providing expected responses

        Returns:
            Test results summary
        """
        logger.info(f"Running consumer tests against mock server: {mock_server_url}")

        total_tests = 0
        passed_tests = 0

        for contract in contracts:
            for i, interaction in enumerate(contract.interactions):
                total_tests += 1
                interaction_id = f"{contract.consumer}_{contract.provider}_{i}"

                result = self._test_consumer_interaction(
                    contract, interaction, interaction_id, mock_server_url
                )

                self.results.append(result)

                if result.result == TestResult.PASSED:
                    passed_tests += 1

        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'results': self.results
        }

    def run_provider_tests(self, contracts: List[Contract], provider_url: str) -> Dict[str, Any]:
        """
        Run provider verification tests.

        Args:
            contracts: List of contracts to verify
            provider_url: URL of the actual provider service

        Returns:
            Test results summary
        """
        logger.info(f"Running provider verification against: {provider_url}")

        total_tests = 0
        passed_tests = 0

        for contract in contracts:
            for i, interaction in enumerate(contract.interactions):
                total_tests += 1
                interaction_id = f"{contract.consumer}_{contract.provider}_{i}"

                result = self._test_provider_interaction(
                    contract, interaction, interaction_id, provider_url
                )

                self.results.append(result)

                if result.result == TestResult.PASSED:
                    passed_tests += 1

        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'results': self.results
        }

    def _test_consumer_interaction(self, contract: Contract, interaction: ContractInteraction,
                                 interaction_id: str, mock_server_url: str) -> ContractTestResult:
        """Test a single consumer interaction."""
        import time
        start_time = time.time()

        try:
            # Make request to mock server
            response = self._make_http_request(interaction.request, mock_server_url)

            # Validate response against contract
            errors = self.validator.validate_response(interaction.response, response)

            if errors:
                return ContractTestResult(
                    contract_id=f"{contract.consumer}_{contract.provider}",
                    interaction_id=interaction_id,
                    result=TestResult.FAILED,
                    description=interaction.description,
                    expected=interaction.response,
                    actual=response,
                    error_message="; ".join(errors),
                    execution_time=time.time() - start_time
                )
            else:
                return ContractTestResult(
                    contract_id=f"{contract.consumer}_{contract.provider}",
                    interaction_id=interaction_id,
                    result=TestResult.PASSED,
                    description=interaction.description,
                    expected=interaction.response,
                    actual=response,
                    execution_time=time.time() - start_time
                )

        except Exception as e:
            return ContractTestResult(
                contract_id=f"{contract.consumer}_{contract.provider}",
                interaction_id=interaction_id,
                result=TestResult.ERROR,
                description=interaction.description,
                expected=interaction.response,
                actual=None,
                error_message=str(e),
                execution_time=time.time() - start_time
            )

    def _test_provider_interaction(self, contract: Contract, interaction: ContractInteraction,
                                 interaction_id: str, provider_url: str) -> ContractTestResult:
        """Test a single provider interaction."""
        import time
        start_time = time.time()

        try:
            # Make request to actual provider
            response = self._make_http_request(interaction.request, provider_url)

            # Validate response against contract
            errors = self.validator.validate_response(interaction.response, response)

            if errors:
                return ContractTestResult(
                    contract_id=f"{contract.consumer}_{contract.provider}",
                    interaction_id=interaction_id,
                    result=TestResult.FAILED,
                    description=interaction.description,
                    expected=interaction.response,
                    actual=response,
                    error_message="; ".join(errors),
                    execution_time=time.time() - start_time
                )
            else:
                return ContractTestResult(
                    contract_id=f"{contract.consumer}_{contract.provider}",
                    interaction_id=interaction_id,
                    result=TestResult.PASSED,
                    description=interaction.description,
                    expected=interaction.response,
                    actual=response,
                    execution_time=time.time() - start_time
                )

        except Exception as e:
            return ContractTestResult(
                contract_id=f"{contract.consumer}_{contract.provider}",
                interaction_id=interaction_id,
                result=TestResult.ERROR,
                description=interaction.description,
                expected=interaction.response,
                actual=None,
                error_message=str(e),
                execution_time=time.time() - start_time
            )

    def _make_http_request(self, request_spec: Dict[str, Any], base_url: str) -> Dict[str, Any]:
        """Make HTTP request based on request specification."""
        method = request_spec.get('method', 'GET').upper()
        path = request_spec.get('path', '/')
        headers = request_spec.get('headers', {})
        body = request_spec.get('body')
        query_params = request_spec.get('query', {})

        url = f"{base_url.rstrip('/')}{path}"

        # Make request
        response = requests.request(
            method=method,
            url=url,
            headers=headers,
            json=body if body else None,
            params=query_params,
            timeout=30
        )

        # Convert response to contract format
        return {
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'body': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
        }

    def generate_contract_from_interactions(self, consumer: str, provider: str,
                                          interactions: List[Dict[str, Any]]) -> Contract:
        """Generate a contract from recorded interactions."""
        contract_interactions = []

        for interaction_data in interactions:
            interaction = ContractInteraction(
                description=interaction_data.get('description', 'Generated interaction'),
                request=interaction_data['request'],
                response=interaction_data['response']
            )
            contract_interactions.append(interaction)

        return Contract(
            consumer=consumer,
            provider=provider,
            contract_type=ContractType.HTTP_API,
            interactions=contract_interactions,
            metadata={'generated': True, 'timestamp': datetime.now().isoformat()}
        )

    def save_contract(self, contract: Contract, file_path: str):
        """Save contract to file."""
        contract_data = contract.to_dict()

        with open(file_path, 'w') as f:
            if file_path.endswith('.json'):
                json.dump(contract_data, f, indent=2)
            elif file_path.endswith(('.yml', '.yaml')):
                yaml.dump(contract_data, f, default_flow_style=False)

    def generate_report(self, output_format: str = 'json') -> str:
        """Generate comprehensive contract test report."""
        if not self.results:
            return "No contract test results available"

        # Calculate summary statistics
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.result == TestResult.PASSED])
        failed_tests = len([r for r in self.results if r.result == TestResult.FAILED])
        error_tests = len([r for r in self.results if r.result == TestResult.ERROR])

        summary = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'error_tests': error_tests,
            'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'average_execution_time': sum(r.execution_time for r in self.results) / total_tests if total_tests > 0 else 0
        }

        if output_format == 'json':
            return self._generate_json_report(summary)
        elif output_format == 'html':
            return self._generate_html_report(summary)
        else:
            return self._generate_text_report(summary)

    def _generate_json_report(self, summary: Dict[str, Any]) -> str:
        """Generate JSON format report."""
        report_data = {
            'summary': summary,
            'timestamp': datetime.now().isoformat(),
            'results': [
                {
                    'contract_id': r.contract_id,
                    'interaction_id': r.interaction_id,
                    'result': r.result.value,
                    'description': r.description,
                    'expected': r.expected,
                    'actual': r.actual,
                    'error_message': r.error_message,
                    'execution_time': r.execution_time,
                    'timestamp': r.timestamp.isoformat() if r.timestamp else None
                }
                for r in self.results
            ]
        }

        return json.dumps(report_data, indent=2)

    def _generate_html_report(self, summary: Dict[str, Any]) -> str:
        """Generate HTML format report."""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Contract Testing Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background: #e9e9e9; border-radius: 3px; }}
                .test-result {{ margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .passed {{ background-color: #d4edda; }}
                .failed {{ background-color: #f8d7da; }}
                .error {{ background-color: #fff3cd; }}
            </style>
        </head>
        <body>
            <h1>Contract Testing Report</h1>

            <div class="summary">
                <h2>Summary</h2>
                <div class="metric">Total Tests: {summary['total_tests']}</div>
                <div class="metric">Passed: {summary['passed_tests']}</div>
                <div class="metric">Failed: {summary['failed_tests']}</div>
                <div class="metric">Errors: {summary['error_tests']}</div>
                <div class="metric">Success Rate: {summary['success_rate']:.1f}%</div>
                <div class="metric">Avg Time: {summary['average_execution_time']:.2f}s</div>
            </div>

            <h2>Test Results</h2>
        """

        for result in self.results:
            status_class = result.result.value
            html += f"""
            <div class="test-result {status_class}">
                <h3>{result.description}</h3>
                <p><strong>Contract:</strong> {result.contract_id}</p>
                <p><strong>Status:</strong> {result.result.value.upper()}</p>
                <p><strong>Execution Time:</strong> {result.execution_time:.2f}s</p>
                {f'<p><strong>Error:</strong> {result.error_message}</p>' if result.error_message else ''}
            </div>
            """

        html += "</body></html>"
        return html

    def _generate_text_report(self, summary: Dict[str, Any]) -> str:
        """Generate text format report."""
        lines = [
            "=" * 80,
            "CONTRACT TESTING REPORT",
            "=" * 80,
            f"Generated: {datetime.now().isoformat()}",
            "",
            "SUMMARY:",
            f"  Total Tests: {summary['total_tests']}",
            f"  Passed: {summary['passed_tests']}",
            f"  Failed: {summary['failed_tests']}",
            f"  Errors: {summary['error_tests']}",
            f"  Success Rate: {summary['success_rate']:.1f}%",
            f"  Average Execution Time: {summary['average_execution_time']:.2f}s",
            "",
            "TEST RESULTS:",
            "-" * 40
        ]

        for result in self.results:
            status_icon = {
                TestResult.PASSED: "✅",
                TestResult.FAILED: "❌",
                TestResult.ERROR: "⚠️"
            }.get(result.result, "❓")

            lines.extend([
                f"{status_icon} {result.description}",
                f"   Contract: {result.contract_id}",
                f"   Status: {result.result.value.upper()}",
                f"   Time: {result.execution_time:.2f}s"
            ])

            if result.error_message:
                lines.append(f"   Error: {result.error_message}")

            lines.append("")

        lines.extend([
            "=" * 80
        ])

        return "\n".join(lines)


# Example contract definitions
def create_example_contracts():
    """Create example contracts for demonstration."""

    # User API contract
    user_contract = Contract(
        consumer="web_frontend",
        provider="user_service",
        contract_type=ContractType.HTTP_API,
        interactions=[
            ContractInteraction(
                description="Get user by ID",
                request={
                    "method": "GET",
                    "path": "/api/users/123",
                    "headers": {"Accept": "application/json"}
                },
                response={
                    "status_code": 200,
                    "headers": {"content-type": "application/json"},
                    "json_schema": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "email": {"type": "string", "format": "email"}
                        },
                        "required": ["id", "name", "email"]
                    }
                }
            ),
            ContractInteraction(
                description="Create new user",
                request={
                    "method": "POST",
                    "path": "/api/users",
                    "headers": {"Content-Type": "application/json"},
                    "body": {
                        "name": "John Doe",
                        "email": "<EMAIL>"
                    }
                },
                response={
                    "status_code": 201,
                    "headers": {"content-type": "application/json"},
                    "json_schema": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "name": {"type": "string"},
                            "email": {"type": "string"}
                        },
                        "required": ["id", "name", "email"]
                    }
                }
            )
        ]
    )

    return [user_contract]


if __name__ == "__main__":
    # Example usage
    tester = ContractTester()

    # Create example contracts
    contracts = create_example_contracts()

    # Save example contract
    tester.save_contract(contracts[0], "tests/contracts/user_service.json")

    print("Example contract saved to tests/contracts/user_service.json")
    print("Contract testing system ready for use!")
