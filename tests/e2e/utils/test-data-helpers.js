/**
 * Test Data Helper Functions
 * Provides utilities for creating and managing test data
 */

import path from 'path';
import fs from 'fs';

export class TestDataHelpers {
  constructor(page) {
    this.page = page;
  }

  /**
   * Create test document
   * @param {Object} docData - Document data
   * @returns {string} Document ID
   */
  async createTestDocument(docData = {}) {
    const defaultData = {
      title: 'Test Document ' + Date.now(),
      description: 'Test document created by E2E tests',
      tags: ['test', 'e2e'],
      folder: null
    };
    
    const data = { ...defaultData, ...docData };
    
    // Navigate to upload page
    await this.page.goto('/documents/upload/');
    
    // Create test file if not provided
    let filePath = docData.filePath;
    if (!filePath) {
      filePath = await this.createTestFile('pdf');
    }
    
    // Upload document
    await this.page.setInputFiles('input[type="file"]', filePath);
    await this.page.fill('input[name="title"]', data.title);
    await this.page.fill('textarea[name="description"]', data.description);
    
    if (data.tags) {
      await this.page.fill('input[name="tags"]', data.tags.join(', '));
    }
    
    if (data.folder) {
      await this.page.selectOption('select[name="folder"]', data.folder);
    }
    
    await this.page.click('button[type="submit"]');
    
    // Extract document ID from URL
    await this.page.waitForURL(/\/documents\/([a-f0-9-]+)\//);
    const match = this.page.url().match(/\/documents\/([a-f0-9-]+)\//);
    return match[1];
  }

  /**
   * Create test folder
   * @param {Object} folderData - Folder data
   * @returns {string} Folder ID
   */
  async createTestFolder(folderData = {}) {
    const defaultData = {
      name: 'Test Folder ' + Date.now(),
      description: 'Test folder created by E2E tests',
      parent: null
    };
    
    const data = { ...defaultData, ...folderData };
    
    await this.page.goto('/documents/folders/');
    await this.page.click('[data-testid="create-folder"]');
    
    await this.page.fill('[data-testid="folder-name-input"]', data.name);
    await this.page.fill('[data-testid="folder-description"]', data.description);
    
    if (data.parent) {
      await this.page.selectOption('[data-testid="parent-folder"]', data.parent);
    }
    
    await this.page.click('[data-testid="create-button"]');
    
    // Return folder name for identification
    return data.name;
  }

  /**
   * Create test note
   * @param {Object} noteData - Note data
   * @returns {string} Note ID
   */
  async createTestNote(noteData = {}) {
    const defaultData = {
      title: 'Test Note ' + Date.now(),
      content: 'This is a test note created by E2E tests.',
      tags: ['test', 'e2e'],
      notebook: 'default'
    };
    
    const data = { ...defaultData, ...noteData };
    
    await this.page.goto('/notes/create/');
    await this.page.fill('input[name="title"]', data.title);
    await this.page.fill('textarea[name="content"]', data.content);
    
    if (data.tags) {
      await this.page.fill('input[name="tags"]', data.tags.join(', '));
    }
    
    if (data.notebook) {
      await this.page.selectOption('select[name="notebook"]', data.notebook);
    }
    
    await this.page.click('button[type="submit"]');
    
    // Extract note ID from URL
    await this.page.waitForURL(/\/notes\/([a-f0-9-]+)\//);
    const match = this.page.url().match(/\/notes\/([a-f0-9-]+)\//);
    return match[1];
  }

  /**
   * Create test journal entry
   * @param {Object} entryData - Journal entry data
   * @returns {string} Entry ID
   */
  async createTestJournalEntry(entryData = {}) {
    const defaultData = {
      title: 'Test Journal Entry ' + Date.now(),
      content: 'This is a test journal entry.',
      mood: 'neutral',
      date: new Date().toISOString().split('T')[0]
    };
    
    const data = { ...defaultData, ...entryData };
    
    await this.page.goto('/notes/journal/');
    
    // Navigate to specific date if provided
    if (data.date) {
      await this.navigateToJournalDate(data.date);
    }
    
    await this.page.click('[data-testid="new-entry"]');
    
    if (data.title) {
      await this.page.fill('input[name="title"]', data.title);
    }
    
    await this.page.fill('textarea[name="content"]', data.content);
    
    if (data.mood) {
      await this.page.selectOption('select[name="mood"]', data.mood);
    }
    
    await this.page.click('button[type="submit"]');
    
    // Return entry data for identification
    return data;
  }

  /**
   * Create test file on filesystem
   * @param {string} type - File type (pdf, txt, docx, etc.)
   * @param {Object} options - File options
   * @returns {string} File path
   */
  async createTestFile(type = 'txt', options = {}) {
    const { 
      content = 'Test file content for E2E testing',
      size = 'small', // small, medium, large
      name = null
    } = options;
    
    const timestamp = Date.now();
    const fileName = name || `test-file-${timestamp}.${type}`;
    const filePath = path.join(process.cwd(), 'tests', 'fixtures', 'generated', fileName);
    
    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    switch (type) {
      case 'txt':
        await this.createTextFile(filePath, content, size);
        break;
      case 'pdf':
        await this.createPdfFile(filePath, content, size);
        break;
      case 'json':
        await this.createJsonFile(filePath, content);
        break;
      default:
        throw new Error(`Unsupported file type: ${type}`);
    }
    
    return filePath;
  }

  /**
   * Create text file
   */
  async createTextFile(filePath, content, size) {
    let fileContent = content;
    
    if (size === 'large') {
      fileContent = content.repeat(1000);
    } else if (size === 'medium') {
      fileContent = content.repeat(100);
    }
    
    fs.writeFileSync(filePath, fileContent, 'utf8');
  }

  /**
   * Create simple PDF file (placeholder)
   */
  async createPdfFile(filePath, content, size) {
    // For E2E tests, we'll create a simple text file with .pdf extension
    // In a real scenario, you'd use a PDF library
    await this.createTextFile(filePath, `PDF Content: ${content}`, size);
  }

  /**
   * Create JSON file
   */
  async createJsonFile(filePath, data) {
    const jsonContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonContent, 'utf8');
  }

  /**
   * Create test user via API
   * @param {Object} userData - User data
   * @returns {Object} User object
   */
  async createTestUser(userData = {}) {
    const defaultData = {
      email: `test-user-${Date.now()}@example.com`,
      password: 'testpass123',
      firstName: 'Test',
      lastName: 'User',
      role: 'user'
    };
    
    const data = { ...defaultData, ...userData };
    
    const response = await this.page.request.post('/api/testing/create-user/', {
      data: data
    });
    
    if (!response.ok()) {
      throw new Error('Failed to create test user');
    }
    
    return await response.json();
  }

  /**
   * Create test organization
   * @param {Object} orgData - Organization data
   * @returns {Object} Organization object
   */
  async createTestOrganization(orgData = {}) {
    const defaultData = {
      name: `Test Org ${Date.now()}`,
      slug: `test-org-${Date.now()}`,
      description: 'Test organization for E2E tests'
    };
    
    const data = { ...defaultData, ...orgData };
    
    const response = await this.page.request.post('/api/testing/create-organization/', {
      data: data
    });
    
    if (!response.ok()) {
      throw new Error('Failed to create test organization');
    }
    
    return await response.json();
  }

  /**
   * Clean up test data
   */
  async cleanupTestData() {
    // Clean up generated files
    const generatedDir = path.join(process.cwd(), 'tests', 'fixtures', 'generated');
    if (fs.existsSync(generatedDir)) {
      const files = fs.readdirSync(generatedDir);
      for (const file of files) {
        fs.unlinkSync(path.join(generatedDir, file));
      }
    }
    
    // Clean up test data via API
    await this.page.request.delete('/api/testing/cleanup-test-data/');
  }

  /**
   * Seed database with test data
   * @param {Object} seedData - Data to seed
   */
  async seedTestData(seedData = {}) {
    const response = await this.page.request.post('/api/testing/seed-data/', {
      data: seedData
    });
    
    if (!response.ok()) {
      throw new Error('Failed to seed test data');
    }
    
    return await response.json();
  }

  /**
   * Get test data by type
   * @param {string} dataType - Type of data to retrieve
   * @returns {Array} Test data
   */
  async getTestData(dataType) {
    const response = await this.page.request.get(`/api/testing/data/${dataType}/`);
    
    if (!response.ok()) {
      throw new Error(`Failed to get test data: ${dataType}`);
    }
    
    return await response.json();
  }

  /**
   * Navigate to specific journal date
   * @param {string} date - Date in YYYY-MM-DD format
   */
  async navigateToJournalDate(date) {
    const [year, month, day] = date.split('-');
    
    // This would require implementing date navigation logic
    // Placeholder for actual implementation
    await this.page.goto(`/notes/journal/?date=${date}`);
  }

  /**
   * Create bulk test documents
   * @param {number} count - Number of documents to create
   * @param {Object} baseData - Base document data
   * @returns {Array} Document IDs
   */
  async createBulkTestDocuments(count, baseData = {}) {
    const documentIds = [];
    
    for (let i = 0; i < count; i++) {
      const docData = {
        ...baseData,
        title: `${baseData.title || 'Bulk Test Document'} ${i + 1}`,
        description: `${baseData.description || 'Bulk test document'} #${i + 1}`
      };
      
      const docId = await this.createTestDocument(docData);
      documentIds.push(docId);
    }
    
    return documentIds;
  }

  /**
   * Generate random test data
   * @param {string} type - Type of random data
   * @param {Object} options - Generation options
   * @returns {any} Generated data
   */
  generateRandomTestData(type, options = {}) {
    const timestamp = Date.now();
    
    switch (type) {
      case 'email':
        return `test-${timestamp}@example.com`;
      case 'title':
        return `Test Title ${timestamp}`;
      case 'content':
        return `Test content generated at ${new Date().toISOString()}`;
      case 'tags':
        return ['test', 'e2e', `generated-${timestamp}`];
      default:
        throw new Error(`Unknown random data type: ${type}`);
    }
  }
}