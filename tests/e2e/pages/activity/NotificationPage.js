/**
 * Page object for Notification functionality
 * Handles notification center, preferences, push notifications, and delivery tracking
 */

const ActivityBasePage = require('./ActivityBasePage');

class NotificationPage extends ActivityBasePage {
  constructor(page) {
    super(page);
    
    // Notification-specific selectors
    this.notificationSelectors = {
      // Notification center
      notificationCenter: '[data-testid="notification-center"]',
      notificationList: '[data-testid="notification-list"]',
      notificationItem: '[data-testid="notification-item"]',
      notificationTitle: '[data-testid="notification-title"]',
      notificationMessage: '[data-testid="notification-message"]',
      notificationTimestamp: '[data-testid="notification-timestamp"]',
      notificationStatus: '[data-testid="notification-status"]',
      
      // Notification actions
      markAsRead: '[data-testid="mark-as-read"]',
      markAsUnread: '[data-testid="mark-as-unread"]',
      deleteNotification: '[data-testid="delete-notification"]',
      markAllRead: '[data-testid="mark-all-read"]',
      notificationDetail: '[data-testid="notification-detail"]',
      
      // Notification filters
      statusFilter: '[data-testid="notification-status-filter"]',
      typeFilter: '[data-testid="notification-type-filter"]',
      channelFilter: '[data-testid="notification-channel-filter"]',
      
      // Bulk operations
      selectAll: '[data-testid="select-all-notifications"]',
      bulkActions: '[data-testid="bulk-actions"]',
      bulkDelete: '[data-testid="bulk-delete"]',
      bulkMarkRead: '[data-testid="bulk-mark-read"]',
      
      // Notification preferences
      preferencesContainer: '[data-testid="notification-preferences"]',
      preferenceMatrix: '[data-testid="preference-matrix"]',
      notificationTypeRow: '[data-testid="notification-type-row"]',
      deliveryChannelColumn: '[data-testid="delivery-channel-column"]',
      preferenceToggle: '[data-testid="preference-toggle"]',
      
      // Push notifications
      pushContainer: '[data-testid="push-notifications"]',
      pushSubscribe: '[data-testid="push-subscribe"]',
      pushUnsubscribe: '[data-testid="push-unsubscribe"]',
      pushStatus: '[data-testid="push-status"]',
      subscriptionList: '[data-testid="subscription-list"]',
      deviceSubscription: '[data-testid="device-subscription"]',
      
      // Do Not Disturb
      dndContainer: '[data-testid="dnd-container"]',
      dndSchedule: '[data-testid="dnd-schedule"]',
      createDndSchedule: '[data-testid="create-dnd-schedule"]',
      temporaryDnd: '[data-testid="temporary-dnd"]',
      dndStatus: '[data-testid="dnd-status"]',
      
      // Notification templates
      templateContainer: '[data-testid="notification-templates"]',
      templateList: '[data-testid="template-list"]',
      templateItem: '[data-testid="template-item"]',
      createTemplate: '[data-testid="create-template"]',
      templatePreview: '[data-testid="template-preview"]',
      
      // Analytics
      analyticsContainer: '[data-testid="notification-analytics"]',
      deliveryChart: '[data-testid="delivery-chart"]',
      engagementChart: '[data-testid="engagement-chart"]',
      summaryStats: '[data-testid="summary-stats"]',
      
      // Real-time indicators
      unreadCount: '[data-testid="unread-count"]',
      notificationBadge: '[data-testid="notification-badge"]',
      liveUpdates: '[data-testid="live-notification-updates"]'
    };
  }

  /**
   * Navigate to notification center
   */
  async navigateToNotificationCenter() {
    await this.page.goto('/activity/notifications/');
    await this.waitForNotificationCenterToLoad();
  }

  /**
   * Wait for notification center to load
   */
  async waitForNotificationCenterToLoad() {
    await this.page.waitForSelector(this.notificationSelectors.notificationCenter);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Get all notifications
   */
  async getNotifications() {
    const notifications = [];
    const items = this.page.locator(this.notificationSelectors.notificationItem);
    const count = await items.count();
    
    for (let i = 0; i < count; i++) {
      const item = items.nth(i);
      const notification = {
        title: await item.locator(this.notificationSelectors.notificationTitle).textContent(),
        message: await item.locator(this.notificationSelectors.notificationMessage).textContent(),
        timestamp: await item.locator(this.notificationSelectors.notificationTimestamp).textContent(),
        status: await item.locator(this.notificationSelectors.notificationStatus).textContent(),
        isRead: await item.getAttribute('data-is-read') === 'true'
      };
      notifications.push(notification);
    }
    
    return notifications;
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount() {
    const countElement = this.page.locator(this.notificationSelectors.unreadCount);
    if (await countElement.count() > 0) {
      const text = await countElement.textContent();
      return parseInt(text, 10) || 0;
    }
    return 0;
  }

  /**
   * Mark notification as read
   */
  async markNotificationAsRead(notificationIndex) {
    const notification = this.page.locator(this.notificationSelectors.notificationItem).nth(notificationIndex);
    const markReadButton = notification.locator(this.notificationSelectors.markAsRead);
    
    if (await markReadButton.isVisible()) {
      await markReadButton.click();
      await this.page.waitForTimeout(500); // Wait for HTMX update
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsAsRead() {
    await this.page.click(this.notificationSelectors.markAllRead);
    await this.page.waitForTimeout(1000); // Wait for bulk operation
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationIndex) {
    const notification = this.page.locator(this.notificationSelectors.notificationItem).nth(notificationIndex);
    const deleteButton = notification.locator(this.notificationSelectors.deleteNotification);
    
    await deleteButton.click();
    
    // Handle confirmation dialog if present
    const confirmDialog = this.page.locator('[data-testid="confirm-delete"]');
    if (await confirmDialog.isVisible()) {
      await confirmDialog.click();
    }
    
    await this.page.waitForTimeout(500);
  }

  /**
   * Filter notifications by status
   */
  async filterByStatus(status) {
    await this.page.selectOption(this.notificationSelectors.statusFilter, status);
    await this.waitForNotificationCenterToLoad();
  }

  /**
   * Filter notifications by type
   */
  async filterByType(type) {
    await this.page.selectOption(this.notificationSelectors.typeFilter, type);
    await this.waitForNotificationCenterToLoad();
  }

  /**
   * Navigate to notification preferences
   */
  async navigateToPreferences() {
    await this.page.goto('/activity/notifications/preferences/');
    await this.page.waitForSelector(this.notificationSelectors.preferencesContainer);
  }

  /**
   * Get notification preference matrix
   */
  async getPreferenceMatrix() {
    const matrix = {};
    const rows = this.page.locator(this.notificationSelectors.notificationTypeRow);
    const rowCount = await rows.count();
    
    for (let i = 0; i < rowCount; i++) {
      const row = rows.nth(i);
      const notificationType = await row.getAttribute('data-notification-type');
      
      matrix[notificationType] = {};
      
      const toggles = row.locator(this.notificationSelectors.preferenceToggle);
      const toggleCount = await toggles.count();
      
      for (let j = 0; j < toggleCount; j++) {
        const toggle = toggles.nth(j);
        const deliveryChannel = await toggle.getAttribute('data-delivery-channel');
        const isEnabled = await toggle.isChecked();
        
        matrix[notificationType][deliveryChannel] = isEnabled;
      }
    }
    
    return matrix;
  }

  /**
   * Toggle notification preference
   */
  async toggleNotificationPreference(notificationType, deliveryChannel) {
    const selector = `${this.notificationSelectors.preferenceToggle}[data-notification-type="${notificationType}"][data-delivery-channel="${deliveryChannel}"]`;
    await this.page.click(selector);
    await this.page.waitForTimeout(500); // Wait for HTMX update
  }

  /**
   * Navigate to push notification management
   */
  async navigateToPushNotifications() {
    await this.page.goto('/activity/push/');
    await this.page.waitForSelector(this.notificationSelectors.pushContainer);
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPushNotifications() {
    const subscribeButton = this.page.locator(this.notificationSelectors.pushSubscribe);
    
    if (await subscribeButton.isVisible()) {
      // Handle permission request
      await this.page.context().grantPermissions(['notifications']);
      
      await subscribeButton.click();
      
      // Wait for subscription to complete
      await this.page.waitForSelector(this.notificationSelectors.pushStatus);
      
      const status = await this.page.locator(this.notificationSelectors.pushStatus).textContent();
      return status.includes('subscribed') || status.includes('active');
    }
    
    return false;
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPushNotifications() {
    const unsubscribeButton = this.page.locator(this.notificationSelectors.pushUnsubscribe);
    
    if (await unsubscribeButton.isVisible()) {
      await unsubscribeButton.click();
      await this.page.waitForTimeout(500);
      
      const status = await this.page.locator(this.notificationSelectors.pushStatus).textContent();
      return status.includes('unsubscribed') || status.includes('inactive');
    }
    
    return false;
  }

  /**
   * Get push subscription devices
   */
  async getPushSubscriptions() {
    const subscriptions = [];
    const devices = this.page.locator(this.notificationSelectors.deviceSubscription);
    const count = await devices.count();
    
    for (let i = 0; i < count; i++) {
      const device = devices.nth(i);
      const subscription = {
        deviceName: await device.locator('[data-testid="device-name"]').textContent(),
        status: await device.locator('[data-testid="device-status"]').textContent(),
        lastUsed: await device.locator('[data-testid="last-used"]').textContent()
      };
      subscriptions.push(subscription);
    }
    
    return subscriptions;
  }

  /**
   * Navigate to Do Not Disturb management
   */
  async navigateToDoNotDisturb() {
    await this.page.goto('/activity/dnd/');
    await this.page.waitForSelector(this.notificationSelectors.dndContainer);
  }

  /**
   * Create DND schedule
   */
  async createDNDSchedule(scheduleData) {
    await this.page.click(this.notificationSelectors.createDndSchedule);
    
    // Wait for form
    await this.page.waitForSelector('[data-testid="dnd-schedule-form"]');
    
    // Fill form
    if (scheduleData.title) {
      await this.page.fill('[name="title"]', scheduleData.title);
    }
    
    if (scheduleData.startTime) {
      await this.page.fill('[name="start_time"]', scheduleData.startTime);
    }
    
    if (scheduleData.endTime) {
      await this.page.fill('[name="end_time"]', scheduleData.endTime);
    }
    
    if (scheduleData.recurrenceType) {
      await this.page.selectOption('[name="recurrence_type"]', scheduleData.recurrenceType);
    }
    
    // Submit
    await this.page.click('[data-testid="submit-dnd-schedule"]');
    
    // Wait for response
    await this.page.waitForSelector('[data-testid="form-response"]');
    
    const response = await this.page.locator('[data-testid="form-response"]').textContent();
    return response.includes('success') || response.includes('created');
  }

  /**
   * Create temporary DND
   */
  async createTemporaryDND(hours) {
    await this.page.click(this.notificationSelectors.temporaryDnd);
    
    // Fill duration
    await this.page.fill('[name="duration_hours"]', hours.toString());
    
    // Submit
    await this.page.click('[data-testid="create-temporary-dnd"]');
    
    await this.page.waitForTimeout(500);
    
    // Check if DND is active
    return await this.isDNDActive();
  }

  /**
   * Check if DND is currently active
   */
  async isDNDActive() {
    const statusElement = this.page.locator(this.notificationSelectors.dndStatus);
    if (await statusElement.count() > 0) {
      const status = await statusElement.textContent();
      return status.includes('active') || status.includes('enabled');
    }
    return false;
  }

  /**
   * Navigate to notification templates
   */
  async navigateToTemplates() {
    await this.page.goto('/activity/templates/');
    await this.page.waitForSelector(this.notificationSelectors.templateContainer);
  }

  /**
   * Create notification template
   */
  async createNotificationTemplate(templateData) {
    await this.page.click(this.notificationSelectors.createTemplate);
    
    // Wait for form
    await this.page.waitForSelector('[data-testid="template-form"]');
    
    // Fill form
    if (templateData.name) {
      await this.page.fill('[name="name"]', templateData.name);
    }
    
    if (templateData.subject) {
      await this.page.fill('[name="subject"]', templateData.subject);
    }
    
    if (templateData.content) {
      await this.page.fill('[name="content"]', templateData.content);
    }
    
    if (templateData.notificationType) {
      await this.page.selectOption('[name="notification_type"]', templateData.notificationType);
    }
    
    // Submit
    await this.page.click('[data-testid="submit-template"]');
    
    // Wait for response
    await this.page.waitForSelector('[data-testid="form-response"]');
    
    const response = await this.page.locator('[data-testid="form-response"]').textContent();
    return response.includes('success') || response.includes('created');
  }

  /**
   * Preview notification template
   */
  async previewTemplate(templateIndex) {
    const template = this.page.locator(this.notificationSelectors.templateItem).nth(templateIndex);
    const previewButton = template.locator('[data-testid="preview-template"]');
    
    await previewButton.click();
    
    // Wait for preview modal
    await this.page.waitForSelector(this.notificationSelectors.templatePreview);
    
    const preview = {
      subject: await this.page.locator('[data-testid="preview-subject"]').textContent(),
      content: await this.page.locator('[data-testid="preview-content"]').textContent()
    };
    
    return preview;
  }

  /**
   * Navigate to notification analytics
   */
  async navigateToAnalytics() {
    await this.page.goto('/activity/analytics/');
    await this.page.waitForSelector(this.notificationSelectors.analyticsContainer);
  }

  /**
   * Get notification analytics summary
   */
  async getAnalyticsSummary() {
    const summary = {};
    
    const statsContainer = this.page.locator(this.notificationSelectors.summaryStats);
    const stats = statsContainer.locator('[data-testid="stat-item"]');
    const count = await stats.count();
    
    for (let i = 0; i < count; i++) {
      const stat = stats.nth(i);
      const label = await stat.locator('[data-testid="stat-label"]').textContent();
      const value = await stat.locator('[data-testid="stat-value"]').textContent();
      summary[label] = value;
    }
    
    return summary;
  }

  /**
   * Test notification delivery
   */
  async testNotificationDelivery(testData) {
    // Navigate to a page that can trigger notifications
    await this.page.goto('/activity/bulk/announcement/');
    
    // Create test notification
    await this.page.fill('[name="title"]', testData.title || 'Test Notification');
    await this.page.fill('[name="message"]', testData.message || 'This is a test notification');
    
    if (testData.recipients) {
      // Select recipients
      for (const recipient of testData.recipients) {
        await this.page.check(`[data-testid="recipient-${recipient}"]`);
      }
    }
    
    // Send notification
    await this.page.click('[data-testid="send-notification"]');
    
    // Wait for confirmation
    await this.page.waitForSelector('[data-testid="notification-sent"]');
    
    return true;
  }

  /**
   * Verify real-time notification updates
   */
  async verifyRealTimeNotifications() {
    const initialCount = await this.getUnreadCount();
    
    // Trigger a notification in another tab/context
    const newPage = await this.page.context().newPage();
    await newPage.goto('/activity/bulk/announcement/');
    
    // Send a test notification
    await newPage.fill('[name="title"]', 'Real-time Test');
    await newPage.fill('[name="message"]', 'Testing real-time updates');
    await newPage.click('[data-testid="send-notification"]');
    
    await newPage.close();
    
    // Wait for real-time update
    await this.page.waitForTimeout(2000);
    
    const finalCount = await this.getUnreadCount();
    
    return finalCount > initialCount;
  }

  /**
   * Test HTMX notification interactions
   */
  async testHTMXNotificationInteractions() {
    const results = {};
    
    // Test mark as read
    const markReadResponse = await this.page.waitForResponse(response =>
      response.url().includes('/notifications/') && response.url().includes('/read/')
    );
    
    await this.markNotificationAsRead(0);
    results.markAsRead = markReadResponse.ok();
    
    // Test notification deletion
    const deleteResponse = await this.page.waitForResponse(response =>
      response.url().includes('/notifications/') && response.status() === 200
    );
    
    await this.deleteNotification(0);
    results.deleteNotification = deleteResponse.ok();
    
    return results;
  }

  /**
   * Verify notification accessibility
   */
  async verifyNotificationAccessibility() {
    const issues = [];
    
    // Check notification list has proper ARIA labels
    const notificationList = this.page.locator(this.notificationSelectors.notificationList);
    const hasAriaLabel = await notificationList.getAttribute('aria-label') !== null;
    if (!hasAriaLabel) {
      issues.push('Notification list missing aria-label');
    }
    
    // Check notification items have proper structure
    const notifications = this.page.locator(this.notificationSelectors.notificationItem);
    const count = await notifications.count();
    
    for (let i = 0; i < Math.min(count, 5); i++) {
      const notification = notifications.nth(i);
      
      // Check for heading or title
      const hasTitle = await notification.locator('h3, h4, h5, [role="heading"]').count() > 0;
      if (!hasTitle) {
        issues.push(`Notification ${i} missing proper heading`);
      }
      
      // Check for proper button labels
      const buttons = notification.locator('button');
      const buttonCount = await buttons.count();
      
      for (let j = 0; j < buttonCount; j++) {
        const button = buttons.nth(j);
        const hasLabel = await button.getAttribute('aria-label') !== null ||
                         await button.locator('span.sr-only').count() > 0;
        if (!hasLabel) {
          issues.push(`Button in notification ${i} missing accessible label`);
        }
      }
    }
    
    return {
      hasIssues: issues.length > 0,
      issues
    };
  }
}

module.exports = NotificationPage;