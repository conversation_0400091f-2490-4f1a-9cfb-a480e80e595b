/**
 * Integration Test Global Teardown
 * 
 * Performs global cleanup after cross-application integration tests.
 * Ensures test environment is properly cleaned up and restored.
 */

const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

async function globalTeardown() {
    console.log('🧹 Starting integration test environment cleanup...');
    
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        const baseUrl = process.env.INTEGRATION_BASE_URL || 'http://localhost:8000';
        
        // 1. Cleanup test data
        console.log('🗑️  Cleaning up test data...');
        try {
            const cleanupResponse = await page.request.post(`${baseUrl}/api/test/cleanup/`, {
                data: {
                    mode: 'integration_tests',
                    cleanup_all: true,
                    preserve_admin: false
                }
            });
            
            if (cleanupResponse.ok()) {
                const cleanupResult = await cleanupResponse.json();
                console.log('✅ Test data cleanup completed');
                console.log(`   Organizations cleaned: ${cleanupResult.organizations_deleted || 0}`);
                console.log(`   Users cleaned: ${cleanupResult.users_deleted || 0}`);
                console.log(`   Projects cleaned: ${cleanupResult.projects_deleted || 0}`);
            } else {
                console.warn('⚠️  Automated cleanup failed - manual cleanup may be required');
            }
        } catch (error) {
            console.warn('⚠️  Could not perform automated cleanup:', error.message);
        }
        
        // 2. Clear cache
        console.log('🔄 Clearing test cache...');
        try {
            const cacheResponse = await page.request.post(`${baseUrl}/api/test/clear-cache/`, {
                data: { test_mode: true }
            });
            
            if (cacheResponse.ok()) {
                console.log('✅ Test cache cleared');
            }
        } catch (error) {
            console.log('ℹ️  Cache clearing skipped');
        }
        
        // 3. Reset WebSocket connections
        console.log('🔌 Resetting WebSocket connections...');
        try {
            const wsResetResponse = await page.request.post(`${baseUrl}/api/test/reset-websockets/`, {
                data: { cleanup: true }
            });
            
            if (wsResetResponse.ok()) {
                console.log('✅ WebSocket connections reset');
            }
        } catch (error) {
            console.log('ℹ️  WebSocket reset skipped');
        }
        
        // 4. Generate cleanup report
        console.log('📊 Generating cleanup report...');
        const cleanupReport = {
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'test',
            baseUrl: baseUrl,
            cleanupActions: [
                'test_data_cleanup',
                'cache_clear',
                'websocket_reset'
            ],
            testArtifacts: {
                reportDir: './reports/integration',
                hasReports: fs.existsSync('./reports/integration'),
                hasScreenshots: fs.existsSync('./reports/integration/screenshots'),
                hasVideos: fs.existsSync('./reports/integration/videos')
            }
        };
        
        // 5. Archive test artifacts if in CI
        if (process.env.CI) {
            console.log('📦 Archiving test artifacts for CI...');
            try {
                const reportDir = './reports/integration';
                if (fs.existsSync(reportDir)) {
                    // Create archive manifest
                    const archiveManifest = {
                        ...cleanupReport,
                        artifacts: []
                    };
                    
                    // List all files in report directory
                    const walkDir = (dir) => {
                        const files = [];
                        const items = fs.readdirSync(dir, { withFileTypes: true });
                        
                        for (const item of items) {
                            const fullPath = path.join(dir, item.name);
                            if (item.isDirectory()) {
                                files.push(...walkDir(fullPath));
                            } else {
                                files.push({
                                    path: fullPath,
                                    size: fs.statSync(fullPath).size,
                                    modified: fs.statSync(fullPath).mtime
                                });
                            }
                        }
                        
                        return files;
                    };
                    
                    archiveManifest.artifacts = walkDir(reportDir);
                    
                    // Write manifest
                    fs.writeFileSync(
                        path.join(reportDir, 'archive-manifest.json'),
                        JSON.stringify(archiveManifest, null, 2)
                    );
                    
                    console.log('✅ Test artifacts archived');
                    console.log(`   Files: ${archiveManifest.artifacts.length}`);
                    console.log(`   Total size: ${Math.round(archiveManifest.artifacts.reduce((sum, f) => sum + f.size, 0) / 1024)}KB`);
                }
            } catch (error) {
                console.warn('⚠️  Could not archive test artifacts:', error.message);
            }
        }
        
        // 6. Verify system state after cleanup
        console.log('🔍 Verifying system state after cleanup...');
        try {
            const healthResponse = await page.request.get(`${baseUrl}/health/`);
            if (healthResponse.ok()) {
                console.log('✅ System health verified after cleanup');
            } else {
                console.warn('⚠️  System health check failed after cleanup');
            }
        } catch (error) {
            console.warn('⚠️  Could not verify system health:', error.message);
        }
        
        // 7. Log cleanup summary
        const summary = {
            timestamp: cleanupReport.timestamp,
            duration: Date.now() - new Date(process.env.INTEGRATION_START_TIME || Date.now()).getTime(),
            status: 'completed',
            actions: cleanupReport.cleanupActions,
            environment: cleanupReport.environment
        };
        
        console.log('📋 Cleanup Summary:');
        console.log(`   Timestamp: ${summary.timestamp}`);
        console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`);
        console.log(`   Environment: ${summary.environment}`);
        console.log(`   Actions: ${summary.actions.join(', ')}`);
        
        // Write cleanup summary
        const reportDir = './reports/integration';
        if (!fs.existsSync(reportDir)) {
            fs.mkdirSync(reportDir, { recursive: true });
        }
        
        fs.writeFileSync(
            path.join(reportDir, 'cleanup-summary.json'),
            JSON.stringify(summary, null, 2)
        );
        
        console.log('✅ Integration test environment cleanup completed');
        
    } catch (error) {
        console.error('💥 Integration test cleanup failed:', error);
        // Don't throw error to avoid masking test failures
    } finally {
        await context.close();
        await browser.close();
    }
}

module.exports = globalTeardown;