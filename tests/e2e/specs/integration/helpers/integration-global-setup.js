/**
 * Integration Test Global Setup
 * 
 * Performs global setup for cross-application integration tests.
 * Ensures proper test environment isolation and data preparation.
 */

const { chromium } = require('@playwright/test');

async function globalSetup() {
    console.log('🚀 Setting up integration test environment...');
    
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
        const baseUrl = process.env.INTEGRATION_BASE_URL || 'http://localhost:8000';
        
        // 1. Verify Django server is running
        console.log('🔍 Verifying Django server availability...');
        await page.goto(`${baseUrl}/health/`, { waitUntil: 'networkidle' });
        const healthResponse = await page.textContent('body');
        if (!healthResponse.includes('OK') && !healthResponse.includes('healthy')) {
            throw new Error('Django server health check failed');
        }
        console.log('✅ Django server is healthy');
        
        // 2. Setup test database
        console.log('🗄️  Setting up test database...');
        const dbSetupResponse = await page.request.post(`${baseUrl}/api/test/setup-database/`, {
            data: {
                mode: 'integration_tests',
                isolated: true,
                seed_data: true
            }
        });
        
        if (!dbSetupResponse.ok()) {
            console.warn('⚠️  Database setup endpoint not available - continuing with existing data');
        } else {
            console.log('✅ Test database configured');
        }
        
        // 3. Create test organization and admin user
        console.log('👤 Creating test admin user...');
        try {
            const adminSetupResponse = await page.request.post(`${baseUrl}/api/test/create-admin/`, {
                data: {
                    username: 'integration_admin',
                    email: '<EMAIL>',
                    password: 'integration_test_pass_123',
                    organization_name: 'Integration Test Organization'
                }
            });
            
            if (adminSetupResponse.ok()) {
                const adminData = await adminSetupResponse.json();
                process.env.INTEGRATION_ADMIN_ID = adminData.user_id;
                process.env.INTEGRATION_ORG_ID = adminData.organization_id;
                console.log('✅ Test admin user created');
            } else {
                console.log('ℹ️  Admin user already exists or endpoint unavailable');
            }
        } catch (error) {
            console.log('ℹ️  Continuing with existing admin setup');
        }
        
        // 4. Verify all required applications are accessible
        console.log('🔍 Verifying application endpoints...');
        const requiredEndpoints = [
            '/api/projects/',
            '/api/tasks/',
            '/api/documents/',
            '/api/users/',
            '/api/authentication/',
            '/api/financial/',
            '/api/analytics/',
            '/api/activity/',
            '/api/infrastructure/',
            '/api/assets/',
            '/api/knowledge/',
            '/api/messaging/',
            '/api/notifications/',
            '/api/realtime/',
            '/api/versioning/',
            '/api/comments/',
            '/api/compliance/',
            '/api/feedback/',
            '/api/profiles/',
            '/api/notes/',
            '/api/common/'
        ];
        
        const endpointResults = await Promise.allSettled(
            requiredEndpoints.map(async endpoint => {
                try {
                    const response = await page.request.get(`${baseUrl}${endpoint}`);
                    return { endpoint, status: response.status(), ok: response.ok() };
                } catch (error) {
                    return { endpoint, status: 'error', error: error.message };
                }
            })
        );
        
        const failedEndpoints = endpointResults
            .filter(result => result.status === 'fulfilled')
            .map(result => result.value)
            .filter(result => !result.ok && ![401, 403].includes(result.status)); // 401/403 are acceptable (auth required)
        
        if (failedEndpoints.length > 0) {
            console.warn('⚠️  Some endpoints are not accessible:', failedEndpoints);
        } else {
            console.log('✅ All application endpoints are accessible');
        }
        
        // 5. Setup Redis/Cache if available
        console.log('🔄 Verifying cache availability...');
        try {
            const cacheResponse = await page.request.get(`${baseUrl}/health/cache/`);
            if (cacheResponse.ok()) {
                console.log('✅ Cache system is available');
                
                // Clear test cache
                await page.request.post(`${baseUrl}/api/test/clear-cache/`);
                console.log('✅ Test cache cleared');
            } else {
                console.log('ℹ️  Cache system not available - some tests may be skipped');
            }
        } catch (error) {
            console.log('ℹ️  Cache verification skipped');
        }
        
        // 6. Setup WebSocket test environment
        console.log('🔌 Setting up WebSocket test environment...');
        try {
            const wsSetupResponse = await page.request.post(`${baseUrl}/api/test/setup-websockets/`, {
                data: { test_mode: true }
            });
            
            if (wsSetupResponse.ok()) {
                console.log('✅ WebSocket test environment configured');
            } else {
                console.log('ℹ️  WebSocket setup not available - real-time tests may be limited');
            }
        } catch (error) {
            console.log('ℹ️  WebSocket setup skipped');
        }
        
        // 7. Pre-populate test data for consistent testing
        console.log('📊 Pre-populating test data...');
        try {
            const testDataResponse = await page.request.post(`${baseUrl}/api/test/populate-data/`, {
                data: {
                    mode: 'integration',
                    include_spatial_data: true,
                    include_financial_data: true,
                    include_documents: true,
                    organization_count: 3,
                    users_per_org: 5,
                    projects_per_org: 2
                }
            });
            
            if (testDataResponse.ok()) {
                const testData = await testDataResponse.json();
                process.env.INTEGRATION_TEST_DATA = JSON.stringify(testData);
                console.log('✅ Test data populated');
            } else {
                console.log('ℹ️  Test data population not available - tests will create their own data');
            }
        } catch (error) {
            console.log('ℹ️  Test data population skipped');
        }
        
        // 8. Store global test configuration
        const globalConfig = {
            baseUrl,
            startTime: new Date().toISOString(),
            testEnvironment: process.env.NODE_ENV || 'test',
            adminUserId: process.env.INTEGRATION_ADMIN_ID,
            organizationId: process.env.INTEGRATION_ORG_ID,
            databaseIsolated: true,
            cacheAvailable: true,
            websocketsAvailable: true
        };
        
        process.env.INTEGRATION_GLOBAL_CONFIG = JSON.stringify(globalConfig);
        
        console.log('🎯 Integration test environment setup complete');
        console.log(`   Base URL: ${baseUrl}`);
        console.log(`   Environment: ${globalConfig.testEnvironment}`);
        console.log(`   Start Time: ${globalConfig.startTime}`);
        
    } catch (error) {
        console.error('💥 Integration test setup failed:', error);
        throw error;
    } finally {
        await context.close();
        await browser.close();
    }
}

module.exports = globalSetup;