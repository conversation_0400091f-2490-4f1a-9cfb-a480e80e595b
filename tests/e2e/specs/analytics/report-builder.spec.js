/**
 * Report Builder Test Suite
 * Tests report creation, building, and generation functionality
 */

const { test, expect } = require('@playwright/test');
const { ReportBuilderPage } = require('../../pages/analytics/ReportBuilderPage');
const { LoginPage } = require('../../pages/LoginPage');
const { HTMXHelpers } = require('../../helpers/htmx-helpers');

test.describe('Report Builder', () => {
  let reportPage;
  let loginPage;
  let htmxHelpers;

  test.beforeEach(async ({ page }) => {
    reportPage = new ReportBuilderPage(page);
    loginPage = new LoginPage(page);
    htmxHelpers = new HTMXHelpers(page);

    // Login as user with report builder access
    await loginPage.goto();
    await loginPage.login('<EMAIL>', 'password123');
    await page.waitForURL('**/dashboard/');
  });

  test.describe('Report Dashboard', () => {
    test('should load report builder dashboard', async () => {
      await reportPage.goto();
      
      // Verify dashboard is loaded
      await expect(reportPage.page.locator(reportPage.selectors.reportDashboard)).toBeVisible();
    });

    test('should display existing reports', async () => {
      await reportPage.goto();
      
      const reports = await reportPage.getReports();
      
      // Should return array even if empty
      expect(Array.isArray(reports)).toBe(true);
      
      // If reports exist, verify structure
      if (reports.length > 0) {
        for (const report of reports) {
          expect(report).toHaveProperty('name');
          expect(report).toHaveProperty('status');
          expect(report.name).toBeTruthy();
        }
      }
    });

    test('should navigate to create new report', async () => {
      await reportPage.goto();
      
      await reportPage.gotoCreateReport();
      
      // Should be on create report page
      await expect(reportPage.page.locator(reportPage.selectors.reportBuilderForm)).toBeVisible();
    });
  });

  test.describe('Report Creation', () => {
    test('should create a basic report', async () => {
      const reportConfig = {
        name: 'Test Analytics Report',
        description: 'Test report for analytics data',
        type: 'analytics',
        format: 'html'
      };
      
      const reportId = await reportPage.createReport(reportConfig);
      
      // Should successfully create report
      expect(reportId).toBeTruthy();
      
      // Should be on report edit page
      const currentUrl = reportPage.page.url();
      expect(currentUrl).toMatch(/\/reports\/.+\//);
    });

    test('should require mandatory fields', async () => {
      await reportPage.gotoCreateReport();
      
      // Try to submit without required fields
      await reportPage.page.click(reportPage.selectors.submitButton);
      
      // Should show validation errors
      const alerts = await reportPage.getAlerts();
      const hasErrorAlerts = alerts.some(alert => alert.type === 'error');
      
      if (hasErrorAlerts) {
        expect(hasErrorAlerts).toBe(true);
      }
    });

    test('should validate report configuration', async () => {
      await reportPage.gotoCreateReport();
      
      // Fill only name field
      await reportPage.fillField('name', 'Invalid Report');
      
      // Submit form
      await reportPage.page.click(reportPage.selectors.submitButton);
      
      // Should handle validation appropriately
      await htmxHelpers.waitForHTMXReady();
      
      // Page should handle the response gracefully
      const hasErrors = await reportPage.hasReportErrors();
      // Errors are acceptable for incomplete forms
    });
  });

  test.describe('Data Source Management', () => {
    test('should add data source to report', async () => {
      // First create a report
      const reportConfig = {
        name: 'Data Source Test Report',
        description: 'Testing data source functionality',
        type: 'custom'
      };
      
      const reportId = await reportPage.createReport(reportConfig);
      expect(reportId).toBeTruthy();
      
      // Add data source
      const dataSourceConfig = {
        type: 'database',
        name: 'Test Data Source',
        query: 'SELECT * FROM test_table'
      };
      
      try {
        await reportPage.addDataSource(dataSourceConfig);
        
        // Should not cause errors
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Data source functionality not available');
      }
    });

    test('should validate data source configuration', async () => {
      // Create a report first
      const reportConfig = {
        name: 'Validation Test Report',
        type: 'custom'
      };
      
      await reportPage.createReport(reportConfig);
      
      // Try to add invalid data source
      try {
        await reportPage.page.click(reportPage.selectors.addDataSourceBtn);
        await reportPage.waitForElement(reportPage.selectors.dataSourceForm);
        
        // Submit without required fields
        await reportPage.submitForm(reportPage.selectors.dataSourceForm);
        
        // Should handle validation
        await htmxHelpers.waitForHTMXReady();
      } catch (error) {
        console.log('Data source form not available');
      }
    });

    test('should list configured data sources', async () => {
      // Create report and add data source
      const reportConfig = {
        name: 'Data Source List Test',
        type: 'custom'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        const dataSources = await reportPage.getDataSources();
        expect(Array.isArray(dataSources)).toBe(true);
      } catch (error) {
        console.log('Data source listing not available');
      }
    });
  });

  test.describe('Widget Management', () => {
    test('should add widget to report', async () => {
      // Create a report first
      const reportConfig = {
        name: 'Widget Test Report',
        type: 'dashboard'
      };
      
      await reportPage.createReport(reportConfig);
      
      // Add widget
      const widgetConfig = {
        type: 'chart',
        title: 'Test Chart Widget',
        chartType: 'bar',
        dataSource: 'default'
      };
      
      try {
        await reportPage.addWidget(widgetConfig);
        
        // Should not cause errors
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Widget functionality not available');
      }
    });

    test('should configure chart widgets', async () => {
      // Create report and add widget
      const reportConfig = {
        name: 'Chart Widget Test',
        type: 'dashboard'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        await reportPage.addWidget({
          type: 'chart',
          title: 'Chart Widget',
          chartType: 'line'
        });
        
        // Configure chart
        const chartConfig = {
          xAxis: 'date',
          yAxis: 'value',
          aggregation: 'sum',
          colorScheme: 'blue'
        };
        
        await reportPage.configureChart(chartConfig);
        
        // Should complete without errors
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Chart configuration not available');
      }
    });

    test('should list report widgets', async () => {
      // Create report
      const reportConfig = {
        name: 'Widget List Test',
        type: 'dashboard'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        const widgets = await reportPage.getWidgets();
        expect(Array.isArray(widgets)).toBe(true);
      } catch (error) {
        console.log('Widget listing not available');
      }
    });
  });

  test.describe('Report Filtering', () => {
    test('should add filters to report', async () => {
      // Create a report first
      const reportConfig = {
        name: 'Filter Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      // Add filter
      const filterConfig = {
        field: 'created_date',
        operator: 'gte',
        value: '2024-01-01'
      };
      
      try {
        await reportPage.addFilter(filterConfig);
        
        // Should not cause errors
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Filter functionality not available');
      }
    });

    test('should validate filter configuration', async () => {
      // Create report
      const reportConfig = {
        name: 'Filter Validation Test',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        // Try to add invalid filter
        await reportPage.page.click(reportPage.selectors.addFilterBtn);
        await reportPage.waitForElement(reportPage.selectors.filterForm);
        
        // Submit without required fields
        await reportPage.submitForm(reportPage.selectors.filterForm);
        
        // Should handle validation
        await htmxHelpers.waitForHTMXReady();
      } catch (error) {
        console.log('Filter form not available');
      }
    });
  });

  test.describe('Report Preview', () => {
    test('should preview report content', async () => {
      // Create a simple report
      const reportConfig = {
        name: 'Preview Test Report',
        type: 'analytics',
        format: 'html'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        // Preview report
        await reportPage.previewReport();
        
        // Get preview content
        const preview = await reportPage.getPreviewContent();
        expect(preview).toBeTruthy();
        expect(preview).toHaveProperty('isEmpty');
        
        // Preview should have some content
        expect(preview.isEmpty).toBe(false);
      } catch (error) {
        console.log('Preview functionality not available');
      }
    });

    test('should handle empty report preview', async () => {
      // Create minimal report
      const reportConfig = {
        name: 'Empty Preview Test',
        type: 'custom'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        await reportPage.previewReport();
        
        const preview = await reportPage.getPreviewContent();
        // Empty reports should be handled gracefully
        expect(preview).toBeTruthy();
      } catch (error) {
        console.log('Preview functionality not available');
      }
    });
  });

  test.describe('Report Generation', () => {
    test('should run report generation', async () => {
      // Create a report
      const reportConfig = {
        name: 'Generation Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        // Run report
        await reportPage.runReport();
        
        // Should complete successfully
        const isBuilding = await reportPage.isReportBuilding();
        expect(isBuilding).toBe(false);
        
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Report generation not available');
      }
    });

    test('should handle report generation timeout', async () => {
      // Create a report
      const reportConfig = {
        name: 'Timeout Test Report',
        type: 'complex'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        // Try to run report with short timeout
        await reportPage.runReport();
        
        // Wait for completion or timeout
        const completed = await reportPage.waitForReportCompletion(5000);
        
        // Either completes or times out gracefully
        expect(typeof completed).toBe('boolean');
      } catch (error) {
        // Timeout is acceptable
        expect(error.message).toMatch(/timeout/i);
      }
    });
  });

  test.describe('Report Export', () => {
    test('should export report in different formats', async () => {
      // Create a report
      const reportConfig = {
        name: 'Export Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      const formats = ['csv', 'excel', 'pdf'];
      
      for (const format of formats) {
        try {
          const exportConfig = {
            format: format,
            includeCharts: true
          };
          
          const download = await reportPage.exportReport(exportConfig);
          
          if (download) {
            expect(download.suggestedFilename()).toMatch(new RegExp(format, 'i'));
            await download.delete(); // Clean up
          }
        } catch (error) {
          console.log(`Export format ${format} not available`);
        }
      }
    });

    test('should configure export options', async () => {
      // Create a report
      const reportConfig = {
        name: 'Export Options Test',
        type: 'dashboard'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        const exportConfig = {
          format: 'pdf',
          includeCharts: false
        };
        
        await reportPage.exportReport(exportConfig);
        
        // Export should complete without errors
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Export options not available');
      }
    });
  });

  test.describe('Report Scheduling', () => {
    test('should schedule report generation', async () => {
      // Create a report
      const reportConfig = {
        name: 'Schedule Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        const scheduleConfig = {
          frequency: 'daily',
          time: '09:00',
          recipients: '<EMAIL>'
        };
        
        await reportPage.scheduleReport(scheduleConfig);
        
        // Should complete without errors
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Scheduling functionality not available');
      }
    });

    test('should validate schedule configuration', async () => {
      // Create a report
      const reportConfig = {
        name: 'Schedule Validation Test',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        // Try invalid schedule
        await reportPage.page.click(reportPage.selectors.scheduleBtn);
        await reportPage.waitForElement(reportPage.selectors.scheduleForm);
        
        // Submit without required fields
        await reportPage.submitForm(reportPage.selectors.scheduleForm);
        
        // Should handle validation
        await htmxHelpers.waitForHTMXReady();
      } catch (error) {
        console.log('Schedule form not available');
      }
    });
  });

  test.describe('Report Management', () => {
    test('should edit existing report', async () => {
      // Create a report first
      const reportConfig = {
        name: 'Edit Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      // Go back to dashboard
      await reportPage.goto();
      
      try {
        // Edit the report
        await reportPage.editReport('Edit Test Report');
        
        // Should be on edit page
        await expect(reportPage.page.locator(reportPage.selectors.reportBuilderForm)).toBeVisible();
      } catch (error) {
        console.log('Edit functionality not available');
      }
    });

    test('should clone existing report', async () => {
      // Create a report first
      const reportConfig = {
        name: 'Clone Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      // Go back to dashboard
      await reportPage.goto();
      
      try {
        // Clone the report
        await reportPage.cloneReport('Clone Test Report');
        
        // Should handle cloning
        await htmxHelpers.waitForHTMXReady();
        
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Clone functionality not available');
      }
    });

    test('should delete report', async () => {
      // Create a report first
      const reportConfig = {
        name: 'Delete Test Report',
        type: 'analytics'
      };
      
      await reportPage.createReport(reportConfig);
      
      // Go back to dashboard
      await reportPage.goto();
      
      try {
        // Delete the report
        await reportPage.deleteReport('Delete Test Report');
        
        // Should handle deletion
        await htmxHelpers.waitForHTMXReady();
        
        // Report should be removed from list
        const reports = await reportPage.getReports();
        const deletedReport = reports.find(r => r.name === 'Delete Test Report');
        expect(deletedReport).toBeFalsy();
      } catch (error) {
        console.log('Delete functionality not available');
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle report building errors', async () => {
      // Create a potentially problematic report
      const reportConfig = {
        name: 'Error Test Report',
        type: 'invalid_type'
      };
      
      try {
        await reportPage.createReport(reportConfig);
        
        // Check for errors
        const hasErrors = await reportPage.hasReportErrors();
        
        if (hasErrors) {
          // Errors should be displayed to user
          const errorElements = await reportPage.page.$$(reportPage.selectors.errorIndicator);
          expect(errorElements.length).toBeGreaterThan(0);
        }
      } catch (error) {
        // Errors during creation are acceptable
        console.log('Report creation failed as expected for invalid config');
      }
    });

    test('should validate form inputs', async () => {
      await reportPage.gotoCreateReport();
      
      // Try various invalid inputs
      const invalidInputs = [
        { field: 'name', value: '' }, // Empty name
        { field: 'name', value: 'a'.repeat(300) }, // Too long name
      ];
      
      for (const input of invalidInputs) {
        try {
          await reportPage.fillField(input.field, input.value);
          await reportPage.page.click(reportPage.selectors.submitButton);
          
          // Should handle validation
          await htmxHelpers.waitForHTMXReady();
        } catch (error) {
          // Validation errors are expected
        }
      }
    });
  });

  test.describe('Performance', () => {
    test('should load report builder within reasonable time', async () => {
      const startTime = Date.now();
      
      await reportPage.goto();
      
      const loadTime = Date.now() - startTime;
      
      // Should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    test('should handle large report generation', async () => {
      // Create a complex report
      const reportConfig = {
        name: 'Large Report Test',
        type: 'analytics',
        description: 'Testing large report generation'
      };
      
      await reportPage.createReport(reportConfig);
      
      try {
        // Add multiple widgets/data sources if possible
        await reportPage.addWidget({
          type: 'chart',
          title: 'Chart 1',
          chartType: 'bar'
        });
        
        await reportPage.addWidget({
          type: 'table',
          title: 'Table 1'
        });
        
        // Generate report
        await reportPage.runReport();
        
        // Should complete without timeout
        const hasErrors = await reportPage.hasReportErrors();
        expect(hasErrors).toBe(false);
      } catch (error) {
        console.log('Complex report generation not available');
      }
    });
  });

  test.describe('Accessibility', () => {
    test('should have accessible form elements', async () => {
      await reportPage.gotoCreateReport();
      
      // Check for form labels
      const formInputs = await reportPage.page.$$('input, select, textarea');
      
      for (const input of formInputs.slice(0, 5)) { // Check first 5
        const label = await input.getAttribute('aria-label');
        const id = await input.getAttribute('id');
        
        if (id) {
          const associatedLabel = await reportPage.page.$(`label[for="${id}"]`);
          expect(label || associatedLabel).toBeTruthy();
        }
      }
    });

    test('should support keyboard navigation', async ({ page }) => {
      await reportPage.gotoCreateReport();
      
      // Test tab navigation
      await page.keyboard.press('Tab');
      const focusedElement = await page.evaluate(() => {
        return document.activeElement?.tagName || 'NONE';
      });
      
      expect(['INPUT', 'SELECT', 'TEXTAREA', 'BUTTON']).toContain(focusedElement);
    });
  });
});
