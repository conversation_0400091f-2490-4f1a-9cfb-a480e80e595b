// Webhook Integration API Test Suite
// Tests for webhook configuration, delivery, and management

const { test, expect } = require('@playwright/test');
const { API_BASE_URL, API_VERSION } = require('./base-api.spec');
const {
  createApiContext,
  getAuthHeaders,
  authenticate,
  createTestData,
  cleanupTestData
} = require('./helpers/api-helpers');
const crypto = require('crypto');

test.describe('Webhook Integration API', () => {
  let apiContext;
  let authToken;
  const createdWebhooks = [];
  let webhookEndpoint;

  test.beforeAll(async ({ playwright }) => {
    apiContext = await createApiContext(playwright);
    authToken = await authenticate(apiContext, {
      username: 'testuser',
      password: 'testpass123'
    });
    
    // Mock webhook endpoint URL
    webhookEndpoint = process.env.WEBHOOK_TEST_URL || 'https://webhook.site/test-endpoint';
  });

  test.afterAll(async () => {
    // Clean up created webhooks
    for (const webhookId of createdWebhooks) {
      await apiContext.delete(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${webhookId}/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );
    }
    
    if (apiContext) {
      await apiContext.dispose();
    }
  });

  test.describe('Webhook Configuration', () => {
    test('Create webhook endpoint', async () => {
      const webhookData = {
        name: 'Test Webhook',
        url: webhookEndpoint,
        events: ['project.created', 'project.updated', 'task.completed'],
        active: true,
        secret: crypto.randomBytes(32).toString('hex'),
        headers: {
          'X-Custom-Header': 'test-value'
        }
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: webhookData
        }
      );

      expect(response.status()).toBe(201);
      const data = await response.json();
      
      expect(data).toHaveProperty('id');
      expect(data).toHaveProperty('name');
      expect(data).toHaveProperty('url');
      expect(data).toHaveProperty('events');
      expect(data).toHaveProperty('active');
      expect(data).toHaveProperty('created_at');
      expect(data).toHaveProperty('signing_secret'); // Should not expose full secret
      
      createdWebhooks.push(data.id);
    });

    test('List webhooks', async () => {
      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      expect(response.status()).toBe(200);
      const data = await response.json();
      
      expect(Array.isArray(data.results || data)).toBe(true);
      if (data.results) {
        expect(data.results.length).toBeGreaterThan(0);
      }
    });

    test('Update webhook configuration', async () => {
      // Create webhook first
      const createResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            name: 'Update Test Webhook',
            url: webhookEndpoint,
            events: ['project.created']
          }
        }
      );
      
      const webhook = await createResponse.json();
      createdWebhooks.push(webhook.id);

      // Update webhook
      const updateResponse = await apiContext.patch(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${webhook.id}/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            events: ['project.created', 'project.deleted', 'task.created'],
            active: false
          }
        }
      );

      expect(updateResponse.status()).toBe(200);
      const updatedData = await updateResponse.json();
      
      expect(updatedData.events).toContain('task.created');
      expect(updatedData.active).toBe(false);
    });

    test('Delete webhook', async () => {
      // Create webhook to delete
      const createResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            name: 'Delete Test Webhook',
            url: webhookEndpoint,
            events: ['project.created']
          }
        }
      );
      
      const webhook = await createResponse.json();

      // Delete webhook
      const deleteResponse = await apiContext.delete(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${webhook.id}/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      expect(deleteResponse.status()).toBe(204);
      
      // Verify deletion
      const getResponse = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${webhook.id}/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );
      
      expect(getResponse.status()).toBe(404);
    });

    test('Webhook event subscription management', async () => {
      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/available-events/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      if (response.status() === 200) {
        const events = await response.json();
        
        expect(Array.isArray(events)).toBe(true);
        expect(events.length).toBeGreaterThan(0);
        
        // Check event structure
        events.forEach(event => {
          expect(event).toHaveProperty('name');
          expect(event).toHaveProperty('description');
          expect(event).toHaveProperty('category');
          expect(event).toHaveProperty('payload_schema');
        });
      }
    });
  });

  test.describe('Webhook Delivery', () => {
    let testWebhookId;

    test.beforeAll(async () => {
      // Create a webhook for delivery tests
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            name: 'Delivery Test Webhook',
            url: webhookEndpoint,
            events: ['project.created', 'task.updated'],
            active: true
          }
        }
      );
      
      const webhook = await response.json();
      testWebhookId = webhook.id;
      createdWebhooks.push(testWebhookId);
    });

    test('Test webhook endpoint', async () => {
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${testWebhookId}/test/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            event: 'test.ping',
            payload: {
              message: 'Test webhook delivery'
            }
          }
        }
      );

      expect(response.status()).toBe(200);
      const result = await response.json();
      
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('response_code');
      expect(result).toHaveProperty('response_time_ms');
      expect(result).toHaveProperty('delivered_at');
      
      if (result.status === 'success') {
        expect(result.response_code).toBe(200);
      }
    });

    test('Webhook delivery logs', async () => {
      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${testWebhookId}/deliveries/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      expect(response.status()).toBe(200);
      const deliveries = await response.json();
      
      expect(Array.isArray(deliveries.results || deliveries)).toBe(true);
      
      if (deliveries.results && deliveries.results.length > 0) {
        const delivery = deliveries.results[0];
        expect(delivery).toHaveProperty('id');
        expect(delivery).toHaveProperty('event');
        expect(delivery).toHaveProperty('status');
        expect(delivery).toHaveProperty('response_code');
        expect(delivery).toHaveProperty('response_time_ms');
        expect(delivery).toHaveProperty('delivered_at');
        expect(delivery).toHaveProperty('next_retry_at');
      }
    });

    test('Webhook retry failed delivery', async () => {
      // First, we need a failed delivery
      // This might require mocking or using a webhook that will fail
      
      const deliveriesResponse = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/${testWebhookId}/deliveries/?status=failed`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );
      
      const deliveries = await deliveriesResponse.json();
      
      if (deliveries.results && deliveries.results.length > 0) {
        const failedDelivery = deliveries.results[0];
        
        const retryResponse = await apiContext.post(
          `${API_BASE_URL}/${API_VERSION}/webhooks/${testWebhookId}/deliveries/${failedDelivery.id}/retry/`,
          {
            headers: await getAuthHeaders(authToken)
          }
        );
        
        expect([200, 202]).toContain(retryResponse.status());
        
        if (retryResponse.status() === 200) {
          const result = await retryResponse.json();
          expect(result).toHaveProperty('status');
          expect(result).toHaveProperty('retry_attempted_at');
        }
      }
    });

    test('Webhook signature verification', async () => {
      const secret = 'test-webhook-secret';
      const payload = JSON.stringify({
        event: 'project.created',
        data: { id: '123', name: 'Test Project' }
      });
      
      // Create HMAC signature
      const signature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');
      
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/verify-signature/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            payload: payload,
            signature: signature,
            secret: secret
          }
        }
      );
      
      if (response.status() === 200) {
        const result = await response.json();
        expect(result).toHaveProperty('valid');
        expect(result.valid).toBe(true);
      }
    });
  });

  test.describe('Webhook Filtering and Conditions', () => {
    test('Create webhook with filters', async () => {
      const webhookData = {
        name: 'Filtered Webhook',
        url: webhookEndpoint,
        events: ['project.updated'],
        filters: {
          project: {
            status: ['active', 'completed'],
            budget__gte: 50000
          }
        },
        active: true
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: webhookData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('filters');
        expect(data.filters.project.status).toContain('active');
        
        createdWebhooks.push(data.id);
      }
    });

    test('Webhook with custom payload transformation', async () => {
      const webhookData = {
        name: 'Transform Webhook',
        url: webhookEndpoint,
        events: ['task.completed'],
        payload_template: {
          id: '{{ task.id }}',
          title: '{{ task.title }}',
          completed_by: '{{ task.assignee.name }}',
          project_name: '{{ task.project.name }}',
          custom_field: 'static_value'
        }
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: webhookData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('payload_template');
        createdWebhooks.push(data.id);
      }
    });
  });

  test.describe('Webhook Security', () => {
    test('IP whitelist configuration', async () => {
      const webhookData = {
        name: 'IP Restricted Webhook',
        url: webhookEndpoint,
        events: ['project.created'],
        security: {
          ip_whitelist: ['***********/24', '********'],
          require_https: true
        }
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/`,
        {
          headers: await getAuthHeaders(authToken),
          data: webhookData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('security');
        expect(data.security.require_https).toBe(true);
        
        createdWebhooks.push(data.id);
      }
    });

    test('Webhook authentication methods', async () => {
      const authMethods = [
        {
          type: 'basic',
          config: {
            username: 'webhook-user',
            password: 'webhook-pass'
          }
        },
        {
          type: 'bearer',
          config: {
            token: 'bearer-token-123'
          }
        },
        {
          type: 'api_key',
          config: {
            header_name: 'X-API-Key',
            api_key: 'api-key-123'
          }
        }
      ];

      for (const auth of authMethods) {
        const webhookData = {
          name: `${auth.type} Auth Webhook`,
          url: webhookEndpoint,
          events: ['project.created'],
          authentication: auth
        };

        const response = await apiContext.post(
          `${API_BASE_URL}/${API_VERSION}/webhooks/`,
          {
            headers: await getAuthHeaders(authToken),
            data: webhookData
          }
        );

        if (response.status() === 201) {
          const data = await response.json();
          
          expect(data).toHaveProperty('authentication');
          expect(data.authentication.type).toBe(auth.type);
          // Sensitive data should be masked
          expect(data.authentication.config).not.toHaveProperty('password');
          expect(data.authentication.config).not.toHaveProperty('token');
          
          createdWebhooks.push(data.id);
        }
      }
    });
  });

  test.describe('Webhook Analytics', () => {
    test('Webhook delivery statistics', async () => {
      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/statistics/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      if (response.status() === 200) {
        const stats = await response.json();
        
        expect(stats).toHaveProperty('total_webhooks');
        expect(stats).toHaveProperty('active_webhooks');
        expect(stats).toHaveProperty('deliveries_24h');
        expect(stats).toHaveProperty('success_rate');
        expect(stats).toHaveProperty('average_response_time_ms');
      }
    });

    test('Individual webhook statistics', async () => {
      if (createdWebhooks.length > 0) {
        const webhookId = createdWebhooks[0];
        
        const response = await apiContext.get(
          `${API_BASE_URL}/${API_VERSION}/webhooks/${webhookId}/statistics/`,
          {
            headers: await getAuthHeaders(authToken)
          }
        );

        if (response.status() === 200) {
          const stats = await response.json();
          
          expect(stats).toHaveProperty('total_deliveries');
          expect(stats).toHaveProperty('successful_deliveries');
          expect(stats).toHaveProperty('failed_deliveries');
          expect(stats).toHaveProperty('average_response_time_ms');
          expect(stats).toHaveProperty('last_delivery_at');
          expect(stats).toHaveProperty('events_breakdown');
        }
      }
    });
  });

  test.describe('Bulk Webhook Operations', () => {
    test('Bulk enable/disable webhooks', async () => {
      // Create multiple webhooks
      const webhookIds = [];
      for (let i = 0; i < 3; i++) {
        const response = await apiContext.post(
          `${API_BASE_URL}/${API_VERSION}/webhooks/`,
          {
            headers: await getAuthHeaders(authToken),
            data: {
              name: `Bulk Test ${i}`,
              url: webhookEndpoint,
              events: ['project.created'],
              active: true
            }
          }
        );
        
        const webhook = await response.json();
        webhookIds.push(webhook.id);
        createdWebhooks.push(webhook.id);
      }

      // Bulk disable
      const disableResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/bulk_update/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            webhook_ids: webhookIds,
            updates: {
              active: false
            }
          }
        }
      );

      if (disableResponse.status() === 200) {
        const result = await disableResponse.json();
        
        expect(result).toHaveProperty('updated');
        expect(result.updated).toBe(3);
      }
    });

    test('Bulk delete webhook deliveries', async () => {
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/deliveries/bulk_delete/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            filters: {
              status: 'failed',
              created_at__lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            }
          }
        }
      );

      if (response.status() === 200) {
        const result = await response.json();
        
        expect(result).toHaveProperty('deleted');
        expect(typeof result.deleted).toBe('number');
      }
    });
  });

  test.describe('Webhook Templates', () => {
    test('List webhook templates', async () => {
      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/webhooks/templates/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      if (response.status() === 200) {
        const templates = await response.json();
        
        expect(Array.isArray(templates)).toBe(true);
        
        if (templates.length > 0) {
          const template = templates[0];
          expect(template).toHaveProperty('id');
          expect(template).toHaveProperty('name');
          expect(template).toHaveProperty('description');
          expect(template).toHaveProperty('events');
          expect(template).toHaveProperty('default_config');
        }
      }
    });

    test('Create webhook from template', async () => {
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/webhooks/from_template/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            template_id: 'slack-notifications',
            url: webhookEndpoint,
            customizations: {
              channel: '#notifications'
            }
          }
        }
      );

      if (response.status() === 201) {
        const webhook = await response.json();
        
        expect(webhook).toHaveProperty('id');
        expect(webhook).toHaveProperty('template_id');
        
        createdWebhooks.push(webhook.id);
      }
    });
  });
});