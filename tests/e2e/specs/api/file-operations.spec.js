// File Operations API Test Suite
// Tests for file upload, download, and document management

const { test, expect } = require('@playwright/test');
const { API_BASE_URL, API_VERSION } = require('./base-api.spec');
const {
  createApiContext,
  getAuthHeaders,
  authenticate,
  createTestData,
  testFileUpload,
  cleanupTestData
} = require('./helpers/api-helpers');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

test.describe('File Operations API', () => {
  let apiContext;
  let authToken;
  const createdResources = [];
  let tempDir;

  test.beforeAll(async ({ playwright }) => {
    apiContext = await createApiContext(playwright);
    authToken = await authenticate(apiContext, {
      username: 'testuser',
      password: 'testpass123'
    });
    
    // Create temp directory for test files
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'api-test-'));
  });

  test.afterAll(async () => {
    // Clean up created resources
    for (const { type, id } of createdResources) {
      await cleanupTestData(apiContext, type, id, authToken);
    }
    
    // Clean up temp directory
    await fs.rm(tempDir, { recursive: true, force: true });
    
    if (apiContext) {
      await apiContext.dispose();
    }
  });

  async function createTestFile(filename, content) {
    const filePath = path.join(tempDir, filename);
    await fs.writeFile(filePath, content);
    return filePath;
  }

  test.describe('File Upload', () => {
    test('Upload single file', async () => {
      const testFilePath = await createTestFile('test-document.txt', 'This is a test document content');
      
      const formData = {
        file: {
          name: 'test-document.txt',
          mimeType: 'text/plain',
          buffer: await fs.readFile(testFilePath)
        },
        title: 'Test Document Upload',
        description: 'Testing file upload functionality'
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: formData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('id');
        expect(data).toHaveProperty('title');
        expect(data).toHaveProperty('file_name');
        expect(data).toHaveProperty('file_size');
        expect(data).toHaveProperty('mime_type');
        expect(data).toHaveProperty('download_url');
        
        expect(data.file_name).toBe('test-document.txt');
        expect(data.mime_type).toBe('text/plain');
        
        createdResources.push({ type: 'documents', id: data.id });
      }
    });

    test('Upload multiple files', async () => {
      const files = [];
      for (let i = 1; i <= 3; i++) {
        const filePath = await createTestFile(`test-file-${i}.txt`, `Content of file ${i}`);
        files.push({
          name: `test-file-${i}.txt`,
          mimeType: 'text/plain',
          buffer: await fs.readFile(filePath)
        });
      }

      const formData = {
        'files[]': files,
        title: 'Multiple Files Upload',
        description: 'Testing multiple file upload'
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload_multiple/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: formData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('uploaded');
        expect(data.uploaded).toBe(3);
        expect(data).toHaveProperty('documents');
        expect(Array.isArray(data.documents)).toBe(true);
        expect(data.documents.length).toBe(3);
        
        data.documents.forEach(doc => {
          createdResources.push({ type: 'documents', id: doc.id });
        });
      }
    });

    test('Upload with different file types', async () => {
      const fileTypes = [
        { name: 'document.pdf', mimeType: 'application/pdf', content: Buffer.from('PDF content') },
        { name: 'image.png', mimeType: 'image/png', content: Buffer.from([0x89, 0x50, 0x4E, 0x47]) },
        { name: 'spreadsheet.xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', content: Buffer.from('Excel content') },
        { name: 'data.json', mimeType: 'application/json', content: Buffer.from('{"test": "data"}') }
      ];

      for (const fileType of fileTypes) {
        const response = await apiContext.post(
          `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
          {
            headers: await getAuthHeaders(authToken),
            multipart: {
              file: {
                name: fileType.name,
                mimeType: fileType.mimeType,
                buffer: fileType.content
              }
            }
          }
        );

        if (response.status() === 201) {
          const data = await response.json();
          expect(data.mime_type).toBe(fileType.mimeType);
          createdResources.push({ type: 'documents', id: data.id });
        } else if (response.status() === 415) {
          // Unsupported media type
          const error = await response.json();
          expect(error).toHaveProperty('detail');
          expect(error.detail).toContain('Unsupported file type');
        }
      }
    });

    test('Upload file size limits', async () => {
      // Create a large file (10MB)
      const largeContent = Buffer.alloc(10 * 1024 * 1024, 'x');
      
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'large-file.txt',
              mimeType: 'text/plain',
              buffer: largeContent
            }
          }
        }
      );

      if (response.status() === 413) {
        // Payload too large
        const error = await response.json();
        expect(error).toHaveProperty('detail');
        expect(error.detail).toContain('File size exceeds limit');
      } else if (response.status() === 201) {
        const data = await response.json();
        createdResources.push({ type: 'documents', id: data.id });
      }
    });

    test('Upload with metadata', async () => {
      const testFilePath = await createTestFile('metadata-test.txt', 'File with metadata');
      
      const formData = {
        file: {
          name: 'metadata-test.txt',
          mimeType: 'text/plain',
          buffer: await fs.readFile(testFilePath)
        },
        title: 'Document with Metadata',
        metadata: JSON.stringify({
          author: 'Test User',
          department: 'Engineering',
          tags: ['test', 'upload', 'metadata'],
          custom_field: 'custom value'
        })
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: formData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('metadata');
        expect(data.metadata.author).toBe('Test User');
        expect(data.metadata.tags).toContain('test');
        
        createdResources.push({ type: 'documents', id: data.id });
      }
    });
  });

  test.describe('File Download', () => {
    let documentId;

    test.beforeAll(async () => {
      // Upload a file for download tests
      const testFilePath = await createTestFile('download-test.txt', 'Content for download testing');
      
      const uploadResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'download-test.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(testFilePath)
            }
          }
        }
      );
      
      if (uploadResponse.status() === 201) {
        const data = await uploadResponse.json();
        documentId = data.id;
        createdResources.push({ type: 'documents', id: documentId });
      }
    });

    test('Download file by ID', async () => {
      if (!documentId) {
        test.skip();
        return;
      }

      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/download/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      expect(response.status()).toBe(200);
      
      const headers = response.headers();
      expect(headers).toHaveProperty('content-disposition');
      expect(headers['content-disposition']).toContain('attachment');
      expect(headers['content-disposition']).toContain('download-test.txt');
      
      const content = await response.text();
      expect(content).toBe('Content for download testing');
    });

    test('Download with inline disposition', async () => {
      if (!documentId) {
        test.skip();
        return;
      }

      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/view/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      if (response.status() === 200) {
        const headers = response.headers();
        expect(headers['content-disposition']).toContain('inline');
      }
    });

    test('Download non-existent file', async () => {
      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/documents/non-existent-id/download/`,
        {
          headers: await getAuthHeaders(authToken)
        }
      );

      expect(response.status()).toBe(404);
    });

    test('Download with range headers', async () => {
      if (!documentId) {
        test.skip();
        return;
      }

      const response = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/download/`,
        {
          headers: {
            ...await getAuthHeaders(authToken),
            'Range': 'bytes=0-10'
          }
        }
      );

      if (response.status() === 206) { // Partial content
        const headers = response.headers();
        expect(headers).toHaveProperty('content-range');
        expect(headers['accept-ranges']).toBe('bytes');
        
        const content = await response.text();
        expect(content.length).toBe(11); // 0-10 inclusive
      }
    });
  });

  test.describe('File Management', () => {
    test('Update file metadata', async () => {
      // Upload a file first
      const testFilePath = await createTestFile('update-test.txt', 'File to update');
      
      const uploadResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'update-test.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(testFilePath)
            },
            title: 'Original Title'
          }
        }
      );
      
      const uploadData = await uploadResponse.json();
      const documentId = uploadData.id;
      createdResources.push({ type: 'documents', id: documentId });

      // Update metadata
      const updateResponse = await apiContext.patch(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/`,
        {
          headers: await getAuthHeaders(authToken),
          data: {
            title: 'Updated Title',
            description: 'Updated description',
            metadata: {
              version: '2.0',
              reviewed: true
            }
          }
        }
      );

      expect(updateResponse.status()).toBe(200);
      const updateData = await updateResponse.json();
      
      expect(updateData.title).toBe('Updated Title');
      expect(updateData.metadata.version).toBe('2.0');
    });

    test('Replace file content', async () => {
      // Upload initial file
      const initialPath = await createTestFile('initial.txt', 'Initial content');
      
      const uploadResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'initial.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(initialPath)
            }
          }
        }
      );
      
      const uploadData = await uploadResponse.json();
      const documentId = uploadData.id;
      createdResources.push({ type: 'documents', id: documentId });

      // Replace file content
      const newPath = await createTestFile('replacement.txt', 'Replaced content');
      
      const replaceResponse = await apiContext.put(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/file/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'replacement.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(newPath)
            }
          }
        }
      );

      if (replaceResponse.status() === 200) {
        const replaceData = await replaceResponse.json();
        
        expect(replaceData.file_name).toBe('replacement.txt');
        expect(replaceData.version).toBeGreaterThan(uploadData.version || 1);
      }
    });

    test('File versioning', async () => {
      // Upload initial version
      const v1Path = await createTestFile('versioned.txt', 'Version 1 content');
      
      const uploadResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'versioned.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(v1Path)
            }
          }
        }
      );
      
      const uploadData = await uploadResponse.json();
      const documentId = uploadData.id;
      createdResources.push({ type: 'documents', id: documentId });

      // Upload new version
      const v2Path = await createTestFile('versioned-v2.txt', 'Version 2 content');
      
      const versionResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/versions/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'versioned-v2.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(v2Path)
            },
            comment: 'Updated content for version 2'
          }
        }
      );

      if (versionResponse.status() === 201) {
        // List versions
        const versionsResponse = await apiContext.get(
          `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/versions/`,
          {
            headers: await getAuthHeaders(authToken)
          }
        );
        
        const versionsData = await versionsResponse.json();
        expect(Array.isArray(versionsData)).toBe(true);
        expect(versionsData.length).toBeGreaterThanOrEqual(2);
      }
    });
  });

  test.describe('Image Processing', () => {
    test('Upload image with thumbnail generation', async () => {
      // Create a simple PNG image
      const pngHeader = Buffer.from([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A
      ]);
      
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/images/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'test-image.png',
              mimeType: 'image/png',
              buffer: pngHeader
            },
            generate_thumbnail: 'true',
            thumbnail_size: '200x200'
          }
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('id');
        expect(data).toHaveProperty('thumbnail_url');
        expect(data).toHaveProperty('dimensions');
        
        createdResources.push({ type: 'images', id: data.id });
      }
    });

    test('Image resize on upload', async () => {
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/images/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'large-image.jpg',
              mimeType: 'image/jpeg',
              buffer: Buffer.from('JPEG content')
            },
            max_width: '1024',
            max_height: '768',
            maintain_aspect_ratio: 'true'
          }
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        if (data.dimensions) {
          expect(data.dimensions.width).toBeLessThanOrEqual(1024);
          expect(data.dimensions.height).toBeLessThanOrEqual(768);
        }
        
        createdResources.push({ type: 'images', id: data.id });
      }
    });
  });

  test.describe('Bulk File Operations', () => {
    test('Bulk file upload', async () => {
      const files = [];
      for (let i = 1; i <= 5; i++) {
        const filePath = await createTestFile(`bulk-${i}.txt`, `Bulk file ${i} content`);
        files.push({
          name: `bulk-${i}.txt`,
          mimeType: 'text/plain',
          buffer: await fs.readFile(filePath)
        });
      }

      const formData = {
        'files[]': files,
        project_id: 'test-project-id',
        tags: JSON.stringify(['bulk-upload', 'test'])
      };

      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/bulk_upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: formData
        }
      );

      if (response.status() === 201) {
        const data = await response.json();
        
        expect(data).toHaveProperty('uploaded');
        expect(data.uploaded).toBe(5);
        expect(data).toHaveProperty('documents');
        
        data.documents.forEach(doc => {
          createdResources.push({ type: 'documents', id: doc.id });
        });
      }
    });

    test('Bulk download as zip', async () => {
      // Create and upload test files
      const fileIds = [];
      for (let i = 1; i <= 3; i++) {
        const filePath = await createTestFile(`zip-${i}.txt`, `File ${i} for zip`);
        
        const uploadResponse = await apiContext.post(
          `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
          {
            headers: await getAuthHeaders(authToken),
            multipart: {
              file: {
                name: `zip-${i}.txt`,
                mimeType: 'text/plain',
                buffer: await fs.readFile(filePath)
              }
            }
          }
        );
        
        if (uploadResponse.status() === 201) {
          const data = await uploadResponse.json();
          fileIds.push(data.id);
          createdResources.push({ type: 'documents', id: data.id });
        }
      }

      // Download as zip
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/download_zip/`,
        {
          headers: await getAuthHeaders(authToken),
          data: { document_ids: fileIds }
        }
      );

      if (response.status() === 200) {
        const headers = response.headers();
        expect(headers['content-type']).toContain('application/zip');
        expect(headers['content-disposition']).toContain('.zip');
      }
    });
  });

  test.describe('File Security', () => {
    test('Virus scanning on upload', async () => {
      // Upload a file that might trigger scanning
      const eicarTest = 'X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*';
      
      const response = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'test-virus.txt',
              mimeType: 'text/plain',
              buffer: Buffer.from(eicarTest)
            }
          }
        }
      );

      // Should either reject the file or mark it for scanning
      if (response.status() === 400) {
        const error = await response.json();
        expect(error.detail).toContain('virus');
      } else if (response.status() === 201) {
        const data = await response.json();
        expect(data).toHaveProperty('scan_status');
        createdResources.push({ type: 'documents', id: data.id });
      }
    });

    test('Access control for files', async () => {
      // Upload a private file
      const privatePath = await createTestFile('private.txt', 'Private content');
      
      const uploadResponse = await apiContext.post(
        `${API_BASE_URL}/${API_VERSION}/documents/upload/`,
        {
          headers: await getAuthHeaders(authToken),
          multipart: {
            file: {
              name: 'private.txt',
              mimeType: 'text/plain',
              buffer: await fs.readFile(privatePath)
            },
            visibility: 'private'
          }
        }
      );
      
      const uploadData = await uploadResponse.json();
      const documentId = uploadData.id;
      createdResources.push({ type: 'documents', id: documentId });

      // Try to access without authentication
      const unauthResponse = await apiContext.get(
        `${API_BASE_URL}/${API_VERSION}/documents/${documentId}/download/`
      );
      
      expect(unauthResponse.status()).toBe(401);
    });
  });
});