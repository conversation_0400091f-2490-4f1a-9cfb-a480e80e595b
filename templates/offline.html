<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - CLEAR Infrastructure Platform</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#8CC63F">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CLEAR">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom Offline Styles -->
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 1rem;
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        .offline-title {
            color: #8CC63F;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .offline-subtitle {
            color: #6c757d;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 2rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #8CC63F, #7AB82F);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #7AB82F, #6BA526);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(140, 198, 63, 0.3);
        }

        .btn-outline-secondary {
            border: 2px solid #dee2e6;
            color: #6c757d;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-secondary:hover {
            background: #f8f9fa;
            border-color: #8CC63F;
            color: #8CC63F;
        }

        .offline-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #dee2e6;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            text-align: left;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8CC63F, #7AB82F);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .feature-text {
            color: #495057;
            font-size: 0.95rem;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            z-index: 1000;
        }

        .status-offline {
            background: #ffc107;
            color: #212529;
        }

        .status-online {
            background: #28a745;
            color: white;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @media (max-width: 576px) {
            .offline-container {
                margin: 0.5rem;
                padding: 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status status-offline" id="connectionStatus">
        📡 Offline
    </div>

    <div class="offline-container">
        <div class="offline-icon">📡</div>

        <h1 class="offline-title">You're Offline</h1>

        <p class="offline-subtitle">
            It looks like you've lost your internet connection. Don't worry - some features are still available while you're offline.
        </p>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="checkConnection()">
                🔄 Try Again
            </button>

            <button class="btn btn-outline-secondary" onclick="goToHomepage()">
                🏠 Go to Homepage
            </button>
        </div>

        <div class="offline-features">
            <h5 class="mb-3" style="color: #495057;">Available Offline:</h5>

            <div class="feature-item">
                <div class="feature-icon">👁️</div>
                <div class="feature-text">
                    View previously loaded projects and data
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">🗺️</div>
                <div class="feature-text">
                    Browse cached infrastructure maps
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">📄</div>
                <div class="feature-text">
                    Access downloaded documents and reports
                </div>
            </div>

            <div class="feature-item">
                <div class="feature-icon">✏️</div>
                <div class="feature-text">
                    Create notes and drafts (synced when online)
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Connection status monitoring
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const isOnline = navigator.onLine;

            if (isOnline) {
                statusElement.className = 'connection-status status-online';
                statusElement.innerHTML = '📶 Online';

                // Auto-redirect to homepage when back online
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusElement.innerHTML = '📡 Offline';
            }
        }

        // Check connection and reload if online
        function checkConnection() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                // Show feedback that we're still offline
                const button = event.target;
                const originalText = button.innerHTML;

                button.innerHTML = '❌ Still Offline';
                button.classList.add('btn-warning');
                button.classList.remove('btn-primary');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-warning');
                    button.classList.add('btn-primary');
                }, 2000);
            }
        }

        // Navigate to homepage
        function goToHomepage() {
            window.location.href = '/';
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);

        // Service Worker registration for offline page
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/js/workers/pwa-service-worker.js')
                .then(registration => {
                    console.log('[Offline Page] Service Worker registered');
                })
                .catch(error => {
                    console.log('[Offline Page] Service Worker registration failed:', error);
                });
        }

        // Handle back button
        window.addEventListener('popstate', function(event) {
            if (navigator.onLine) {
                return;
            } else {
                event.preventDefault();
                history.pushState(null, null, '/offline/');
            }
        });

        // Prevent form submissions when offline
        document.addEventListener('submit', function(event) {
            if (!navigator.onLine) {
                event.preventDefault();
                alert('This action requires an internet connection. Please try again when you are back online.');
            }
        });

        // Auto-refresh when connection is restored
        let wasOffline = !navigator.onLine;

        setInterval(() => {
            const isOnline = navigator.onLine;

            if (wasOffline && isOnline) {
                console.log('[Offline Page] Connection restored, redirecting...');
                window.location.href = '/';
            }

            wasOffline = !isOnline;
        }, 1000);
    </script>
</body>
</html>
