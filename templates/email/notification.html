<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ subject|escape }} - {{ site_name|escape }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #0d6efd;
            padding-bottom: 20px;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #0d6efd;
            text-decoration: none;
        }
        .content {
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #0b5ed7;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <a href="{{ site_url|escape }}" class="logo" role="link">{{ site_name|escape }}</a>
      </div>
      <div class="content">
        <p>Hello {{ user.get_full_name|default:user.username }},</p>

        {% if html_message %}
          {{ html_message|safe }}
        {% else %}
          <p>{{ message|linebreaksbr }}</p>
        {% endif %}

      </div>
      <div style="text-align: center; margin: 30px 0;">
        <a href="{{ site_url|escape }}" class="button" role="link">Visit {{ site_name|escape }}</a>
      </div>
      <div class="footer">
        <p>
          Best regards,
          <br />
          The {{ site_name|escape }} Team
        </p>
        <p style="font-size: 12px; color: #999; margin-top: 20px;">
          This is an automated notification from {{ site_name|escape }}.
          <br />
          You're receiving this because you have an account on our platform.
          <br />
          To manage your notification preferences, visit your
          <a href="{{ site_url|escape }}/settings/notifications" role="link">account settings</a>.
        </p>
      </div>
    </div>
  </body>
</html>
